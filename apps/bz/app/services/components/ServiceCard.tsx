'use client';

import React from 'react';
import styled from '@benzinga/themetron';
import { BenzingaLogo } from '@benzinga/logos-ui';
import { ServiceProduct } from '../../../pages/api/services-products';

export const ServiceCard: React.FC<{ product: ServiceProduct }> = ({ product }) => {
  return (
    <ServiceCardWrapper $accent={product.logoText.accentColor}>
      <div className="product-card-top">
        <div className="product-card-header-logo">
          <BenzingaLogo variant="dark" />
          <div>{product.logoText.brandName}</div>
        </div>
        <div className="product-card-header-content">
          <h2>{product.title}</h2>
          <p>{product.description}</p>
        </div>
      </div>
      <div className="product-card-bottom">
        <div className="product-card-body-highlight">
          <h3 className={product.highlight.shrink ? 'trial-text' : ''}>{product.highlight.value}</h3>
          <span>{product.highlight.title}</span>
          <p>{product.highlight.description}</p>
        </div>
        <a className="product-card-button" href={product.url} rel="noreferrer" target="_blank">
          <button>get started here</button>
        </a>
      </div>
    </ServiceCardWrapper>
  );
};

const ServiceCardWrapper = styled.div<{ $accent }>`
  border-radius: 4px;
  background: white;
  display: flex;
  flex-direction: column;
  z-index: 9;
  color: black;
  border: 1px solid #ceddf2;

  .product-card-top,
  .product-card-bottom {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 1rem;
    height: 250px;
  }

  .product-card-top {
    background-color: rgba(242, 248, 255, 0.4);
    border-bottom: 1px solid #e1ebfa;
  }

  .product-card-header-logo {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
    padding-left: 0.5rem;

    .benzinga-logo {
      width: 120px;
    }

    div {
      color: ${props => props.$accent ?? '#1693E6'};
      text-transform: capitalize;
      font-size: 22px;
      line-height: 18px;
    }
  }

  .product-card-header-content {
    text-align: left;
    min-height: 124px;

    h2 {
      font-size: 20px;
      line-height: 32px;
      font-weight: 700;
      color: #192940;
    }

    p {
      color: #395173;
      font-size: 16px;
      line-height: 26px;
      font-weight: 400;
    }
  }

  .product-card-body-highlight {
    text-align: left;

    h3 {
      font-size: 36px;
      font-weight: 400;
      color: #3f83f8;

      &.trial-text {
        font-size: 24px;
        margin: 10px 0px;
      }
    }

    span {
      font-size: 14px;
      line-height: 24px;
      font-weight: 700;
      text-transform: uppercase;
      color: #192940;
    }

    p {
      font-size: 14px;
      line-height: 24px;
      font-weight: 400px;
      color: #395173;
      margin: 0;
    }
  }

  .product-card-button {
    display: flex;
    justify-content: center;
    align-items: center;

    button {
      font-weight: 700;
      font-size: 14px;
      padding: 12px 32px;
      width: 100%;
      background-color: #3f83f8;
      color: white;
      border-radius: 4px;
      text-align: center;
      transition: background-color 0.3s ease-in-out;
      text-transform: uppercase;
    }
  }
`;
