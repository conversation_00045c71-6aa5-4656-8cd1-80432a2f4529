import React, { Suspense } from 'react';
import { DateTime } from 'luxon';

import { SectionTitle } from '@benzinga/core-ui';
import { BasicNewsManager, News } from '@benzinga/basic-news-manager';
import { Ratings } from '@benzinga/calendar-manager';
import { formatPrice, formatUpsideDownside } from '@benzinga/calendars';

import { getGlobalSession } from '../../api/session';

import QuoteCalendar from '../../../src/components/Quote/QuoteCalendar';
import DrilldownFooter from '../../../src/components/Quote/V2/DrilldownFooter';
import { QuoteLayout } from '../../../src/components/Quote/V2/QuoteLayout';
import AnalystRatingsFAQ, { stockTrend } from '../../../src/components/Quote/FAQ/AnalystRatingsFAQ';
import ZacksHeadScript from '../../../src/components/Quote/V2/ZacksHeadScript';
import getQuoteV2PageData, { QuoteV2PageProps, getQuoteFeedTypeByLocale } from '../../../src/quoteV2Utils';
import { AnalystRatingSummary, AnalystRatingSummaryBoxes } from '../../../src/components/Quote/AnalystRatingSummary';
import { useTranslation } from 'react-i18next';
import i18n, { LocaleType, setLanguageByHost } from '@benzinga/translate';
import { FullDisplayBullVsBear } from '../../../src/components/Quote/V2/BullVsBear';
import { QuoteAltSuggestions } from '../../../src/components/Quote/QuoteAltSuggestions';
import { sanitizeHTML } from '@benzinga/frontend-utils';

const RaptiveAdPlaceholder = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.RaptiveAdPlaceholder };
  }),
);

interface AnalystRatingsQuoteV2PageProps extends QuoteV2PageProps {
  analystRatingsNews: News[] | null;
}

const QuoteAnalystRatingsPage: React.FC<AnalystRatingsQuoteV2PageProps> = ({
  activeTab,
  advertiserProfile,
  altSuggestions,
  isAdvertiser,
  isETF,
  metaProps,
  overviewValues,
  peersChartData,
  profile,
  showGovLink,
  symbol,
}) => {
  const { t } = useTranslation(['quote', 'common'], { i18n });

  if (altSuggestions) {
    return <QuoteAltSuggestions {...altSuggestions} symbol={symbol} />;
  }

  const lastThreeRatings = profile.ratingsSummary.ratings
    .filter(rating => {
      const upsideDownside = formatUpsideDownside(
        rating?.quote?.last ?? rating?.quote?.lastTradePrice,
        rating.pt_current,
      );
      return upsideDownside !== '';
    })
    .slice(0, 3);

  const formatDate = (date: string) =>
    DateTime.fromFormat(date, 'yyyy-MM-dd').setLocale(i18n.language).toFormat('MMMM d, yyyy');

  const generatePriceTargetAndAnalystSummary = (): string | null => {
    if (!lastThreeRatings[0]) return null;
    const lastTradePrice = lastThreeRatings[0].quote.lastTradePrice;
    const mostRecentFirmNames = lastThreeRatings.map(rating => rating.analyst);
    const areAllMostRecentFirmNamesTheSame = mostRecentFirmNames.every(name => name === mostRecentFirmNames[0]);
    const mostRecentFirmDates = lastThreeRatings.map(rating => rating.date);

    const listFormatter = new Intl.ListFormat(i18n.language, {
      style: 'long',
      type: 'conjunction',
    });
    const analystRatingsCompanies = listFormatter.format(mostRecentFirmNames);
    const analystRatingsDates = mostRecentFirmDates.map(date => {
      const formattedDate = formatDate(date);
      return formattedDate;
    });

    const analystRatingsDateText = analystRatingsDates.every(date => date === analystRatingsDates[0])
      ? analystRatingsDates[0]
      : listFormatter.format(analystRatingsDates);

    const combinedPriceTargetFromLastThreeRatings = lastThreeRatings.reduce(
      (acc, rating) => acc + (rating.pt_current ? Number(rating.pt_current) : 0),
      0,
    );
    const averagePriceTargetFromLastThreeRatings = combinedPriceTargetFromLastThreeRatings / lastThreeRatings.length;

    const getAveragePriceTargetPercentageChange = () => {
      let change = (averagePriceTargetFromLastThreeRatings - lastTradePrice) * 100;
      change = change / lastTradePrice;
      return change.toFixed(2);
    };

    const getAverageTickerDirection = () => {
      const result = averagePriceTargetFromLastThreeRatings > lastTradePrice ? stockTrend.upside : stockTrend.downside;
      return result;
    };

    const averagePriceTargetPercentageChange = getAveragePriceTargetPercentageChange();
    const averageTickerDirection = getAverageTickerDirection();

    const companyName = profile.richQuoteData.companyStandardName;
    const consensusPriceTarget = formatPrice(profile?.ratingsSummary?.consensusPriceTarget ?? 0);
    const numRatings = profile.ratingsSummary.numUniqueRatings;
    const numLastThreeRatings = lastThreeRatings.length;
    const ratingsCompanies = areAllMostRecentFirmNamesTheSame ? mostRecentFirmNames[0] : analystRatingsCompanies;
    const ratingsDate = analystRatingsDateText;
    const averagePriceTarget = formatPrice(averagePriceTargetFromLastThreeRatings);
    const averagePercentageChange = averagePriceTargetPercentageChange;
    const tickerDirection = averageTickerDirection;

    const highestPriceTarget = profile.ratingsSummary.highestPriceRating?.price
      ? formatPrice(profile.ratingsSummary.highestPriceRating.price)
      : null;
    const highestPriceTargetFirm = profile.ratingsSummary.highestPriceRating?.firm || null;
    const highestPriceTargetDate = profile.ratingsSummary.highestPriceRating?.date
      ? formatDate(profile.ratingsSummary.highestPriceRating.date)
      : null;

    const lowestPriceTarget = profile.ratingsSummary.lowestPriceRating?.price
      ? formatPrice(profile.ratingsSummary.lowestPriceRating.price)
      : null;
    const lowestPriceTargetFirm = profile.ratingsSummary.lowestPriceRating?.firm;
    const lowestPriceTargetDate = profile.ratingsSummary.lowestPriceRating?.date
      ? formatDate(profile.ratingsSummary.lowestPriceRating.date)
      : null;

    const result: string = t('Quote.AnalystRatings.description', {
      averagePercentageChange,
      averagePriceTarget,
      companyName,
      consensusPriceTarget,
      highestPriceTarget,
      highestPriceTargetDate,
      highestPriceTargetFirm,
      lowestPriceTarget,
      lowestPriceTargetDate,
      lowestPriceTargetFirm,
      numLastThreeRatings,
      numRatings,
      ratingsCompanies,
      ratingsDate,
      tickerDirection,
    });
    return result;
  };

  const priceTargetAndAnalystSummary = generatePriceTargetAndAnalystSummary();

  return (
    <QuoteLayout
      advertiserProfile={advertiserProfile}
      defaultPath={activeTab}
      isAdvertiser={isAdvertiser}
      profile={profile}
      showGovLink={showGovLink}
      symbol={symbol}
    >
      <ZacksHeadScript />
      {/* Beef this up. */}
      <div className="main-div flex flex-col gap-y-8">
        <div className="flex flex-col lg:flex-row">
          <div className="flex flex-col">
            <div className="w-full md:flex">
              <div className="w-full">
                <AnalystRatingSummaryBoxes ratingsSummary={profile.ratingsSummary} />
                <FullDisplayBullVsBear profile={profile} statements={overviewValues?.bullSayBearSay ?? null} />
                <div className="flex flex-col gap-2 mt-4">
                  <SectionTitle level={1} uppercase={false}>
                    {metaProps.title}
                  </SectionTitle>
                  {/* <p className="section-description">{metaProps.description}</p> */}
                  <p
                    className="section-description"
                    dangerouslySetInnerHTML={{
                      __html: priceTargetAndAnalystSummary
                        ? priceTargetAndAnalystSummary
                        : t('Quote.Paragraphs.analyst-ratings', { stockName: profile.richQuoteData?.name }),
                    }}
                  />
                </div>
              </div>
              <Suspense fallback={<div />}>
                <RaptiveAdPlaceholder
                  className="w-[300px] min-w-[300px] h-[250px] md:ml-4"
                  disableMargins={true}
                  onlyDesktop={true}
                  type="static-sidebar"
                />
              </Suspense>
            </div>
            <div className="flex flex-col gap-y-8">
              <AnalystRatingSummary overviewValues={overviewValues} ratingsSummary={profile.ratingsSummary} />
              <div className="flex flex-col">
                <SectionTitle className="mb-2" level={3}>
                  {t('Quote.AnalystRatings.analyst-ratings-for', { name: profile.richQuoteData?.name })}
                </SectionTitle>
                <QuoteCalendar
                  calendar="analyst-ratings"
                  initialData={profile.ratingsSummary.ratings}
                  symbol={symbol}
                />
              </div>
            </div>
          </div>
        </div>
        <RaptiveAdPlaceholder className="w-[300px] min-w-[300px]" onlyMobile={true} type="content-small" />
        <DrilldownFooter
          advertiserProfile={advertiserProfile}
          isAdvertiser={isAdvertiser}
          isETF={isETF}
          leftSection={
            <>
              <SectionTitle level={3} size="2xl">
                {t('Quote.FAQ.faq')}
              </SectionTitle>
              <AnalystRatingsFAQ quote={profile.richQuoteData} ratingsSummary={profile.ratingsSummary} />
              <p>
                {t('Buttons.browse', { ns: 'common' })}{' '}
                {/* eslint-disable-next-line @next/next/no-html-link-for-pages */}
                <a href="/analyst-stock-ratings">{t('Quote.AnalystRatings.analyst-ratings-and-price-targets')}</a>{' '}
                {t('Quote.Helpers.on-all-stocks')}
              </p>
            </>
          }
          peers={profile.quotes}
          peersChartData={peersChartData}
          symbol={symbol}
          type={profile.schedule.type}
        />
      </div>
    </QuoteLayout>
  );
};

export default QuoteAnalystRatingsPage;

export const getServerSideProps = async ({ query: { ticker }, req, res }) => {
  if (req?.headers?.host) {
    await setLanguageByHost(req?.headers?.host); // set language for server side translations
  }

  const symbol: string = sanitizeHTML(ticker) || '';
  const activeTab = 'analyst-ratings';

  const session = getGlobalSession();
  const basicNewsManager = session.getManager(BasicNewsManager);

  const pageData = await getQuoteV2PageData(
    symbol,
    activeTab,
    getQuoteFeedTypeByLocale(i18n.language as LocaleType),
    req?.headers?.host,
  );

  if (pageData?.props?.errorCode) {
    res.statusCode = pageData.props.errorCode;
    return pageData;
  }

  // Only assert when adding specific properties
  if ('props' in pageData) {
    const analystRatingsNews = await basicNewsManager.simplyQueryNews({ channels: [67], tickers: [symbol] });
    (pageData.props as AnalystRatingsQuoteV2PageProps).analystRatingsNews = (analystRatingsNews?.ok as News[]) || null;

    const typedProps = pageData.props as AnalystRatingsQuoteV2PageProps;
    const ratings = typedProps?.profile?.ratingsSummary?.ratings;
    if (Array.isArray(ratings) && typedProps.profile.quotes) {
      typedProps.profile.ratingsSummary.ratings.map((rating: Ratings) => {
        const quote = typedProps?.profile?.quotes?.[symbol] || null;
        if (quote) {
          rating.quote = quote;
        }
      });
    }
  }

  return pageData;
};
