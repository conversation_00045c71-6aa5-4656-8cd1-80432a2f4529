{"name": "@benzinga/root", "version": "0.0.0", "license": "MIT", "scripts": {"start": "cross-env NX_DAEMON=true NODE_OPTIONS=--max_old_space_size=65536 nx serve", "secure": "cross-env NX_DAEMON=true NODE_OPTIONS=--max_old_space_size=65536 nx serve --experimental-https", "turbo": "cross-env NX_DAEMON=true NODE_OPTIONS=--max_old_space_size=65536 nx serve --turbo", "bz-docker": "docker-compose up", "build": "cross-env NODE_OPTIONS=--max_old_space_size=65536 nx build", "build:all": "yarn nx run-many --target=build --parallel --all --exclude=bz-mobile,pro-e2e", "build:libs": "cross-env NODE_OPTIONS=--max_old_space_size=65536 nx run-many --target build --all --exclude pro --parallel", "build:changelog": "nx build bz-changelog --prod", "test": "nx test", "lint": "nx lint", "lint:fix-all": "yarn nx run-many --target=lint --parallel --all --exclude=bz-mobile,pro-e2e,tsmd,third-party-chartiq,third-party-charting-library --fix", "lint:staged": "node scripts/changelog/lint.js", "dep-graph": "nx dep-graph", "help": "nx help", "prepare": "husky", "changelog:add-log": "yarn build:changelog && node ./dist/apps/bz-changelog/main.js add-log", "changelog:build": "yarn build:changelog && node ./dist/apps/bz-changelog/main.js build build apps", "changelog:check-changelog-exists": "yarn build:changelog && node ./dist/apps/bz-changelog/main.js exist-log", "changelog:check-missing-changelog": "yarn build:changelog && node ./dist/apps/bz-changelog/main.js check-log", "changelog:create-log": "yarn build:changelog && node ./dist/apps/bz-changelog/main.js add-log", "changelog:create-changelog": "yarn build:changelog && node ./dist/apps/bz-changelog/main.js add-log", "changelog:lint:staged": "yarn build:changelog && node ./dist/apps/bz-changelog/main.js lint", "changelog:update-md": "yarn build:changelog && node ./dist/apps/bz-changelog/main.js update-md", "changelog:create-release": "yarn build:changelog && node ./dist/apps/bz-changelog/main.js create-release", "auto-translation": "nx run third-party-tools-auto-translation:translate", "run:protobuf-def-gen": "yarn build decode-quote-messages-indexes && node ./dist/apps/decode-quote-messages-indexes/main.js ./protobuf/quote.proto ./libs/data/manager/quotes-v3-fields/src/quote-mapping.json", "protobuf:scanner": "git submodule update  --recursive --remote --init --force && yarn run:protobuf-def-gen && ./node_modules/.bin/pbjs -t static-module -w es6  protobuf/scanner.proto --es6 -o libs/data/manager/scanner/src/scanner_protos.js && ./node_modules/.bin/pbts libs/data/manager/scanner/src/scanner_protos.js -o libs/data/manager/scanner/src/scanner_protos.d.ts", "protobuf:quotes": "git submodule update  --recursive --remote --init --force && yarn run:protobuf-def-gen && ./node_modules/.bin/pbjs -t static-module -w es6 protobuf/quotews.proto --es6 -o libs/data/manager/quotes-v3/src/quotews_protos.js && ./node_modules/.bin/pbts libs/data/manager/quotes-v3/src/quotews_protos.js -o libs/data/manager/quotes-v3/src/quotews_protos.d.ts", "protobuf:all": "yarn protobuf:scanner && yarn protobuf:quotes"}, "private": true, "dependencies": {"@ag-grid-community/client-side-row-model": "31.3.1", "@ag-grid-community/csv-export": "31.3.1", "@ag-grid-community/react": "31.3.1", "@ag-grid-community/styles": "^31.3.1", "@ag-grid-enterprise/clipboard": "31.3.1", "@ag-grid-enterprise/column-tool-panel": "31.3.1", "@ag-grid-enterprise/core": "31.3.1", "@ag-grid-enterprise/excel-export": "31.3.1", "@ag-grid-enterprise/filter-tool-panel": "31.3.1", "@ag-grid-enterprise/menu": "31.3.1", "@ag-grid-enterprise/range-selection": "31.3.1", "@ag-grid-enterprise/rich-select": "31.3.1", "@ag-grid-enterprise/server-side-row-model": "^31.3.1", "@ag-grid-enterprise/set-filter": "31.3.1", "@ag-grid-enterprise/side-bar": "31.3.1", "@ag-grid-enterprise/status-bar": "31.3.1", "@ant-design/icons": "^6.0.0", "@ant-design/nextjs-registry": "^1.0.2", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@apollo/client": "^3.10.1", "@blueprintjs/core": "^5.19.0", "@blueprintjs/datetime": "^5.3.32", "@blueprintjs/icons": "^5.22.0", "@blueprintjs/popover2": "^2.1.31", "@blueprintjs/table": "^5.3.14", "@breezystack/lamejs": "^1.2.7", "@chargebee/chargebee-js-react-wrapper": "^0.6.5", "@ckeditor/ckeditor5-build-classic": "^35.1.0", "@ckeditor/ckeditor5-react": "^5.0.2", "@coralogix/browser": "^2.8.0", "@coralogix/react-native-sdk": "^1.0.15", "@datadog/browser-rum": "^5.12.0", "@dnd-kit/core": "^6.0.8", "@emotion/is-prop-valid": "^1.2.2", "@expo/config-plugins": "8.0.0", "@expo/metro-config": "~0.18.11", "@expo/prebuild-config": "7.0.0", "@fortawesome/fontawesome-pro": "^6.7.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/pro-duotone-svg-icons": "^6.7.2", "@fortawesome/pro-light-svg-icons": "^6.7.2", "@fortawesome/pro-regular-svg-icons": "^6.7.2", "@fortawesome/pro-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@harmony-dev/protobufjs-cli": "^1.0.1", "@miblanchard/react-native-slider": "^2.6.0", "@mux/mux-player": "2.0", "@mux/mux-player-react": "2.0", "@next/bundle-analyzer": "15.3.1", "@next/eslint-plugin-next": "^15.3.1", "@next/third-parties": "15.3.1", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/netinfo": "11.3.1", "@react-native-firebase/analytics": "19.2.2", "@react-native-firebase/app": "19.2.2", "@react-navigation/bottom-tabs": "6.5.20", "@react-navigation/material-top-tabs": "6.6.13", "@react-navigation/native": "6.1.17", "@react-navigation/stack": "6.3.29", "@react-spring/web": "^9.7.5", "@segment/analytics-next": "^1.72.1", "@segment/analytics-react-native": "2.19.1", "@segment/sovran-react-native": "1.1.1", "@sentry/browser": "6.19.7", "@splidejs/react-splide": "^0.7.12", "@stripe/react-stripe-js": "^1.16.5", "@stripe/stripe-js": "^1.48.0", "@svgr/webpack": "^8.1.0", "@taboola/react-native-plugin-3x": "3.1.5", "@tailwindcss/forms": "^0.5.7", "@tanstack/react-query": "^5.40.1", "@tanstack/react-table": "^8.13.2", "@tanstack/react-virtual": "^3.1.3", "@tippyjs/react": "^4.2.6", "@types/dashify": "^1.0.3", "@types/downloadjs": "^1.4.6", "@types/emoji-mart": "^3.0.9", "@types/inflected": "^2.1.3", "@types/numeral": "^2.0.5", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-modal": "^3.16.3", "@types/showdown": "^2.0.6", "@use-gesture/react": "^10.3.1", "@vvo/tzdb": "6.106.0", "add": "^2.0.6", "animated-number-react": "^0.1.2", "antd": "^5.25.4", "apexcharts": "^3.48.0", "as-event-tracker": "^1.1.1", "autoprefixer": "^10.4.19", "axios": "^1.6.0", "bignumber.js": "^9.0.1", "camelcase-keys": "^7.0.0", "chart.js": "^4.4.7", "chartbeat-react-native-sdk": "^0.0.22", "charting-library": "file:./third-party-packages/charting-library/charting_library_v24_4", "chartjs-chart-treemap": "^3.1.0", "connatix-player-sdk-react-native": "^2.4.6", "connected-react-router": "6.9.3", "cross-env": "^7.0.3", "dashify": "^2.0.0", "date-fns": "^2.23.0", "detectincognitojs": "^1.3.7", "dialog-polyfill": "^0.5.6", "downloadjs": "^1.4.7", "downshift": "^2.0.20", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "emoji-mart": "3.0.1", "emoji-picker-react": "^2.0.4", "eslint-define-config": "^2.1.0", "eslint-plugin-next": "^0.0.0", "expo": "~51.0.31", "expo-apple-authentication": "~6.4.2", "expo-asset": "10.0.10", "expo-auth-session": "5.5.2", "expo-av": "~14.0.7", "expo-blur": "~13.0.2", "expo-build-properties": "~0.12.5", "expo-clipboard": "6.0.3", "expo-constants": "~16.0.2", "expo-crypto": "13.0.2", "expo-dev-client": "~4.0.26", "expo-device": "6.0.2", "expo-document-picker": "~12.0.2", "expo-file-system": "17.0.1", "expo-font": "~12.0.10", "expo-haptics": "13.0.1", "expo-image-manipulator": "12.0.5", "expo-image-picker": "~15.0.7", "expo-intent-launcher": "11.0.1", "expo-linear-gradient": "13.0.2", "expo-linking": "6.3.1", "expo-media-library": "16.0.4", "expo-notifications": "0.28.16", "expo-random": "14.0.1", "expo-secure-store": "13.0.2", "expo-sharing": "12.0.1", "expo-splash-screen": "~0.27.5", "expo-status-bar": "1.12.1", "expo-store-review": "7.0.2", "expo-structured-headers": "3.8.0", "expo-system-ui": "~3.0.7", "expo-updates": "~0.25.24", "expo-web-browser": "13.0.3", "fast-json-patch": "^3.0.0", "fastdom": "^1.0.9", "file-saver": "^2.0.5", "fingerprintjs2": "2.1.0", "history": "4.10.1", "i18n-iso-countries": "7.5.0", "i18next": "^24.2.1", "inflected": "^2.1.0", "intercom-fashion": "^1.1.0", "intl-pluralrules": "^2.0.1", "invariant": "^2.2.4", "ioredis": "^5.4.1", "isomorphic-dompurify": "^0.24.0", "js-cookie": "^2.2.1", "lightweight-charts": "1.2.1", "limiter": "^2.1.0", "lodash": "^4.17.21", "loglevel": "^1.7.0", "logrocket": "^1.0.3", "lottie-react-native": "7.0.0", "luxon": "1.28.1", "metro-resolver": "0.80.10", "moment": "^2.29.1", "moment-timezone": "^0.5.26", "nchan": "^1.0.8", "next": "15.3.1", "next-gravity-forms": "^2.0.0", "next-plugin-svgr": "^1.1.10", "nextjs-cors": "^2.2.0", "node-cache": "^5.1.2", "node-html-parser": "^5.1.0", "node-vibrant": "^3.2.1-alpha.1", "normalizr": "^3.6.2", "numeral": "^2.0.6", "papaparse": "^5.0.1", "plotly.js": "^3.0.1", "polished": "^4.1.3", "popmotion": "8.7.1", "postcss": "^8.4.38", "protobufjs": "^7.1.2", "qhistory": "^1.0.3", "query-string": "^9.0.0", "ramda": "^0.26.1", "ramda-adjunct": "^2.12.0", "re-reselect": "3.4.0", "re-resizable": "^6.9.1", "react": "^19.1.0", "react-amphtml": "^4.0.2", "react-apexcharts": "^1.4.1", "react-color": "^2.19.3", "react-cookie": "^4.1.1", "react-copy-to-clipboard": "^5.1.0", "react-dfp": "^0.21.0", "react-dnd": "^14.0.5", "react-dnd-html5-backend": "^14.1.0", "react-dom": "^19.1.0", "react-fast-marquee": "^1.6.4", "react-file-drop": "^3.1.2", "react-file-utils": "^1.2.0", "react-google-recaptcha-v3": "^1.10.1", "react-gravity-form": "^1.1.5", "react-helmet": "^6.1.0", "react-hook-form": "^7.43.5", "react-hot-toast": "^2.5.2", "react-hotkeys-hook": "^4.4.1", "react-i18next": "^15.1.3", "react-icons": "^4.9.0", "react-infinite-scroller": "^1.2.6", "react-intersection-observer": "^9.16.0", "react-konami": "^0.5.0", "react-lazyload": "^3.2.0", "react-lite-youtube-embed": "^2.4.0", "react-loadable": "^5.5.0", "react-modal": "^3.16.1", "react-mosaic-component": "^5.1.0", "react-native": "0.74.5", "react-native-actionsheet": "2.4.2", "react-native-collapsible": "1.6.1", "react-native-device-info": "10.13.2", "react-native-disable-battery-optimizations-android": "1.0.7", "react-native-dotenv": "^3.4.11", "react-native-event-listeners": "1.0.3", "react-native-gesture-handler": "2.16.1", "react-native-get-random-values": "1.11.0", "react-native-global-props": "1.1.5", "react-native-google-mobile-ads": "14.1.0", "react-native-iap": "12.15.1", "react-native-image-crop-picker": "0.41.2", "react-native-image-zoom-viewer": "3.0.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-modal": "13.0.1", "react-native-pager-view": "6.3.0", "react-native-paper": "4.9.2", "react-native-permissions": "^4.1.5", "react-native-quick-sqlite": "^8.2.7", "react-native-reanimated": "3.10.1", "react-native-render-html": "6.3.4", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-shimmer-placeholder": "2.0.9", "react-native-tab-view": "3.5.2", "react-native-track-player": "4.1.1", "react-native-url-polyfill": "1.3.0", "react-native-vector-icons": "10.1.0", "react-native-webrtc": "124.0.3", "react-native-webview": "13.8.6", "react-number-format": "^5.3.3", "react-onclickoutside": "6.9.0", "react-onesignal": "^3.0.1", "react-outside-click-handler": "^1.3.0", "react-plotly.js": "^2.6.0", "react-popper": "^2.3.0", "react-portal": "4.2.0", "react-pose": "^4.0.8", "react-prebid": "^2.1.1", "react-redux": "8.0.5", "react-resize-detector": "^12.0.2", "react-router": "5.2.1", "react-router-dom": "6.11.2", "react-router-redux": "^4.0.8", "react-select": "^5.10.1", "react-share": "^5.2.2", "react-split": "2.0.14", "react-sticky-box": "^0.9.3", "react-tiny-popover": "^8.0.4", "react-tweet": "^3.2.1", "react-virtualized": "^9.22.6", "react-wrap-balancer": "^1.1.0", "recharts": "^2.15.3", "recompose": "^0.30.0", "redux-devtools-extension": "^2.13.8", "redux-form": "^8.2.4", "redux-logic": "2.1.1", "redux-persist": "6.0.0", "redux-saga": "1.3.0", "redux-thunk": "2.3.0", "reselect": "^4.0.0", "sharp": "^0.33.3", "shortid": "^2.2.14", "showdown": "^2.1.0", "stream": "0.0.2", "stream-browserify": "^3.0.0", "stream-chat": "^8.56.1", "stream-chat-expo": "5.6.1", "stream-chat-react": "^12.12.0", "string-strip-html": "^13.4.6", "striptags": "^3.2.0", "styled-components": "^6.1.18", "subscribe-ui-event": "^2.0.5", "tailwind-merge": "^3.2.0", "tailwind-variants": "^1.0.0", "tailwindcss": "^3.4.3", "tailwindcss-animated": "^1.1.2", "tailwindcss-truncate-multiline": "^1.0.3", "tippy.js": "^6.3.7", "tslib": "^2.3.0", "url-regex": "^5.0.0", "uuid": "8.3.2", "victory-native": "36.9.2", "vite-plugin-node-polyfills": "^0.21.0", "webrtc-adapter": "^7.7.0", "xlsx": "^0.17.0", "yarn": "^1.22.22"}, "devDependencies": {"@babel/core": "^7.14.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-export-namespace-from": "^7.24.7", "@babel/preset-react": "^7.14.5", "@google-cloud/translate": "^8.5.0", "@nx/cypress": "18.3.3", "@nx/esbuild": "18.3.3", "@nx/eslint": "18.3.3", "@nx/eslint-plugin": "18.3.3", "@nx/expo": "18.3.3", "@nx/jest": "18.3.3", "@nx/js": "18.3.3", "@nx/next": "18.3.3", "@nx/node": "18.3.3", "@nx/react": "18.3.3", "@nx/react-native": "18.3.3", "@nx/rollup": "18.3.3", "@nx/vite": "18.3.3", "@nx/web": "18.3.3", "@nx/webpack": "18.3.3", "@nx/workspace": "18.3.3", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.7", "@react-native-community/cli-platform-android": "12.3.0", "@react-native/babel-preset": "^0.73.18", "@react-native/codegen": "^0.75.3", "@react-native/metro-config": "^0.73.2", "@rollup/plugin-url": "^7.0.0", "@rspack/core": "^0.5.4", "@rspack/plugin-react-refresh": "^0.5.4", "@svgr/rollup": "^8.0.1", "@swc-node/register": "~1.8.0", "@swc/cli": "~0.1.62", "@swc/core": "~1.3.85", "@swc/helpers": "~0.5.2", "@swc/jest": "0.2.20", "@testing-library/jest-native": "~5.4.3", "@testing-library/react": "14.0.0", "@testing-library/react-native": "~12.4.2", "@types/fast-json-patch": "^1.1.5", "@types/jest": "^29.4.0", "@types/js-cookie": "2.2.2", "@types/luxon": "1.27.1", "@types/moment": "^2.13.0", "@types/node": "18.16.9", "@types/node-fetch": "^2.5.12", "@types/ramda": "0.26.41", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@types/react-helmet": "^5.0.21", "@types/react-lazyload": "^3.2.3", "@types/react-loadable": "^5.5.7", "@types/react-portal": "^4.0.1", "@types/react-select": "^5.0.1", "@types/react-virtualized": "^9.22.2", "@types/recharts": "^1.1.0", "@types/sharedworker": "^0.0.28", "@types/shortid": "^0.0.29", "@types/ua-parser-js": "^0.7.36", "@typescript-eslint/eslint-plugin": "7.3.0", "@typescript-eslint/parser": "7.3.0", "@vitejs/plugin-react": "^4.2.0", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "^1.0.4", "@vitest/ui": "^1.3.1", "babel-jest": "^29.4.1", "babel-preset-expo": "~10.0.0", "browserify-zlib": "^0.2.0", "cross-env": "^7.0.3", "css-loader": "^6.11.0", "cypress": "^13.0.0", "deep-object-diff": "^1.1.9", "deprecated-react-native-prop-types": "^5.0.0", "esbuild": "^0.19.2", "eslint": "8.57.0", "eslint-config-next": "15.3.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-cypress": "^2.13.4", "eslint-plugin-import": "2.27.5", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-sort-destructure-keys": "^1.5.0", "eslint-plugin-sort-keys-fix": "^1.1.2", "eslint-plugin-typescript-sort-keys": "^3.2.0", "eslint-plugin-unused-imports": "^3.1.0", "esm": "^3.2.25", "git-revision-webpack-plugin": "^5.0.0", "husky": "^9.0.11", "jest": "^29.4.1", "jest-environment-jsdom": "^29.4.1", "jest-environment-node": "^29.4.1", "jest-react-native": "18.0.0", "jsdom": "~22.1.0", "just-extend": "^6.2.0", "nx": "18.3.3", "postcss-loader": "^8.1.1", "prettier": "^3.2.5", "react-native-svg": "15.1.0", "react-native-svg-transformer": "1.5.0", "react-native-web": "^0.19.9", "react-refresh": "^0.14.0", "react-test-renderer": "18.3.1", "sass": "^1.72.0", "style-loader": "^3.3.4", "swc-loader": "0.1.15", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "typescript": "5.4.5", "verdaccio": "^5.0.4", "vite": "~5.0.0", "vite-plugin-checker": "^0.6.4", "vite-plugin-commonjs": "^0.10.1", "vite-plugin-dts": "~3.8.1", "vite-plugin-mkcert": "^1.17.3", "vite-plugin-static-copy": "^1.0.1", "vite-plugin-svgr": "^3.3.0", "vitest": "^1.3.1", "webpack-cli": "^5.1.4"}, "resolutions": {"react-select": "^5.10.1", "react-datepicker": "^8.3.0"}, "nx": {"includedScripts": []}}