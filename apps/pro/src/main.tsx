import './init';

import '@ant-design/v5-patch-for-react-19';
import { addToInfoBar } from './components/infoBar/infoBarActions';
import * as Sentry from '@sentry/browser';
import React, { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { Helmet } from 'react-helmet';
import loadable from 'react-loadable';
import { Provider as ReduxProvider } from 'react-redux';

import { SessionContextProvider } from '@benzinga/session-context';

import './assets/styles/index.styl';
import './assets/styles/index.scss';
import configureStore from './redux/configureStore';
import { Site, AsyncSite } from './routes.web';
import { windowResize } from './actions/uiActions';
import {
  AuthenticationManager,
  AuthenticationManagerEvent,
  createSession,
  Session,
  SessionEnvironment,
  SessionErrorEvent,
} from '@benzinga/session';
import Hooks from '@benzinga/hooks';
import LayoutsGate from './layout/LayoutsGate';

import { selectSettings, selectWidgetGlobalSettings } from './selectors/settingsSelectors';
import { SearchContextProvider } from '@benzinga/pro-ui';
import { isSymbolSearchItem } from '@benzinga/search-modules';
import { BenzingaContextProvider } from '@benzinga/user-context';

import { handleNewSession, updateSession } from './actions/sessionActions';
import { updateWidgetGlobalSettings } from './actions/settingsActions';

import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

import { LAYOUT_VERSION } from './actions/layoutActions';
import WidgetLinkingGate from './widget-linking/WidgetLinkingGate';

import alarm from './assets/audio/alerts/alarm.wav';
import droplet from './assets/audio/alerts/droplet.wav';
import glass from './assets/audio/alerts/glass.wav';
import morsecode from './assets/audio/alerts/morsecode.wav';
import opencan from './assets/audio/alerts/opencan.wav';
import pot from './assets/audio/alerts/pot.wav';
import shatter from './assets/audio/alerts/shatter.wav';
import siren from './assets/audio/alerts/siren.wav';
import { GlobalSettingManifest } from '@benzinga/widget-tools';
import { LoggingGate } from './logging/LoggingGate';
import { sessionEnv } from './init';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

async function main() {
  const env: SessionEnvironment = sessionEnv;

  const { store } = configureStore();
  const session = createSession({
    ...env,

    'benzinga-advanced-news': {
      keepSocketAlive: `true`,
      ...env['benzinga-advanced-news'],
    },
    'benzinga-authentication': {
      maxLayoutVersion: `${LAYOUT_VERSION}`,
      ...env['benzinga-authentication'],
    },
    'benzinga-play-sound': {
      sounds: {
        alarm: alarm,
        droplet: droplet,
        glass: glass,
        morsecode: morsecode,
        opencan: opencan,
        pot: pot,
        shatter: shatter,
        siren: siren,
      },
    },
    'benzinga-widget-tools': {
      getGlobalParameters: (manifest: GlobalSettingManifest) =>
        selectWidgetGlobalSettings(store.getState())[manifest.id],
      getWidgetById: () => store.getState().widgetsById,
      setGlobalParameters: (manifest: GlobalSettingManifest, cb: (settings: object) => object) =>
        store.dispatch(updateWidgetGlobalSettings(manifest.id, cb) as any),
    },
  });
  try {
    await loadable.preloadReady();
  } catch (error: any) {
    Sentry.captureException(error);
  }

  store.dispatch(updateSession(session));

  // register event listener for window resizing
  window.addEventListener('resize', () => {
    windowResize(window.innerWidth, window.innerHeight, store.dispatch);
  });

  const element = document.getElementById('init');
  const root = createRoot(element as HTMLElement);
  root.render(<App session={session} store={store} />);
}

interface Props {
  session: Session;
  store: ReturnType<typeof configureStore>['store'];
}

const App: React.FC<Props> = props => {
  Hooks.useSubscriber(
    props.session,
    event => {
      if (event.type === 'error') {
        const error = (event as SessionErrorEvent).error;
        if (error !== undefined) {
          Sentry.captureException(error);
          console.error(error);
        }
      }
    },
    ['error'],
  );

  const authManager = props.session.getManager(AuthenticationManager);

  Hooks.useSubscriber(authManager, (event: AuthenticationManagerEvent) => {
    switch (event.type) {
      case 'authentication:post_login_get_session':
        props.store.dispatch({ type: 'REINITIALIZE_APP' });
        break;
      case 'authentication:session_update':
        event.auth && handleNewSession(event.auth);
        break;
    }
  });

  const queryClient = new QueryClient();

  return (
    <DndProvider backend={HTML5Backend}>
      <ReduxProvider store={props.store}>
        <SessionContextProvider session={props.session}>
          <BenzingaContextProvider
            iFrameLoginURL={window?.env?.LOGIN.IFRAME_URL ? new URL(window?.env?.LOGIN.IFRAME_URL) : undefined}
            loginNextURL={
              window?.env?.LOGIN.REGISTER_NEXT_URL
                ? new URL(window?.env?.LOGIN.NEXT_URL, new URL(window.location.href))
                : undefined
            }
            loginRegisterURL={
              window?.env?.LOGIN.REGISTER_NEXT_URL
                ? new URL(window?.env?.LOGIN.REGISTER_NEXT_URL, new URL(window.location.href))
                : undefined
            }
            slug={{ product: 'benzinga-pro' }}
          >
            <Site>
              <LoggingGate>
                <SearchContextProvider
                  onRecentModuleItemChange={item => {
                    if (isSymbolSearchItem(item)) {
                      props.store.dispatch(addToInfoBar(item.data.symbol));
                    }
                  }}
                  shouldDisplayDefaultTickers={() => selectSettings(props.store.getState()).autocompleteDefaultsAllowed}
                  shouldDisplayRecentTickers={() => true}
                >
                  <LayoutsGate>
                    <QueryClientProvider client={queryClient}>
                      <WidgetLinkingGate>
                        <Helmet>
                          <title>Benzinga Pro: The Best Stock Market News Feed Tool</title>
                          <meta charSet="utf-8" />
                          <meta
                            content="Benzinga Pro is the best way to research the stock market for better trades. Join thousands of stock investors who capture news events before they drive stock price changes."
                            name="description"
                          />
                        </Helmet>
                        <AsyncSite />
                      </WidgetLinkingGate>
                    </QueryClientProvider>
                  </LayoutsGate>
                </SearchContextProvider>
              </LoggingGate>
            </Site>
          </BenzingaContextProvider>
        </SessionContextProvider>
      </ReduxProvider>
    </DndProvider>
  );
};

main().catch(error => {
  console.error(error);
  Sentry.captureException(error);
});
