import React from 'react';
import { Impression } from '@benzinga/analytics';
import Image from 'next/image';

export const ErxGapsBannerAd: React.FC = () => {
  return (
    <Impression campaign_id="ERxMay" unit_type="services-page">
      <div className="relative h-fit md:h-[320px] w-full rounded-md bg-[#0C101F] overflow-hidden mt-4">
        <a
          className="z-10 w-full h-full relative"
          href="https://events2.benzinga.com/erx-gaps-workshop-may-2025-registration/?utm_campaign=ERxMay&utm_adType=services-page&utm_ad=freemc"
          rel="noreferrer"
          target="_blank"
        >
          <div className="h-full flex flex-col items-flex-start justify-between p-4 pl-6 text-white z-20 gap-2">
            <div className="font-bold text-lg text-[#479CFF] uppercase tracking-[0.3em]">Free Masterclass</div>
            <div className="text-2xl md:text-4xl font-bold uppercase">
              See the Pattern Behind <br />
              NVDA&apos;s <span className="text-[#1A81FA]">785% Run</span>
            </div>
            <p className="max-w-[500px] text-[#D1D2D8] line-height-[22px] text-sm md:text-[15px]">
              The ERx Gap system revealed the pattern behind NVDA&apos;s 785% move. Now it&apos;s your turn: get the
              full course, gap qualifier checklist, and advanced buy strategies today.
            </p>
            <div className="flex flex-row items-center justify-between gap-4 w-full">
              <button className="rounded-md bg-[#1A81FA] px-8 py-4 whitespace-nowrap uppercase font-bold w-full md:w-fit">
                View Masterclass Now
              </button>
              <p className="text-sm font-bold w-[339px] text-center md:block hidden">
                Even during market pullbacks, select stocks like NVDA, CELH, and TTD surged — thanks to a repeatable
                post-earnings setup.
              </p>
            </div>
          </div>
        </a>
        <Image
          alt="ERX Gaps Monitor"
          className="absolute top-0 right-0"
          height={320}
          src="/next-assets/images/banners/edge/cta/erx-gaps-banner-bg.png"
          width={1000}
        ></Image>
      </div>
    </Impression>
  );
};

export default ErxGapsBannerAd;
