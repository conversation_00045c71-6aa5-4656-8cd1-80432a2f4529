'use client';

import React from 'react';
import styled from '@benzinga/themetron';
import { BzImage } from '@benzinga/image';

export const BzEdgeBlock = () => {
  return (
    <ContentBlock>
      <div className="content">
        <div className="logo-wrapper">
          <BzImage alt="Benzinga Edge Logo" height="14px" src="/next-assets/images/bz-edge-logo.svg" width="145px" />
        </div>
        <h2>Benzinga&apos;s Top Trade Ideas Delivered Daily</h2>
        <p>
          Get daily trade ideas, weekly stock picks, market research, and proprietary datasets to sharpen your investing
          edge.
        </p>
        <a
          href="https://www.benzinga.com/premium/ideas/benzinga-edge/?utm_campaign=wallstreetadvantage&utm_adType=servicespagead&utm_ad=toptrades"
          target="_blank"
        >
          get started here
        </a>
      </div>
      <BzImage
        alt="services-content-block"
        height={230}
        objectFit="cover"
        objectPosition="left"
        src="/next-assets/images/services/bz-edge-reports.png"
        width={451}
      />
    </ContentBlock>
  );
};

const ContentBlock = styled.div`
  background-image: url('/next-assets/images/services/bz-edge-bg.png');
  max-width: 1000px;
  width: 100%;
  border: 1px solid #364458;
  border-radius: 4px;
  height: 230px;
  margin: auto;
  margin-bottom: 2rem;
  text-align: left;
  overflow: hidden;
  z-index: 10;

  display: flex;
  flex-direction: row;

  .content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-between;
    padding: 1.5rem;
    max-width: 550px;

    h2 {
      font-size: 24px;
      line-height: 32px;
      color: white;
      margin: 0;
      font-weight: 700;
    }

    p {
      color: #99aecc;
      font-size: 16px;
      line-height: 24px;
      font-weight: 400;
    }

    a {
      font-weight: 700;
      font-size: 14px;
      padding: 12px 60px;
      background-color: #3f83f8;
      color: white;
      border-radius: 4px;
      text-align: center;
      transition: background-color 0.3s ease-in-out;
      text-transform: uppercase;
    }
  }

  .logo-wrapper {
    display: flex;
  }

  @media (max-width: 800px) {
    .content {
      padding: 1rem;
      min-width: 80%;

      h2 {
        font-size: 20px;
        line-height: 24px;
      }

      p {
        font-size: 14px;
        line-height: 16px;
      }

      a {
        font-size: 14px;
        padding: 8px 24px;
      }
    }
  }
`;
