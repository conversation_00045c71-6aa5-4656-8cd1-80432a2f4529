// import { Dayjs } from 'dayjs';
import { DateTime } from 'luxon';
import luxonGenerateConfig from 'rc-picker/lib/generate/luxon';
import generatePicker from 'antd/es/date-picker/generatePicker';
import 'antd/es/date-picker/style/index';

//TO DO: FIX WEIRD PARSING ISSUE LATER
type GeneratePickerType = typeof generatePicker<DateTime>;
type DatePickerType = ReturnType<GeneratePickerType>;

const DatePicker: DatePickerType = generatePicker<DateTime>(luxonGenerateConfig);

export default DatePicker;
