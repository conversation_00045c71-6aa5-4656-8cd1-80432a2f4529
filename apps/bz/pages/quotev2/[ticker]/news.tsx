import React from 'react';

import styled from '@benzinga/themetron';
import { SectionTitle } from '@benzinga/core-ui';

import { getQuoteData, getQuoteNews } from '../../api/quote';
import { getGlobalSession } from '../../api/session';

import News from '../../../src/components/Quote/V2/News';
import Peers from '../../../src/components/Quote/V2/Peers';
import { QuoteLayout } from '../../../src/components/Quote/V2/QuoteLayout';
import DefaultSidebar from '../../../src/components/Sidebars/DefaultSidebar';
import { NodeQueryParams } from '@benzinga/basic-news-manager';
import getQuoteV2PageData, {
  QuoteV2PageProps,
  getQuoteFeedTypeByLocale,
  getQuoteFeedTokenTypeByLocale,
  getQuoteNotFoundData,
} from '../../../src/quoteV2Utils';
import { News as NewsI } from '@benzinga/basic-news-manager';
import { useTranslation } from 'react-i18next';
import i18n, { LocaleType, setLanguageByHost } from '@benzinga/translate';
import { QuoteAltSuggestions } from '../../../src/components/Quote/QuoteAltSuggestions';
import { sanitizeHTML } from '@benzinga/frontend-utils';

interface NewsQuoteV2PageProps extends QuoteV2PageProps {
  quoteNews: NewsI[];
}

const QuoteNewsPage = ({
  activeTab,
  advertiserProfile,
  altSuggestions,
  isAdvertiser,
  isETF,
  metaProps,
  peersChartData,
  profile,
  quoteNews,
  showGovLink,
  symbol,
}) => {
  const { t } = useTranslation('quote', { i18n });

  if (altSuggestions) {
    return <QuoteAltSuggestions {...altSuggestions} symbol={symbol} />;
  }

  return (
    <QuoteLayout
      advertiserProfile={advertiserProfile}
      defaultPath={activeTab}
      isAdvertiser={isAdvertiser}
      profile={profile}
      showGovLink={showGovLink}
      symbol={symbol}
    >
      <QuoteNewsPageWrapper className="main-div">
        <div className="flex flex-col gap-4 lg:flex-row w-full">
          <div className="w-full lg:w-9/12">
            <div className="main-div divider-bottom mb-4">
              <div className="flex flex-col gap-2 mb-4">
                <SectionTitle level={1} uppercase={false}>
                  {metaProps.title}
                </SectionTitle>
                <p className="section-description">
                  {t('Quote.Paragraphs.news', { stockName: profile.richQuoteData?.name })}
                </p>
              </div>
            </div>
            <div className="mt-4">
              <News initialNews={quoteNews} symbol={symbol} />
            </div>
          </div>
          {!isETF && !isAdvertiser && Object.keys(peersChartData).length > 0 && (
            <div className="w-full lg:w-3/12">
              <React.Suspense>
                <DefaultSidebar>
                  <div className="w-full">
                    <SectionTitle level={3} size="2xl">
                      {t('Quote.Peers.people-also-watch')}
                    </SectionTitle>
                    <Peers
                      chartData={peersChartData}
                      peers={profile.quotes}
                      symbol={symbol}
                      type={profile.schedule.type}
                    />
                  </div>
                </DefaultSidebar>
              </React.Suspense>
            </div>
          )}
        </div>
      </QuoteNewsPageWrapper>
    </QuoteLayout>
  );
};

export default QuoteNewsPage;

export const getServerSideProps = async ({ query: { ticker }, req, res }) => {
  if (req?.headers?.host) {
    setLanguageByHost(req?.headers?.host); // set language for server side translations
  }

  const symbol: string = sanitizeHTML(ticker) || '';
  const activeTab = 'news';

  const query: NodeQueryParams = {
    displayOutput: 'full',
    page: 0,
    pageSize: 50,
    symbols: [ticker],
    tokenType: getQuoteFeedTokenTypeByLocale(i18n.language as LocaleType),
  };

  const session = getGlobalSession();

  const pageData = (await getQuoteV2PageData(
    symbol,
    activeTab,
    getQuoteFeedTypeByLocale(i18n.language as LocaleType),
    req?.headers?.host,
  )) as {
    props: NewsQuoteV2PageProps;
  };

  if (pageData?.props?.errorCode) {
    res.statusCode = pageData.props.errorCode;
    return pageData;
  }

  const quoteNews = await getQuoteNews(session, query);
  pageData.props.quoteNews = quoteNews;

  if (quoteNews.length < 10) {
    pageData.props.metaProps.robots = 'noindex, nofollow';
  }

  return pageData;
};

const QuoteNewsPageWrapper = styled.div`
  .pagination {
    flex-direction: row !important;
  }

  .news-content {
    .content-headline {
      display: flex;
      flex-direction: column;
      gap: 12px;
      padding: 16px;
      border: 1px solid #e1ebfa;
      color: #192940;

      .content-title {
        font-size: 16px;
        font-weight: 700;
        color: #192940;
      }

      .content-headline-datetime {
        color: #99aecc;
      }

      .author-date-text {
        font-size: 12px;
        color: #395173;
        font-weight: 700;

        /* span {
          color: #99aecc;
        } */
      }
    }

    ul {
      list-style: none;
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .date-heading {
      h2 {
        text-decoration: none;
        color: #192940;
        font-weight: 700;
        font-size: 20px;
        line-height: 28px;
      }
      .date-heading-hr {
        display: none;
      }
      margin-bottom: 12px;
    }
  }

  .checkbox-container {
    label {
      color: #5b7292;
      font-size: 12px;
      text-transform: capitalize;
      font-weight: 700;
    }
  }
`;
