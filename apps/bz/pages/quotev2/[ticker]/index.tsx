/* eslint-disable @next/next/no-img-element */
import React from 'react';
import classNames from 'classnames';
import { useTranslation } from 'react-i18next';
import LazyLoad from 'react-lazyload';

// @ Benzinga Packages
import { ErrorBoundary, SectionTitle, Spinner } from '@benzinga/core-ui';

// @ Local Functions and Components
import ProfileFAQ from '../../../src/components/Quote/FAQ/ProfileFAQ';
import FinancialsSection from '../../../src/components/Quote/V2/FinancialsSection';
import QuoteMetrics from '../../../src/components/Quote/V2/QuoteMetrics';
import CryptoQuoteMetrics from '../../../src/components/Quote/V2/CryptoQuoteMetrics';
import ETFQuoteMetrics from '../../../src/components/Quote/V2/ETFQuoteMetrics';
import OverviewRow from '../../../src/components/Quote/V2/OverviewRow';
import ETFOverviewHeader from '../../../src/components/Quote/V2/ETFOverviewHeader';
import CryptoOverviewHeader from '../../../src/components/Quote/V2/CryptoOverviewHeader';
import CompareSection from '../../../src/components/Quote/V2/CompareSection';
import AboutQuote from '../../../src/components/Quote/V2/AboutQuote';
import QuoteNewsSection from '../../../src/components/Quote/V2/QuoteNewsSection';
import { QuoteLayout } from '../../../src/components/Quote/V2/QuoteLayout';
import AnalystRatingsCharts from '../../../src/components/Quote/V2/AnalystRatingsCharts';
import { OverviewSectionHeader } from '../../../src/components/Quote/V2/OverviewSectionHeader';
import CryptoQuoteNewsSection from '../../../src/components/Quote/V2/CryptoQuoteNewsSection';
import VideoBlocks from '../../../src/components/Quote/V2/VideoBlocks';
import DrilldownFooter from '../../../src/components/Quote/V2/DrilldownFooter';
import { QuoteAltSuggestions } from '../../../src/components/Quote/QuoteAltSuggestions';
import ETFQuoteSidebar from '../../../src/components/Quote/V2/ETFQuoteSidebar';
import { MetricsSidebar } from '../../../src/components/Quote/V2/MetricsSidebar';
import {
  QuoteV2PageProps,
  RatingsByMonthWithCounts,
  getQuoteV2PageData,
  getQuoteFeedTypeByLocale,
  QuoteV2PageData,
} from '../../../src/quoteV2Utils';
import { QuoteProfile } from '../../../src/entities/quoteEntity';
import { HeaderProps } from '../../../src/entities/header';
import { getGlobalSession } from '../../api/session';

import { Schema } from '@benzinga/seo';
import { BullBearStatements, DelayedQuote, TickerDetail } from '@benzinga/quotes-manager';
import { ContentManager, WordpressSidebar } from '@benzinga/content-manager';
import { NewTaboola as TaboolaPlacement } from '@benzinga/article';
import { TradingViewWidget } from '@benzinga/charts-ui';
import i18n, { LocaleType, setLanguageByHost } from '@benzinga/translate';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import { useBenzingaEdge } from '@benzinga/edge';
import { QuotePageSidebar } from '../../../src/components/Quote/V2/QuotePageSidebar';

export interface QuotePageProps extends QuoteV2PageProps {
  overviewValues: {
    bullSayBearSay: BullBearStatements;
    financials: {
      down: number;
      up: number;
    };
    technicals: {
      down: number;
      up: number;
    };
    ratingsByMonthWithCounts: RatingsByMonthWithCounts[];
  };
  headerProps: HeaderProps;
}

const QuotePageV2 = ({
  activeTab,
  advertiserProfile,
  altSuggestions,
  isAdvertiser,
  isCrypto,
  isETF,
  metaProps,
  overviewValues,
  peersChartData,
  profile,
  rankingData,
  showGovLink,
  sidebar,
  symbol,
}: QuotePageProps) => {
  const { t } = useTranslation(['quote', 'common'], { i18n });
  const hasAdLight = useBenzingaEdge().adLightEnabled;

  if (altSuggestions) {
    return <QuoteAltSuggestions {...altSuggestions} symbol={symbol} />;
  }

  const compareData = {
    de: profile?.fundamentals?.operationRatios?.[0]?.totalDebtEquityRatio,
    eps: profile?.fundamentals?.earningReports?.[0]?.basicEps,
    pb: profile?.fundamentals?.valuationRatios?.[0]?.pbRatio,
    pe: profile?.fundamentals?.valuationRatios?.[0]?.peRatio,
    roe: profile?.fundamentals?.operationRatios?.[0]?.roe,
    rsi: profile?.technicals?.rsi,
  };

  const peersSymbols = profile?.quotes ? Object.keys(profile?.quotes).filter(peer => peer !== symbol) : [];

  const advertiserBanner = advertiserProfile?.entity?.company_overview_banner;
  const tradingViewSymbol = isCrypto
    ? symbol.replace('/USD', 'USDT')
    : profile.richQuoteData.type === 'ETF'
      ? symbol
      : profile.richQuoteData.bzExchange
        ? `${profile.richQuoteData.bzExchange}:${symbol.replace('/', '.')}`
        : symbol;

  return (
    <>
      <Schema
        data={corporationSchema(symbol, profile.richQuoteData, profile.tickerDetails, metaProps?.canonical ?? '')}
        name="corporation-schema"
      />
      <QuoteLayout
        advertiserProfile={advertiserProfile}
        defaultPath={activeTab}
        isAdvertiser={isAdvertiser}
        isCrypto={isCrypto}
        profile={profile}
        showGovLink={showGovLink}
        symbol={symbol}
      >
        <div className="main-div w-full flex gap-4 px-4">
          <div className="w-full flex flex-col gap-8 md:gap-4 p-0">
            {isETF && profile?.etfFund && <ETFOverviewHeader profile={profile} rankingData={rankingData} />}
            {!isETF && !isAdvertiser && !isCrypto && (
              <OverviewRow {...overviewValues} rankingData={rankingData} symbol={symbol} />
            )}
            {isCrypto && <CryptoOverviewHeader profile={profile} rankingData={rankingData} />}
            <div className="flex flex-col gap-2">
              <SectionTitle level={1} uppercase={false}>
                {metaProps.title}
              </SectionTitle>
              <p className="section-description">
                {t('Quote.Paragraphs.profile', {
                  stockName: profile.richQuoteData?.name,
                  symbol: profile.richQuoteData?.symbol,
                })}
              </p>
            </div>
            <div className="top-area-and-sidebar-container w-full flex mx-auto max-w-[1280px] gap-4">
              <div className="w-full lg:w-8/12 flex flex-col gap-8 md:gap-4">
                <div className="">
                  <div className="w-full flex flex-col gap-8">
                    {/* <div className="flex flex-col gap-2">
                      <SectionTitle level={1} uppercase={false}>
                        {metaProps.title}
                      </SectionTitle>
                      <p className="section-description">{metaProps.description}</p>
                    </div> */}
                    <div className="w-full flex flex-col lg:flex-row gap-8 md:gap-4">
                      <div className="w-full flex flex-col">
                        <div className="flex flex-col gap-8 md:gap-4">
                          {isAdvertiser && advertiserBanner && (
                            <div
                              className="w-full inline-image-banner hidden md:block md:h-[400px] mb-4"
                              style={{
                                backgroundImage: `url(${advertiserBanner})`,
                              }}
                            ></div>
                          )}
                          <div
                            className={classNames('card flex-grow flex flex-col justify-center min-h-[400px]', {
                              'md:min-h-[500px]': !isETF,
                              'md:min-h-[520px]': isETF,
                            })}
                          >
                            <React.Suspense fallback={<Spinner />}>
                              <TradingViewWidget
                                customProps={{
                                  mobileHeight: 400,
                                }}
                                widgetProps={{
                                  height: isETF ? 485 : 460,
                                  hide_side_toolbar: true,
                                  hide_top_toolbar: true,
                                  locale: i18n.language,
                                  symbol: `${tradingViewSymbol}`,
                                  theme: 'light',
                                  width: '100%',
                                }}
                              />
                              {/* <LightweightChart symbol={symbol} theme={'light'} /> */}
                            </React.Suspense>
                          </div>
                          <div className="metrics-mobile w-full">
                            <MetricsSidebar
                              advertiserProfile={advertiserProfile}
                              isAdvertiser={isAdvertiser}
                              isCrypto={isCrypto}
                              isETF={isETF}
                              profile={profile}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="w-full">
                      <ErrorBoundary name="quote-news-section">
                        {isCrypto ? (
                          <CryptoQuoteNewsSection profile={profile} sidebar={sidebar} />
                        ) : (
                          <div className="flex flex-col">
                            <QuoteNewsSection
                              belowNewsSection={
                                isETF ? <FAQSection profile={profile} title={t('Quote.FAQ.faq')} /> : null
                              }
                              isETF={isETF}
                              profile={profile}
                            />
                          </div>
                        )}
                      </ErrorBoundary>
                    </div>
                  </div>
                </div>
                {isAdvertiser && advertiserProfile && (
                  <div className="main-div">
                    <div className="mt-[24px]">
                      <div className="flex gap-2 items-center mb-4">
                        <SectionTitle level={3} size="2xl">
                          Watch
                        </SectionTitle>
                      </div>
                      <VideoBlocks advertiserProfile={advertiserProfile} />
                    </div>
                  </div>
                )}
                {!isETF && !isAdvertiser && !isCrypto && (
                  <div className="mx-auto max-w-[1280px] w-full">
                    {overviewValues.financials && (
                      <OverviewSectionHeader title={t('Quote.Financials.title')} values={overviewValues.financials} />
                    )}
                    <div className="flex flex-col gap-8 md:gap-4 lg:flex-row">
                      <div className="w-full flex flex-col gap-8 md:gap-4">
                        <FinancialsSection symbol={symbol} />
                        <AnalystRatingsCharts
                          averageRating={profile.ratingsSummary.consensusRatingValue}
                          ratings={profile.ratingsSummary.uniqueRatingsFromLastThreeYears}
                          ratingsByMonthWithCounts={overviewValues.ratingsByMonthWithCounts}
                        />
                      </div>
                    </div>
                  </div>
                )}
                {isAdvertiser && advertiserProfile?.entity?.company_deep_dive && (
                  <div className="main-div">
                    <div className="my-4">
                      <div className="flex gap-2 items-center mb-4">
                        <SectionTitle level={3} size="2xl">
                          Deep Dive
                        </SectionTitle>
                      </div>
                      <div className="card flex flex-col gap-8  lg:flex-row my-4">
                        <div
                          className="w-full lg:w-6/12"
                          dangerouslySetInnerHTML={{ __html: advertiserProfile?.entity?.company_deep_dive ?? '' }}
                        />
                        {advertiserProfile?.entity?.company_deep_dive_banner && (
                          <div
                            className="w-full lg:w-6/12 inline-image-banner h-[300px]"
                            style={{
                              backgroundImage: `url(${advertiserProfile?.entity?.company_deep_dive_banner})`,
                            }}
                          ></div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
              <QuotePageSidebar
                advertiserProfile={advertiserProfile}
                isAdvertiser={isAdvertiser}
                isCrypto={isCrypto}
                isETF={isETF}
                profile={profile}
                symbol={symbol}
              />
            </div>
            {!isETF && !isAdvertiser && !isCrypto && (
              <div className="">
                <CompareSection
                  compareData={compareData}
                  currentSymbol={symbol}
                  initialSymbols={peersSymbols[0] ? [symbol, peersSymbols[0]] : [symbol]}
                  //initialSymbols={[symbol]}
                />
              </div>
            )}
            {!isCrypto && (
              <DrilldownFooter
                advertiserProfile={advertiserProfile}
                isAdvertiser={isAdvertiser}
                isETF={isETF}
                leftSection={!isETF ? <FAQSection profile={profile} title={t('Quote.FAQ.faq')} /> : null}
                peers={profile.quotes}
                peersChartData={peersChartData}
                symbol={symbol}
                type={profile.schedule?.type}
              />
            )}
            <div className="">
              <LazyLoad offset={100} once>
                <div className="wrapper w-full mb-5 overflow-hidden">
                  <div className="md:w-3/4 xs:w-full">
                    <TaboolaPlacement
                      container="taboola-feed-below-quotes"
                      mode="alternating-thumbnails-a"
                      placement="Feed Below Quotes"
                      settings={{
                        other: 'auto',
                      }}
                      url=""
                      vizSensor={false}
                    />
                  </div>
                </div>
              </LazyLoad>
            </div>
          </div>
        </div>
      </QuoteLayout>
    </>
  );
};

const FAQSection: React.FC<{ title: string; profile: QuoteProfile }> = ({ profile, title }) => (
  <>
    <SectionTitle level={3} size="2xl">
      {title}
    </SectionTitle>
    <ProfileFAQ profile={profile} searchValue={profile.symbol} />
  </>
);

const corporationSchema = (
  symbol: string,
  richQuoteData: DelayedQuote,
  tickerDetails: TickerDetail,
  canonical: string,
) => {
  return {
    '@context': 'https://schema.org',
    '@type': 'Corporation',
    description: tickerDetails?.[0]?.company?.longDescription ?? '',
    name: richQuoteData?.companyStandardName ?? '',
    tickerSymbol: symbol,
    url: canonical ?? '',
  };
};

export const getServerSideProps = async ({ query: { ticker }, req, res }) => {
  if (req?.headers?.host) {
    await setLanguageByHost(req?.headers?.host); // set language for server side translations
  }

  const symbol: string = sanitizeHTML(ticker) || '';

  const activeTab = 'profile';

  if (typeof res.setHeader === 'function' && symbol) {
    res.setHeader('Surrogate-Key', symbol);
  }

  const pageData = await getQuoteV2PageData(
    symbol,
    activeTab,
    getQuoteFeedTypeByLocale(i18n.language as LocaleType),
    req?.headers?.host,
  );

  if (pageData?.props?.errorCode) {
    res.statusCode = pageData.props.errorCode;
  }
  if ('props' in pageData) {
    const contentManager = getGlobalSession().getManager(ContentManager);
    (pageData.props as QuoteV2PageProps).sidebar =
      ((await contentManager.getWordpressPost(59458))?.ok as WordpressSidebar) || null;
  }

  return pageData;
};

export default QuotePageV2;
