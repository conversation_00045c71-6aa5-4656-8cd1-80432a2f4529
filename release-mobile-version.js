const util = require('util');
const exec = require('child_process').exec;

const STAGING_RELEASE_BRANCH = 'task/BZM-#13384-mobile-release-v3-5-9-5';
const PRODUCTION_RELEASE_BRANCH = 'task/BZM-#13384-mobile-release-v3-5-9-5';
const BASE_RELEASE_VERSION = 'v3.5.9.4';
const FORCE_BASE_VERSION = true;
const RC_POSTFIX = 'rc';
const RELEASE_BRANCH_PREFIX = 'task/BZM-';
const EAS_STG_CHANNEL = 'v3.5.9-stage';
const EAS_PROD_CHANNEL = 'v3.5.9';

async function executeNativeCommand(cmd, liveOutput = false) {
  return new Promise(async (resolve, reject) => {
      const proc = exec(cmd);
      let stdout, stderr;
      if (liveOutput) {
        proc.stdin.pipe(process.stdin);
        proc.stdout.pipe(process.stdout);
        proc.stderr.pipe(process.stderr);
      }
      proc.stdout.on('data', (data) => {
        stdout += data;
      });
      proc.stderr.on('data', (data) => {
        stderr += data;
      });

      proc.on('exit', (code) => {
        if (code) {
          reject(stderr);
        } else {
          resolve(stdout);
        }
      });
  });
}

function senitizeBranchNames(str) {
  const branches = [];
  let currentBranch = '';
  str.split('\n').forEach(b => {
    let branchName = b.trim();
    if (branchName.startsWith('* ')) {
      branchName = branchName.substr(2, branchName.length - 1);
      currentBranch = branchName;
    }
    branches.push(branchName);
  });
  return {
    currentBranch, branches
  }
}

async function listBranches() {
  return executeNativeCommand(`git branch --list '*BZM-*'`)
  .then(senitizeBranchNames);
}

async function determineNextReleaseVersion(branches, isStaging, prodReleaseType = -1) {
  let lastVersion;
  if (FORCE_BASE_VERSION) {
    lastVersion = BASE_RELEASE_VERSION;
  } else {
    const prevVersions = branches.branches
      .filter(b => b.startsWith(RELEASE_BRANCH_PREFIX))
      .map(b => b.replace(RELEASE_BRANCH_PREFIX, ''))
      .sort((a, b) => a.localeCompare(b));
    lastVersion = prevVersions.length ?
      prevVersions[prevVersions.length - 1] : BASE_RELEASE_VERSION;
    lastVersion = lastVersion.replace('-', '.');
  }
  console.log('Last release version:', lastVersion);

  const versionComps = lastVersion.split('.');

  if (isStaging) {
    // increment nano version
    const micro = parseInt(versionComps[versionComps.length - 1]) + 1;
    versionComps[versionComps.length - 1] = micro;
    // add rc postfix if staging
    versionComps.push(RC_POSTFIX);
  } else {
    const microIndex = versionComps.length - 2;
    const nanoIndex = versionComps.length - 1;
    const micro = parseInt(versionComps[microIndex]);
    const nano = parseInt(versionComps[nanoIndex]);
    if (prodReleaseType === '1') {
      // increment nano version
      versionComps[nanoIndex] = nano + 1;
    } else if (prodReleaseType === '2') {
      // increment micro version
      versionComps[microIndex] = micro + 1;
      // reset nano version to 1
      versionComps[nanoIndex] = 1;
    } else {
      console.log('Invalid choice.');
      process.exit(1);
    }
  }
  const nextVersion = versionComps.join('.');
  console.log('Next release version:', nextVersion);
  return nextVersion;
}

async function getAnswer(question) {
  return new Promise((resolve, reject) => {
    const readline = require('readline').createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    readline.question(question, answer => {
      resolve(answer)
      readline.close();
    });

  });
}

async function askForDecision() {
  const question = `Select the release you want to proceed with.\n` +
  `1) Staging release. (EAS OTA update on existing testflight versions)\n` +
  `2) Production release\n` +
  `Your choice: \n`;
  return await getAnswer(question);
}

async function askForProdReleaseType() {
  const question = `Select the release type you want to proceed with.\n` +
  `1) EAS OTA update release on existing store version (nano update - v3.4.2.x)\n` +
  `2) New app store version (micro update v3.4.x.1)\n` +
  `Your choice: \n`;
  return await getAnswer(question);
}

async function doStagingRelease(version) {
  const cmd = `cd apps/bz-mobile && ` +
    `export REACT_NATIVE_EAS_BZ_VERSION=${version} && ` +
    `eas update --branch ${EAS_STG_CHANNEL} --message "Updated version ${version}"`;
  await executeNativeCommand(cmd, true);
}

async function cutReleaseBranch(version) {
  const trimmedVersion = version.replace(/\.(?=[^.]*$)/,"-");
  const cmd = `git checkout -b ${RELEASE_BRANCH_PREFIX}${trimmedVersion} && ` +
    `git push -u origin ${RELEASE_BRANCH_PREFIX}${trimmedVersion}`;
  await executeNativeCommand(cmd, true);
}

async function doProductionRelease(version) {
  const branchName = version.split('.').slice(0, 3).join('.');
  const cmd = `cd apps/bz-mobile && ` +
    `export REACT_NATIVE_EAS_BZ_VERSION=${version} && ` +
    `eas update --branch ${EAS_PROD_CHANNEL} --message "Updated version ${version}"`;
  console.log(cmd);
  await executeNativeCommand(cmd, true);
}

async function main() {
  try {
    // fetch available mobile branches
    const branches = await listBranches();
    // check current branch
    if (branches.currentBranch === STAGING_RELEASE_BRANCH) {
      console.log('Current branch is staging release branch: ', branches.currentBranch);
      const choice = await askForDecision();
      if (choice === '1') {
        // proceed for staging release
        const nextVersion = await determineNextReleaseVersion(branches, true);
        await doStagingRelease(nextVersion);
      } else if (choice === '2') {
        // proceed for production release
        const prodReleaseType = await askForProdReleaseType();
        const nextVersion = await determineNextReleaseVersion(branches, false, prodReleaseType);
        if (prodReleaseType === 2) {
          await cutReleaseBranch(nextVersion);
        }
        await doProductionRelease(nextVersion);
      } else {
        console.log('Invalid choice. Please try again with correct choice.');
      }
    } else {
      console.log('Invalid current branch. Please checkout one of the release branches.');
      console.log(STAGING_RELEASE_BRANCH);
    }
  } catch (e) {
    console.error(e);
  }
}

main();