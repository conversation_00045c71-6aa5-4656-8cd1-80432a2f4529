import { B<PERSON>Header } from '@benzinga/next-utils';
import React from 'react';
// import RegisterDomCss from './RegisterDomCss';
import Script from 'next/script';
import { LocaleType } from '@benzinga/translate';
// import dynamic from 'next/dynamic';

// const RegisterDomCss = dynamic(() => import('./RegisterDomCss'), { ssr: false });

function RootHeadTag({ language }: { language?: LocaleType | undefined }) {
  return (
    <head>
      <BZHeader locale={language} />
      <link crossOrigin="use-credentials" href="https://accounts.benzinga.com" rel="preconnect" />
      <link href="https://cdn.benzinga.com" rel="preconnect" />
      <link crossOrigin="use-credentials" href="https://cdn.segment.com" rel="preconnect" />
      <link href="https://ads.adthrive.com" rel="dns-prefetch" />
      <link href="https://data-api-next.benzinga.com" rel="dns-prefetch" />
      <link href="https://www.googletagservices.com" rel="dns-prefetch" />
      <link href="https://www.googletagmanager.com" rel="dns-prefetch" />
      <link href="https://securepubads.g.doubleclick.net/tag/js/gpt.js" rel="dns-prefetch" />
      <link as="script" href="https://securepubads.g.doubleclick.net/tag/js/gpt.js" rel="preload" />
      <link as="script" href="https://www.google-analytics.com/analytics.js" rel="preload" />
      {/* <RegisterDomCss suppressHydrationWarning /> */}
    </head>
  );
}

export default RootHeadTag;
