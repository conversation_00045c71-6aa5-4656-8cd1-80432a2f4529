import getQuoteV2PageData, { getQuoteFeedTypeByLocale, QuoteV2PageProps } from '../../../src/quoteV2Utils';
import { QuoteLayout } from '../../../src/components/Quote/V2/QuoteLayout';
import i18n, { LocaleType, setLanguageByHost } from '@benzinga/translate';
import UnusualOptions from '../../../src/components/Quote/unusualOptions';
import { CalendarPageProps } from '../../calendars/[calendar]';
import { getCalendarData } from './../../../utils/calendarData';
import { sanitizeHTML } from '@benzinga/frontend-utils';

const UnusualOptionsLayout: React.FC<QuoteV2PageProps & CalendarPageProps> = ({
  activeTab,
  advertiserProfile,
  calendarDataSet,
  isAdvertiser,
  profile,
  showGovLink,
  symbol,
}) => {
  return (
    <QuoteLayout
      advertiserProfile={advertiserProfile}
      defaultPath={activeTab}
      isAdvertiser={isAdvertiser}
      profile={profile}
      showGovLink={showGovLink}
      symbol={symbol}
    >
      <UnusualOptions calendarDataSet={calendarDataSet} profile={profile} symbol={symbol} />
    </QuoteLayout>
  );
};

export const getServerSideProps = async ({ query, req, resolvedUrl }) => {
  if (req?.headers?.host) {
    await setLanguageByHost(req?.headers?.host);
  }

  const { ticker } = query || {};

  const symbol: string = sanitizeHTML(ticker) || '';
  const activeTab = 'unusual-options';

  const { calendarDataSet, initialDateRange } = await getCalendarData({ resolvedUrl, symbol });

  const pageData = await getQuoteV2PageData(
    symbol,
    activeTab,
    getQuoteFeedTypeByLocale(i18n.language as LocaleType),
    req?.headers?.host,
  );

  if ('props' in pageData) {
    return {
      props: {
        ...pageData?.props,
        calendarDataSet: calendarDataSet || null,
        initialDateRange: initialDateRange || null,
      },
    };
  }

  return pageData;
};

export default UnusualOptionsLayout;
