import { SafeError, SafePromise } from '@benzinga/safe-await';
import { AdvancedNewsManager, Story, StoryCategory, StoryObject } from '@benzinga/advanced-news-manager';
import { ArticleBlock, ArticleData, GetArticleI, InternalAsset } from './entities/article';
import { ArticleVideoPlayer, ContentManager, Term } from '@benzinga/content-manager';
import { loadServerSideBlockData } from '@benzinga/blocks-utils';
import { Session } from '@benzinga/session';
import { News, NodeCategory } from '@benzinga/basic-news-manager';
import { getSessionSingleton } from '@benzinga/session';
import { TAG_KEYS_MAP } from './assets/markup/tag-map';
import { camelCaseKeys, camelCaseKeysShallow, validURL } from '@benzinga/utils';

import { getQuoteDetailsFromSymbols, replaceRedditHTML } from './utilsFunc';
import { printProHeadlineArticleContent } from './utilsFunc';

import { parse, Node, HTMLElement as HTMLParserElement } from 'node-html-parser';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
// import imageSize from 'request-image-size';

import { probeImage } from '@benzinga/utils';

import { decode } from 'html-entities';

import { fixedEncodeURI } from '@benzinga/utils';
import { ArticleManager } from './manager';
import { tickerizeContent } from './tokenization';
import { TidParam } from '@benzinga/internal-news-manager';

export const getArticle = async (session: Session, node_id: number | string): SafePromise<GetArticleI> => {
  const articleRes = await session.getManager(ArticleManager).getInternalNode(node_id);

  if (articleRes.ok) {
    const article = articleRes.ok as GetArticleI;

    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
    const isImageExpired = new Date(article.CreatedAt ?? Date.now()) < oneYearAgo;

    if (!isImageExpired) {
      article.image = findPrimaryImage(article.assets);
    }

    // if (!article.image) {
    //   console.error('WARNING::Primary Image not found or not set!', {
    //     assets: article.assets,
    //     node_id,
    //   });
    // }

    article.Body = tickerizeContent(article.Body);
    article.Body = replaceRedditHTML(article.Body);
    const sponsored = isSponsoredArticle(camelCaseKeysShallow(article) as ArticleData);

    article.isHeadline = !article.Body || (article.IsBzProPost && !article.IsBzPost);

    const MAX_ARTICLE_LENGTH_FOR_PARSER = 1000000;
    // If article weight is more than 1mb display it in frame
    article.shouldDisplayedInFrame = !article.isHeadline && article?.Body?.length > MAX_ARTICLE_LENGTH_FOR_PARSER;

    // If pr_secfilings appears in article then it's old, should avoid to parse that type
    const shouldParseBlocks =
      !article.isHeadline && article.Type !== 'pr_secfilings' && !article.shouldDisplayedInFrame;

    if (shouldParseBlocks) {
      article.blocks = await parseArticleBodyToBlocks(session, article.Body, sponsored, article.NodeID);
      article.parsedBody = splitHTMLContent(article.Body);
    }

    if (article.isHeadline) {
      article.Body = printProHeadlineArticleContent();
    }

    if (article.UserID) {
      const authorRes = await session.getManager(ArticleManager).getAuthor(article.UserID);

      article.Author = authorRes?.ok;
      if (article.Author && article.Author.uid && article.Author.profileUrl === 'https://www.benzinga.com/author/') {
        article.Author.profileUrl = `https://www.benzinga.com/user/${article.Author.uid}`;
      } else if (article.Author && article.Author.profileUrl) {
        article.Author.profileUrl = article.Author.profileUrl?.replace('https://www.benzinga.com/', '/');
      }
    }

    try {
      const symbols = article?.Tickers?.map(ticker => ticker?.name) ?? [];
      if (Array.isArray(symbols)) {
        const quoteDetails = await getQuoteDetailsFromSymbols(symbols);

        if (quoteDetails) {
          article.Quotes = quoteDetails;
        }
      }
    } catch (e) {
      console.error('Article Get Quotes Error:', e);
    }

    try {
      if (article.image) {
        const primaryImageDetails = await probeImage(fixedEncodeURI(article.image));
        if (primaryImageDetails && primaryImageDetails?.width && primaryImageDetails?.width > 100) {
          article.primaryImage = {
            alt: article.Title ?? '',
            height: primaryImageDetails?.height ?? 380,
            url: article.image,
            width: primaryImageDetails?.width ?? 700,
          };
        }

        // console.error('WARNING::Probe Image Skipped!', {
        //   assets: article.assets,
        //   node_id,
        //   url: article.image,
        // });
      }
    } catch (error: any) {
      if (error.code !== 'ECONNRESET') {
        console.error(
          'ERROR::Primary Image Probe Error!',
          JSON.stringify({
            assets: article.assets,
            error,
            node_id,
            url: article.image,
          }),
        );
      }
    }

    const termsReq = await getArticleTerms(session, article);
    article.Terms = termsReq.ok as Term[];

    const commentsManager = session.getManager(ArticleManager);
    const commentCountResponse = await commentsManager.getCommentCount(article.NodeID);
    const commentCount = commentCountResponse?.ok?.data?.count ?? 0;
    article.commentCount = commentCount;

    return {
      ok: article,
    };
  } else if (articleRes.err) {
    return { err: articleRes.err };
  } else {
    return { err: new SafeError('Something went wrong!', 'articleRequest') };
  }
};

export const splitHTMLContentRaw = (htmlString: string): Node[] => {
  const body = parse(htmlString);
  const splitContent: Node[] = [];

  if (Array.isArray(body?.childNodes)) {
    body.childNodes.forEach(item => {
      splitContent.push(item);
    });
  }

  return splitContent;
};

export const splitHTMLContent = (htmlString: string): string[] => {
  const body = parse(htmlString);
  const splitContent: string[] = [];

  if (Array.isArray(body?.childNodes)) {
    body.childNodes.forEach(item => {
      splitContent.push(item.toString());
    });
  }

  return splitContent;
};

const notAllowedInPTags = ['ul', 'ol', 'div', 'p', 'form', 'table', 'img'];

const formatImageCoreBlock = async (element: HTMLParserElement): Promise<ArticleBlock> => {
  try {
    let attributes: Record<string, string> = {};

    if (element.attributes) {
      attributes = {
        alt: '',
        ...element?.attributes,
      };
    }

    const isImageHasExtension = (url: string): boolean => {
      if (!url) return false;

      return url.match(/\.(jpeg|jpg|gif|png|svg|webp|heic|tif|bmp)$/) != null;
    };

    const isImageSafe = (url: string): boolean => {
      return !url.includes('http://');
    };

    let skipImageSizeCheck = false;

    if (
      !isImageHasExtension(element?.attributes.src) ||
      !isImageSafe(element?.attributes.src) ||
      element?.attributes.src.startsWith('/')
    ) {
      skipImageSizeCheck = true;
    }

    if (!skipImageSizeCheck && (!attributes.width || !attributes.height)) {
      const sizes = await probeImage(element?.attributes.src);

      if (sizes?.width && !attributes.width) {
        attributes.width = sizes?.width as unknown as string;
      }

      if (sizes?.height) {
        if (attributes.width) {
          attributes.height = `${(Number(attributes.width) * sizes?.height) / sizes?.width}`;
        } else {
          attributes.height = sizes?.height as unknown as string;
        }
      }
    }

    return {
      blockName: 'core/image',
      hasChild: false,
      innerHTML: '',
      nodeType: 1,
      tag: 'img',
      tagAttributes: attributes,
    };
  } catch (e) {
    return {
      blockName: 'core/image',
      hasChild: false,
      innerHTML: '',
      nodeType: 1,
      tag: 'img',
      tagAttributes: element?.attributes || {},
    };
  }
};

const base = new URL('https://www.benzinga.com');
const isExternal = (url: string): boolean => new URL(url, base).hostname !== base.hostname;

export const formatElementBlock = async (
  session: Session,
  element: HTMLParserElement,
  sponsored?: boolean,
  articleId?: number,
): Promise<ArticleBlock> => {
  if (element?.rawTagName === 'img') {
    return await formatImageCoreBlock(element);
  } else {
    return await formatHTMLCoreBlock(session, element, sponsored, articleId);
  }
};

const formatHTMLCoreBlock = async (
  session: Session,
  element: HTMLParserElement,
  sponsored?: boolean,
  articleId?: number,
): Promise<ArticleBlock> => {
  const childBlocks: unknown[] = [];
  const extractedElements: unknown[] = [];

  let content = '';
  if (Array.isArray(element?.childNodes) && element?.childNodes.length) {
    content = element.childNodes.map((item: Node) => item.toString()).join('');

    let index = 0;
    for (const childElement of element.childNodes as HTMLParserElement[]) {
      try {
        const block = await formatElementBlock(session, childElement, sponsored, articleId);

        if (block?.tag === 'a') {
          // Kinda a hack --> https://gitlab.benzinga.io/benzinga/fusion/-/work_items/9104?iid_path=true#note_264485
          if (block?.tagAttributes['ref']) {
            block.tagAttributes['ref'] = null;
          }
          let href = block?.tagAttributes['href'];
          href = href?.trim() ?? null;
          block.tagAttributes['target'] = '_blank';
          if (sponsored && typeof href === 'string' && validURL(href) && isExternal(href)) {
            block.tagAttributes['rel'] = 'nofollow noopener noreferrer';
          }
        }

        // Extract elements which cant be inside the P tag to prevent markup break
        if (element.rawTagName === 'p') {
          if (notAllowedInPTags.includes(childElement.rawTagName)) {
            extractedElements.push({
              block,
              index,
            });
            continue;
          }
        }

        childBlocks.push(block);

        // Insert extracted child elements which cant be inside the P tag to prevent markup break
        if (block?.extracted?.length) {
          block?.extracted?.forEach(item => {
            childBlocks.push(item.block);
          });
        }

        index++;
      } catch (error) {
        console.error(`ERROR:: Child Format Error - articleId: ${articleId}`, error);
      }
    }
  } else {
    content = element.rawText;
  }

  const tagAttributes = convertAttributesToCamelCase(element?.attributes) || {};

  // console.log('tagAttributes', tagAttributes);

  if (tagAttributes.className === 'bz-widget' && tagAttributes['data-name']) {
    const postResponse = await session
      .getManager(ContentManager)
      .getWordpressPost(tagAttributes['data-name'] as unknown as number);
    const widget = postResponse.ok;

    if (widget && widget.success !== false && Array.isArray(widget.blocks)) {
      widget.blocks = await loadServerSideBlockData(session, widget.blocks);
    }

    return {
      blockName: 'acf/widget-container',
      // blocks: [],
      blocks: widget?.blocks,
      dynamic: false,
      tagAttributes,
      type: 'widget',
    } as unknown as ArticleBlock;
  }

  return {
    blockName: 'core/html',
    childBlocks: childBlocks ?? [],
    extracted: extractedElements ?? [],
    hasChild: !!(Array.isArray(element?.childNodes) && element?.childNodes.length),
    innerHTML: content ? decode(content) : '',
    nodeType: element?.nodeType ?? 0,
    tag: element?.rawTagName ? element?.rawTagName.toLowerCase() : '',
    tagAttributes,
  } as ArticleBlock;
};

export const convertAttributesToCamelCase = (attributes: { [key: string]: any }): { [key: string]: any } => {
  try {
    const attrs = {};

    if (attributes) {
      Object.keys(attributes).forEach(key => {
        attrs[TAG_KEYS_MAP[key] || key] = attributes[key];
      });
    }

    return attrs;
  } catch (error) {
    console.error('Error converting attributes to camel case:', error);

    return {};
  }
};

export const deduplicateElements = (
  elementString: string,
  firstBlock: ArticleBlock,
  secondBlock: ArticleBlock,
  articleBlocks: ArticleBlock[],
  currentIndex: number,
) => {
  if (!elementString) return articleBlocks;
  if (
    (firstBlock?.tag === 'p' || secondBlock?.tag === 'p') &&
    (firstBlock.tag === 'img' || firstBlock?.innerHTML?.slice(0, elementString.length).includes(elementString)) &&
    (secondBlock.tag === 'img' || secondBlock?.innerHTML?.slice(0, elementString.length).includes(elementString)) &&
    secondBlock?.innerHTML?.includes(firstBlock?.innerHTML ?? '')
  ) {
    articleBlocks.splice(currentIndex - 1, 1);
  }

  return articleBlocks;
};

export const parseArticleBodyToBlocks = async (
  session: Session,
  body: string,
  sponsored?: boolean,
  articleId?: number,
) => {
  const elements = body ? splitHTMLContentRaw(body) : [];

  const htmlNodeElements = elements.filter((item: Node) => {
    return !item.rawText || item.rawText?.trim() !== '' || (!!item.rawText && item.childNodes.length);
  });

  const articleBlocks: ArticleBlock[] = [];
  let htmlNodeElementsIndex = 0;

  // Process and clean up blocks
  const processBlock = async (block: HTMLParserElement, index: number) => {
    const formattedBlock = await formatElementBlock(session, block, sponsored, articleId);

    // Handle remove Twitter script as part of Twitter embed
    if (
      formattedBlock?.tag === 'figure' &&
      formattedBlock.tagAttributes?.className?.includes('twitter') &&
      Array.isArray(formattedBlock.childBlocks)
    ) {
      formattedBlock.childBlocks = formattedBlock.childBlocks.map(child => {
        if (
          typeof child === 'object' &&
          child.tag === 'div' &&
          child.tagAttributes?.className === 'wp-block-embed__wrapper'
        ) {
          if (Array.isArray(child.childBlocks)) {
            child.childBlocks = child.childBlocks.filter(
              block => block && typeof block === 'object' && 'tag' in block && block.tag !== 'script',
            );
          }

          if (typeof child.innerHTML === 'string') {
            child.innerHTML = child.innerHTML.replace(/<script[^>]*>.*?<\/script>/g, '');
          }
        }
        return child;
      });
    }

    articleBlocks.push(formattedBlock);

    if (formattedBlock?.extracted?.length) {
      formattedBlock.extracted.forEach(item => {
        const previousBlock = articleBlocks[index - 1];

        // Hack to solve duplicate table/image issue
        deduplicateElements('<table', previousBlock, formattedBlock, articleBlocks, index);

        // Hack to remove duplicate images wrapped in P tag
        if (
          typeof formattedBlock.innerHTML === 'string' &&
          formattedBlock.innerHTML.includes('<img') &&
          item.block.tag === 'img' &&
          item.block?.tagAttributes?.src &&
          formattedBlock.innerHTML.includes(item.block.tagAttributes.src)
        ) {
          articleBlocks.pop();
        }
        articleBlocks.push(item.block);
      });
    }
  };

  for (const block of htmlNodeElements) {
    htmlNodeElementsIndex++;
    try {
      await processBlock(block as HTMLParserElement, htmlNodeElementsIndex);
    } catch (error) {
      console.error('ERROR:: Error format parent element block', error);
    }
  }

  return articleBlocks;
};

export const isProOnlyPost = (article: StoryObject | ArticleData, bodyCheck = false): boolean => {
  if (article) {
    const benzingaProAuthorIds = [11389];
    const benzingaProAuthorNames = ['Benzinga Newsdesk'];
    if (typeof article.author === 'string' && benzingaProAuthorNames.includes(article.author)) {
      return true;
    }
    const author = 'userId' in article ? article.userId : article.author?.uid;
    if (author && benzingaProAuthorIds.includes(author)) {
      return true;
    }
    const result = !!((bodyCheck && !article['body']) || (article.isBzProPost && !article.isBzPost));
    return result;
  }
  return false;
};

export const isSponsoredArticle = (article: ArticleData | News | StoryObject): boolean => {
  if (article) {
    const articleMeta = article?.meta ? camelCaseKeys<any>(article?.meta) : {};
    const showAdvertiserDisclosure = articleMeta?.flags?.showAdvertiserDisclosure || false;
    const tags = article.tags?.map((tag: NodeCategory) => tag.name) ?? [];
    const channels = article.channels?.map((channel: NodeCategory) => channel.name) ?? [];
    const tagsResult = tags.includes('Partner Content');
    const channelsResult = channels.includes('Partner Content');
    return tagsResult || channelsResult || showAdvertiserDisclosure;
  }
  return false;
};

export const getSponsoredContentLabel = (
  article: ArticleData | News | StoryObject,
): { isSponsored: boolean; label: string } => {
  const sponsored = isSponsoredArticle(article);
  let label = '';
  if (sponsored) {
    label = 'Partner Content';
    const articleMeta = article?.meta ? camelCaseKeys<any>(article?.meta) : {};
    const isContributedContent = articleMeta?.flags?.ContributedContent || false;

    if (isContributedContent) {
      label = 'Contributed Partner Content';
    }
  }
  return { isSponsored: sponsored, label: label };
};

export const checkIfIsSponsoredSourceContent = async (articleType: string, session: Session): Promise<boolean> => {
  if (articleType) {
    const advancedNewsManager = session.getManager(AdvancedNewsManager);
    const contentTypes = await advancedNewsManager.getContentTypes();
    const result = contentTypes.ok;
    if (result) {
      return result?.sponsored?.includes(articleType);
    }
  }
  return false;
};

export const isOfferingArticle = (article: ArticleData) => {
  if (article) {
    const tags = article.tags?.map(tag => tag.tid) ?? [];
    const channels = article.channels?.map(channel => channel.tid) ?? [];
    const tagsResult = tags.includes(16786);
    const channelsResult = channels.includes(142057);
    return tagsResult || channelsResult;
  }
  return false;
};

export const getArticleTerms = async (_session: Session, article: GetArticleI): SafePromise<Term[]> => {
  const channels = article.Channels ? article.Channels : [];
  const tags = article.Tags ? article.Tags : [];
  const tids = channels.map(c => c.tid).concat(tags.map(t => t.tid));
  const results = await Promise.all(
    tids
      .map(async (tid: number) => {
        const termRes = await getSessionSingleton().getManager(ContentManager).getTermById(String(tid));

        return Array.isArray(termRes?.ok) ? termRes.ok?.[0] : null;
      })
      .filter(term => term !== null),
  );

  return {
    ok: results as Term[],
  };
};

export const findPrimaryImage = (assets: InternalAsset) => {
  if (Array.isArray(assets) && assets.length) {
    for (const asset of assets) {
      if (asset.type === 'image' && asset.primary && asset.attributes.filepath) {
        // ToDo: This is dumb. Get backend to to return full URL.
        return 'https://www.benzinga.com/' + asset.attributes.filepath;
      }
    }
  }
  return '';
};

export const getInitialTickers = (article: ArticleData): string[] => {
  if (!Array.isArray(article.quotes)) return [];
  return article.quotes?.filter(ticker => ticker.type === 'STOCK').map(item => item.symbol);
};

export const getPrimaryTickers = (tickers?: StoryCategory[]) => {
  const result = Array.isArray(tickers)
    ? tickers.sort((a, b) => (a.primary === b.primary ? 0 : a.primary ? -1 : 1)).map(ticker => ticker.name)
    : [];
  return result;
};

export const getArticleTagsNames = (articleData: Story): string[] => {
  const tags = articleData?.getTags();
  if (Array.isArray(tags)) {
    return tags.map(item => item.name);
  } else {
    return [];
  }
};

export const getNodeTagsNames = (node: News): string[] => {
  if (Array.isArray(node?.tags)) {
    return node.tags.map(item => item.name);
  } else {
    return [];
  }
};

export const getArticleChannelsNames = (articleData: Story): string[] => {
  const channels = articleData.getChannels();
  if (Array.isArray(channels)) {
    return channels.map(item => item.name);
  } else {
    return [];
  }
};

export const getNodeChannelsNames = (node: News): string[] => {
  if (Array.isArray(node?.channels)) {
    return node.channels.map(item => item.name);
  } else {
    return [];
  }
};

export const getArticleTickers = (articleData: Story): string[] => {
  const tickers = articleData.getTickers();
  if (Array.isArray(tickers)) {
    return tickers.map(item => item.name);
  } else {
    return [];
  }
};

export const filterRelatedNodes = (story: Story, nodes: News[]): News[] => {
  const relatedArticles: News[] = [];
  const matchedArticles = {};

  const storyTags = getArticleTagsNames(story);
  let storyChannels = getArticleChannelsNames(story);
  storyChannels = storyChannels.filter(channelName => !['Cannabis', 'News', 'Markets'].includes(channelName));

  if (Array.isArray(nodes)) {
    nodes.forEach(node => {
      const nodeTags = getNodeTagsNames(node);
      let nodeChannels: string[] = [];
      let matchedChannels: string[] = [];

      if (storyChannels.length) {
        nodeChannels = getNodeChannelsNames(node);
        matchedChannels = nodeChannels.filter(value => storyChannels.includes(value));
      }

      const matchedTags = nodeTags.filter(value => storyTags.includes(value));

      if (
        node.id &&
        (matchedTags.length || matchedChannels.length) &&
        story.getNodeId() !== node.id &&
        !matchedArticles[node.id]
      ) {
        matchedArticles[node.id] = true;
        relatedArticles.push(node);
      }
    });
  }

  return relatedArticles;
};

const TABOOLA_POSITION_STORAGE_SLUG = 'taboola-article-position';
const DEFAULT_TABOOLA_POSITION = 0;
export const TABOOLA_ALLOWED_POSITIONS = [0, 1, 2, 3, 4, 5];

export const getTaboolaPlacementFromStorage = (): number | null => {
  const value = window?.localStorage?.getItem(TABOOLA_POSITION_STORAGE_SLUG);

  if (typeof value === 'string') {
    return Number(value);
  }

  return null;
};

export const setTaboolaPlacementToStorage = (value: number): void => {
  window?.localStorage?.setItem(TABOOLA_POSITION_STORAGE_SLUG, String(value));
};

export const getSovPosition = (positions: number[] | string[], percentages: number[]): number | string => {
  if (positions.length !== percentages.length) {
    throw new Error('Positions and Percentages should be same length');
  }

  const randomValue = Math.floor(Math.random() * 100);
  let cumulativePercentage = 0;

  for (let i = 0; i < percentages.length; i++) {
    cumulativePercentage += percentages[i];
    if (randomValue < cumulativePercentage) {
      return positions[i];
    }
  }

  return positions[0];
};

export const manageTaboolaPosition = (): number => {
  try {
    const storageValue = getTaboolaPlacementFromStorage();

    if (typeof storageValue === 'number') {
      return storageValue;
    } else {
      const taboolaPosition = getSovPosition(TABOOLA_ALLOWED_POSITIONS, [0, 70, 0, 0, 0, 0]);
      setTaboolaPlacementToStorage(taboolaPosition as number);

      return taboolaPosition as number;
    }
  } catch {
    return DEFAULT_TABOOLA_POSITION;
  }
};

export const isTaboolaAllowedToShow = (articleIndex: number): boolean => {
  const storageValue = getTaboolaPlacementFromStorage();

  if (typeof storageValue === 'number') {
    return storageValue == articleIndex;
  } else {
    return false;
  }
};

const VIDEO_PLAYER_SOV_SLUG = 'videoPlayerSOVSlug';

export const getArticleVideoPlayerSOV = (videoPlayerSettings: ArticleVideoPlayer, playersKeys: string[]): string => {
  let videoPlayerSOVSlug = '';
  if (videoPlayerSettings?.connatix) {
    videoPlayerSOVSlug = 'connatix';
  } else {
    videoPlayerSOVSlug = window[VIDEO_PLAYER_SOV_SLUG] || getSovPosition(playersKeys, [10, 90]);
  }

  return videoPlayerSOVSlug;
};

export const setArticleVideoPlayerSOV = (videoPlayerSlug: string) => {
  window[VIDEO_PLAYER_SOV_SLUG] = videoPlayerSlug;
};

export const isTaxonomyExists = (queryTaxonomiesNames: string[], taxonomies: StoryCategory[]): boolean => {
  if (Array.isArray(taxonomies) && Array.isArray(queryTaxonomiesNames)) {
    return taxonomies?.some(item => {
      return queryTaxonomiesNames.includes(item.name);
    });
  }

  return false;
};

export const countElementsInBodyBlocks = (articleBlocks: ArticleBlock[], elementTagName: string): number => {
  if (Array.isArray(articleBlocks)) {
    return articleBlocks.filter(item => item?.tag === elementTagName)?.length || 0;
  }

  return 0;
};

export type TemplateOverride = 'new' | 'old' | null;

export const shouldUseNewTemplate = (_articleId: string, override: TemplateOverride = null): boolean => {
  if (override === 'new') return true;
  if (override === 'old') return false;
  return true;
  // const numericId = parseInt(articleId, 10);
  // return numericId % 100 < 80;
};

interface TemplateFeatures {
  useNewTemplate: boolean;
  hasTaboola: boolean;
  hasInfiniteScroll: boolean;
}

export const getTemplateFeatures = (articleId: string, template?: TemplateOverride): TemplateFeatures => {
  const useNewTemplate = shouldUseNewTemplate(articleId, template);

  if (!useNewTemplate) {
    return {
      hasInfiniteScroll: false,
      hasTaboola: false,
      useNewTemplate: false,
    };
  }

  // const num = parseInt(articleId, 10);
  // const thirdToLastDigit = Math.floor((num % 1000) / 100);

  return {
    hasInfiniteScroll: true,
    //hasInfiniteScroll: false,
    hasTaboola: true,
    //hasTaboola: thirdToLastDigit < 5, // 50% of new template pages (digits 0-4)
    useNewTemplate: true,
  };
};
