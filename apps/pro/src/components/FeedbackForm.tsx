import { isEmpty } from 'ramda';
import React, { ChangeEvent, Component, FormEvent } from 'react';
import styled from '@benzinga/themetron';
import { UserManager } from '@benzinga/user-manager';

import { SessionContext, SessionContextType } from '@benzinga/session-context';

import Spinner from './ui/Spinner';
import { But<PERSON>, Modal } from 'antd';
import { newIntercomWindow } from '../utils/popouts';
import fastStringify from 'fast-json-stable-stringify';
import { DesktopNotificationManager } from '@benzinga/desktop-notification-manager';

const PRODPAD_API_URL = 'https://api.prodpad.com/v1/feedbacks';

const PRODPAD_API_KEY = '****************************************************************';

const defaultLabelColor = '#9c9c9c';
const focusLabelColor = '#1693E6';

const validateEmail = (email: string) => {
  const re =
    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return re.test(email);
};

const FirstLine = styled.h1`
  color: black;
  margin-bottom: 0.5em;
  font-size: 32px;
  font-weight: 700;
  line-height: 40px;
  text-align: center;
`;

const SecondLine = styled.h2`
  color: black;
  font-size: 24px;
  line-height: 32px;
  text-align: center;
  font-weight: 500;
  margin-bottom: 0.5em;
`;

interface InputProps {
  $isError?: boolean;
  onBlur: () => void;
  onChange: (event: ChangeEvent<any>) => void;
  onFocus: () => void;
  placeholder?: string;
  type?: string;
  value: string;
}

interface FormFieldBlockProps {
  height: string;
  margin: string;
}

const FormFieldBlock = styled.div<FormFieldBlockProps>`
  height: ${props => props.height};
  position: relative;
  margin: 0 auto ${props => props.margin} auto;
  max-width: 100%;
  width: 570px;
`;

const BlockInput = styled.input<InputProps>`
  border: 1px solid ${props => (props.$isError ? 'red' : '#dcdcdc')};
  border-radius: 4px;
  color: black;
  display: inline-block;
  font-size: 16px;
  height: 36px;
  line-height: 34px;
  margin-top: 10px;
  max-width: 100%;
  min-height: 38px;
  padding: 0px 14px;
  transition: border 0.15s ease-in-out;
  width: 570px;
  :focus {
    border-color: ${props => (props.$isError ? 'red' : '#1693E6')};
  }
`;

interface LabelProps {
  color: string;
  opacity: string;
}

const FormLabel = styled.label<LabelProps>`
  background: white;
  color: ${props => props.color};
  display: block;
  left: 10px;
  line-height: 20px;
  opacity: ${props => props.opacity};
  padding: 0 4px;
  position: absolute;
  top: 0;
  transition: opacity 0.15s ease-in-out;
  width: auto;
  z-index: 2;
`;

const InvalidFormField = styled.p`
  color: red;
  display: block;
  padding-left: 14px;
  padding-top: 2px;
`;

const TextArea = styled.textarea<InputProps>`
  border: 1px solid #dcdcdc;
  border-radius: 4px;
  color: black;
  display: inline-block;
  font-size: 16px;
  font-family: inherit;
  height: 140px;
  line-height: 160%;
  margin-top: 10px;
  max-width: 100%;
  padding: 7px 14px;
  resize: none;
  transition: border 0.15s ease-in-out;
  width: 570px;
  :focus {
    border-color: #1693e6;
  }
`;

const SubmitButton = styled.button`
  color: white;
  background-color: #1693e6;
  border: 0;
  border-radius: 4px;
  display: block;
  font-size: 14px;
  height: 40px;
  line-height: 40px;
  margin: auto;
  min-width: 175px;
  padding: 0 16px;
  transition:
    color 0.15s linear,
    box-shadow 0.15s ease-in-out,
    background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out;

  :disabled {
    opacity: 0.3;
    cursor: default;
    box-shadow: none;
  }
`;

const StyledSpinner = styled(Spinner)`
  color: yellow;
`;

const ReportButtonWrapper = styled.div`
  display: flex;
  justify-content: center;
  margin-bottom: 1em;
`;

const ReportButton = styled(Button)`
  padding: 0 25px !important;
`;

const ReportButtonText = styled.span`
  font-size: 18px;
  font-weight: 600;
`;

interface Props {
  handleClose: () => void;
  isVisible: boolean;
  onSubmit: () => void;
}

interface State {
  description: string;
  isLoading: boolean;
  labelColors: {
    [field: string]: string;
    description: string;
    email: string;
    name: string;
  };
  userEmail: string;
  userName: string;
}

class FeedbackForm extends Component<Props, State> {
  // Using the modern Context API pattern
  static contextType = SessionContext;

  constructor(props: Props) {
    super(props);

    // Initialize with empty values first
    this.state = {
      description: '',
      isLoading: false,
      labelColors: {
        description: defaultLabelColor,
        email: defaultLabelColor,
        name: defaultLabelColor,
      },
      userEmail: '',
      userName: '',
    };

    // Try to get user info if context is available
    try {
      const context = this.context as SessionContextType;
      if (context) {
        const userManager = context.getManager(UserManager);
        if (userManager) {
          const user = userManager.getUser();
          if (user) {
            const { displayName, email } = user;
            this.state = {
              ...this.state,
              userEmail: email ?? '',
              userName: displayName ?? '',
            };
          }
        }
      }
    } catch (error) {
      console.error('Failed to get user info from context:', error);
    }
  }

  setDefaultState = () => {
    // Initialize with empty values first
    const defaultState = {
      description: '',
      isLoading: false,
      labelColors: {
        description: defaultLabelColor,
        email: defaultLabelColor,
        name: defaultLabelColor,
      },
      userEmail: '',
      userName: '',
    };

    // Try to get user info if context is available
    try {
      const context = this.context as SessionContextType;
      if (context) {
        const userManager = context.getManager(UserManager);
        if (userManager) {
          const user = userManager.getUser();
          if (user) {
            const { displayName, email } = user;
            this.setState({
              ...defaultState,
              userEmail: email ?? '',
              userName: displayName ?? '',
            });
            return;
          }
        }
      }
      // If we couldn't get user info, just set the default state
      this.setState(defaultState);
    } catch (error) {
      console.error('Failed to get user info from context:', error);
      this.setState(defaultState);
    }
  };

  onChangeEmail = (event: ChangeEvent<any>) => {
    this.setState({
      userEmail: event.target.value,
    });
  };

  onChangeName = (event: ChangeEvent<any>) => {
    this.setState({
      userName: event.target.value,
    });
  };

  onChangeDescription = (event: ChangeEvent<any>) => {
    this.setState({
      description: event.target.value,
    });
  };

  submitForm = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const { description, userEmail, userName } = this.state;
    const reqBody = {
      email: userEmail,
      feedback: `${description}

Submitted from: ${window.location.origin}
Commit hash: ${process.env.GIT_COMMITHASH}
Version: ${process.env.GIT_VERSION}`,
      name: userName,
    };

    const headers = {
      Authorization: PRODPAD_API_KEY,
      'Content-Type': 'application/json',
    };

    this.setLoading(true);

    const feedbackPromise = fetch(PRODPAD_API_URL, {
      body: fastStringify(reqBody),
      headers,
      method: 'POST',
      mode: 'cors',
    }).then(() => {
      this.setLoading(false);
      this.props.onSubmit();
      // Check if context exists before trying to access getManager
      const context = this.context as SessionContextType;
      if (context) {
        try {
          const notificationManager = context.getManager(DesktopNotificationManager);
          if (notificationManager) {
            notificationManager.showInternalNotification('Thank you!', {
              body: `The Benzinga team appreciates your feedback and will review this shortly.`,
            });
          }
        } catch (error) {
          console.error('Failed to show notification:', error);
        }
      }
    });

    feedbackPromise.catch(() => {
      this.setLoading(false);
    });

    return false;
  };

  setLoading = (isLoading: boolean) => {
    this.setState({
      isLoading,
    });
  };

  isFilledName = () => !isEmpty(this.state.userName);
  isFilledDescription = () => !isEmpty(this.state.description);
  isFilledEmail = () => !isEmpty(this.state.userEmail);

  isValidEmail = () => !this.isFilledEmail() || validateEmail(this.state.userEmail);

  isDisabledSubmit = () => !(this.isFilledDescription() && this.isFilledName() && this.isValidEmail());

  getLabelColor = (field: string) => this.state.labelColors[field];
  makeLabelColorSetter = (field: string, color: string) => () => {
    this.setState(prevState => {
      if (prevState.labelColors[field] === color) {
        return null;
      } else {
        return {
          labelColors: {
            ...prevState.labelColors,
            [field]: color,
          },
        };
      }
    });
  };

  handleReportClick = () => {
    if (window.Intercom) {
      window.Intercom('show');
    } else {
      newIntercomWindow({
        onError: () => {
          window.location.href = 'mailto:<EMAIL>';
        },
      });
    }

    this.props.handleClose();
  };

  render() {
    const { description, isLoading, userEmail, userName } = this.state;
    const { handleClose, isVisible } = this.props;
    const emailLabelOpacity = this.isFilledEmail() ? '1' : '0';
    const descriptionLabelOpacity = this.isFilledDescription() ? '1' : '0';
    const nameLabelOpacity = this.isFilledName() ? '1' : '0';

    return (
      <Modal
        afterClose={this.setDefaultState}
        bodyStyle={{ background: 'white' }}
        confirmLoading={isLoading}
        footer={null}
        maskClosable={!isLoading}
        onCancel={handleClose}
        open={isVisible}
        width={1024}
        zIndex={1000}
      >
        <FirstLine>We love to hear your ideas!</FirstLine>
        <SecondLine>What would make Benzinga Pro better for you?</SecondLine>
        <ReportButtonWrapper>
          <ReportButton danger={true} onClick={this.handleReportClick} size="large" type={'primary'}>
            <ReportButtonText>Looking for Support or have a BUG to report? CLICK HERE</ReportButtonText>
          </ReportButton>
        </ReportButtonWrapper>
        <form action="#" onSubmit={this.submitForm}>
          <FormFieldBlock height="74px" margin="0">
            <BlockInput
              onBlur={this.makeLabelColorSetter('name', defaultLabelColor)}
              onChange={this.onChangeName}
              onFocus={this.makeLabelColorSetter('name', focusLabelColor)}
              placeholder="Name"
              value={userName}
            />
            <FormLabel color={this.getLabelColor('name')} opacity={nameLabelOpacity}>
              Name
            </FormLabel>
            {this.isFilledName() ? null : <InvalidFormField>Uh-oh! You forgot to add your name.</InvalidFormField>}
          </FormFieldBlock>
          <FormFieldBlock height="48px" margin="24px">
            <BlockInput
              $isError={!this.isValidEmail()}
              onBlur={this.makeLabelColorSetter('email', defaultLabelColor)}
              onChange={this.onChangeEmail}
              onFocus={this.makeLabelColorSetter('email', focusLabelColor)}
              placeholder="Email"
              type="email"
              value={userEmail}
            />
            <FormLabel color={this.getLabelColor('email')} opacity={emailLabelOpacity}>
              Email
            </FormLabel>
          </FormFieldBlock>
          <FormFieldBlock height="184px" margin="0">
            <TextArea
              onBlur={this.makeLabelColorSetter('description', defaultLabelColor)}
              onChange={this.onChangeDescription}
              onFocus={this.makeLabelColorSetter('description', focusLabelColor)}
              placeholder="It would be awesome if..."
              value={description}
            />
            <FormLabel color={this.getLabelColor('description')} opacity={descriptionLabelOpacity}>
              It would be awesome if...
            </FormLabel>
            {this.isFilledDescription() ? null : (
              <InvalidFormField>Uh-oh! You forgot to add your feedback.</InvalidFormField>
            )}
          </FormFieldBlock>
          <SubmitButton disabled={this.isDisabledSubmit()}>
            {isLoading ? <StyledSpinner /> : 'Submit Feedback'}
          </SubmitButton>
        </form>
      </Modal>
    );
  }
}

export default FeedbackForm;
