import { ExpressionObject, StockSymbol } from '@benzinga/session';

import { WidgetLinkingID } from '@benzinga/widget-linking';
import { LegacyFilter } from '@benzinga/scanner-config-manager';
import { WatchlistId } from '@benzinga/watchlist-manager';
import { CategoryId, NewsfeedFieldType, SourceId } from '@benzinga/advanced-news-manager';
import { DisplayType, Importance, NewsConfigV1, NewsConfigV2, NewsConfigV3 } from '@benzinga/news-user-settings';

export type AdvancedNewsfeedWidgetIteration = AdvancedNewsfeedWidgetIterationV12;
export type AdvancedNewsfeedAllIterations = [
  AdvancedNewsfeedWidgetIterationV12,
  AdvancedNewsfeedWidgetIterationV11,
  AdvancedNewsfeedWidgetIterationV10,
  AdvancedNewsfeedWidgetIterationV9,
  AdvancedNewsfeedWidgetIterationV8,
  AdvancedNewsfeedWidgetIterationV7,
  AdvancedNewsfeedWidgetIterationV6,
  AdvancedNewsfeedWidgetIterationV5,
  AdvancedNewsfeedWidgetIterationV4,
  AdvancedNewsfeedWidgetIterationV3,
  AdvancedNewsfeedWidgetIterationV2,
  AdvancedNewsfeedWidgetIterationV1,
];

export const defaultNewsfeedGlobalSettings: NewsfeedGlobalLayoutSettings = {
  allCaps: false,
  disableResponsiveDisplay: false,
  displayDateBanner: true,
  displayOrOperatorInExpression: true,
  displayType: 'Title',
  headerSpacing: 1,
  highlightBeatsMisses: true,
  highlightDollarAmounts: true,
  highlightPercentages: true,
  highlightQuarters: true,
  textSize: 14,
  themes: [
    {
      category: 'category',
      hue: 360,
      target: '165347',
    },
    {
      category: 'category',
      hue: 360,
      target: '145889',
    },
    {
      category: 'category',
      hue: 360,
      target: '38925',
    },
  ],
};

export interface NewsfeedGlobalLayoutSettingsV1 {
  allCaps: boolean;
  disableResponsiveDisplay: boolean;
  displayDateBanner: boolean;
  displayOrOperatorInExpression: boolean;
  displayType: DisplayType;
  headerSpacing: number;
  highlightBeatsMisses: boolean;
  highlightDollarAmounts: boolean;
  highlightPercentages: boolean;
  highlightQuarters: boolean;
  textSize: number;
  themes: StoryColorTheme[];
}

export type ThemeCategory = 'category' | 'source' | 'sourceGroups' | 'watchlist';

export type Hue = number;
export interface StoryColorTheme {
  category: ThemeCategory | null;
  hue: Hue;
  target: string | null;
}

export type NewsfeedGlobalLayoutSettings = NewsfeedGlobalLayoutSettingsV1;

interface AdvancedNewsfeedWidgetIterationV12 {
  widgetParameters: {
    desktopNotificationsEnabled: boolean;
    isPopout?: boolean;
    flightMode?: boolean;
    config: NewsConfigV3;
  };
  widgetGlobalSettings: NewsfeedGlobalLayoutSettingsV1;
  version: 12;
}

interface AdvancedNewsfeedWidgetIterationV11 {
  widgetParameters: {
    desktopNotificationsEnabled: boolean;
    isPopout?: boolean;
    flightMode?: boolean;
    config: NewsConfigV2;
  };
  widgetGlobalSettings: NewsfeedGlobalLayoutSettingsV1;
  version: 11;
}
interface AdvancedNewsfeedWidgetIterationV10 {
  widgetParameters: {
    desktopNotificationsEnabled: boolean;
    isPopout?: boolean;
    flightMode?: boolean;
    config: NewsConfigV1;
  };
  widgetGlobalSettings: NewsfeedGlobalLayoutSettingsV1;
  version: 10;
}

type NewsfeedFeedSettingsV9 = Omit<NewsfeedFeedSettingsV8, 'relevantCategories'> & {
  relevantCategories: Partial<Record<SourceId, CategoryId[]>>;
};

export interface AdvancedNewsfeedWidgetIterationV9 {
  widgetParameters: {
    desktopNotificationsEnabled: boolean;
    displayType?: DisplayType;
    feedExpression: ExpressionObject<NewsfeedFieldType>;
    feedSettings: NewsfeedFeedSettingsV9;
    isPopout?: boolean;
    flightMode?: boolean;
    sendGroup: WidgetLinkingID;
  };
  widgetGlobalSettings: NewsfeedGlobalLayoutSettingsV1;
  version: 9;
}

type NewsfeedFeedSettingsV8 = NewsfeedFeedSettingsV5 & {
  sourceGroups: SourceId[];
};

export interface AdvancedNewsfeedWidgetIterationV8 {
  widgetParameters: {
    desktopNotificationsEnabled: boolean;
    displayType?: DisplayType;
    feedExpression: ExpressionObject<NewsfeedFieldType>;
    feedSettings: NewsfeedFeedSettingsV8;
    isPopout?: boolean;
    flightMode?: boolean;
    sendGroup: WidgetLinkingID;
  };
  widgetGlobalSettings: NewsfeedGlobalLayoutSettingsV1;
  version: 8;
}

type AdvancedNewsfeedWidgetIterationV7 = Omit<AdvancedNewsfeedWidgetIterationV6, 'version'> & { version: 7 };
type AdvancedNewsfeedWidgetIterationV6 = Omit<AdvancedNewsfeedWidgetIterationV5, 'version'> & { version: 6 };

type NewsfeedFeedSettingsV5 = Omit<NewsfeedFeedSettingsV4, 'importance'> & {
  importance: Importance;
};

export interface AdvancedNewsfeedWidgetIterationV5 {
  widgetParameters: {
    desktopNotificationsEnabled: boolean;
    displayType?: DisplayType;
    feedExpression: ExpressionObject<NewsfeedFieldType>;
    feedSettings: NewsfeedFeedSettingsV5;
    isPopout?: boolean;
    flightMode?: boolean;
    sendGroup: WidgetLinkingID;
  };
  widgetGlobalSettings: NewsfeedGlobalLayoutSettingsV1;
  version: 5;
}

interface NewsfeedFeedSettingsV4 {
  // Deprecated. It may be removed in future release.
  categories: CategoryId[];
  importance: 'off' | 'low' | 'mid' | 'high';
  keywords: string[];
  relevantCategories: CategoryId[];
  screenerFilters: LegacyFilter[];
  sources: SourceId[];
  symbols: StockSymbol[];
  watchlists: WatchlistId[];
  createdAt: [string | null, string | null];
}

export interface AdvancedNewsfeedWidgetIterationV4 {
  widgetParameters: {
    desktopNotificationsEnabled: boolean;
    displayType?: DisplayType;
    feedExpression: ExpressionObject<NewsfeedFieldType>;
    feedSettings: NewsfeedFeedSettingsV4;
    isPopout?: boolean;
    flightMode?: boolean;
    sendGroup: WidgetLinkingID;
  };
  widgetGlobalSettings: NewsfeedGlobalLayoutSettingsV1;
  version: 4;
}

export const getBlankFiltersV4 = (initialFilters?: Partial<NewsfeedFeedSettingsV4>): NewsfeedFeedSettingsV4 => ({
  categories: initialFilters?.categories ?? [],
  createdAt: initialFilters?.createdAt ?? [null, null],
  importance: initialFilters?.importance ?? 'off',
  keywords: initialFilters?.keywords ?? [],
  relevantCategories: initialFilters?.relevantCategories ?? [],
  screenerFilters: initialFilters?.screenerFilters ?? [],
  sources: initialFilters?.sources ?? [],
  symbols: initialFilters?.symbols ?? [],
  watchlists: initialFilters?.watchlists ?? [],
});

export interface AdvancedNewsfeedWidgetIterationV3 {
  widgetParameters: {
    desktopNotificationsEnabled: boolean;
    displayType?: DisplayType;
    feedExpression: ExpressionObject<NewsfeedFieldType>;
    feedSettings: NewsfeedFeedSettingsV4;
    isPopout?: boolean;
    flightMode?: boolean;
  };
  widgetGlobalSettings: NewsfeedGlobalLayoutSettingsV1;
  version: 3;
}

export interface AdvancedNewsfeedWidgetIterationV2 {
  widgetParameters: {
    desktopNotificationsEnabled: boolean;
    displayType?: DisplayType;
    feedExpression: ExpressionObject<NewsfeedFieldType>;
    feedSettings: NewsfeedFeedSettingsV4;
    filterExpression: ExpressionObject<NewsfeedFieldType>;
    isPopout?: boolean;
  };
  widgetGlobalSettings: NewsfeedGlobalLayoutSettingsV1;
  version: 2;
}

export interface AdvancedNewsfeedWidgetIterationV1 {
  widgetParameters: {
    desktopNotificationsEnabled: boolean;
    displayType?: DisplayType;
    expression?: ExpressionObject<NewsfeedFieldType> | null;
    filters?: NewsfeedFeedSettingsV4;
    isPopout?: boolean;
  };
  widgetGlobalSettings: NewsfeedGlobalLayoutSettingsV1;
  version: 1;
}
