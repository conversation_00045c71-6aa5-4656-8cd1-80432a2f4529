'use client';
import { GridOptions, ColGroupDef, CellClickedEvent, GridApi, ColumnResizedEvent } from '@ag-grid-community/core';
import {
  useLayoutFill,
  useQuoteColDef,
  AdvancedColDef,
  BenzingaGrid,
  SendLinkContext,
  useColGroup,
  NoResults,
} from '@benzinga/pro-ui';
import { DefaultTableParameters, TableParameters } from '@benzinga/ag-grid-utils';
import { ScannerProtos, QuoteProtos } from '@benzinga/scanner-manager';
import React, { useRef, useContext, startTransition, useEffect } from 'react';
import CustomLoadingOverlay from './overlays/CustomLoadingOverlay';
import { SessionContext } from '@benzinga/session-context';
import { useScannerFeedContext } from './ScannerContext';
import Hooks from '@benzinga/hooks';
import { LoggingManager } from '@benzinga/session';
import { useWidgetParameters } from '@benzinga/widget-tools';
import { ScannerWidgetManifest } from './widget';
import { arrayDeepEqual } from '@benzinga/utils';
import { DateTime } from 'luxon';
import { TimeConfig, useTime } from '@benzinga/time-manager-hooks';
import styled, { css } from '@benzinga/themetron';

interface Props {
  paused: boolean;
  onDataFieldClicked: (dataFieldName: string, instrument: QuoteProtos.Quote) => void;
  isScreener?: boolean;
}

export const ScannerGrid: React.FC<Props> = props => {
  const session = useContext(SessionContext);
  const latestTimeConfig = useTime();
  const gridApi = useRef<GridApi | undefined>(undefined);
  const ref = useRef<HTMLDivElement>(null);
  const propsRef = React.useRef(props);
  const { parameters, setter } = useWidgetParameters(ScannerWidgetManifest);
  const feed = useScannerFeedContext();

  propsRef.current = props;

  const exportParams = React.useMemo(
    () => ({
      filename: 'Scanner',
    }),
    [],
  );

  const handleGridLayoutChanged = Hooks.useDebounce((tableParameters: Partial<TableParameters>) => {
    setter.config(old => ({
      ...old,
      tableParameters: {
        ...old.tableParameters,
        ...tableParameters,
      },
    }));
  }, 500);

  const gridOptions: GridOptions = React.useMemo(
    () => ({
      animateRows: true,
      defaultColDef: {},
      enableRangeSelection: true,
      headerHeight: 30,
      loadingOverlayComponent: CustomLoadingOverlay,
      loadingOverlayComponentParams: { loadingMessage: 'Waiting for Real-Time Data...' },
      noRowsOverlayComponent: () => (
        <NoResults
          style={{
            left: '5%',
            position: 'absolute',
            top: '15%',
          }}
        />
      ),
      onCellClicked: (event: CellClickedEvent) => {
        const dataFieldName = event.colDef.field as string;
        propsRef.current.onDataFieldClicked(dataFieldName, event.data as QuoteProtos.Quote);
      },
      onGridReady: params => {
        gridApi.current = params.api;
        gridApi.current.setRowData(feed.getPrevious());
      },
      rowHeight: 20,
      sideBar: {
        toolPanels: [
          {
            iconKey: 'columns',
            id: 'columns',
            labelDefault: 'Columns',
            labelKey: 'columns',
            toolPanel: 'agColumnsToolPanel',
            toolPanelParams: {
              suppressPivotMode: true,
              suppressPivots: true,
              suppressRowGroups: true,
              suppressValues: true,
            },
          },
        ],
      },
      suppressClickEdit: true,
      suppressContextMenu: false,
      suppressMenuHide: false,
    }),
    [feed],
  );

  const setSortField = React.useCallback(
    (sortField: string, sortDir: ScannerProtos.SortDir) => {
      session.getManager(LoggingManager).log(
        'debug',
        {
          category: 'scanner',
          data: { sortDir, sortField },
          message: 'loading config from on-ready',
        },
        ['console'],
      );
      setter.config(old => ({ ...old, sortDir, sortField }));
    },
    [session, setter],
  );

  const applyCellUpdates = React.useCallback(
    (update: ScannerProtos.IRowUpdate) => {
      const row = gridApi.current?.getRowNode(update.rowId as string);
      if (!row) {
        session.getManager(LoggingManager).log(
          'info',
          {
            category: 'scanner',
            message: `applyCellUpdates() No row found where id=${update.rowId}`,
          },
          ['console'],
        );
        return;
      }
      const updated = update.updated as QuoteProtos.Quote;
      Object.keys(updated).forEach(key => {
        const newValue = updated[key as keyof QuoteProtos.IQuote];
        try {
          const currentValue = row.data[key];
          row.data[key] = newValue;

          // Process notifications for cell update highlights
          if (key === 'price') {
            highlightCellPrice(update.rowId as string, key, currentValue, newValue, ref);
          } else if (
            !parameters.stopColumnHighlighting &&
            [
              'tradeCount',
              'regularHighCount',
              'regularLowCount',
              'preMarketHighCount',
              'preMarketLowCount',
              'afterMarketHighCount',
              'afterMarketLowCount',
              'dayHighCount',
              'dayLowCount',
              'priceSpikeUpCount',
              'priceSpikeDownCount',
              'haltedCount',
            ].includes(key)
          ) {
            highlightCellCount(update.rowId as string, key, currentValue, newValue, ref);
          }
        } catch (e) {
          session.getManager(LoggingManager).log(
            'warn',
            {
              category: 'scanner',
              data: e,
              message: `Failed to set value, key=${key}, value=${newValue}`,
            },
            ['console'],
          );
        }
      });
    },
    [parameters.stopColumnHighlighting, session],
  );

  React.useEffect(() => {
    if (gridApi.current && !props.paused) {
      gridApi.current.setRowData(feed.getPrevious());
    }
  }, [feed, props.paused]);
  const formatRowData = React.useCallback(
    (quotes: QuoteProtos.IQuote[]) => {
      return quotes.map(quote => {
        const formattedQuote: Record<string, any> = {};
        for (const key in quote) {
          if (key.toLowerCase().includes('time')) {
            const rawValue = quote[key];
            if (rawValue) {
              const dateTime = DateTime.fromFormat(String(rawValue), 'yyyyMMddHHmmss', { zone: 'America/New_York' });
              if (dateTime.isValid) {
                const localDateTime = dateTime.setZone(latestTimeConfig.timezone);
                formattedQuote[key] =
                  latestTimeConfig.timeFormat === 'AM/PM'
                    ? localDateTime.toFormat('MMM d, yyyy h:mm a')
                    : localDateTime.toFormat('MMM d, yyyy HH:mm');
                continue;
              }
            }
          }
          formattedQuote[key] = quote[key];
        }
        return formattedQuote;
      });
    },
    [latestTimeConfig.timeFormat, latestTimeConfig.timezone],
  );

  const timeConfigRef = useRef<TimeConfig | null>(latestTimeConfig);

  Hooks.useSubscriber(props.paused ? undefined : feed, event => {
    switch (event.type) {
      case 'data_update': {
        timeConfigRef.current = latestTimeConfig;
        event.transactions.updates?.forEach(update => applyCellUpdates(update));
        startTransition(() => {
          gridApi.current?.setRowData(formatRowData(event.rows));
        });
        break;
      }
    }
  });

  const gridLayout = React.useMemo(() => {
    return {
      ...DefaultTableParameters,
      ...parameters.config.tableParameters,
      columns: parameters.config.tableParameters.columns.map(col => ({
        ...col,
        hide: col.hide ?? false,
      })),
    };
  }, [parameters.config.tableParameters]);

  useEffect(() => {
    const columnState = gridApi?.current?.getColumnState();
    if (columnState?.length && !arrayDeepEqual(gridLayout.columns, columnState)) {
      setTimeout(() => {
        gridApi.current?.applyColumnState({
          applyOrder: true,
          defaultState: { ...gridLayout.defaultState, width: 100 },
          state: gridLayout.columns,
        });
      }, 1);
    }
  }, [gridLayout]);

  const sendLink = React.useContext(SendLinkContext);
  const handleRefreshTrigger = React.useCallback(
    (shouldTrigger: boolean) => {
      if (shouldTrigger) gridApi?.current?.setRowData(formatRowData(feed.getPrevious()));
    },
    [feed, formatRowData],
  );

  const columnLayout = useLayoutFill(gridLayout.columns);
  const memoizedColumnLayout = React.useMemo(() => columnLayout, [columnLayout]);

  const columnDefs = useUpdateSort(
    useQuoteColDef(memoizedColumnLayout, handleRefreshTrigger),
    parameters.config.sortField,
    parameters.config.sortDir,
    setSortField,
  );

  const { columnDefs: colGroupDefs } = useColGroup({
    columnDefs,
    columnHeaderGrouping: parameters.columnHeaderGrouping,
  });

  const handleColumnResized = (event: ColumnResizedEvent) => {
    startTransition(() => gridApi.current?.refreshCells({ columns: event?.columns ?? [] }));
  };

  return (
    <Div isScreener={props.isScreener} ref={ref}>
      <BenzingaGrid
        columnDefs={colGroupDefs}
        exportParams={exportParams}
        getRowId={props => props.data.rowId ?? props.data.symbol}
        gridLayout={gridLayout}
        gridOptions={gridOptions}
        onColumnResized={handleColumnResized}
        onGridLayoutChanged={handleGridLayoutChanged}
        onSymbolClick={sendLink.onSymbolClick}
        rowBuffer={0}
        suppressHeaderFocus={true}
        suppressRowHoverHighlight={true}
        symbolColIDs={['symbol']}
      />
    </Div>
  );
};

export default ScannerGrid;

/**
 * Called when a cell needs to be highlighed
 */
function highlightCellPrice(
  symbol: string,
  fieldName: string,
  oldValue: unknown,
  newValue: unknown,
  ref: React.RefObject<HTMLDivElement | null>,
) {
  if (typeof newValue != 'number' || typeof oldValue != 'number') {
    return;
  }
  const cls = newValue > oldValue ? 'u-upBg' : 'u-downBg';
  const sel = `[row-id="${symbol}"] [col-id="${fieldName}"]`;
  const el = ref.current?.querySelector(sel);
  if (el && ref) {
    //el.classList.remove(cls); // Only needed for the flash animation I think (uses tons of cpu)
    el.classList.add(cls);
    setTimeout(() => el.classList.remove(cls), 500);
  } else {
    // Don't warn, this can happen because ag-grid removes elements that aren't visible
    // console.warn('No element found for query', sel);
  }
}
function highlightCellCount(
  symbol: string,
  fieldName: string,
  oldValue: unknown,
  newValue: unknown,
  ref: React.RefObject<HTMLDivElement | null>,
) {
  const cls = 'u-accentBorder';
  const sel = `[row-id="${symbol}"] [col-id="${fieldName}"]`;
  const el = ref.current?.querySelector(sel);
  if (el && ref && oldValue !== newValue) {
    //el.classList.remove(cls); // Only needed for the flash animation I think (uses tons of cpu)
    el.classList.add(cls);
    setTimeout(() => el.classList.remove(cls), 500);
  } else {
    // Don't warn, this can happen because ag-grid removes elements that aren't visible
    // console.warn('No element found for query', sel);
  }
}

const useUpdateSort = (
  columnDefs: (ColGroupDef | AdvancedColDef)[],
  sortFieldName: string | undefined,
  sortDir: ScannerProtos.SortDir | undefined,
  setSortField: (sortField: string, sortDir: ScannerProtos.SortDir) => void,
): AdvancedColDef[] => {
  const [defs] = React.useState(() => {
    const update = (columnDefs: AdvancedColDef[]) =>
      columnDefs?.map(column => {
        if ((column as ColGroupDef).children) {
          return {
            ...column,
            children: update((column as ColGroupDef).children),
          };
        } else {
          return {
            ...column,
            advancedSort: {
              ...column.advancedSort,
              localSort: false,
              onSetSort: sort =>
                setSortField(
                  column.field ?? '',
                  sort === 'asc'
                    ? ScannerProtos.SortDir.ASC
                    : sort === 'desc'
                      ? ScannerProtos.SortDir.DESC
                      : sort === 'desc-abs'
                        ? ScannerProtos.SortDir.DESC_ABS
                        : ScannerProtos.SortDir.NONE,
                ),
              val:
                sortFieldName !== column.field
                  ? null
                  : sortDir === ScannerProtos.SortDir.ASC
                    ? ('asc' as const)
                    : sortDir === ScannerProtos.SortDir.DESC
                      ? ('desc' as const)
                      : sortDir === ScannerProtos.SortDir.DESC_ABS && column.advancedSort?.absSort
                        ? ('desc-abs' as const)
                        : null,
            } as AdvancedColDef['advancedSort'],
          };
        }
      });
    return update(columnDefs);
  });
  return defs;
};

const Div = styled.div<{ isScreener?: boolean }>`
  ${props =>
    props.isScreener
      ? css`
          width: 100% !important;
        `
      : css``}
  height: 100%
`;
