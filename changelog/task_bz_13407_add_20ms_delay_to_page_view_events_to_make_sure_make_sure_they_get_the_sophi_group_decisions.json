{"affectedProjects": ["bz", "@benzinga/root", "bz-e2e", "bz-mobile", "data-manager-alts", "data-manager-article", "india", "money", "pro", "pro-e2e", "proto", "react-utils-data-hooks-content-manager", "ui-ads", "ui-alternative-investments", "ui-article", "ui-auth", "ui-blocks", "ui-bz-onboarding", "ui-calendars", "ui-charts", "ui-crypto", "ui-entity", "ui-forms", "ui-money", "ui-navigation", "ui-news", "ui-product", "ui-quotes", "ui-table", "ui-templates", "ui-ticker", "ui-trade-ideas", "ui-ui", "ui-widgets", "utils-analytics", "visualization-iqchart", "widget-pro-bz-chart", "widget-pro-details", "widget-pro-newsfeed", "widget-pro-notification", "widget-scanner", "widget-ticker-finder"], "description": "add 20ms delay to page view events to make sure make sure they get the sophi group decisions", "epic": null, "issueNumber": "13407", "project": "BZ", "projects": ["@benzinga/root", "bz-e2e", "bz-mobile", "data-manager-alts", "data-manager-article", "india", "money", "pro", "pro-e2e", "proto", "react-utils-data-hooks-content-manager", "ui-ads", "ui-alternative-investments", "ui-article", "ui-auth", "ui-blocks", "ui-bz-onboarding", "ui-calendars", "ui-charts", "ui-crypto", "ui-entity", "ui-forms", "ui-money", "ui-navigation", "ui-news", "ui-product", "ui-quotes", "ui-table", "ui-templates", "ui-ticker", "ui-trade-ideas", "ui-ui", "ui-widgets", "utils-analytics", "visualization-iqchart", "widget-pro-bz-chart", "widget-pro-details", "widget-pro-newsfeed", "widget-pro-notification", "widget-scanner", "widget-ticker-finder"], "type": "task", "updatedAt": "2025-07-02T04:42:56.574Z"}