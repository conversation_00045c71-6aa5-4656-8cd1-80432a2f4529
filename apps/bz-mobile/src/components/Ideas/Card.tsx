import React, { useState, useCallback } from 'react';
import { View, StyleSheet, Text, Share, StyleProp, ViewStyle, Alert } from 'react-native';
import moment from 'moment';
import { truncate } from 'lodash';
import { default as MaterialCommunityIcons } from 'react-native-vector-icons/MaterialCommunityIcons';

import { colors } from '../../screens/Ideas/colors';
import { WP } from '../../services/responsive';
import { size } from '../../services/fontSize';
import { Tag } from './Tag';

import BenzingaHTML from '../../components/BenzingaHTML';
import ArticleView from '../../components/Article/ArticleView';

import UserData from '../../services/app';
import { useTheme } from '../../theme/themecontext';
import Icon, { IconTypes } from '../Icon/Icon';
import { TradeIdea } from '@benzinga/trade-ideas-manager';
import { StackNavigationProp } from '@react-navigation/stack';
import { TradeIdeasNavigationStackParamList } from '../../navigation/TradeIdeasNavigationStack';
import { AppNavigationStackParamList } from '../../app/App';
import { News } from '@benzinga/advanced-news-manager';
import CardHeader from './CardHeader';
import Data from '../../data-mobile/data';
import CustomPressable from '../CustomPressable';

interface IdeaCardProps {
  showNode?: boolean;
  containerStyle?: StyleProp<ViewStyle>;
  fromTradeIdeaScreen?: boolean;
  idea: TradeIdea;
  navigation: StackNavigationProp<AppNavigationStackParamList & TradeIdeasNavigationStackParamList>;
  blockUser?: (item: TradeIdea) => void;
  reportAbuse?: (item: TradeIdea) => void;
  reloadTradeIdeas?: () => void;
  setTradeIdeaDeleting?: (arg: boolean) => void;
}

export const IdeaCard = ({
  blockUser,
  containerStyle,
  fromTradeIdeaScreen = false,
  idea,
  navigation,
  reloadTradeIdeas,
  reportAbuse,
  setTradeIdeaDeleting,
  showNode = true,
}: IdeaCardProps) => {
  // const [modalVisible, setModalVisible] = useState(false)
  const { colors } = useTheme();
  // const [voteValue, setVoteValue] = useState('');
  const [upVotes, setUpVotes] = useState(idea.upvotes);
  const [downVotes, setDownVotes] = useState(idea.downvotes);
  const reason = idea?.reason;
  const matches = reason
    ? reason.match(
        /(https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,})/g,
      )
    : [];
  let html = idea.duration ? `<p><b>Why:</b> ${reason}</p>` : `<p>${reason}</p>`;
  matches?.forEach(match => {
    if (match.match(/\.(png|jpeg|jpg)/)) {
      html = html.replace(match, `<img style="max-height: 400px;" src="${match}" />`);
    } else {
      html = html.replace(
        match,
        `<br/><a style="color: #21a7fd;" href="${match}" target="_blank" rel="nofollow">${match}</a><br/><br/>`,
      );
    }
  });

  const goToSpecificUserIdeas = (traderID: number, traderName: string) => {
    //  console.log(traderID, traderName)
    global.Analytics.event('Navigation', 'Show Trader', `Post(${idea.id})`);
    navigation.navigate('UserTradeIdeas', {
      traderID,
      traderName,
    });
  };

  const formatMessage = () => {
    let text = '';

    const link = `https://www.benzinga.com/trade-ideas/${idea.id}`;
    const name = idea.trader.name;
    const reason = truncate(idea.reason, { length: 50 });
    const direction = idea.position;
    const symbol = idea.tickers;
    const user = UserData.user();

    if (user && idea.trader.benzingaUID === user.benzingaUid) {
      if (direction && symbol) {
        text = `${direction} ${symbol}, "${reason}", read the full idea here: ${link}`;
      } else if (!direction?.length && symbol) {
        text = `My thoughts on ${symbol}, "${reason}", read the full idea here: ${link}`;
      } else if (!direction?.length && !symbol?.length) {
        text = `Check out my post on Benzinga: "${reason}", read more here: ${link}`;
      }
      return text;
    }

    if (direction && symbol) {
      text = `${name} is ${direction} ${symbol}, "${reason}", read the full idea here: ${link}`;
    } else if (!direction && symbol) {
      text = `${name}'s thoughts on ${symbol}, "${reason}", read more here: ${link}`;
    } else if (!direction?.length && !symbol?.length) {
      text = `${name} posted on Benzinga: "${reason}", read more here: ${link}`;
    }

    return text;
  };

  const share = (idea: TradeIdea) => {
    global.Analytics.event('Share', 'Share Post', `Post(${idea.id})`);
    global.Analytics.share(`Ideas Share Post(${idea.id})`);
    Share.share({ message: formatMessage() }, { subject: 'Share Trade Idea' })
      .then(result => console.log(result))
      .catch(errorMsg => console.log(errorMsg));
  };

  const traderDetailsComponent = () => {
    return (
      <View style={styles.header}>
        <View style={[styles.profileNIContainer, { flex: 3 }]}>
          <CustomPressable onPress={() => goToSpecificUserIdeas(idea?.trader?.id, idea?.trader?.name)}>
            <Text numberOfLines={1} style={[styles.name, { color: colors.text }]}>
              {idea?.trader?.name}
            </Text>
          </CustomPressable>
          {idea.tags.length ? (
            <Text
              style={[
                styles.name,
                {
                  color: colors.borderBlue,
                  borderColor: colors.borderBlue,
                },
                styles.tags,
              ]}
            >
              {idea.tags[0].toUpperCase()}
            </Text>
          ) : null}
        </View>
        <View style={styles.dateContainer}>
          <Text style={styles.date}>
            {idea?.createdAt !== null
              ? moment(idea?.createdAt?.replace(' UTC', 'Z').replace(' ', 'T')).format('MMM D, YY - h:mmA')
              : ''}
          </Text>
        </View>
      </View>
    );
  };

  const _footer = () => {
    const user = UserData.user();

    return (
      <View
        style={{
          flexDirection: 'row',
          padding: 12,
          paddingBottom: 4,
        }}
      >
        <View style={styles.footerBtnContainer}>
          <CustomPressable
            activeOpacity={0.5}
            onPress={() => onCommentPress(idea)}
            style={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
            }}
          >
            {idea.comments?.length ? (
              <Text
                style={{
                  fontSize: 14,
                  color: 'gray',
                  marginRight: 4,
                  fontWeight: 'bold',
                }}
              >
                {idea.comments.length}
              </Text>
            ) : null}
            <Icon type={IconTypes.MaterialIcons} color={'gray'} name="messenger-outline" size={18} />
          </CustomPressable>
        </View>
        <View style={styles.footerBtnContainer}>
          <CustomPressable activeOpacity={0.5} onPress={() => share(idea)} style={{ marginLeft: 8 }}>
            <Icon type={IconTypes.MaterialCommunityIcons} name={'share-outline'} size={20} color={'gray'} />
          </CustomPressable>
        </View>
        <View style={styles.footerBtnContainer}>
          <CustomPressable
            activeOpacity={0.5}
            onPress={() => reportAbuse && reportAbuse(idea)}
            style={{ marginLeft: 8 }}
          >
            <Icon type={IconTypes.Feather} name={'flag'} size={20} color={'gray'} />
          </CustomPressable>
        </View>
        {_renderBlock(idea)}
        {idea.trader.benzingaUID === user?.benzingaUid && (
          <CustomPressable activeOpacity={0.5} onPress={() => onDeletePress(idea.id)} style={styles.footerBtnContainer}>
            <Icon type={IconTypes.AntDesign} name={'delete'} size={20} color={colors.red} />
          </CustomPressable>
        )}
      </View>
    );
  };

  const onDeletePress = (id: number) => {
    Alert.alert('Delete Trade Idea', 'Are you sure you want to delete Trade Idea?', [
      {
        text: 'Cancel',
        onPress: () => console.log('Cancel pressed'),
      },
      {
        text: 'OK',
        onPress: () => {
          setTradeIdeaDeleting && setTradeIdeaDeleting(true);
          Data.tradeIdeas()
            .deleteTradeIdea(id)
            .then(res => {
              reloadTradeIdeas && reloadTradeIdeas();
              if (res.ok) {
                if (fromTradeIdeaScreen) {
                  navigation.goBack();
                }
              }
              setTradeIdeaDeleting && setTradeIdeaDeleting(false);
            });
        },
      },
    ]);
  };

  const _renderBlock = (idea: TradeIdea) => {
    const reportItem = (idea: TradeIdea) => {
      if (blockUser) {
        blockUser(idea);
      }
    };

    return (
      <View style={{ flexDirection: 'row-reverse', flex: 1 }}>
        <CustomPressable onPress={() => reportItem(idea)}>
          <Text numberOfLines={1} style={styles.report}>
            Block User
          </Text>
        </CustomPressable>
      </View>
    );
  };

  const _renderTags = () => {
    return idea?.action ? (
      <Tag
        backgroundColor={colors.white}
        borderColor={'rgba(114, 134, 190, 0.3)'}
        containerStyle={styles.marginLeft}
        leftIcon={<MaterialCommunityIcons color={'rgba(114, 134, 190, 1)'} name="message-text-outline" size={12} />}
        rightIcon={null}
        rightNumber={idea?.allocation}
        title={idea?.action}
        titleColor={'rgba(114, 130, 190, 1)'}
      />
    ) : (
      <View />
    );
  };

  const onQuotePress = (symbol: string) => {
    console.log('PRESSED QUOTE: ', symbol);

    global.Analytics.event('Navigation', 'Show Quote', `Post(${idea.id})`);

    const _symbol = symbol.split('/USD')[0];
    if (symbol.includes('/USD')) {
      navigation.navigate('CryptoQuote', {
        symbol: _symbol,
      });
    } else {
      navigation.navigate('Quote', {
        symbol: _symbol,
      });
    }
  };

  const onCommentPress = (idea: TradeIdea) => {
    global.Analytics.event('Navigation', 'Show Comments', `Post(${idea.id})`);

    navigation.navigate('TradeIdea', {
      tradeIdeaId: idea?.id,
      tradeIdea: idea,
      reloadTradeIdeas: reloadTradeIdeas,
    });
  };

  const showArticle = useCallback(
    (article: News) => {
      global.Analytics.event('Navigation', 'Show Article', `Post(${idea.id})`);

      navigation.navigate('Article', {
        article: article,
        screen: 'Article',
      });
    },
    [navigation],
  );

  const votes = async (currentVote: 'upvote' | 'downvote') => {
    await Data.tradeIdeas()
      .vote(idea, currentVote)
      .then(res => {
        if (res.ok) {
          setDownVotes(res.ok.counts.downvotes);
          setUpVotes(res.ok.counts.upvotes);
          // setVoteValue(currentVote);
        }
        console.log(`${currentVote} Res: `, res);
      })
      .catch(err => console.log('Votes Error: ', err));
  };

  const renderVotes = () => {
    // const downVoteColor = (vote: string) => (vote === 'downvote' ? colors.disabledGrey : colors.linkText);
    // const upVoteColor = (vote: string) => (vote === 'upvote' ? colors.disabledGrey : colors.linkText);

    return (
      <View style={styles.votesContainer}>
        <CustomPressable
          onPress={() => {
            votes('upvote');
          }}
          // disabled={voteValue === 'upvote'}
          style={styles.voteIconContainer}
        >
          <Icon type={IconTypes.AntDesign} name="arrowup" size={18} color={colors.linkText} />
          <Text style={[styles.votesNumber, { color: colors.linkText }]}>{upVotes}</Text>
        </CustomPressable>
        <CustomPressable
          onPress={() => {
            votes('downvote');
          }}
          // disabled={voteValue === 'downvote'}
          style={styles.voteIconContainer}
        >
          <Icon type={IconTypes.AntDesign} name="arrowdown" size={18} color={colors.linkText} />
          <Text style={[styles.votesNumber, { color: colors.linkText }]}>{downVotes}</Text>
        </CustomPressable>
      </View>
    );
  };

  const renderOptionals = () => {
    return (
      <>
        {idea.duration ? (
          <Text style={{ color: colors.text, fontSize: 18, marginVertical: 5 }}>
            <Text style={{ fontWeight: 'bold' }}>Duration: </Text>
            {`'${idea.duration}' ${idea.durationText ? idea.durationText : ''}`}
          </Text>
        ) : null}
        {idea.risk && (
          <Text style={{ color: colors.text, fontSize: 18, marginVertical: 5 }}>
            <Text style={{ fontWeight: 'bold' }}>Risk: </Text>
            {`${idea.risk}`}
          </Text>
        )}
        {renderVotes()}
      </>
    );
  };

  // const closeModal = useCallback(() => {
  //     setModalVisible(false);
  // }, [setModalVisible, modalVisible])

  return (
    <View
      style={[
        styles.container,
        containerStyle,
        {
          backgroundColor: colors.newCard,
          borderColor: colors.border,
          borderRadius: 5,
        },
      ]}
    >
      <View style={styles.tagContainer}>
        {Array.isArray(idea.tickers) ? (
          idea.tickers.map((ticker, index) => {
            return <CardHeader idea={idea} onQuotePress={onQuotePress} ticker={ticker} key={`idea-${index}`} />;
          })
        ) : (
          <CardHeader idea={idea} onQuotePress={onQuotePress} ticker={idea.tickers} />
        )}
        {_renderTags()}
      </View>
      {html ? <BenzingaHTML html={html} style={styles.reasonText} /> : null}
      {idea.node && showNode ? (
        <View
          style={{
            borderColor: colors.border,
            borderWidth: 1,
            borderRadius: 4,
            paddingHorizontal: 8,
            paddingVertical: 4,
            marginTop: 8,
          }}
        >
          <ArticleView article={idea.node} onPressArticle={showArticle} />
        </View>
      ) : null}
      {traderDetailsComponent()}
      <View
        style={{
          borderWidth: 0.5,
          borderTopColor: colors.border,
          marginHorizontal: -WP('3'),
          borderBottomColor: colors.card,
          marginTop: 16,
        }}
      />
      {renderOptionals()}
      {_footer()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: WP('3'),
    paddingBottom: WP('2'),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: WP('3'),
  },
  profileNIContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  name: {
    fontWeight: 'bold',
    fontSize: size.small,
  },
  tags: {
    marginHorizontal: 8,
    padding: WP('1'),
    borderWidth: 2,
    textAlign: 'center',
  },
  dateContainer: {
    flexDirection: 'row',
    flex: 2,
    justifyContent: 'flex-end',
  },
  date: {
    color: colors.grey1,
    marginLeft: WP('2'),
    fontSize: size.xxsmall,
  },
  footerBtnContainer: {
    marginRight: 8,
  },
  report: {
    color: colors.grey1,
    fontSize: size.xxsmall,
    paddingHorizontal: 4,
  },
  tagContainer: {
    marginVertical: WP('2'),
    flexDirection: 'row',
  },
  marginLeft: {
    marginLeft: WP('3'),
  },
  reasonText: {
    color: colors.black,
    paddingTop: 4,
    fontSize: size.small,
    fontWeight: '400',
  },
  quoteTitleStyle: {
    paddingVertical: 2,
    // paddingLeft: 3,
    fontSize: 16,
    marginLeft: 6,
    // borderColor: '#ccc',
    // borderWidth: 1,
    // borderRadius: 4
  },
  votesContainer: {
    flexDirection: 'row',
    marginHorizontal: 5,
    justifyContent: 'flex-end',
    marginVertical: 5,
  },
  votesNumber: {
    fontSize: 16,
    marginHorizontal: 5,
  },
  voteIconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});
