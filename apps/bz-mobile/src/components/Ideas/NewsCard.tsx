import React from 'react';
import { View } from 'react-native';
import ArticleView from '../../components/Article/ArticleView';
import BenzingaHTML from '../../components/BenzingaHTML';
import { borderStyles } from '../../constants/Styles';
import SeeAllButton from '../../components/EarningSeason/SeeAllButton';
import { useTheme } from '../../theme/themecontext';
import { TradeIdeaComment } from '../../data-mobile/tradeIdeas';
import { News } from '@benzinga/advanced-news-manager';

interface NewsCardProps {
  showArticle: (wiim: News) => void;
  topStory: { node: News; posts: TradeIdeaComment[] };
}

export const NewsCard = ({ showArticle, topStory }: NewsCardProps) => {
  const { colors, isDark } = useTheme();
  return (
    <View
      style={[
        borderStyles.horizontal,
        {
          backgroundColor: isDark ? colors.background : '#fff',
          paddingBottom: 16,
          borderBottomColor: colors.border,
          borderTopColor: colors.border,
        },
      ]}
    >
      <View style={{ marginVertical: 20 }}>
        <ArticleView
          article={topStory.node}
          onPressArticle={showArticle}
          showAuthor={true}
          showTeaser={true}
          style={{
            padding: 16,
          }}
        />
      </View>
      {topStory.posts?.map((post, p) => {
        // const html = `<b>${post.trader.name}</b> ${post.text}`
        //                 const  html = `
        // <p style='color:red;'>
        //   Hello World!
        // </p>`

        const html = `<b>${post.trader.name} </b> <p style='color:${isDark ? '#99AECC' : colors.text};'> ${
          post.text
        }</p>`;
        return (
          <View
            key={p}
            style={{
              display: 'flex',
              flexDirection: 'row',
              flexWrap: 'wrap',
              paddingHorizontal: 16,
              paddingBottom: 6,
            }}
          >
            <BenzingaHTML html={html} />
          </View>
        );
      })}
      <SeeAllButton
        onPress={() => showArticle(topStory.node)}
        style={{ marginHorizontal: 16, marginTop: 4 }}
        title="Read to Join the Conversation"
      />
    </View>
  );
};
