import React from 'react';
import { View, Text } from 'react-native';
import BenzingaHTML from '../components/BenzingaHTML';
import { useTheme } from '../theme/themecontext';
import { TradeIdeaComment } from '@benzinga/trade-ideas-manager';

export const CommentView = ({ comment }: { comment: TradeIdeaComment }) => {
  const { colors } = useTheme();

  const matches = comment.text
    ? comment.text.match(
        /(https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,})/g,
      )
    : [];
  let html = comment.text;
  matches?.forEach(match => {
    if (match.match(/\.(png|jpeg|jpg)/)) {
      html = html.replace(match, `<img style="max-height: 400px;" src="${match}" />`);
    } else {
      html = html.replace(
        match,
        `<a style="color: #21a7fd;" href="${match}" target="_blank" rel="nofollow">${match}</a>`,
      );
    }
  });

  return (
    <View style={{ padding: 8 }}>
      <Text
        style={{
          marginVertical: 4,
          fontWeight: 'bold',
          color: colors.text,
        }}
      >
        {comment.trader.name}
      </Text>
      <BenzingaHTML
        html={`<div>${html}</div>`}
        style={{
          paddingTop: 4,
          fontSize: 14,
          fontWeight: '400',
          color: colors.text,
        }}
      />
    </View>
  );
};
