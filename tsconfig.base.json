{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2023", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@benzinga/ads": ["libs/ui/ads/src/index.ts"], "@benzinga/ads-utils": ["libs/ui/ads-utils/src/index.ts"], "@benzinga/advanced-news-manager": ["libs/data/manager/advanced-news/src/index.ts"], "@benzinga/ag-grid-utils": ["libs/utils/ag-grid-utils/src/index.ts"], "@benzinga/alternative-investments": ["libs/ui/alternative-investments/src/index.ts"], "@benzinga/alts-manager": ["libs/data/manager/alts/src/index.ts"], "@benzinga/analyst-ratings-visualization": ["libs/visualization/analyst-ratings/src/index.ts"], "@benzinga/analytics": ["libs/utils/analytics/src/index.ts"], "@benzinga/article": ["libs/ui/article/src/index.ts"], "@benzinga/article-manager": ["libs/data/manager/article/src/index.ts"], "@benzinga/atomics": ["libs/ui/atomics/src/index.ts"], "@benzinga/auth": ["libs/utils/auth/src/index.ts"], "@benzinga/auth-ui": ["libs/ui/auth/src/index.ts"], "@benzinga/auto-translation": ["libs/third-party/tools/auto-translation/src/index.ts"], "@benzinga/autocomplete-manager": ["libs/data/manager/autocomplete/src/index.ts"], "@benzinga/bar-chart-visualization": ["libs/visualization/bar-chart/src/index.ts"], "@benzinga/basic-news-manager": ["libs/data/manager/basic-news/src/index.ts"], "@benzinga/benzinga-gpt": ["libs/data/manager/benzinga-gpt/src/index.ts"], "@benzinga/benzinga-gpt-search-manager": ["libs/data/manager/gpt-search/src/index.ts"], "@benzinga/benzinga-squawk-sdk": ["libs/data/squawk-sdk/src/index.ts"], "@benzinga/blocks": ["libs/ui/blocks/src/index.ts"], "@benzinga/blocks-utils": ["libs/utils/blocks/src/index.ts"], "@benzinga/bz-onboarding": ["libs/ui/bz-onboarding/src/index.ts"], "@benzinga/calculator": ["libs/ui/calculators/src/index.ts"], "@benzinga/calendar-commons": ["libs/data/manager/calendars/commons/src/index.ts"], "@benzinga/calendar-conference-calls-manager": ["libs/data/manager/calendars/conference-calls/src/index.ts"], "@benzinga/calendar-dividends-manager": ["libs/data/manager/calendars/dividends/src/index.ts"], "@benzinga/calendar-earnings-manager": ["libs/data/manager/calendars/earnings/src/index.ts"], "@benzinga/calendar-economics-manager": ["libs/data/manager/calendars/economics/src/index.ts"], "@benzinga/calendar-fda-manager": ["libs/data/manager/calendars/fda/src/index.ts"], "@benzinga/calendar-government-trades-manager": ["libs/data/manager/calendars/government-trades/src/index.ts"], "@benzinga/calendar-guidance-manager": ["libs/data/manager/calendars/guidance/src/index.ts"], "@benzinga/calendar-ipos-manager": ["libs/data/manager/calendars/ipos/src/index.ts"], "@benzinga/calendar-ma-manager": ["libs/data/manager/calendars/ma/src/index.ts"], "@benzinga/calendar-manager": ["libs/data/manager/calendar/src/index.ts"], "@benzinga/calendar-manager-hooks": ["libs/react-utils/data-hooks/calendar-manager/src/index.ts"], "@benzinga/calendar-offerings-manager": ["libs/data/manager/calendars/offerings/src/index.ts"], "@benzinga/calendar-option-activity-manager": ["libs/data/manager/calendars/option-activity/src/index.ts"], "@benzinga/calendar-ratings-manager": ["libs/data/manager/calendars/ratings/src/index.ts"], "@benzinga/calendar-rest-lite": ["libs/data/manager/calendar-rest-lite/src/index.ts"], "@benzinga/calendar-retail-manager": ["libs/data/manager/calendars/retail/src/index.ts"], "@benzinga/calendar-sec-manager": ["libs/data/manager/calendars/sec/src/index.ts"], "@benzinga/calendar-short-interest-manager": ["libs/data/manager/calendars/short-interest/src/index.ts"], "@benzinga/calendar-splits-manager": ["libs/data/manager/calendars/splits/src/index.ts"], "@benzinga/calendar-squawk-manager": ["libs/data/manager/calendars/squawk/src/index.ts"], "@benzinga/calendars": ["libs/ui/calendars/src/index.ts"], "@benzinga/chart-config-manager": ["libs/data/manager/chart-config/src/index.ts"], "@benzinga/chart-manager": ["libs/data/manager/chart/src/index.ts"], "@benzinga/chart-manager-hooks": ["libs/react-utils/data-hooks/chart-manager/src/index.ts"], "@benzinga/charts-ui": ["libs/ui/charts/src/index.ts"], "@benzinga/chartwidget": ["libs/widget/chartwidget/src/index.ts"], "@benzinga/chat-manager": ["libs/data/manager/chat/src/index.ts"], "@benzinga/comments-ui": ["libs/ui/comments/src/index.ts"], "@benzinga/commodities-manager": ["libs/data/manager/commodities/src/index.ts"], "@benzinga/containers": ["libs/utils/containers/src/index.ts"], "@benzinga/content-manager": ["libs/data/manager/content/src/index.ts"], "@benzinga/content-manager-hooks": ["libs/react-utils/data-hooks/content-manager/src/index.ts"], "@benzinga/coralogix-monitoring": ["libs/utils/monitoring/coralogix/src/index.ts"], "@benzinga/core-ui": ["libs/ui/core/src/index.ts"], "@benzinga/crypto": ["libs/ui/crypto/src/index.ts"], "@benzinga/crypto-manager": ["libs/data/manager/crypto/src/index.ts"], "@benzinga/crypto-manager-hooks": ["libs/react-utils/data-hooks/crypto-manager/src/index.ts"], "@benzinga/data-manager-holdings": ["libs/data/manager/holdings/src/index.ts"], "@benzinga/data-option-chain": ["libs/data/manager/option-chain/src/index.ts"], "@benzinga/datadog-monitoring": ["libs/utils/monitoring/datadog/src/index.ts"], "@benzinga/date-utils": ["libs/utils/date/src/index.ts"], "@benzinga/desktop-notification-manager": ["libs/utils/manager/desktop-notification/src/index.ts"], "@benzinga/device-utils": ["libs/utils/device/src/index.ts"], "@benzinga/edge": ["libs/ui/edge/src/index.ts"], "@benzinga/entity-ui": ["libs/ui/entity/src/index.ts"], "@benzinga/etf-manager": ["libs/data/manager/etfs/src/index.ts"], "@benzinga/etfs": ["libs/ui/etfs/src/index.ts"], "@benzinga/events-manager": ["libs/data/manager/events/src/index.ts"], "@benzinga/filter-ui": ["libs/ui/filter/src/index.ts"], "@benzinga/fission": ["libs/legacy/fission/src/index.ts"], "@benzinga/fontawesome-custom": ["libs/icons/fontawesome-custom/src/index.ts"], "@benzinga/forex-manager": ["libs/data/manager/forex-manager/src/index.ts"], "@benzinga/forms-ui": ["libs/ui/forms/src/index.ts"], "@benzinga/frontend-utils": ["libs/utils/frontend/src/index.ts"], "@benzinga/fund-manager": ["libs/data/manager/fund/src/index.ts"], "@benzinga/fund-manager-hooks": ["libs/react-utils/data-hooks/fund-manager/src/index.ts"], "@benzinga/geo-manager": ["libs/data/manager/geo/src/index.ts"], "@benzinga/globalStyles": ["libs/styles/global-styles/src/index.ts"], "@benzinga/google-sheets-db": ["libs/data/manager/google-sheets-db/src/index.ts"], "@benzinga/gov-trades": ["libs/data/manager/gov-trades/src/index.ts"], "@benzinga/graph-heatmap": ["libs/data/manager/heatmap/src/index.ts"], "@benzinga/guage-visualization": ["libs/visualization/guage/src/index.ts"], "@benzinga/heatmap-visualization": ["libs/visualization/heatmap/src/index.ts"], "@benzinga/hooks": ["libs/react-utils/hooks/src/index.ts"], "@benzinga/iam": ["libs/data/iam/src/index.ts"], "@benzinga/iap": ["libs/data/manager/iap/src/index.ts"], "@benzinga/icons": ["libs/icons/icons/src/index.ts"], "@benzinga/identity": ["libs/ui/identity/src/index.ts"], "@benzinga/image": ["libs/ui/image/src/index.ts"], "@benzinga/insider-trades-manager": ["libs/data/manager/insider-trades/src/index.ts"], "@benzinga/intercom-manager": ["libs/data/manager/intercom/src/index.ts"], "@benzinga/internal-news-manager": ["libs/data/manager/internal-news/src/index.ts"], "@benzinga/iq-chart-visualization": ["libs/visualization/iqchart/src/index.ts"], "@benzinga/iter": ["libs/utils/iter/src/index.ts"], "@benzinga/layout-manager": ["libs/data/manager/layout/src/index.ts"], "@benzinga/licensing-manager": ["libs/data/manager/licensing/src/index.ts"], "@benzinga/livestream": ["libs/data/manager/livestream/src/index.ts"], "@benzinga/livestream-hooks": ["libs/react-utils/data-hooks/livestream-manager/src/index.ts"], "@benzinga/logo-analytics-manager": ["libs/data/manager/logo-analytics/src/index.ts"], "@benzinga/logos-ui": ["libs/ui/logos/src/index.ts"], "@benzinga/marketing-wins-manager": ["libs/data/manager/marketing-wins/src/index.ts"], "@benzinga/miller-columns-ui": ["libs/ui/miller-columns/src/index.ts"], "@benzinga/money": ["libs/ui/money/src/index.ts"], "@benzinga/mortgage-rates-manager": ["libs/data/manager/mortgage-rates/src/index.ts"], "@benzinga/movers-manager": ["libs/data/manager/movers/src/index.ts"], "@benzinga/navigation-ui": ["libs/ui/navigation/src/index.ts"], "@benzinga/news": ["libs/ui/news/src/index.ts"], "@benzinga/news-alerts-manager": ["libs/data/manager/news-alerts/src/index.ts"], "@benzinga/news-manager-hooks": ["libs/react-utils/data-hooks/news-manager/src/index.ts"], "@benzinga/news-user-settings": ["libs/data/manager/news-user-settings/src/index.ts"], "@benzinga/next-utils": ["libs/ui/next-utils/src/index.ts"], "@benzinga/notes-manager": ["libs/data/manager/notes/src/index.ts"], "@benzinga/notification-manager": ["libs/data/manager/notification/src/index.ts"], "@benzinga/onboarding-manager": ["libs/data/manager/onboarding/src/index.ts"], "@benzinga/partner-widget": ["libs/widget/partner-widget/src/index.ts"], "@benzinga/permission-manager": ["libs/data/manager/permission/src/index.ts"], "@benzinga/pie-chart-visualization": ["libs/visualization/pie-chart/src/index.ts"], "@benzinga/play-sound-manager": ["libs/utils/manager/play-sound/src/index.ts"], "@benzinga/plotly-visualization": ["libs/visualization/plotly/src/index.ts"], "@benzinga/plus-manager": ["libs/data/manager/plus/src/index.ts"], "@benzinga/price-alert": ["libs/price-alert/src/index.ts"], "@benzinga/price-alert-manager": ["libs/data/manager/price-alerts/src/index.ts"], "@benzinga/pro-bz-chart-widget": ["libs/widget/pro-bz-chart/src/index.ts"], "@benzinga/pro-calendars-widget": ["libs/widget/pro-calendars/src/index.ts"], "@benzinga/pro-chart-widget": ["libs/widget/pro-chart/src/index.ts"], "@benzinga/pro-chat-widget": ["libs/widget/pro-chat/src/index.ts"], "@benzinga/pro-details-widget": ["libs/widget/pro-details/src/index.ts"], "@benzinga/pro-gpt-widget": ["libs/widget/pro-gpt/src/index.ts"], "@benzinga/pro-graph-widget": ["libs/widget/pro-graph/src/index.ts"], "@benzinga/pro-home-widget": ["libs/widget/pro-home/src/index.ts"], "@benzinga/pro-insiders-widget": ["libs/widget/pro-insiders/src/index.ts"], "@benzinga/pro-movers-widget": ["libs/widget/pro-movers/src/index.ts"], "@benzinga/pro-newsfeed-widget": ["libs/widget/pro-newsfeed/src/index.ts"], "@benzinga/pro-notification-widget": ["libs/widget/pro-notification/src/index.ts"], "@benzinga/pro-option-chain-widget": ["libs/widget/pro-option-chain/src/index.ts"], "@benzinga/pro-research-widget": ["libs/widget/pro-research/src/index.ts"], "@benzinga/pro-scanner-widget": ["libs/widget/pro-scanner/src/index.ts"], "@benzinga/pro-signals-widget": ["libs/widget/pro-signals/src/index.ts"], "@benzinga/pro-tools": ["libs/react-utils/pro-tools/src/index.ts"], "@benzinga/pro-ui": ["libs/ui/pro-ui/src/index.ts"], "@benzinga/pro-user-settings": ["libs/data/manager/pro-user-settings/src/index.ts"], "@benzinga/pro-utils-widget": ["libs/widget/pro-widget-utils/src/index.ts"], "@benzinga/pro-watchlist-widget": ["libs/widget/pro-watchlist/src/index.ts"], "@benzinga/product-manager": ["libs/data/manager/product/src/index.ts"], "@benzinga/product-notifications": ["libs/data/manager/product-notifications/src/index.ts"], "@benzinga/product-ui": ["libs/ui/product/src/index.ts"], "@benzinga/quote-holdings-manager": ["libs/data/manager/quote-holdings/src/index.ts"], "@benzinga/quotes-manager": ["libs/data/manager/quotes/src/index.ts"], "@benzinga/quotes-manager-hooks": ["libs/react-utils/data-hooks/quotes-manager/src/index.ts"], "@benzinga/quotes-ui": ["libs/ui/quotes/src/index.ts"], "@benzinga/quotes-v3-fields-manager": ["libs/data/manager/quotes-v3-fields/src/index.ts"], "@benzinga/quotes-v3-holdings-manager": ["libs/data/manager/quotes-v3-holdings/src/index.ts"], "@benzinga/quotes-v3-manager": ["libs/data/manager/quotes-v3/src/index.ts"], "@benzinga/quotes-v3-manager-hooks": ["libs/react-utils/data-hooks/quotes-v3-manager/src/index.ts"], "@benzinga/reviews-ui": ["libs/ui/reviews/src/index.ts"], "@benzinga/safe-await": ["libs/utils/safe-await/src/index.ts"], "@benzinga/sankey-visualization": ["libs/visualization/sankey/src/index.ts"], "@benzinga/scanner-config-manager": ["libs/data/manager/scanner-config/src/index.ts"], "@benzinga/scanner-manager": ["libs/data/manager/scanner/src/index.ts"], "@benzinga/scanner-manager-hooks": ["libs/react-utils/data-hooks/scanner-manager/src/index.ts"], "@benzinga/scanner-widget": ["libs/widget/scanner/src/index.ts"], "@benzinga/search-modules": ["libs/data/search-modules/src/index.ts"], "@benzinga/search-ui": ["libs/ui/search/src/index.ts"], "@benzinga/securities-manager": ["libs/data/manager/securities/src/index.ts"], "@benzinga/securities-manager-hooks": ["libs/react-utils/data-hooks/securities-manager/src/index.ts"], "@benzinga/seo": ["libs/utils/seo/src/index.ts"], "@benzinga/session": ["libs/data/session/src/index.ts"], "@benzinga/session-context": ["libs/react-utils/session-context/src/index.ts"], "@benzinga/shop-manager": ["libs/data/manager/shop/src/index.ts"], "@benzinga/signals-manager": ["libs/data/manager/signals/src/index.ts"], "@benzinga/signals-user-settings": ["libs/data/manager/signals-user-settings/src/index.ts"], "@benzinga/squawk-manager": ["libs/data/manager/squawk/src/index.ts"], "@benzinga/stock-reports-manager": ["libs/data/manager/stock-reports/src/index.ts"], "@benzinga/subscribable": ["libs/utils/subscribable/src/index.ts"], "@benzinga/subscription-manager": ["libs/data/manager/subscription/src/index.ts"], "@benzinga/table": ["libs/ui/table/src/index.ts"], "@benzinga/templates": ["libs/ui/templates/src/index.ts"], "@benzinga/testing": ["libs/ui/testing/src/index.ts"], "@benzinga/text-to-speech-manager": ["libs/data/manager/text-to-speech/src/index.ts"], "@benzinga/themed-icons": ["libs/icons/themed-icons/src/index.ts"], "@benzinga/themetron": ["libs/react-utils/themetron/src/index.ts"], "@benzinga/ticker-ui": ["libs/ui/ticker/src/index.ts"], "@benzinga/time-manager": ["libs/data/manager/time/src/index.ts"], "@benzinga/time-manager-hooks": ["libs/react-utils/data-hooks/time-manager/src/index.ts"], "@benzinga/token-manager": ["libs/data/manager/token/src/index.ts"], "@benzinga/top-stocks-manager": ["libs/data/manager/top-stocks-manager/src/index.ts"], "@benzinga/tracking-manager": ["libs/data/manager/tracking/src/index.ts"], "@benzinga/tracking-manager-mobile": ["libs/data/manager/tracking-mobile/src/index.ts"], "@benzinga/trade-ideas": ["libs/ui/trade-ideas/src/index.ts"], "@benzinga/trade-ideas-manager": ["libs/data/manager/trade-ideas/src/index.ts"], "@benzinga/trading-view-charting-library-visualization": ["libs/visualization/trading-view-charting-libarary/src/index.ts"], "@benzinga/trading-view-light-weight-chart-visualization": ["libs/visualization/trading-view-light-weight-chart/src/index.ts"], "@benzinga/translate": ["libs/ui/translate/src/index.ts"], "@benzinga/trending-topics-manager": ["libs/data/manager/trending-topics/src/index.ts"], "@benzinga/ui": ["libs/ui/ui/src/index.ts"], "@benzinga/ui/styles": ["libs/ui/styles/src/index.ts"], "@benzinga/user-context": ["libs/react-utils/user-context/src/index.ts"], "@benzinga/user-manager": ["libs/data/manager/user/src/index.ts"], "@benzinga/user-settings": ["libs/data/manager/user-settings/src/index.ts"], "@benzinga/user-ui": ["libs/ui/user/src/index.ts"], "@benzinga/utils": ["libs/utils/utils/src/index.ts"], "@benzinga/valentyn-icons": ["libs/icons/valentyn/src/index.ts"], "@benzinga/verticals-manager": ["libs/data/manager/verticals/src/index.ts"], "@benzinga/videos-manager": ["libs/data/manager/videos/src/index.ts"], "@benzinga/visualization-utils": ["libs/visualization/visualization-utils/src/index.ts"], "@benzinga/watchlist-holdings-manager": ["libs/data/manager/watchlists-holdings/src/index.ts"], "@benzinga/watchlist-manager": ["libs/data/manager/watchlists/src/index.ts"], "@benzinga/watchlist-manager-hooks": ["libs/react-utils/data-hooks/watchlist-manager/src/index.ts"], "@benzinga/watchlist-ui": ["libs/ui/watchlist-ui/src/index.ts"], "@benzinga/widget-linking": ["libs/data/manager/widget-linking/src/index.ts"], "@benzinga/widget-tools": ["libs/react-utils/widget-tools/src/index.ts"], "@benzinga/widget-utils": ["libs/widget/utils/src/index.ts"], "@benzinga/widget/ticker-finder": ["libs/widget/ticker-finder/src/index.ts"], "@benzinga/widgets": ["libs/ui/widgets/src/index.ts"], "@benzinga/wiims-manager": ["libs/data/manager/wiims/src/index.ts"], "@benzinga/wnstn-chat-widget": ["libs/widget/wnstn-chat/src/index.ts"], "@benzinga/zing-rewards-manager": ["libs/data/manager/zing-rewards/src/index.ts"], "chartiq": ["libs/third-party/chartiq/src/index.ts"], "sensa-market": ["libs/widget/sensa-market/src/index.ts"]}, "esModuleInterop": true, "noImplicitAny": false, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": true, "strict": true, "strictBindCallApply": true, "strictFunctionTypes": true, "strictNullChecks": true, "strictPropertyInitialization": true, "resolveJsonModule": true}, "exclude": ["node_modules", "tmp"]}