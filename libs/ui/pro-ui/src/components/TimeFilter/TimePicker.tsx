import { DateTime } from 'luxon';
import * as React from 'react';
import { DatePicker as AntdDatePicker } from 'antd';
import dayjsGenerateConfig from 'rc-picker/lib/generate/luxon';

export const DatePicker = AntdDatePicker.generatePicker<DateTime>(dayjsGenerateConfig);
export type TimePickerProps = Omit<React.ComponentProps<typeof DatePicker>, 'picker'>;

const TimePicker = React.forwardRef<any, TimePickerProps>((props, ref) => {
  return <DatePicker {...props} mode={undefined} picker="time" ref={ref} />;
});

TimePicker.displayName = 'TimePicker';

export default TimePicker;
