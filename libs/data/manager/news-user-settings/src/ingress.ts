import { UserSettingConfig } from '@benzinga/user-settings';
import { AllNewsUserSetting, NewsUserSetting, NewsVersion } from './entities';
import { FilterObject } from '@benzinga/quotes-v3-fields-manager';
import { LegacyFilter } from '@benzinga/scanner-config-manager';
export const getName = (filter: LegacyFilter) => {
  if (filter.field.name === 'watchlist') {
    return 'symbol';
  }
  return filter.field.name;
};

export const FilterToObject = (filter: LegacyFilter): FilterObject => ({
  field: getName(filter),
  isCustom: filter.isCustom,
  operator: filter.operator,
  parameters: filter.parameters,
});

const loader: (typeof NewsUserSettingConfig)['loader'] = (setting, allSettings): NewsUserSetting => {
  switch (setting?.version) {
    case 1:
      return loader(
        {
          ...setting,
          configs: setting.configs.map(c => ({
            ...c,
            feedSettings: {
              ...c.feedSettings,
              screenerFilters: c.feedSettings.screenerFilters.map(FilterToObject),
            },
          })),
          version: 2,
        },
        allSettings,
      );
    case 2:
      return loader(
        {
          ...setting,
          configs: setting.configs.map(c => ({
            ...c,
            feedSettings: {
              ...c.feedSettings,
              denyRelevantCategories: {},
              denySourceGroups: [],
              denySources: [],
              denyWatchlists: [],
            },
          })),
          version: 3,
        },
        allSettings,
      );
    case 3:
      return setting;
    default:
      return {
        configs: [],
        dontShowTodaysMarketMovingNewsDialog:
          (allSettings?.['dontShowTodaysMarketMovingNewsDialog'] as boolean | null) ?? false,
        version: 3,
      };
  }
};

export const NewsUserSettingConfig: UserSettingConfig<'news', NewsVersion, NewsUserSetting, AllNewsUserSetting> = {
  key: 'news',
  loader,
  version: 3,
};
