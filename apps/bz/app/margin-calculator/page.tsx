import React from 'react';
import { Metadata } from 'next';
import { Layout } from '@benzinga/core-ui';
import { PageType, Schema, BENZINGA_LOGO_URL } from '@benzinga/seo';

import { LayoutMain } from './components/LayoutMain';
import { LayoutSidebar } from './components/LayoutSidebar';
import PageLayout from '../_components/PageLayout';
import { getPageData } from './data';
import { PageMeta } from './meta';

const title = 'Margin Calculator';
const description =
  "Using leverage can result in outsized returns, but contains risk. Use Benzinga's margin calculator to analyze outcomes on stock purchased with margin.";
const canonical = 'https://www.benzinga.com/margin-calculator';
export const metadata: Metadata = {
  alternates: {
    canonical,
  },
  authors: [{ name: '<PERSON><PERSON>' }],
  description,
  openGraph: {
    description,
    images: [{ alt: 'Benzinga Logo', height: 300, url: BENZINGA_LOGO_URL, width: 400 }],
    title,
    type: 'website',
    url: canonical,
  },
  title,
};

export default async function Page() {
  const post = await getPageData();

  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': PageType.Tool,
    author: { '@type': 'Person', name: '<PERSON>inga' },
    description:
      "Using leverage can result in outsized returns, but contains risk. Use Benzinga's margin calculator to analyze outcomes on stock purchased with margin.",
    name: 'Margin Calculator',
    url: 'https://www.benzinga.com/margin-calculator',
  };
  return (
    <PageLayout pageProps={{ post }}>
      <PageMeta />
      <Schema data={jsonLd} name="margin-calculator" />
      <Layout layoutMain={<LayoutMain post={post} />} layoutSidebar={<LayoutSidebar sidebar={post?.sidebar} />} />
    </PageLayout>
  );
}
