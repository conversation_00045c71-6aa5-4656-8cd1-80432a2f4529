import React, { useCallback, useRef, useState, useMemo, useEffect, memo } from 'react';
import {
  StyleSheet,
  Text,
  View,
  Modal,
  ActivityIndicator,
  StyleProp,
  ViewStyle,
  InteractionManager,
  AppState,
} from 'react-native';
import { Swipeable } from 'react-native-gesture-handler';
import AlertModal from 'react-native-modal';
import { DateTime } from 'luxon';
import { isEmpty, isNil } from 'ramda';
import ActionButton from './ActionButton';
import { Price } from '../components/Quote/Price';
import Movement from '../components/Quote/Movement';
import Data from '../data-mobile/data';
import Colors from '../constants/Colors';
import { useTheme } from '../theme/themecontext';
import Icon, { IconTypes } from './Icon/Icon';
import AlertTable from './Watchlist/AlertTable';
import WatchlistsManager from './WatchlistsManager';
import { PostButton } from './Buttons/PostButton';
import { useNavigation } from '@react-navigation/native';
import CryptoSubscriptedName from './CryptoSubscriptedName';
import { StackNavigationProp } from '@react-navigation/stack';
import { AppNavigationStackParamList } from '../app/App';
import { SafeType } from '@benzinga/safe-await';
import { Watchlist, WatchlistSymbol } from '@benzinga/watchlist-manager';
import {
  AFTER_MARKET,
  DelayedQuote,
  NO_TRADING,
  PRE_MARKET,
  QuoteSessionType,
  REGULAR,
  Schedule,
} from '@benzinga/quotes-manager';
import { QuoteFeedEvent } from '@benzinga/quotes-manager';
import { colors } from '../constants/Styles';
import { useIsLoggedIn } from '../hooks/useIsLoggedIn';
import CustomPressable from './CustomPressable';
import { Quote } from '@benzinga/quotes-v3-manager';
import { formatAfterHours, formatEDT, formatNumericDateTime } from '@benzinga/date-utils';

interface QuoteViewProps {
  symbol: string | undefined;
  style?: StyleProp<ViewStyle>;
  showAlertSettings?: boolean;
  action?: () => void;
  search?: (symbol: string) => void;
}

interface RenderPriceDetailsProps {
  primary: {
    change: number | null;
    changePercent: number | null;
    price: number | null | undefined;
    timeLabel: string;
  };
  secondary: {
    change: number | null;
    changePercent: number | null;
    price: number | null | undefined;
    timeLabel: string;
  } | null;
}

export const getCurrentSessionType = (schedule?: Schedule): QuoteSessionType | null => {
  const activeSessions = schedule?.sessions?.filter(session => {
    const startTime = DateTime.fromMillis(session.startTime);
    const endTime = DateTime.fromMillis(session.endTime);
    const currentTime = DateTime.fromMillis(new Date().getTime());

    return currentTime > startTime && currentTime < endTime;
  });

  if (!(isEmpty(activeSessions) || isNil(activeSessions))) {
    return activeSessions[0].type as QuoteSessionType;
  }

  return null;
};

interface TimeLabels {
  timestampClose: string;
  timestampEth: string;
}

export const isExtendedHours = (type: QuoteSessionType) =>
  type === PRE_MARKET || type === AFTER_MARKET || type === NO_TRADING;

export const getTimeLabels = (
  quote: DelayedQuote,
  sessionType?: QuoteSessionType,
  options?: { longDate?: boolean; isRealTime?: boolean },
): TimeLabels => {
  const formatter = options?.isRealTime ? formatNumericDateTime : formatEDT;
  const isExtended = sessionType ? isExtendedHours(sessionType) : false;
  let timestampClose =
    typeof quote.lastTradeTime === 'number' ? `Last Update: ${formatter(quote.lastTradeTime, options?.longDate)} ` : '';
  let timestampEth = '';
  if (isExtended && quote.lastTradeTime) {
    timestampClose = `At Close: ${formatAfterHours(quote.lastTradeTime, options?.longDate)} EDT`;
    if (quote.ethPrice !== undefined) {
      let timestampEthPrefix = 'Pre-Market';
      if (sessionType === AFTER_MARKET || sessionType === NO_TRADING) {
        timestampEthPrefix = 'After-Hours';
      }
      timestampEth =
        typeof quote.ethTime === 'number'
          ? `${timestampEthPrefix}: ${formatEDT(quote.ethTime, options?.longDate)} EDT`
          : '';
    }
  }
  return {
    timestampClose,
    timestampEth,
  };
};

const getQuoteSessionPrimaryData = (quotes: Quote | undefined, quoteData: DelayedQuote, session: QuoteSessionType) => {
  const close = quoteData.close;
  const previousClosePrice = quoteData.previousClosePrice;

  const isRegular = session === REGULAR;
  const isNoTrading = session === NO_TRADING;
  const isPremarket = session === PRE_MARKET;
  // const isAfterMarket = session === 'AFTER_MARKET';

  const isCrypto = quoteData?.type === 'CRYPTO';

  // If the lastTradeTime is present on quotes, the quote is real-time.
  const isRealTime = Boolean(quotes?.lastTradeTime);

  let price: number | null | undefined = null;
  let change: number | null = null;
  let changePercent: number | null = null;
  let timeLabel = '-';

  if (isCrypto) {
    // For crypto, we always use the lastTradePrice
    price = quotes?.lastTradePrice ?? quoteData?.lastTradePrice;
    change = quotes?.change ?? null;
    changePercent = quotes?.percentChange ?? null;
    timeLabel = getTimeLabels(quoteData as DelayedQuote, 'REGULAR', {
      isRealTime: false,
      longDate: true,
    }).timestampClose;
  } else if (isRegular) {
    price = (quotes as Quote)?.price ?? quotes?.askPrice;
    change = quotes?.change ?? null;
    changePercent = quotes?.percentChange ?? null;
    timeLabel = getTimeLabels((quotes as DelayedQuote) || quoteData, session, {
      isRealTime,
    }).timestampClose;
  } else if (close) {
    price = close;
    change = price - previousClosePrice;
    changePercent = (change / previousClosePrice) * 100;
    timeLabel = getTimeLabels(quoteData as DelayedQuote, session, { longDate: true }).timestampClose;
  } else if (isNoTrading && previousClosePrice) {
    price = previousClosePrice;
    change = quotes?.change ?? null;
    changePercent = quotes?.percentChange ?? null;
    timeLabel = getTimeLabels(quoteData as DelayedQuote, session, { longDate: true }).timestampClose;
  } else if (isPremarket) {
    price = previousClosePrice;
    // change = price ? price - previousClosePrice : null;
    // changePercent = change ? (change / previousClosePrice) * 100 : quotes?.changePercent ?? null;
    change = quotes?.change ?? null;
    changePercent = quotes?.percentChange ?? null;
    timeLabel = getTimeLabels(quoteData as DelayedQuote, session).timestampClose;
  }
  return {
    change,
    changePercent,
    price,
    timeLabel,
  };
};

const getQuoteSessionSecondaryData = (
  quotes: Quote | undefined,
  quoteData: DelayedQuote,
  session: QuoteSessionType,
) => {
  let price: number | null | undefined = null;
  let change: number | null = null;
  let changePercent: number | null = null;
  let timeLabel = '-';

  const isRegular = session === REGULAR;
  const isPremarket = session === PRE_MARKET;

  if (isRegular) {
    return {
      change: null,
      changePercent: null,
      price: null,
      timeLabel: '',
    };
  }

  const closePrice = quoteData?.close;
  const ethPrice = quoteData?.ethPrice;

  if (isPremarket && quotes) {
    const previousClosePrice = quoteData?.previousClosePrice;

    price = (quotes as Quote)?.price ?? quotes?.askPrice;
    change = price ? price - previousClosePrice : null;
    changePercent = change ? (change / previousClosePrice) * 100 : quotes?.percentChange ?? null;
    // changePercent = quotes?.changePercent ?? null;
    timeLabel = getTimeLabels(quoteData, session).timestampEth;
  } else if (isExtendedHours(session) && closePrice !== undefined) {
    // change = price - quoteData?.lastTradePrice;
    // changePercent = (change / quoteData?.lastTradePrice) * 100;
    price = ethPrice;
    change = price - closePrice;
    changePercent = (change / closePrice) * 100;
    timeLabel = getTimeLabels(quoteData, session).timestampEth;
  } else if (ethPrice !== undefined) {
    // TODO: Test this case
    const previousClosePrice = quoteData?.previousClosePrice;
    price = ethPrice;
    change = price - previousClosePrice;
    changePercent = (change / previousClosePrice) * 100;
    timeLabel = getTimeLabels(quoteData, session).timestampEth;
  }

  return {
    change,
    changePercent,
    price,
    timeLabel,
  };
};

const QuoteViewComponent = ({ action, search, showAlertSettings = true, style, symbol }: QuoteViewProps) => {
  if (!symbol) {
    return null;
  }

  const { colors, isDark } = useTheme();
  const [isWatchlistAlert, setIsWatchlistAlert] = useState<boolean>(false);
  const [watchlistAlertSettings, setWatchlistAlertSettings] = useState<{ symbols: WatchlistSymbol[] }>();
  const [showActionModal, setShowActionModal] = useState(false);
  const [currentSchedule, setCurrentSchedule] = useState<QuoteSessionType | null>();
  const selectedAddToWatchlist = useRef<boolean>();
  const isAuthorized = useIsLoggedIn();
  const navigation = useNavigation<StackNavigationProp<AppNavigationStackParamList>>();
  const [isAlertTableLoading, setAlertTableLoading] = useState(false);

  // Ref for Swipeable component
  const swipeableRef = useRef<Swipeable>(null);

  // // Track component mount state
  const isMounted = useRef(true);
  const pendingOperations = useRef<Array<() => void>>([]);
  const abortControllerRef = useRef<AbortController>(new AbortController());
  const appStateRef = useRef(AppState.currentState);
  const animationFrameIds = useRef<number[]>([]);
  const timeoutIds = useRef<NodeJS.Timeout[]>([]);

  // Set up app state change listener
  useEffect(() => {
    const subscription = AppState.addEventListener('change', nextAppState => {
      if (appStateRef.current === 'active' && nextAppState.match(/inactive|background/)) {
        // App going to background, clean up resources
        cleanupAllAnimations();
      }
      appStateRef.current = nextAppState;
    });

    return () => {
      subscription.remove();
    };
  }, []);

  // Utility function to clean up all animations
  const cleanupAllAnimations = useCallback(() => {
    // Cancel all animation frames
    animationFrameIds.current.forEach(id => {
      cancelAnimationFrame(id);
    });
    animationFrameIds.current = [];

    // Clear all timeouts
    timeoutIds.current.forEach(id => {
      clearTimeout(id);
    });
    timeoutIds.current = [];
  }, []);

  // Helper to safely request animation frame
  const safeRequestAnimationFrame = useCallback((callback: FrameRequestCallback): number => {
    const id = requestAnimationFrame(callback);
    animationFrameIds.current.push(id);
    return id;
  }, []);

  // Master cleanup function
  const masterCleanup = useCallback(() => {
    isMounted.current = false;

    // Abort all network requests
    abortControllerRef.current.abort();

    // Clean up animations
    cleanupAllAnimations();

    // Clean up all pending operations
    pendingOperations.current.forEach(cleanup => {
      if (typeof cleanup === 'function') {
        try {
          cleanup();
        } catch (e) {
          console.warn('Error during cleanup:', e);
        }
      }
    });
    pendingOperations.current = [];

    // Create a fresh abort controller for future requests
    abortControllerRef.current = new AbortController();
  }, [cleanupAllAnimations]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      // Use InteractionManager to ensure cleanup after any animations
      InteractionManager.runAfterInteractions(() => {
        masterCleanup();
      });
    };
  }, [masterCleanup]);

  // Safe state setter with component mounted check
  const safeSetState = useCallback(<T extends any>(setter: React.Dispatch<React.SetStateAction<T>>) => {
    return (value: T) => {
      if (isMounted.current) {
        requestAnimationFrame(() => {
          if (isMounted.current) {
            setter(value);
          }
        });
      }
    };
  }, []);

  // Create a wrapper for any fetch calls
  const safeFetchCallback = useCallback(
    async <T,>(
      promiseFactory: (signal: AbortSignal) => Promise<T>,
      onSuccess?: (data: T) => void,
      onError?: (error: any) => void,
      timeoutMs = 10000, // 10 second timeout
    ): Promise<T | void> => {
      if (!isMounted.current) return;

      let timeoutId: NodeJS.Timeout | undefined;
      const abortController = new AbortController();
      const { signal } = abortController;

      // Create a timeout to abort long-running requests
      const timeoutPromise = new Promise<void>((_, reject) => {
        timeoutId = setTimeout(() => {
          abortController.abort();
          reject(new Error('Request timeout'));
        }, timeoutMs);
      });

      // Add abort controller to cleanup
      const cleanup = () => {
        if (timeoutId) clearTimeout(timeoutId);
        abortController.abort();
      };
      pendingOperations.current.push(cleanup);

      try {
        // Race the fetch against a timeout
        const result = (await Promise.race([promiseFactory(signal), timeoutPromise])) as T;

        if (isMounted.current && onSuccess) {
          onSuccess(result);
        }

        // Remove from pending operations
        pendingOperations.current = pendingOperations.current.filter(op => op !== cleanup);
        if (timeoutId) clearTimeout(timeoutId);

        return result;
      } catch (error) {
        if (isMounted.current && onError) {
          onError(error);
        }

        // Remove from pending operations
        pendingOperations.current = pendingOperations.current.filter(op => op !== cleanup);
        if (timeoutId) clearTimeout(timeoutId);
      }
    },
    [],
  );

  // Wrap any component creation that might use animation
  const withAnimationCleanup = useCallback(<T extends any>(callback: () => T): T => {
    // Capture the current animation count
    const previousAnimationCount = animationFrameIds.current.length;

    // Call the component creation function
    const result = callback();

    // Register a cleanup function for any new animations
    if (animationFrameIds.current.length > previousAnimationCount) {
      const newAnimationCount = animationFrameIds.current.length;
      const cleanup = () => {
        // Cancel any animation frames that were created
        for (let i = previousAnimationCount; i < newAnimationCount; i++) {
          if (i < animationFrameIds.current.length) {
            cancelAnimationFrame(animationFrameIds.current[i]);
          }
        }
      };
      pendingOperations.current.push(cleanup);
    }

    return result;
  }, []);

  // Create a function to get quote feed
  const getQuoteFeed = useCallback(() => {
    if (symbol) {
      return Data.quotes().createQuoteFeed(symbol);
    }
    return undefined;
  }, [symbol]);

  // Use the subscriber pattern with useCallback for the handler
  const quoteUpdateHandler = useCallback(
    (event: QuoteFeedEvent) => {
      const newQuote = event?.quote as Quote;
      if (newQuote && isMounted.current) {
        // Use animation frame to update state to work with the React render cycle
        safeRequestAnimationFrame(() => {
          if (isMounted.current) {
            // setQuoteData(newQuote);
            safeSetState(setQuote)(newQuote as Quote);
          }
        });
      }
    },
    [safeRequestAnimationFrame],
  );

  // Use the subscriber right away and store the feed
  const [quoteFeed, setQuoteFeed] = useState(() => {
    const feed = getQuoteFeed();
    if (feed) {
      // Subscribe to the feed
      const unsubscribe = feed?.subscribe?.(quoteUpdateHandler);
      if (typeof unsubscribe === 'function') {
        pendingOperations.current.push(unsubscribe);
      }
    }
    return feed;
  });

  // Properly handle subscription cleanup and updates when symbol changes
  useEffect(() => {
    // Clean up previous subscription if there is one
    pendingOperations.current.forEach(cleanup => {
      if (typeof cleanup === 'function') {
        try {
          cleanup();
        } catch (e) {
          console.warn('Error during cleanup:', e);
        }
      }
    });
    pendingOperations.current = [];

    // Get new feed
    const feed = getQuoteFeed();
    setQuoteFeed(feed);

    if (feed) {
      // Subscribe to the feed - use optional chaining and check result is a function
      const unsubscribe = feed?.subscribe?.(quoteUpdateHandler);
      if (typeof unsubscribe === 'function') {
        pendingOperations.current.push(unsubscribe);
      }

      // Return cleanup function
      return () => {
        if (typeof unsubscribe === 'function') {
          try {
            unsubscribe();
          } catch (e) {
            console.warn('Error during unsubscribe:', e);
          }
        }
      };
    }
  }, [getQuoteFeed, quoteUpdateHandler]);

  const storedQuote = useMemo(() => {
    let _symbol = symbol;
    if (_symbol && _symbol.indexOf('/USD') === -1 && (quote?.source === 'Crypto' || quote?.type === 'CRYPTO')) {
      _symbol = _symbol.toUpperCase() + '/USD';
    }
    return Data.quotes()
      .getStore()
      .getDelayedQuote(_symbol || '');
  }, [symbol, quote?.source, quote?.type]);

  // // Initialize state with memoized value
  const [quoteData, setQuoteData] = useState<DelayedQuote | undefined>();
  const [quote, setQuote] = useState<Quote | undefined>();

  // Use safeFetch for schedule data
  useEffect(() => {
    safeFetchCallback(
      async signal => {
        const _schedule = await Data.quotes().getSchedule();
        const _quoteData = symbol ? await Data.quotes().getDelayedQuotes([symbol]) : undefined;
        return {
          schedule: _schedule,
          quoteData: _quoteData,
        };
      },
      result => {
        if (result?.schedule?.ok) {
          safeSetState(setCurrentSchedule)(getCurrentSessionType(result?.schedule?.ok));
        }
        if (result?.quoteData?.ok && !isEmpty(result?.quoteData?.ok)) {
          // If we have a quote, set it
          if (symbol) {
            safeSetState(setQuoteData)(result.quoteData.ok[symbol]);
          }
        }
      },
      error => {
        console.error('Error getting quote schedule', error);
      },
    );
  }, [safeFetchCallback, safeSetState, symbol]);

  // Return early if no symbol or quote
  if (!symbol) return <View />;

  const [secondaryPriceMode, setSecondaryPriceMode] = useState(false);

  const renderPriceDetails = useCallback(
    ({ primary, secondary }: RenderPriceDetailsProps) => {
      if (!primary) {
        return null;
      }
      const { change, changePercent, timeLabel } = secondaryPriceMode && secondary !== null ? secondary : primary;

      return withAnimationCleanup(() => (
        <View
          style={[styles.quoteChange as StyleProp<ViewStyle>, { flexDirection: 'column', justifyContent: 'flex-end' }]}
        >
          <Movement change={change} isDark={isDark} percentChange={changePercent} />
          <Text style={[styles.quoteAfterMarket, isDark && { color: '#99AECC' }]}>{timeLabel ? timeLabel : '-'}</Text>
        </View>
      ));
    },
    [isDark, secondaryPriceMode, withAnimationCleanup],
  );

  const renderPrice = useCallback(() => {
    if (quoteData === undefined || quoteData === null || currentSchedule === undefined || currentSchedule === null) {
      return null;
    }
    const isCrypto = quoteData?.type === 'CRYPTO';
    const primaryData = getQuoteSessionPrimaryData(quote, quoteData as DelayedQuote, currentSchedule);
    const secondaryData = isCrypto
      ? null
      : getQuoteSessionSecondaryData(quote, quoteData as DelayedQuote, currentSchedule);
    return (
      <CustomPressable
        onPress={() => {
          if (!isCrypto) {
            setSecondaryPriceMode(!secondaryPriceMode);
          }
        }}
      >
        <View style={{ alignItems: 'flex-end' }}>
          <Price
            price={secondaryPriceMode ? secondaryData?.price : primaryData.price}
            style={[styles.quotePrice, { color: colors.text }]}
          />
          {renderPriceDetails({
            primary: primaryData,
            secondary: isCrypto ? null : secondaryData,
          })}
        </View>
      </CustomPressable>
    );
  }, [quoteData, quote, currentSchedule, colors.text, setSecondaryPriceMode, secondaryPriceMode]);

  const _closeActions = useCallback(() => {
    setShowActionModal(false);
  }, []);

  const clearWatchlistAlertModal = useCallback(() => {
    setIsWatchlistAlert(false);
  }, []);

  const openWatchlistAlertSettings = useCallback(() => {
    safeSetState(setAlertTableLoading)(true);

    safeFetchCallback(
      async signal => {
        return await Data.watchlists().getWatchlists(true);
      },
      (watchlists: { ok: Watchlist[] } | SafeType<Watchlist[] | undefined, string, unknown>) => {
        safeSetState(setAlertTableLoading)(false);
        let foundSymbol = false;
        watchlists?.ok?.forEach(watchlist => {
          watchlist?.symbols?.forEach(s => {
            if (s.symbol === symbol) {
              safeSetState(setWatchlistAlertSettings)({ symbols: [s] });
              foundSymbol = true;
              return;
            }
          });
        });

        if (!foundSymbol) {
          safeSetState(setWatchlistAlertSettings)(undefined);
        }

        safeSetState(setIsWatchlistAlert)(true);
      },
      () => {
        safeSetState(setAlertTableLoading)(false);
        safeSetState(setIsWatchlistAlert)(true);
      },
    );
  }, [symbol, safeSetState, safeFetchCallback]);

  const renderAlertSettings = useCallback(() => {
    return (
      <AlertModal
        avoidKeyboard={true}
        hasBackdrop={true}
        isVisible={isWatchlistAlert}
        onBackdropPress={clearWatchlistAlertModal}
        onModalHide={() => {
          if (selectedAddToWatchlist.current) {
            selectedAddToWatchlist.current = false;
            if (!isAuthorized) {
              navigation.navigate('AuthModal');
            } else {
              setShowActionModal(true);
            }
          }
        }}
        style={{ margin: 0 }}
      >
        <CustomPressable onPress={clearWatchlistAlertModal} style={styles.backdropContainer} />
        {watchlistAlertSettings ? (
          <AlertTable watchList={watchlistAlertSettings} closeModal={clearWatchlistAlertModal} />
        ) : isAlertTableLoading ? (
          <View style={[styles.alertLoadingContainer, { backgroundColor: colors.background, alignItems: 'center' }]}>
            <ActivityIndicator size={25} color={colors.text} />
          </View>
        ) : (
          <View style={[styles.addToWatchlistContainer, { backgroundColor: colors.background }]}>
            <PostButton
              onPress={() => {
                setIsWatchlistAlert(false);
                selectedAddToWatchlist.current = true;
              }}
              icon={'plus-circle-outline'}
              title="Add to Watchlist"
            />
          </View>
        )}
      </AlertModal>
    );
  }, [
    isWatchlistAlert,
    clearWatchlistAlertModal,
    watchlistAlertSettings,
    isAlertTableLoading,
    colors.background,
    colors.text,
    isAuthorized,
    navigation,
  ]);

  const handleDone = useCallback(() => {
    global.Analytics.event('Manage Watchlists', 'Done Pressed', `Quote(${quote?.symbol})`);
    _closeActions();
  }, [_closeActions, quote?.symbol]);

  const swipeableOptions = useCallback(() => {
    return (
      <View style={{ flexDirection: 'row', justifyContent: 'flex-end', marginBottom: 8 }}>
        <View style={styles.vAlertContainer}>
          <CustomPressable
            onPress={() => {
              if (!isAuthorized) {
                navigation.navigate('AuthModal');
              } else {
                setShowActionModal(true);
              }
            }}
            style={{ marginHorizontal: 4 }}
          >
            <Icon type={IconTypes.SimpleLineIcons} name="magnifier-add" size={28} color={colors.text} />
          </CustomPressable>
        </View>
        {showAlertSettings && (
          <View style={styles.vAlertContainer}>
            <CustomPressable
              onPress={() => {
                openWatchlistAlertSettings();
              }}
              style={{ marginHorizontal: 4 }}
            >
              <Icon type={IconTypes.SimpleLineIcons} name="bell" size={28} color={colors.text} />
            </CustomPressable>
          </View>
        )}
      </View>
    );
  }, []);

  return (
    <Swipeable ref={swipeableRef} renderRightActions={swipeableOptions}>
      <View style={[styles.quote, { backgroundColor: colors.backgroundTertiary }]}>
        <View style={[styles.quotePrimaryInfo, style]}>
          <View style={styles.quoteInfoView}>
            <CryptoSubscriptedName symbol={quoteData?.symbol || symbol} />
            <Text style={[styles.quoteName, { color: isDark ? '#99AECC' : '#5B7292' }]} numberOfLines={2}>
              {quoteData?.description}
            </Text>
          </View>
          <View style={[styles.quotePricingView, action ? { paddingRight: 0 } : {}]}>
            {search ? (
              <CustomPressable
                hitSlop={{
                  top: 20,
                  bottom: 20,
                  right: 20,
                  left: 20,
                }}
                onPress={() => search(symbol)}
                style={{ marginRight: 15 }}
              >
                <Icon
                  type={IconTypes.AntDesign}
                  name={'plus'}
                  size={20}
                  color={isDark ? Colors.lightGrey : Colors.black}
                />
              </CustomPressable>
            ) : (
              <View>{renderPrice()}</View>
            )}
          </View>
          {action && <ActionButton onPress={action} />}
          {renderAlertSettings()}
        </View>
        <Modal
          onRequestClose={() => _closeActions()}
          animationType="slide"
          transparent={false}
          presentationStyle="overFullScreen"
          visible={showActionModal}
        >
          <WatchlistsManager selectedQuote={quoteData} clickedWatchlist={null} done={handleDone} action={null} />
        </Modal>
      </View>
    </Swipeable>
  );
};

const styles = StyleSheet.create({
  quote: {
    width: '100%',
    alignSelf: 'center',
  },
  row: {
    display: 'flex',
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  quotePrimaryInfo: {
    display: 'flex',
    justifyContent: 'space-between',
    flexDirection: 'row',
    paddingLeft: 4,
    paddingRight: 8,
  },
  quoteDetailedInfo: {
    padding: 8,
  },
  quoteInfoView: {
    flex: 1,
    padding: 8,
    display: 'flex',
    justifyContent: 'center',
    flexDirection: 'column',
  },
  quoteAction: {
    padding: 16,
    paddingLeft: 8,
    flex: 0,
    display: 'flex',
    justifyContent: 'space-around',
    flexDirection: 'column',
  },
  quotePricingView: {
    display: 'flex',
    alignItems: 'flex-end',
    justifyContent: 'center',
    flexDirection: 'column',
    paddingVertical: 8,
  },
  noChange: {
    color: 'black',
  },
  quoteName: {
    fontSize: 12,
  },
  quotePrice: {
    fontSize: 20,
    fontFamily: 'Graphik-Semibold',
    textAlign: 'right',
  },
  quoteChange: {
    textAlign: 'right',
  },
  quoteDate: {
    fontSize: 12,
    color: '#555',
    textAlign: 'right',
  },
  quoteAfterMarket: {
    fontSize: 10,
    color: colors.black,
  },
  vAlertContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingRight: 8,
    paddingLeft: 4,
  },
  backdropContainer: {
    flex: 1,
  },
  addToWatchlistContainer: {
    paddingTop: 28,
    paddingBottom: 42,
    paddingHorizontal: 16,
  },
  alertLoadingContainer: {
    paddingHorizontal: 16,
    paddingVertical: 42,
  },
});

// Memoize the entire component to prevent unnecessary re-renders
const QuoteView = memo(QuoteViewComponent);

export default QuoteView;
