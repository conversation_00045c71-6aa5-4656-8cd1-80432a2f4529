import React, { useCallback, useEffect, useMemo, useState, useRef } from 'react';
import {
  Platform,
  RefreshControl,
  Share,
  StyleSheet,
  View,
  Dimensions,
  Modal,
  FlatList,
  Animated,
  TouchableOpacity,
} from 'react-native';
import { formatTwitterText } from '@benzinga/utils';
import { decode } from 'html-entities';

// Components
import ArticleFullView from '../components/Article/ArticleFullView';
import ArticleChannels from '../components/Article/ArticleChannels';
import Earnings from '../components/Quote/Earnings';
import QuoteView from '../components/QuoteView';
import { LoadingView } from '../components/LoadingView';
import Ratings from '../components/Quote/Ratings';
import Data from '../data-mobile/data';
import { PostButton } from '../components/Buttons/PostButton';
import { useTheme } from '../theme/themecontext';
import CellContainer from '../components/Table/CellContainer';
import { borderStyles } from '../constants/Styles';
import { BenzingaTitleImage } from '../components/Images';
import { ArticleApiClient } from '../data-mobile/article/client';
import Icon, { IconTypes } from '../components/Icon/Icon';
import { useIsLoggedIn } from '../hooks/useIsLoggedIn';
import { DetailedQuotesBySymbol, DetailedQuote } from '@benzinga/quotes-manager';
import { InternalNode, InternalTag } from '@benzinga/article-manager';
import { NewsNavigationStackParamList } from '../navigation/NewsNavigationStack';
import { AppNavigationStackParamList } from '../app/App';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { HomeNavigationStackParamList } from '../navigation/HomeNavigationStack';
import { BaseQuote, News, NewsImage } from '@benzinga/advanced-news-manager';
import { MarketDataNavigationStackParamList } from '../navigation/MarketDataNavigationStack';
import { ChatNavigationStackParamList } from '../navigation/ChatNavigationStack';
import CustomPressable from '../components/CustomPressable';
import BZTaboolaAd, { BZTBL_AD_POSITION } from '../components/BZTaboolaAd';
import { navigate } from '../services';
import { AccountNavigationStackParamList } from '../navigation/AccountNavigationStack';
import AsyncStorage from '@react-native-async-storage/async-storage';
import dayjs from 'dayjs';
import useTrackPageView from '../hooks/useTrackPageView';
import { EdgeRankings } from '../components/EdgeRankings';
import { TradeIdeasNavigationStackParamList } from '../navigation/TradeIdeasNavigationStack';
import BZConnatixAd from '../components/BZConnatixAd';

interface ArticleScreenProps {
  navigation: StackNavigationProp<NewsNavigationStackParamList & AppNavigationStackParamList>;
  route: RouteProp<
    | HomeNavigationStackParamList
    | MarketDataNavigationStackParamList
    | NewsNavigationStackParamList
    | ChatNavigationStackParamList,
    'Article'
  >;
}

interface CampaignData {
  top: string;
  bottom: string;
  middle: string;
}

const ArticleScreen = (props: ArticleScreenProps) => {
  const { navigation, route } = props;
  const { colors } = useTheme();
  const styles = withColors(colors);
  const isLoggedIn = useIsLoggedIn();

  const [article, setArticle] = useState(route?.params?.article);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [quotes, setQuotes] = useState<
    DetailedQuote[] | DetailedQuotesBySymbol[] | undefined | DetailedQuotesBySymbol | BaseQuote[]
  >();
  const [isArticleLoading, setIsArticleLoading] = useState<boolean>(true);
  const hasEdgeRankingPermission = useMemo(
    () => Data.permissions()?.hasAccess?.('com/read', 'unlimited-calendars')?.ok ?? false,
    [],
  );

  useTrackPageView('Article', '_');

  useEffect(() => {
    if (article?.blocks && article.blocks.length) {
      const hasBlockQuote = article.blocks.map((item: { innerHTML: string | string[] }) => {
        if (item.innerHTML.includes('<blockquote')) {
          return true;
        }
        return false;
      });
      setIsArticleLoading(hasBlockQuote.includes(true));
    }
  }, [article]);

  useEffect(() => {
    const timeOut = setTimeout(() => {
      setIsArticleLoading(false);
    }, 6000);

    return () => {
      clearTimeout(timeOut);
    };
  }, [isRefreshing]);

  useEffect(() => {
    if (!isLoggedIn) {
      unregisteredUserArticleViewCount();
    }
    renderNavigation();
    if (article || route?.params?.id) {
      setIsLoading(true);
      fetchArticleData();
    }
    // loadRelatedArticles(pageNo);

    return () => {
      global.Analytics.instance().chartbeatPageViewEnd();
    };
  }, []);

  useEffect(() => {
    // load quotes
    const quotes = route?.params?.article?.quotes ? route?.params?.article?.quotes : article?.quotes;
    if (quotes?.length) {
      quotes.map((quote: BaseQuote) => {
        if (quote?.symbol?.match(/\$/)) {
          quote.symbol = quote.symbol.replace('$', '') + '/USD';
        }
        return quote;
      });
      setQuotes(quotes);
      loadQuote(quotes);
    }
  }, [article, isLoading, isRefreshing]);

  const unregisteredUserArticleViewCount = async () => {
    const newsCount = await AsyncStorage.getItem('UnregisteredUserArticleViewCount');
    if (!newsCount) {
      AsyncStorage.setItem(
        'UnregisteredUserArticleViewCount',
        JSON.stringify({ newsCount: 0, date: dayjs().toISOString() }),
      );
    } else {
      const parsedData = JSON.parse(newsCount);
      if (parsedData.newsCount >= 5) {
        navigation.reset({ index: 0, routes: [{ name: 'Main' }] });
      }
      AsyncStorage.setItem(
        'UnregisteredUserArticleViewCount',
        JSON.stringify({ newsCount: parsedData.newsCount + 1, date: parsedData.date }),
      );
    }
  };

  /*const loadRelatedArticles = async (pageNo: number) => {
    const tickers = article?.tickers?.map((item: { name: string }) => {
      return item.name;
    });
    const params = GetQryForTab(route.params.newsType ?? '');
    const articles = await Data.news().simplyQueryNews({ ...params, tickers }, { limit: pageNo, type: article?.type });

    if (articles.ok) {
      const result = articles.ok.filter(item => item.id !== article?.id);
      setRelatedArticles(result);
    } else {
      console.log('[Error] :', articles.err);
    }
    setHasMoreRelatedArticles(pageNo > 25 ? false : true);
  };*/

  const renderNavigation = () => {
    const isFromStarredArticles = route?.params?.isFromStarredArticles;
    navigation.setOptions({
      headerBackTitleVisible: Platform.OS === 'android' ? false : !isFromStarredArticles,
      headerLeftContainerStyle: {
        flex: 1,
      },
      headerRight: () => (
        <CustomPressable onPress={() => share()} style={styles.shareIcon}>
          <Icon color={colors.text} name={'share-outline'} size={20} type={IconTypes.Ionicons} />
        </CustomPressable>
      ),
      headerTitle: () => {
        return (
          <View style={styles.headerTitle}>
            <BenzingaTitleImage />
          </View>
        );
      },
    });
  };

  const fetchArticleData = () => {
    const client = new ArticleApiClient();
    const articleId = article?.id?.toString() || (route?.params?.id as string);

    if (!articleId) {
      console.error('Cannot fetch article: No article ID available');
      setIsLoading(false);
      setIsRefreshing(false);
      return;
    }

    client
      .getArticleNode(articleId)
      .then(async res => {
        if (res && (res as { ok: InternalNode }).ok) {
          const campaign: CampaignData | unknown = await client.getCampaignNode(articleId);
          const articleData = adaptArticleData((res as { ok: InternalNode }).ok, campaign);
          setArticle(articleData);
          setIsLoading(false);
          setIsRefreshing(false);
        } else {
          setIsLoading(false);
          setIsRefreshing(false);
        }
      })
      .catch((error) => {
        setIsLoading(false);
        setIsRefreshing(false);
      });
    if (article) {
      global.Analytics.instance().chartbeatPageView(article);
    }
  };

  const loadQuote = (quotes: BaseQuote[]) => {
    const quoteSymbols = quotes.map(quote => quote.symbol);

    Data.quotes()
      .getDetailedQuotes(quoteSymbols)
      .then(_quotes => {
        _quotes.ok && setQuotes(_quotes.ok);
      })
      .catch(err => {
        console.error('quoteFetcher', err);
      });
  };

  const adaptArticleData = (article: InternalNode, campaign: CampaignData | unknown) => {
    const thumbnail = article?.image;
    return {
      author: article.name,
      blocks: article.blocks,
      body: article.Body,
      campaign: campaign,
      channels: article?.Channels?.map(channel => {
        return { name: channel?.name };
      }),
      created: article.CreatedAt,
      id: article.NodeID,
      quotes: (article as News & InternalNode)?.quotes ? (article as News & InternalNode)?.quotes : article?.Quotes,
      stocks: article?.Tickers?.map(item => {
        return { name: item.name };
      }),
      tags: article?.Tags?.map(tag => {
        return { name: tag?.name };
      }),
      teaser: article.TeaserText,
      thumbnail: thumbnail,
      title: article.Title,
      updated: article.UpdatedAt,
      url: `https://www.benzinga.com/${article.CanonicalPath}`,
      type: article.Type,
      tickers: article.Tickers,
      commentCount: article.commentCount,
    };
  };

  const share = useCallback(() => {
    global.Analytics.event('Share', 'Share Article', 'Article');
    const url = article?.url ? article.url : `https://www.benzinga.com/node/${article?.id}`;
    const tickers = (article as News & InternalNode)?.Tickers?.map((item: InternalTag) => item.name);
    const message = formatTwitterText({ text: article?.title ?? '', tickers });
    const finalMessage = Platform.OS === 'android' ? `${message}\n${url}` : decode(article?.title);
    Share.share({
      message: finalMessage,
      title: decode(article?.title),
      url: url,
    })
      //after successful share return result
      .then(result => console.log(result))
      //If any thing goes wrong it comes here
      .catch(errorMsg => console.error(errorMsg));
  }, [article]);

  const handleRefresh = () => {
    setIsRefreshing(true);
    fetchArticleData();
  };

  const _showArticle = useCallback(
    (article: News) => {
      global.Analytics.event('Navigation', 'Show Article', 'Article');
      navigation.push('Article', {
        article: article,
      });
    },
    [navigation],
  );

  const _showTicker = useCallback(
    (ticker: string) => {
      global.Analytics.event('Navigation', 'Show Ticker', 'Article');
      if (ticker?.includes('/USD')) {
        const _symbol = ticker.split('/USD')[0];
        Data.tracking().trackTickerEvent('click', { symbol: _symbol, source: 'Article' });
        navigation.push('CryptoQuote', {
          symbol: _symbol,
        });
      } else {
        Data.tracking().trackTickerEvent('click', { symbol: ticker, source: 'Article' });
        navigation.push('Quote', {
          symbol: ticker,
        });
      }
    },
    [navigation],
  );

  const _showChannel = (channel: string) => {
    global.Analytics.event('Navigation', 'Show Channel', 'Article');
    navigation.push('NewsFeed', {
      channel: channel,
    });
  };

  const createPost = () => {
    global.Analytics.event('Navigation', 'Show Create Post', 'Article');
    navigation.navigate('Share', {
      node: article,
    });
  };

  const images = route?.params?.article?.image;

  const commentBtnTitle = useMemo(() => {
    if (!isLoggedIn) {
      return 'Log In to Comment';
    }
    if (article?.commentCount > 0) {
      return `${article?.commentCount} Comments`;
    } else if (article?.commentCount === 1) {
      return '1 Comment';
    }
    return 'Be the first to Comment!';
  }, [article]);

  const [isCollapsed, setIsCollapsed] = useState(false);
  const VIDEO_HEIGHT = 160;
  const VIDEO_WIDTH = (16 / 9) * VIDEO_HEIGHT;

  const adRef = useRef<{ play: () => void; pause: () => void } | null>(null);

  const positionXY = useRef(
    new Animated.ValueXY({
      x: Dimensions.get('window').width - VIDEO_WIDTH - 32,
      y: Dimensions.get('window').height / 1.3 - VIDEO_HEIGHT - 16,
    }), // Adjusted initial position
  ).current;

  const toggleCollapse = () => {
    const screenWidth = Dimensions.get('window').width;
    const finalX = isCollapsed ? screenWidth - VIDEO_WIDTH - 32 : screenWidth - 32;
    const finalY = Dimensions.get('window').height / 1.3 - VIDEO_HEIGHT - 16; // Keep the bottom margin consistent

    Animated.timing(positionXY, {
      toValue: { x: finalX, y: finalY },
      duration: 300, // Adjusted duration for smooth animation
      useNativeDriver: false,
    }).start(({ finished }) => {
      if (finished) {
        // Animation completed
        if (isCollapsed) {
          playAd();
        } else {
          pauseAd();
        }
        setIsCollapsed(!isCollapsed);
      } else {
        // Animation interrupted
        console.log('Animation interrupted');
      }
    });
  };

  const playAd = () => {
    if (adRef.current) {
      adRef.current.play();
    }
  };

  const pauseAd = () => {
    if (adRef.current) {
      adRef.current.pause();
    }
  };

  if (article) {
    return (
      <>
        <View style={[{ backgroundColor: colors.backgroundTertiary }]}>
          <FlatList
            data={[article]}
            overScrollMode="never"
            refreshControl={
              <RefreshControl onRefresh={handleRefresh} refreshing={isRefreshing} tintColor={colors.text} />
            }
            renderItem={() => {
              return (
                <>
                  <Modal
                    animationType="none"
                    style={styles.loadingViewModal}
                    transparent={true}
                    visible={isArticleLoading}
                  >
                    <LoadingView
                      style={{ backgroundColor: `${colors.cardBackground}55`, flex: 1, justifyContent: 'center' }}
                    />
                  </Modal>
                  <View
                    style={[
                      styles.container,
                      borderStyles.horizontal,
                      {
                        backgroundColor: colors.card,
                        padding: 16,
                      },
                    ]}
                  >
                    <ArticleFullView
                      article={article}
                      images={images as NewsImage[]}
                      isFromStarredArticles={route?.params?.isFromStarredArticles}
                      onPressTicker={_showTicker}
                      showArticle={_showArticle}
                    />

                    {isLoading ? (
                      <LoadingView />
                    ) : (
                      <ArticleChannels
                        article={article}
                        onPressChannel={channel => {
                          _showChannel(channel);
                        }}
                        style={{ marginTop: 12 }}
                      />
                    )}
                  </View>

                  <PostButton
                    title={commentBtnTitle}
                    style={{ paddingLeft: 0, borderWidth: 0 }}
                    onPress={() => {
                      isLoggedIn
                        ? (
                            navigation as StackNavigationProp<
                              AccountNavigationStackParamList | NewsNavigationStackParamList
                            >
                          ).navigate('ArticleComments', {
                            article: article,
                          })
                        : navigate('Login', { dismissOnAuth: true });
                    }}
                  />

                  <View style={{ backgroundColor: colors.backgroundTertiary }}>
                    {quotes && Object.values(quotes)?.length ? (
                      <View
                        style={{
                          paddingVertical: 20,
                        }}
                      >
                        {Object.values(quotes).map((quote: DetailedQuote, i: number) => {
                          return (
                            <CellContainer index={i} key={`quote-${i}`}>
                              {i === 0 ? (
                                <View style={{ marginBottom: 18 }}>
                                  <QuoteView symbol={quote.symbol} />
                                  <EdgeRankings
                                    inArticle={true}
                                    navigation={
                                      navigation as StackNavigationProp<
                                        NewsNavigationStackParamList &
                                          AppNavigationStackParamList &
                                          TradeIdeasNavigationStackParamList
                                      >
                                    }
                                    symbol={quote.symbol}
                                  />
                                  {hasEdgeRankingPermission ? (
                                    <PostButton
                                      style={{ marginTop: 0 }}
                                      title={'Overview'}
                                      onPress={() => _showTicker(quote.symbol)}
                                    />
                                  ) : null}
                                </View>
                              ) : (
                                <CustomPressable onPress={() => _showTicker(quote.symbol)}>
                                  <QuoteView symbol={quote.symbol} />
                                </CustomPressable>
                              )}
                            </CellContainer>
                          );
                        })}
                      </View>
                    ) : null}
                    <CellContainer containerStyle={[styles.adContainer]}>
                      <View style={{ backgroundColor: colors.background }}>
                        <BZTaboolaAd unitType={BZTBL_AD_POSITION.FEED} />
                      </View>
                    </CellContainer>
                    {quotes && Object.values(quotes)?.length ? (
                      <View>
                        {Object.values(quotes).map((quote, i) => {
                          if (quote.earnings?.length || quote.ratings?.length) {
                            return (
                              <View key={`quote-calendar-data-${i}`} style={{ marginTop: 8 }}>
                                {quote.earnings && quote.earnings.length && (
                                  <Earnings earnings={quote.earnings} title={`${quote.symbol} Earnings`} />
                                )}
                                {quote.ratings && quote.ratings.length && (
                                  <Ratings
                                    pressedTicker={ticker => _showTicker(ticker)}
                                    ratings={quote.ratings}
                                    title={`${quote.symbol} Ratings`}
                                  />
                                )}
                              </View>
                            );
                          }
                          return null;
                        })}
                      </View>
                    ) : null}
                  </View>
                </>
              );
            }}
          />
        </View>
        <Animated.View
          style={[
            styles.pipContainer,
            {
              transform: [{ translateX: positionXY.x }, { translateY: positionXY.y }],
              width: VIDEO_WIDTH,
              height: VIDEO_HEIGHT,
            },
          ]}
        >
          <View style={{ flexDirection: 'row' }}>
            <TouchableOpacity onPress={toggleCollapse} style={styles.collapseTab}>
              <View style={[styles.collapseIcon, styles.collapseIconLeft]}>
                <Icon
                  type={IconTypes.FontAwesome}
                  name={`angle-${isCollapsed ? 'left' : 'right'}`}
                  size={36}
                  color={colors.text}
                />
              </View>
            </TouchableOpacity>
            <View style={{ backgroundColor: 'grey' }}>
              <BZConnatixAd ref={adRef} />
            </View>
          </View>
        </Animated.View>
      </>
    );
  }
};

const withColors = colors =>
  StyleSheet.create({
    pipContainer: {
      position: 'absolute',
      zIndex: 1,
      margin: 8,
      shadowColor: '#fff',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    collapseTab: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    collapseIcon: {
      backgroundColor: colors.backgroundSecondary,
      borderWidth: 1,
      borderColor: colors.border,
      paddingHorizontal: 6,
      paddingVertical: 6,
      alignItems: 'center',
      justifyContent: 'center',
    },
    collapseIconLeft: {
      borderTopLeftRadius: 8,
      borderBottomLeftRadius: 8,
    },
    collapseIconRight: {
      borderTopRightRadius: 8,
      borderBottomRightRadius: 8,
    },
    author: {
      color: '#555',
      fontSize: 14,
    },
    body: {
      color: '#000',
      fontSize: 14,
      paddingHorizontal: 16,
    },
    container: {
      display: 'flex',
      overflow: 'visible',
    },
    headerTitle: {
      alignItems: 'center',
      flex: 1,
      justifyContent: 'center',
    },
    loadingViewModal: {
      alignItems: 'center',
      height: Dimensions.get('window').height,
      justifyContent: 'center',
      position: 'absolute',
      width: Dimensions.get('window').width,
      zIndex: 1,
    },
    shareIcon: {
      flex: 1,
      justifyContent: 'center',
      marginRight: 8,
    },
    adContainer: {
      paddingHorizontal: 16,
      paddingVertical: 10,
    },
  });

export default ArticleScreen;
