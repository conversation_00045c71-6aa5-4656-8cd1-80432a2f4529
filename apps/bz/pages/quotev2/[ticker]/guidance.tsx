import React from 'react';

import { SectionTitle } from '@benzinga/core-ui';

import { getQuoteData } from '../../api/quote';
import QuoteCalendar from '../../../src/components/Quote/QuoteCalendar';
import GuidanceFAQ from '../../../src/components/Quote/FAQ/GuidanceFAQ';
import { QuoteLayout } from '../../../src/components/Quote/V2/QuoteLayout';
import DrilldownFooter from '../../../src/components/Quote/V2/DrilldownFooter';
import getQuoteV2PageData, {
  QuoteV2PageProps,
  getQuoteFeedTypeByLocale,
  getQuoteNotFoundData,
} from '../../../src/quoteV2Utils';
import { useTranslation } from 'react-i18next';
import i18n, { LocaleType, setLanguageByHost } from '@benzinga/translate';
import { QuoteAltSuggestions } from '../../../src/components/Quote/QuoteAltSuggestions';
import { sanitizeHTML } from '@benzinga/frontend-utils';

const QuoteGuidancePage: React.FC<QuoteV2PageProps> = ({
  activeTab,
  advertiserProfile,
  altSuggestions,
  isAdvertiser,
  isETF,
  metaProps,
  peersChartData,
  profile,
  showGovLink,
  symbol,
}) => {
  const { t } = useTranslation(['quote', 'common'], { i18n });

  if (altSuggestions) {
    return <QuoteAltSuggestions {...altSuggestions} symbol={symbol} />;
  }

  return (
    <QuoteLayout
      advertiserProfile={advertiserProfile}
      defaultPath={activeTab}
      isAdvertiser={isAdvertiser}
      profile={profile}
      showGovLink={showGovLink}
      symbol={symbol}
    >
      <div className="main-div px-4">
        <div className="flex flex-col gap-2 mb-8">
          <SectionTitle level={1} uppercase={false}>
            {metaProps.title}
          </SectionTitle>
          <p className="section-description">
            {t('Quote.Paragraphs.guidance', { stockName: profile.richQuoteData?.name })}
          </p>
        </div>
        <QuoteCalendar
          calendar="guidance"
          companyName={profile?.richQuoteData?.companyStandardName}
          hideFilters
          initialData={profile?.guidanceSummary?.guidance}
          symbol={symbol}
        />
      </div>

      <DrilldownFooter
        advertiserProfile={advertiserProfile}
        isAdvertiser={isAdvertiser}
        isETF={isETF}
        leftSection={
          <>
            <SectionTitle level={3} size="2xl">
              {t('Quote.FAQ.faq')}
            </SectionTitle>
            <GuidanceFAQ guidanceSummary={profile?.guidanceSummary} quote={profile.richQuoteData} />
            <p>
              {/* eslint-disable-next-line @next/next/no-html-link-for-pages */}
              {t('Buttons.browse', { ns: 'common' })} {/* eslint-disable-next-line @next/next/no-html-link-for-pages */}
              <a href="/calendars/guidance">{t('Quote.Guidance.guidance-and-forecast')}</a>{' '}
              {t('Quote.Helpers.on-all-stocks')}
            </p>
          </>
        }
        peers={profile.quotes}
        peersChartData={peersChartData}
        symbol={symbol}
        type={profile.schedule.type}
      />
    </QuoteLayout>
  );
};

export default QuoteGuidancePage;

export const getServerSideProps = async ({ query: { ticker }, req, res }) => {
  if (req?.headers?.host) {
    setLanguageByHost(req?.headers?.host); // set language for server side translations
  }

  const symbol: string = sanitizeHTML(ticker) || '';
  const activeTab = 'guidance';

  const pageData = await getQuoteV2PageData(
    symbol,
    activeTab,
    getQuoteFeedTypeByLocale(i18n.language as LocaleType),
    req?.headers?.host,
  );

  if (!pageData || pageData?.props?.errorCode) {
    res.statusCode = pageData?.props?.errorCode || 503;
    return pageData;
  }

  return pageData;
};
