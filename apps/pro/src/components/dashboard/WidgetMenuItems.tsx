import React, { ReactNode } from 'react';
import styled from '@benzinga/themetron';
import { PermissionPrompt, usePermission, PermissionArgs, PermissionType } from '@benzinga/user-context';

import { connect } from 'react-redux';
import { useNavigate } from 'react-router-dom';

import { Widget, WidgetType } from '../../entities/widgetEntity';
import { RootState } from '../../redux/types';
import { isWindowWidthLargerThenSmall } from '../../selectors/uiSelectors';
import { selectWidgetsByType } from '../../selectors/widgetSelectors';
import { MenuItem } from './MenuItem';
import { UnpackedArray, objectShallowEqualWithExclude } from '@benzinga/utils';
import { ProContext } from '@benzinga/pro-tools';
import { Manifest } from '../../manifest';
import { MenuItemType } from '@benzinga/widget-tools';
import { SessionContext } from '@benzinga/session-context';
import { Session } from '@benzinga/session';
import { GridAdd, Help, Settings } from '@benzinga/valentyn-icons';
import { isProductionSite } from '../Site';
import { MainDiv, IconDiv, MenuSpan, MenuDiv, MenuItem as WidgetMenuItem } from './WidgetMenuItem';
import { MenuDividerType } from 'antd/es/menu/interface';
interface OwnProps {
  menuHoverable: boolean;
}
interface ReduxState {
  isWindowWidthLargerThenSmall: boolean;
  selectWidgetByType: (widgetType: WidgetType) => () => Widget | undefined;
}

type Props = OwnProps & ReduxState;

const WidgetMenuBarComponent: React.FC<Props> = React.memo(
  props => {
    const proContext = React.useContext(ProContext);
    const [displayPrompt, setDisplayPrompt] = React.useState<(PermissionArgs & PermissionType) | undefined>(undefined);

    const closePrompt = React.useCallback(() => setDisplayPrompt(undefined), []);

    const navigate = useNavigate();

    const openSettings = React.useCallback(() => {
      navigate(`/dashboard/preferences/settings/`);
    }, [navigate]);

    const goToHelp = React.useCallback(() => {
      const openHelpPage = proContext.openHelpPage;
      openHelpPage?.('');
    }, [proContext.openHelpPage]);

    const session = React.useContext(SessionContext);

    const [childrenItems, setChildrenItems] = React.useState<Record<string, MenuItemType[]>>({});

    const updateItems = React.useCallback(() => {
      const recursiveChildren = (children: MenuItemType[], manifest: UnpackedArray<typeof Manifest>) => {
        children.forEach(async child => {
          if (typeof child.children === 'function') {
            const children = child.children(session.getSession());
            if (children instanceof Promise) {
              const newChildren = await children;
              setChildrenItems(old => ({ ...old, [`${manifest.id} ${child.name}`]: newChildren }));
              recursiveChildren(newChildren, manifest);
            } else if (Array.isArray(children) && children.length > 0) {
              setChildrenItems(old => ({ ...old, [`${manifest.id} ${child.name}`]: children }));
              recursiveChildren(children, manifest);
            }
          } else if (Array.isArray(child.children) && child.children.length > 0) {
            recursiveChildren(child.children, manifest);
          }
        });
      };
      Manifest.filter(m => typeof m.menuItem === 'object').forEach(m => {
        recursiveChildren([{ ...m, ...(m.menuItem as MenuItemType) } as MenuItemType], m);
      });
      Manifest.filter(m => typeof m.menuItem === 'function').forEach(async m => {
        const children = (
          m.menuItem as ((session: Session) => MenuItemType) | ((session: Session) => Promise<MenuItemType>)
        )?.(session.getSession());
        recursiveChildren(
          [
            {
              ...m,
              ...(children instanceof Promise ? await children : children),
              permission: m.permission ? [m.permission] : [],
            },
          ],
          m,
        );
      });
    }, [session]);

    React.useEffect(() => {
      updateItems();
    }, [updateItems]);

    const isAdmin = usePermission('#', '#');

    const menuItems = React.useMemo(() => {
      return [
        ...Manifest.filter(manifest => manifest.menuItem)
          .filter(m => (m.state === 'alpha' ? !isProductionSite() : true))
          .filter(manifest => (manifest.state === 'deprecated' ? isAdmin : true))
          .map(manifest => {
            const menuChildren =
              typeof manifest?.menuItem === 'object' && manifest?.menuItem !== null && 'children' in manifest.menuItem
                ? typeof manifest.menuItem.children === 'function'
                  ? childrenItems[`${manifest.id} ${manifest?.name}`]
                  : manifest.menuItem.children
                : undefined;

            const baseItem = {
              key: `${manifest.id} ${manifest.name}`,
              label: (
                <WidgetMenuItem
                  childrenItems={childrenItems}
                  item={
                    manifest.menuItem === true
                      ? { ...manifest, permission: manifest.permission ? [manifest.permission] : [] }
                      : ({ ...manifest, ...(manifest.menuItem as MenuItemType) } as MenuItemType)
                  }
                  key={`${manifest.id}`}
                  manifest={manifest}
                  showHelpers={!menuChildren}
                />
              ),
              popupClassName: 'menubar-submenu-popup',
              type: 'submenu',
            } as any;

            if (menuChildren && menuChildren.length > 0) {
              baseItem.children = menuChildren.map(item => ({
                key: `${manifest.id} ${item.name}`,
                label: (
                  <WidgetMenuItem
                    childrenItems={childrenItems}
                    item={item}
                    key={`${manifest.id} ${item.name}`}
                    manifest={manifest}
                  />
                ),
              }));
            }

            return baseItem;
          }),
        {
          type: 'divider',
        } as MenuDividerType,
        {
          key: 'settings',
          label: (
            <MainDiv>
              <MenuDiv>
                <MenuSpan>
                  <IconDiv height="1.5rem" width="1.25rem">
                    <Settings height="1.5rem" width="1.25rem" />
                  </IconDiv>
                  <span style={{ marginLeft: `8px` }}>Tool Settings</span>
                </MenuSpan>
              </MenuDiv>
            </MainDiv>
          ),
          onClick: openSettings,
        },
        {
          key: 'help',
          label: (
            <MainDiv>
              <MenuDiv>
                <MenuSpan>
                  <IconDiv height="1.2rem" width="1.2rem">
                    <Help height="1.5rem" width="1.25rem" />
                  </IconDiv>
                  <span style={{ marginLeft: `8px` }}>Help</span>
                </MenuSpan>
              </MenuDiv>
            </MainDiv>
          ),
          onClick: goToHelp,
        },
      ];
    }, [openSettings, goToHelp, isAdmin, childrenItems]);

    return (
      <>
        <MenuItem menu={menuItems} menuHoverable={props.menuHoverable} onClick={updateItems} placement="left">
          <IconWrapper>
            <GridAdd height="1.25em" title="Add Tool To Workspace" width="1.25em" />
          </IconWrapper>
        </MenuItem>
        {displayPrompt && <PermissionPrompt {...displayPrompt} onClose={closePrompt} />}
      </>
    );
  },
  (p, n) => objectShallowEqualWithExclude(p, n, ['selectWidgetByType']),
);

const IconWrapper = styled.div`
  color: ${props => props.theme.colors.foreground} !important;
`;

const mapStateToProps = (state: RootState): ReduxState => ({
  isWindowWidthLargerThenSmall: isWindowWidthLargerThenSmall(state),
  selectWidgetByType:
    <T extends WidgetType>(widgetType: T) =>
    () => {
      const widgets = selectWidgetsByType(state, { type: widgetType }) as unknown as Extract<Widget, { type: T }>[];
      return widgets[0];
    },
});

export default connect<ReduxState, Record<string, never>, OwnProps, RootState>(mapStateToProps)(
  WidgetMenuBarComponent as any,
) as React.FC<OwnProps>;
