'use client';
import React from 'react';

import styled from '@benzinga/themetron';
import { SessionContext } from '@benzinga/session-context';
import { TC } from '@benzinga/themetron';

import { Button, Checkbox } from 'antd';
import type { CheckboxProps } from 'antd';

import { arrayDeepSymmetricDifference } from '@benzinga/utils';
import { Signal, SignalType } from '@benzinga/signals-manager';

import { TrackingManager } from '@benzinga/tracking-manager';

export enum NotificationType {
  desktop = 'desktop',
  sound = 'sound',
  voice = 'voice',
}

const testSignals: TestSignalConfig[] = [
  {
    description: 'TEST SIGNAL -- APRN New 52 week Low of 0.89',
    signalType: SignalType.fiftyTwoWeekLow,
    symbol: 'APRN',
    testName: '52-Week Low Test Signal',
  },
  {
    description: 'TEST SIGNAL -- Price spike DOWN -0.007 (-40.23%)',
    properties: {
      amount: '-0.007',
      percent: '-40.23',
    },
    signalType: SignalType.spike_down,
    symbol: 'APRN',
    testName: 'Price Spike Down Test Signal',
  },
];

interface Props {
  addSignal: (signal: Signal) => void;
  enabledNotifications: NotificationType[];
  setEnabledNotifications: (enabledNotifications: NotificationType[]) => void;
}

interface TestSignalConfig extends Pick<Signal, 'description' | 'signalType' | 'symbol'> {
  properties?: any;
  testName: string;
}

export const SignalsNotifications: React.FC<Props> = props => {
  const session = React.useContext(SessionContext);

  const addTestSignal = React.useCallback(
    ({ testName: _, ...testSignal }: TestSignalConfig) =>
      () => {
        session.getManager(TrackingManager).trackNotificationEvent('add', {
          notification_type: 'signals_test',
        });
        const signal: Signal = {
          date: new Date().toISOString(),
          id: new Date().getTime(),
          key: new Date().getTime(),
          ...(testSignal as any),
        };
        props.addSignal(signal);
      },
    [props, session],
  );

  const onNotificationChange = (checkedValues: CheckboxProps['value'][]) => {
    const { enabledNotifications, setEnabledNotifications } = props;
    const symbolWasRemoved = enabledNotifications.length > checkedValues.length;
    const title = `Signals Notification ${symbolWasRemoved ? 'Removed' : 'Added'}`;

    const changedNotification = arrayDeepSymmetricDifference(enabledNotifications, checkedValues);

    session.getManager(TrackingManager).trackNotificationEvent(symbolWasRemoved ? 'add' : 'remove', {
      notification_type: 'signals',
    });

    setEnabledNotifications(checkedValues as NotificationType[]);
  };

  const renderTestSignalButton = (testSignal: TestSignalConfig) => {
    return (
      <Button key={testSignal.testName} onClick={addTestSignal(testSignal)}>
        {testSignal.testName}
      </Button>
    );
  };

  const { enabledNotifications } = props;

  return (
    <SignalsNotificationsWrapper>
      <SignalsTextBlock>
        <span>Notifications are a way to bring your attention to new signals when they arrive.</span>
      </SignalsTextBlock>

      <StyledCheckboxGroup onChange={onNotificationChange} value={enabledNotifications}>
        <TC.Row>
          <StyledCheckbox value={NotificationType.desktop}>
            <CheckboxContentWrapper>
              <NotificationTitle>Notification</NotificationTitle>
            </CheckboxContentWrapper>
            <NotificationDescriptionWrapper>
              <NotificationDescription>
                Sends a notification to your computer when a new signal arrives. Notification configuration is
                accessible in the main settings.
              </NotificationDescription>
            </NotificationDescriptionWrapper>
          </StyledCheckbox>
        </TC.Row>
        <TC.Row>
          <StyledCheckbox value={NotificationType.sound}>
            <CheckboxContentWrapper>
              <NotificationTitle>Sound</NotificationTitle>
            </CheckboxContentWrapper>
            <NotificationDescriptionWrapper>
              <NotificationDescription>
                Plays a sound through your speakers when a new signal arrives
              </NotificationDescription>
            </NotificationDescriptionWrapper>
          </StyledCheckbox>
        </TC.Row>
        <TC.Row>
          <StyledCheckbox value={NotificationType.voice}>
            <CheckboxContentWrapper>
              <NotificationTitle>Synthesized Voice</NotificationTitle>
            </CheckboxContentWrapper>
            <NotificationDescriptionWrapper>
              <NotificationDescription>
                Signals are spoken aloud through your speakers. Due to the number of Session High/Low updates, they are
                disabled for those signal categories
              </NotificationDescription>
            </NotificationDescriptionWrapper>
          </StyledCheckbox>
        </TC.Row>
      </StyledCheckboxGroup>

      <span>You can test your notification settings by triggering a test signal:</span>
      <TestSignalButtons>{testSignals.map(renderTestSignalButton)}</TestSignalButtons>
    </SignalsNotificationsWrapper>
  );
};

const SignalsNotificationsWrapper = styled(TC.Column)`
  flex-basis: 100%;
  min-width: 150px;
`;

const SignalsTextBlock = styled.div`
  margin-bottom: 0.5em;
`;

const StyledCheckboxGroup = styled(Checkbox.Group)`
  margin-bottom: 5px;
  margin-top: 5px;
  flex-direction: column;
  flex-grow: 1;
`;

const StyledCheckbox = styled(Checkbox)`
  align-items: center;
  height: 38px;
`;

const CheckboxContentWrapper = styled(TC.Column)`
  display: inline-flex;
`;

const NotificationTitle = styled(TC.Span)`
  color: ${props => props.theme.colors.foregroundActive};
  display: inline-block;
  font-weight: 600;
`;

const NotificationDescriptionWrapper = styled.div`
  margin-left: 2em;
`;

const NotificationDescription = styled.span`
  font-size: 10px;
  font-style: italic;
`;

const TestSignalButtons = styled(TC.Row)`
  margin-top: 5px;
`;
