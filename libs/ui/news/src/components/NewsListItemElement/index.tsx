import React from 'react';
import styled from '@benzinga/themetron';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import { StoryObject } from '@benzinga/advanced-news-manager';
import { News } from '@benzinga/basic-news-manager';
import { getSponsoredContentLabel } from '@benzinga/article-manager';
import { Ticker } from '@benzinga/ticker-ui';
import { PostDateType, PostElapsed } from '../PostCard/PostElapsed';
import { BzImage } from '@benzinga/image';
import { appEnvironment, appName, formatImageUrl } from '@benzinga/utils';
import { NewsElementCommentCount } from '../NewsElementCommentCount';
import { NoFirstRender } from '@benzinga/hooks';
import { InView } from 'react-intersection-observer';
import { isGlobalImpressionStored, storeGlobalImpression } from '@benzinga/content-manager';
import { TrackingManager } from '@benzinga/tracking-manager';
import { SessionContext } from '@benzinga/session-context';

export interface NewsListItemElementProps {
  contentId?: string;
  title?: string;
  variant?: 'primary' | 'secondary';
  node: StoryObject | News;
  showAuthor?: boolean;
  showImage?: boolean;
  showTeaser?: boolean;
  showTicker?: boolean;
  dateType?: PostDateType;
  showTime?: boolean;
  locale?: string;
  showCommentsIcon?: boolean;
  inLineTicker?: boolean;
}

export const NewsListItemElement: React.FC<NewsListItemElementProps> = ({
  contentId,
  dateType = 'relative',
  inLineTicker = false,
  locale = 'en',
  node,
  showAuthor = false,
  showCommentsIcon = false,
  showImage = false,
  showTeaser = false,
  showTicker = true,
  showTime = true,
  variant = 'primary',
}) => {
  const dataAction = contentId ? `${contentId} Article Click` : 'Article Click';
  const tickers = Array.isArray(node?.stocks) ? node.stocks.map(ticker => ticker.name) : [];
  const sponsoredData = getSponsoredContentLabel(node);
  const href = node.url ? node.url : `https://www.benzinga.com/node/${node.id}`;
  const date = node.created || node.updated;
  const image = formatImageUrl(node);
  const imageSize = appEnvironment().isApp(appName.india) ? 72 : inLineTicker && showTeaser ? 126 : 90;

  const imageAlt = (node as StoryObject)?.assets?.[0]?.title ?? '';
  const session = React.useContext(SessionContext);

  const registerImpression = isVisible => {
    if (isVisible && !isGlobalImpressionStored(`sponsored-item-${node.id}`)) {
      storeGlobalImpression(`sponsored-item-${node.id}`);
      session.getManager(TrackingManager).trackCampaignEvent('view', {
        partner_id: (node?.meta?.Reach?.Disclosure?.tid || 0).toString(),
        unit_type: `sponsored-item-${node.id}`,
      });
    }
  };

  return (
    <NewsListItemElementWrapper className={`news-block ${variant}`} imageSize={imageSize}>
      {variant === 'primary' ? (
        <div className="news-block-headline">
          <div className="flex w-full">
            {image && showImage && (
              <div className="news-block-image-wrapper">
                <BzImage
                  alt={imageAlt}
                  blurDataURL={`${image}?width=30`}
                  height={imageSize}
                  layout="fixed"
                  loaderProps={{ width: 300 }}
                  loading="lazy"
                  objectFit="cover"
                  src={image}
                  width={imageSize}
                />
              </div>
            )}
            <div className={`flex flex-col justify-between w-full ${showImage && !image ? 'pl-2' : ''}`}>
              {sponsoredData.isSponsored ? (
                <InView onChange={isVisible => registerImpression(isVisible)}>
                  <span className="news-item-sponsored-tag">{sponsoredData.label}</span>
                </InView>
              ) : null}
              <a data-action={dataAction} href={href?.replace('https://www.benzinga.com', '')}>
                {node.title && (
                  <DoubleLine className="article-title">
                    <span dangerouslySetInnerHTML={{ __html: sanitizeHTML(node.title) }} />
                  </DoubleLine>
                )}
                {showTeaser && node.teaser && (
                  <DoubleLine className="article-teaser">
                    <span dangerouslySetInnerHTML={{ __html: sanitizeHTML(node.teaser) }} />
                  </DoubleLine>
                )}
              </a>
              <div
                className={
                  inLineTicker
                    ? 'flex flex-col md:flex-row-reverse items-start md:items-center justify-between py-1 pr-1 w-full'
                    : ''
                }
              >
                {showAuthor && !sponsoredData.isSponsored && node.author ? (
                  <span className="article-author text-sm font-semibold mr-2">{node.author}</span>
                ) : null}
                <div className="flex items-center">
                  {!sponsoredData.isSponsored && date ? (
                    <PostElapsed
                      className="article-datetime"
                      created={date}
                      dateType={dateType}
                      locale={locale}
                      showTime={showTime}
                    />
                  ) : null}
                  {showCommentsIcon && !sponsoredData.isSponsored && (
                    <NoFirstRender>
                      <div className="flex items-center ml-2 text-sm text-gray-500">
                        <span className="mr-2 font-light">|</span>
                        <NewsElementCommentCount id={node.id} url={href} />
                      </div>
                    </NoFirstRender>
                  )}
                </div>
                <React.Suspense>
                  {showTicker && tickers && tickers?.length ? <TickerList tickers={tickers} /> : null}
                </React.Suspense>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex">
          {date && (
            <div className="whitespace-nowrap pr-2 date-box">
              <PostElapsed
                className="article-datetime"
                created={date}
                dateType={dateType}
                locale={locale}
                showTime={showTime}
              />
            </div>
          )}
          <div>
            <ul className="flex flex-wrap gap-1">
              {showTicker &&
                tickers &&
                tickers.map((ticker, index) => {
                  return (
                    <li key={index}>
                      <Ticker key={index} symbol={ticker} />
                    </li>
                  );
                })}
            </ul>
            <span>
              {node?.title && (
                <a
                  className="news-block-headline"
                  dangerouslySetInnerHTML={{ __html: sanitizeHTML(node.title) }}
                  data-action={dataAction}
                  href={href.replace('https://www.benzinga.com', '')}
                />
              )}
            </span>
          </div>
        </div>
      )}
    </NewsListItemElementWrapper>
  );
};

export const TickerList = ({ tickers }: { tickers: string[] }) => {
  return (
    <TickerListWrapper className="ticker-list">
      <div className="ticker-list-container">
        {tickers.slice(0, tickers.length > 4 ? 3 : 4).map((ticker: string, i: number) => {
          return <Ticker key={i} symbol={ticker?.replace(/^\$([A-z]+)/, '$1/USD')} />;
        })}
        {tickers.length > 4 ? (
          <OtherTickersWrapper>
            <div className="other-tickers-label">{tickers.length - 3} others</div>
            <div className="other-tickers">
              {tickers.slice(tickers.length > 4 ? 3 : 4, tickers.length + 1).map((ticker: string, i: number) => {
                return (
                  <div key={i}>
                    <Ticker hidePopup={true} symbol={ticker?.replace(/^\$([A-z]+)/, '$1/USD')} />
                  </div>
                );
              })}
            </div>
          </OtherTickersWrapper>
        ) : null}
      </div>
    </TickerListWrapper>
  );
};

const NewsListItemElementWrapper = styled.div<{ imageSize: number }>`
  &.news-block {
    line-height: ${({ theme }) => theme.lineHeight.normal};
    .news-block-headline {
      a {
        color: ${({ theme }) => theme.colorPalette.black};

        &:hover {
          color: #69a5ff;
        }
      }

      .news-item-sponsored-tag {
        display: block;
        font-size: 13px;
        color: ${({ theme }) => theme.colorPalette.blue500};
        font-weight: ${({ theme }) => theme.fontWeight.semibold};
      }
    }
    .news-block-image-wrapper {
      width: ${props => props.imageSize}px;
      height: ${props => props.imageSize}px;
      min-width: ${props => props.imageSize}px;
      margin-right: 1rem;
      border-radius: ${({ theme }) => theme.borderRadius.default};
      overflow: hidden;
    }
    .article-title {
      font-size: ${({ theme }) => theme.fontSize.lg};
      font-weight: ${({ theme }) => theme.fontWeight.semibold};
    }
    .article-teaser {
      font-size: ${({ theme }) => theme.fontSize.md};
      font-weight: ${({ theme }) => theme.fontWeight.normal};
    }

    .date-box {
      min-width: 130px;
    }
  }
`;

const TickerListWrapper = styled.div`
  &.ticker-list {
    .ticker-list-container {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-top: 4px;
    }
  }
`;

const OtherTickersWrapper = styled.div`
  .other-tickers {
    position: absolute;
    display: none;
    max-height: 120px;
    margin: 2px 0;
    overflow-y: scroll;
    z-index: 50;
  }
  .other-tickers,
  .other-tickers-label {
    align-items: baseline;
    background-color: white;
    border-radius: ${({ theme }) => theme.borderRadius.default};
    border: solid 1px ${({ theme }) => theme.colorPalette.gray300};
    color: ${({ theme }) => theme.colorPalette.gray600};
    font-weight: bold;
    padding: 0px 4px;
    font-size: 14px;
  }
  .other-tickers-label {
    display: inline-flex;
  }
  .ticker-box {
    border: none;
  }
  &:hover {
    .other-tickers {
      display: block;
    }
  }
`;

const DoubleLine = styled.div`
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  @media (max-width: 768px) {
    -webkit-line-clamp: 4;
    font-weight: normal;
  }
`;
