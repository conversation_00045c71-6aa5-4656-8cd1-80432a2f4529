import { render } from '@testing-library/react-native';
import * as React from 'react';
import BenzingaHTML from '../BenzingaHTML';
import { News } from '@benzinga/advanced-news-manager';

describe('Benzinga HTML', () => {
  test('renders correctly', () => {
    const showQuote = jest.fn().mockImplementation((ticker: string) => console.log(ticker));
    const showArticle = jest.fn().mockImplementation((article: News) => console.log(article));
    const appComponent = render(<BenzingaHTML showQuote={showQuote} showArticle={showArticle} />);
    expect(appComponent.toJSON()).toMatchSnapshot();
  });

  afterAll(() => {
    jest.resetAllMocks();
  });
});
