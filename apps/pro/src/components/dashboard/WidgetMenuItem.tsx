import React from 'react';
import { ProContext } from '@benzinga/pro-tools';
import styled from '@benzinga/themetron';
import { Menu, Tooltip } from 'antd';
import { UnpackedArray } from '@benzinga/utils';
import { MenuItemType, WidgetType } from '@benzinga/widget-tools';
import { Manifest } from '../../manifest';
import { useSelector } from 'react-redux';
import { selectWidgetsByType } from '../../selectors/widgetSelectors';
import { Widget } from '../../entities/widgetEntity';
import { RootState } from '../../redux/types';
import { DevelopmentStageCornerBanner } from '../ui/DevelopmentStageCornerBanner';
import { DevelopmentStage } from '../../entities/developmentStageEntity';
import { isWindowWidthLargerThenSmall as isWindowWidthLTS } from '../../selectors/uiSelectors';
import { Layout } from '@benzinga/valentyn-icons';
import { TrackingManager } from '@benzinga/tracking-manager';
import { SessionContext } from '@benzinga/session-context';
import { Popout } from '@benzinga/themed-icons';

export const MenuItem: React.FC<{
  childrenItems: Record<string, MenuItemType[]>;
  item: MenuItemType;
  manifest: UnpackedArray<typeof Manifest>;
  showHelpers?: boolean;
}> = props => {
  const { childrenItems, item, manifest, showHelpers = true, ...internals } = props;
  const session = React.useContext(SessionContext);
  const proContext = React.useContext(ProContext);
  const { isWindowWidthLargerThenSmall, selectWidgetByType } = useSelector((state: RootState) => ({
    isWindowWidthLargerThenSmall: isWindowWidthLTS(state),
    selectWidgetByType:
      <T extends WidgetType>(widgetType: T) =>
      () => {
        const widgets = selectWidgetsByType(state, { type: widgetType }) as unknown as Extract<Widget, { type: T }>[];
        return widgets[0];
      },
  }));

  const handleNewWidget = React.useCallback(
    (item: MenuItemType, manifest: UnpackedArray<typeof Manifest>) => () => {
      const addWidget = proContext.addWidget;

      session.getManager(TrackingManager).trackLinkEvent('click', {
        link_action: 'open_widget',
        link_id: manifest.id,
        link_type: 'menu_bar',
        value: manifest.name,
      });

      if (!isWindowWidthLargerThenSmall) {
        const widget = selectWidgetByType(manifest.id)();
        if (!widget) {
          addWidget(manifest.id, undefined, 'none');
        }
      } else {
        if (item.config) addWidget(manifest.id, item.config);
        else addWidget(manifest.id);
      }
    },
    [session, isWindowWidthLargerThenSmall, proContext.addWidget, selectWidgetByType],
  );

  const handlePopoutClick = React.useCallback(
    (item: MenuItemType, manifest: UnpackedArray<typeof Manifest>) => async () => {
      if (item.onPopout) {
        item.onPopout();
        return;
      }

      session.getManager(TrackingManager).trackLinkEvent('click', {
        link_action: 'open_widget',
        link_id: manifest.id,
        link_type: 'menu_bar_popout',
        value: manifest.name,
      });

      const widgetId = await proContext.addWidget(manifest.id, item.config, 'none');
      if (widgetId) {
        proContext.addPopoutForWidget({ widgetId });
        proContext.removeWidget(widgetId);
      }
    },
    [session, proContext],
  );

  const addItem = React.useCallback(
    (_internals: object, item: MenuItemType, manifest: UnpackedArray<typeof Manifest>) => (
      <MainDiv key={`${item}`}>
        <MenuDiv onClick={handleNewWidget(item, manifest)}>
          <MenuSpan>
            {manifest.state === 'beta' && <DevelopmentStageCornerBanner isMenuStyle stage={DevelopmentStage.beta} />}
            {manifest.state === 'alpha' && <DevelopmentStageCornerBanner isMenuStyle stage={DevelopmentStage.alpha} />}
            {manifest.state === 'deprecated' && (
              <DevelopmentStageCornerBanner isMenuStyle stage={DevelopmentStage.deprecated} />
            )}
            {item.icon && (
              <IconDiv height="1.2rem" width="1.2rem">
                <item.icon />
              </IconDiv>
            )}
            <span
              {...(item.icon !== undefined
                ? { style: { fontWeight: 'bold', marginLeft: `8px` } }
                : { style: { fontWeight: 'bold', marginLeft: `5px` } })}
            >
              {item.name}
            </span>
          </MenuSpan>

          {showHelpers && (
            <HoverIconDiv>
              <Tooltip mouseEnterDelay={1} placement="bottomLeft" title={`Open ${item.name} in workspace`}>
                <StyledWidgetIcon as={Layout} />
              </Tooltip>
            </HoverIconDiv>
          )}
        </MenuDiv>

        {showHelpers && (
          <PopoutSpan>
            <Tooltip mouseEnterDelay={1} placement="bottomLeft" title={`Open ${item.name} in popout`}>
              <Popout height="1.5rem" onClick={handlePopoutClick(item, manifest)} width="1.25rem" />
            </Tooltip>
          </PopoutSpan>
        )}
      </MainDiv>
    ),
    [handleNewWidget, handlePopoutClick, showHelpers],
  );

  return addItem(internals, item, manifest);
};

const StyledWidgetIcon = styled.div`
  color: ${props => props.theme.colors.foregroundInactive};
`;

export const MainDiv = styled.div`
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  div {
    opacity: 0;
    transition: opacity 0.2s ease-out;
  }
  svg {
    opacity: 0.35;
  }
  &:hover {
    div {
      opacity: 1;
      transition: opacity 0.2s ease-out;
    }
    svg {
      opacity: 1;
    }
    span {
      background-color: ${props => props.theme.colors.backgroundInactive};
      border-radius: 5px;
      -moz-transition: all 0.51s;
      -o-transition: all 0.51s;
      -webkit-transition: all 0.51s;
      transition: opacity 0.7s ease-in-out;
    }
  }
`;

export const IconDiv = styled.div<{ height: string; width: string }>`
  display: contents;
  svg {
    height: ${props => props.height};
    width: ${props => props.width};
    color: ${props => props.theme.colors.foregroundInactive};
    padding: 0px;
  }
`;

export const MenuSpan = styled.span`
  display: flex;
  width: 100%;
  align-items: center;
  height: 100%;
`;

export const MenuDiv = styled.span`
  display: flex;
  width: 100%;
  align-items: center;
  height: 100%;
  padding: 6px 7px 6px 8px;
`;

const PopoutSpan = styled.div`
  display: flex;
  margin-left: 5px;
  margin-right: 2px;
  padding: 6px;
  transition: opacity 0.5s ease-in-out;
  &:hover {
    background-color: ${props => props.theme.colors.backgroundInactive};
    color: white;
    border-radius: 5px;
    -moz-transition: all 0.51s;
    -o-transition: all 0.51s;
    -webkit-transition: all 0.51s;
    transition: opacity 0.7s ease-in-out;
  }
  svg {
    cursor: pointer;
  }
`;

const HoverIconDiv = styled.div`
  display: flex;
  height: 100%;
  justify-content: space-between;
  align-items: center;
  svg {
    cursor: pointer;
  }
`;
