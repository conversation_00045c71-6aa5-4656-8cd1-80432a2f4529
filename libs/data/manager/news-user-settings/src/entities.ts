import { ExpressionObject, StockSymbol } from '@benzinga/session';
import { UserSettingsFeed } from '@benzinga/user-settings';
import { CategoryId, NewsfeedFieldType, SourceId } from '@benzinga/advanced-news-manager';
import { WatchlistId } from '@benzinga/watchlist-manager';
import { WidgetLinkingID } from '@benzinga/widget-linking';
import { FilterObject } from '@benzinga/quotes-v3-fields-manager';
import { LegacyFilter } from '@benzinga/scanner-config-manager';

export type AllNewsUserSetting = NewsUserSettingV1 | NewsUserSettingV2 | NewsUserSettingV3;

export type NewsVersion = NewsVersionV3;
export type NewsConfig = NewsConfigV3;
export type NewsUserSetting = NewsUserSettingV3;
export type NewsSettingsFeed = UserSettingsFeed<'news', NewsVersion, NewsUserSetting, AllNewsUserSetting>;

export type DisplayType = 'Abstract' | 'Expanded' | 'Title';

export interface Importance {
  id: 'off' | 'low' | 'mid' | 'high' | 'custom';
  title: string;
  description: string;
  filters: {
    filterId: string;
    action: 'include' | 'exclude';
  }[];
}

export type NewsfeedFeedSettingsV12 = {
  denyRelevantCategories: Partial<Record<SourceId, CategoryId[]>>;
  denySourceGroups: SourceId[];
  denySources: SourceId[];
  denyWatchlists: WatchlistId[];

  relevantCategories: Partial<Record<SourceId, CategoryId[]>>;
  sourceGroups: SourceId[];
  importance: Importance;
  keywords: string[];
  screenerFilters: FilterObject[];
  sources: SourceId[];
  symbols: StockSymbol[];
  watchlists: WatchlistId[];
  createdAt: [string | null, string | null];
};

export interface NewsConfigV3 {
  isFavorite?: boolean;
  title?: string;
  id?: string;
  displayType?: DisplayType;
  feedExpression: ExpressionObject<NewsfeedFieldType>;
  feedSettings: NewsfeedFeedSettingsV12;
  sendGroup: WidgetLinkingID;
}

export type NewsVersionV3 = 3;
export interface NewsUserSettingV3 {
  dontShowTodaysMarketMovingNewsDialog: boolean;
  configs: NewsConfigV3[];
  version: NewsVersionV3;
}

export type NewsfeedFeedSettingsV11 = {
  relevantCategories: Partial<Record<SourceId, CategoryId[]>>;
  sourceGroups: SourceId[];
  importance: Importance;
  categories: CategoryId[];
  keywords: string[];
  screenerFilters: FilterObject[];
  sources: SourceId[];
  symbols: StockSymbol[];
  watchlists: WatchlistId[];
  createdAt: [string | null, string | null];
};
export interface NewsConfigV2 {
  isFavorite?: boolean;
  title?: string;
  id?: string;
  displayType?: DisplayType;
  feedExpression: ExpressionObject<NewsfeedFieldType>;
  feedSettings: NewsfeedFeedSettingsV11;
  sendGroup: WidgetLinkingID;
}

export type NewsVersionV2 = 2;
export interface NewsUserSettingV2 {
  dontShowTodaysMarketMovingNewsDialog: boolean;
  configs: NewsConfigV2[];
  version: NewsVersionV2;
}

export type NewsfeedFeedSettingsV10 = {
  relevantCategories: Partial<Record<SourceId, CategoryId[]>>;
  sourceGroups: SourceId[];
  importance: Importance;
  categories: CategoryId[];
  keywords: string[];
  screenerFilters: LegacyFilter[];
  sources: SourceId[];
  symbols: StockSymbol[];
  watchlists: WatchlistId[];
  createdAt: [string | null, string | null];
};

export interface NewsConfigV1 {
  isFavorite?: boolean;
  title?: string;
  id?: string;
  displayType?: DisplayType;
  feedExpression: ExpressionObject<NewsfeedFieldType>;
  feedSettings: NewsfeedFeedSettingsV10;
  sendGroup: WidgetLinkingID;
}

export type NewsVersionV1 = 1;
export interface NewsUserSettingV1 {
  dontShowTodaysMarketMovingNewsDialog: boolean;
  configs: NewsConfigV1[];
  version: NewsVersionV1;
}
