import React from 'react';
import styled from '@benzinga/themetron';
import { ReactNode } from 'react';
import { PreferencesToggleNew } from './toggleNew';
import { Row } from 'antd';
import he from 'he';

interface SwitchBoxProps {
  title: string;
  helpIcon?: ReactNode;
  active: boolean;
  onClick(value: boolean): void;
}

export const SwitchBox: React.FC<SwitchBoxProps> = props => {
  return (
    <Wrapper>
      <Row style={{ flexWrap: 'nowrap' }}>
        <DescriptionRow>
          <TitleWrapper>
            <Title>{he.decode(props.title)}</Title>
          </TitleWrapper>
          {props.helpIcon}
        </DescriptionRow>
        <PreferencesToggleNew active={props.active} onClick={props.onClick} />
      </Row>
    </Wrapper>
  );
};

const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  border: 1px ${props => props.theme.colors.actionFocus} solid;
  border-radius: 10px;
  padding: 8px;
  flex-grow: 1;
`;

const TitleWrapper = styled.div`
  display: flex;
  align-items: center;
`;

const Title = styled.div`
  font-family: 'Nimbus Sans D OT';
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  color: ${props => props.theme.colors.foregroundActive};
  margin: 0px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
`;

const DescriptionRow = styled(Row)`
  flex-grow: 1;
  flex-wrap: nowrap;
`;
