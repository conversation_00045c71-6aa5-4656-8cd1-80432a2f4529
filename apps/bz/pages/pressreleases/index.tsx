import React, { ReactElement } from 'react';
import { GetServerSideProps, NextPage } from 'next';
import { Meta, MetaProps } from '@benzinga/seo';
import { Layout, LayoutBox } from '@benzinga/core-ui';
import Error from '../_error';
import styled from '@benzinga/themetron';
import { PageType } from '@benzinga/seo';
import { ContentFeed, NewsListItemElement } from '@benzinga/news';
import { AdvancedNewsManager, StoryObject } from '@benzinga/advanced-news-manager';
import { BasicNewsManager } from '@benzinga/basic-news-manager';
import { getGlobalSession } from '../api/session';
import LazyLoad from 'react-lazyload';

const AdMgid = React.lazy(() => import('../../src/components/Ads/MGID/AdMgid'));

const RaptiveAdPlaceholder = React.lazy(() =>
  import('@benzinga/ads').then(module => ({
    default: module.RaptiveAdPlaceholder,
  })),
);

const CallToActionForm = React.lazy(() =>
  import('@benzinga/forms-ui').then(module => {
    return { default: module.CallToActionForm };
  }),
);

interface Props {
  news: StoryObject[];
  pressReleaseTypes: string | null;
  errorCode?: number;
  metaProps?: MetaProps;
}

const GetLayoutSidebar: React.FC = () => {
  return (
    <>
      <LayoutBox>
        <React.Suspense fallback={<div className="h-[250px] w-[300px] mb-2" />}>
          <RaptiveAdPlaceholder className="w-[300px] mb-2 overflow-hidden" type="static-sidebar" />
        </React.Suspense>
      </LayoutBox>
      <LayoutBox>
        <React.Suspense>
          <CallToActionForm
            // hubspotFormId="************************************"
            beehiivFormId="c03f46e3-b180-439f-8cf4-103cbf2ac567"
            subtitle="Enter your email to get Benzinga's ultimate morning update: The PreMarket Activity Newsletter"
            title="Beat the Market With Our Free Pre-Market Newsletter"
          />
        </React.Suspense>
      </LayoutBox>
    </>
  );
};

const LayoutBelow: React.FC = () => {
  return (
    <LayoutBox>
      <LazyLoad offset={100} once>
        <AdMgid />
      </LazyLoad>
    </LayoutBox>
  );
};

const GetLayoutMain: React.FC<{ pressReleaseTypes: string | null; news: StoryObject[] }> = ({
  news,
  pressReleaseTypes,
}) => {
  return (
    <Container>
      <LayoutBox className="border-t border-gray-400 pt-1">
        <ContentFeed
          contentId={pageTitle}
          isInfinite
          limit={15}
          loadMore={!!pressReleaseTypes}
          newsItemElement={(node: StoryObject) => {
            return <NewsListItemElement contentId="Press Releases" key={node.id} node={node} variant="secondary" />;
          }}
          nodes={news}
          query={{
            type: pressReleaseTypes ?? '',
          }}
        />
      </LayoutBox>
      <LayoutBelow />
    </Container>
  );
};

const pageTitle = 'Latest Press Releases';
const pageDescription = 'The latest Press Releases updated realtime';

const PressReleasePage: NextPage<Props> = ({ errorCode, news, pressReleaseTypes, metaProps }: Props) => {
  if (!news || !news.length) {
    return <Error statusCode={errorCode || 503} />;
  }

  return (
    <>
      <Meta description={pageDescription} title={pageTitle} canonical={metaProps?.canonical} />
      <Layout
        layoutMain={GetLayoutMain({ news: news ?? [], pressReleaseTypes }) as ReactElement}
        layoutSidebar={GetLayoutSidebar({}) as ReactElement}
        title={pageTitle}
      />
    </>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ res }) => {
  const session = getGlobalSession();
  const newsManager = await session.getManager(AdvancedNewsManager);
  const basicNewsManager = await session.getManager(BasicNewsManager);
  const pressReleaseTypesRes = await newsManager.getPressReleases();
  const pressReleaseTypes = pressReleaseTypesRes?.ok?.join(',');
  const pressReleaseNewsReq = basicNewsManager.simplyQueryNews({}, { limit: 45, type: pressReleaseTypes });

  try {
    const [pressReleaseNewsRes] = await Promise.all([pressReleaseNewsReq]);
    const pressReleaseNews = pressReleaseNewsRes?.ok ?? null;

    return {
      props: {
        metaProps: {
          canonical: 'https://www.benzinga.com/pressreleases',
          pageType: PageType.Website,
          robots: 'nofollow',
        },
        news: pressReleaseNews,
        pressReleaseTypes,
      },
    };
  } catch (error) {
    res.statusCode = 503;
    console.error('PressReleasePage Error:', error);
    return {
      props: {
        errorCode: 503,
      },
    };
  }
};

const Container = styled.div`
  .news-block.secondary {
    font-weight: ${({ theme }) => theme.fontWeight.semibold};
    .news-block-headline {
      color: ${({ theme }) => theme.colorPalette.blue700};
    }
  }
`;

export default PressReleasePage;
