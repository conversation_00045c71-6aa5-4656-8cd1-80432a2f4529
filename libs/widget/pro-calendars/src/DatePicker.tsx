'use client';
import classnames from 'classnames';
import React, { useCallback, useMemo, useState, useRef, useEffect } from 'react';
import dayjs, { Dayjs } from 'dayjs';

import { DatePicker as AntdDatePicker } from 'antd';
import 'antd/es/date-picker/style/index';
import {
  DateRange,
  DefaultCalendarDate,
  toDateRange,
  getDatePreset,
  DatePreset,
} from '@benzinga/calendar-manager-hooks';
import styled from 'styled-components';

enum DateFormat {
  full = 'YYYY-MM-DD',
  pretty = 'MMM Y',
  small = 'YYYY',
}

const { RangePicker } = AntdDatePicker;

const PresetLabel: React.FC<{ onClick: React.MouseEventHandler; label: string }> = ({ label, onClick }) => {
  const ref = React.useRef<HTMLSpanElement>(null);

  React.useEffect(() => {
    const newRef = ref.current;
    if (newRef) {
      newRef.parentElement?.addEventListener('click', onClick as any);
    }
    return () => {
      if (newRef) {
        newRef.parentElement?.removeEventListener('click', onClick as any);
      }
    };
  }, [onClick]);

  return <span ref={ref}>{label}</span>;
};

interface Props {
  dateRange: DateRange;
  onChangeDate: (dates: DateRange) => void;
  presets?: DatePreset[];
}

const PLACEHOLDER: [string, string] = ['All', 'All'];
const getInputClassName = (dateStartMoment: Dayjs | null, dateEndMoment: Dayjs | null): string | undefined => {
  if (!dateStartMoment || !dateEndMoment) {
    return 'bz-placeholder-displayed';
  }
  return undefined;
};

export const DatePicker: React.FC<Props> = ({ dateRange, onChangeDate: onChangeDateProps, ...props }) => {
  const [today, setToday] = useState<string>(dayjs().format(DateFormat.full));

  const dateRangeValue = toDateRange(dateRange);

  const dateStartMoment = useMemo(
    () => (dateRangeValue.dateStart ? dayjs(dateRangeValue.dateStart) : null),
    [dateRangeValue.dateStart],
  );

  const dateEndMoment = useMemo(
    () => (dateRangeValue.dateEnd ? dayjs(dateRangeValue.dateEnd) : null),
    [dateRangeValue.dateEnd],
  );

  const inputClassName = getInputClassName(dateStartMoment, dateEndMoment);

  const pickerRef = useRef<any>(null);
  const pickerContainerRef = useRef<HTMLDivElement>(null);

  const [forceOpen, setForceOpen] = useState<boolean>(false);

  // When we select a preset date range, this function gets called multiple times in a single render.
  // We use Timeout handles to make sure the corresponding functions only run once.
  // These handles are lost on the next render and that's fine.
  let focusTimeout: NodeJS.Timeout;
  let forceOpenTimeout: NodeJS.Timeout;

  const presets = React.useMemo(
    () =>
      props.presets?.map(preset => ({
        ...preset,
        label: (
          <PresetLabel
            label={preset.label}
            onClick={e => {
              e.preventDefault();
              e.stopPropagation();
              const value = preset.value();
              const dateStart = (value?.[0]?.format(DateFormat.full) ?? null) as DefaultCalendarDate;
              const dateEnd = (value?.[1]?.format(DateFormat.full) ?? null) as DefaultCalendarDate;
              onChangeDateProps({
                dateEnd,
                datePresetId: preset.id,
                dateStart,
              });
            }}
          />
        ),
      })),
    [onChangeDateProps, props.presets],
  );

  /* eslint-disable */
  const handleOpenChange = useCallback(
    (status: boolean) => {
      if (status && today !== dayjs().format(DateFormat.full)) {
        setToday(dayjs().format(DateFormat.full));
      }
      if (status && status !== forceOpen) {
        clearTimeout(focusTimeout);
        clearTimeout(forceOpenTimeout);

        //we focus on the picker which highlights the start date field on click.
        //Otherwise it works normally without highlighting the start field.
        focusTimeout = setTimeout(() => pickerRef.current?.focus(), 50);
        forceOpenTimeout = setTimeout(() => setForceOpen(true), 60);
      } else if (!status && status !== forceOpen) {
        clearTimeout(focusTimeout);
        clearTimeout(forceOpenTimeout);

        //we blur the picker to unhighlight the end date on selection complete.
        //We MUST do this before close.
        //If done after close, the picker makes the user select the start date twice and then the end date the next time it's used.
        //If done before close, the picker works normally the next time it's opened.
        focusTimeout = setTimeout(() => pickerRef.current?.blur(), 50);
        forceOpenTimeout = setTimeout(() => setForceOpen(false), 60);
      }
    },
    [today, forceOpen],
  );
  /* eslint-enable */

  //We block all mouse down events on the picker start and end date input fields so that we can control what fields are brought into focus on click.
  //This is needed to be able to click on the end-date field and have the picker receive input for the start date first.
  useEffect(() => {
    const clickTarget = pickerContainerRef.current;
    const handleClick = (e: Event) => {
      let target = e.target as HTMLElement | null;
      while (target != null) {
        if (target.classList.contains('ant-picker-range') || target.classList.contains('ant-picker-input')) {
          e.stopPropagation();
          e.preventDefault();

          handleOpenChange(true);
          return;
        }
        target = target.parentElement;
      }
    };

    clickTarget?.addEventListener('mousedown', handleClick, { capture: true });

    return () => {
      clickTarget?.removeEventListener('mousedown', handleClick, { capture: true });
    };
  }, [handleOpenChange]);

  const onCalendarChange = React.useCallback<Required<React.ComponentProps<typeof RangePicker>>['onCalendarChange']>(
    (dayRange, _stringRange, info) => {
      const dateStart = (dayRange?.[0]?.format(DateFormat.full) ?? null) as DefaultCalendarDate;
      const dateEnd = (dayRange?.[1]?.format(DateFormat.full) ?? null) as DefaultCalendarDate;
      onChangeDateProps({ dateEnd, dateStart });
      if (info.range === 'end') {
        handleOpenChange(false);
      }
    },
    [handleOpenChange, onChangeDateProps],
  );

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const defaultRange = useMemo(() => [dayjs(), dayjs()] as [Dayjs | null, Dayjs | null], [today]);
  const panelRender = panelNode => <StyleWrapperDatePicker>{panelNode}</StyleWrapperDatePicker>;
  const datePresetId = dateRange.datePresetId;

  return (
    <Container className="bz-range-picker" ref={pickerContainerRef}>
      {datePresetId && <PresetName>{getDatePreset(datePresetId).label}</PresetName>}
      <RangePicker
        allowClear={false}
        allowEmpty={[true, true]}
        className={classnames(`bz-range-picker-input ${datePresetId && 'opacity-0'}`, inputClassName)}
        defaultValue={defaultRange}
        onCalendarChange={onCalendarChange}
        onOpenChange={handleOpenChange}
        open={forceOpen}
        panelRender={panelRender}
        placeholder={PLACEHOLDER}
        placement="bottomLeft"
        presets={presets}
        ref={pickerRef}
        separator="-"
        size="small"
        value={useMemo(() => [dateStartMoment, dateEndMoment], [dateStartMoment, dateEndMoment])}
      />
    </Container>
  );
};

const StyleWrapperDatePicker = styled.div`
  max-width: 100vw;

  .ant-picker-panel-layout {
    overflow: auto;
  }
  .ant-picker-panel {
    &:last-child {
      @media (max-width: 768px) {
        width: 0;
        .ant-picker-header {
          position: absolute;
          right: 0;
          .ant-picker-header-prev-btn,
          .ant-picker-header-view {
            visibility: hidden;
          }
        }

        .ant-picker-body {
          display: none;
        }
      }
      @media (min-width: 768px) {
        .ant-picker-presets {
          min-width: 29% !important;
        }
        /* width: 280px !important; */
        .ant-picker-header {
          position: relative;
          .ant-picker-header-prev-btn,
          .ant-picker-header-view {
            visibility: initial;
          }
        }

        .ant-picker-body {
          display: block;
        }
      }
    }
  }
`;

const Container = styled.div`
  .opacity-0 {
    opacity: 0;
  }
`;

const PresetName = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  text-align: center;
  pointer-events: none;
`;
