
.pro-black-friday-v1 {
  background: linear-gradient(0deg, #020D1D, #020D1D),
  linear-gradient(270deg, #3F80F8 7.7%, rgba(2, 13, 29, 0) 51.79%);
  border-left: 6px solid #3F80F8;

  .banner {
    padding-right: 0 !important;
    .banner-content {
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
    }

    h3 {
      color: white;
      font-size: 29px;
      line-height: 32px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      text-transform: uppercase;
      @media (max-width: 1280px) {
        font-size: 26px;
        line-height: 28px;
      }
      @media (max-width: 1000px) {
        font-size: 22px;
        line-height: 26px;
      }
      @media (max-width: 800px) {
        font-size: 14px;
        line-height: 20px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      margin-left: 200px;
      padding-right: 3px;

      .banner-button {
        background-color: #3F80F8;
        font-size: 24px;
        white-space: nowrap;
        height: 100%;
        border-radius: 0px !important;

        &:hover {
          background-color:#3F80F8;
        }

        @media (max-width: 800px) {
          padding: 0.5rem 1rem !important;
          font-size: 14px
        }
      }
      .banner-button-bg {
        background-image: url('/next-assets/images/banners/pro-black-friday.png');
        position: absolute;
        background-size: contain;
        width: 471px;
        height: 80px;
        top: 0;
        left: -351px;
        background-position: right;

      }
      @media(max-width: 1416px) {
        padding-right: 0px;
      }
      @media (max-width: 1280px) {
       margin-left: 4rem;
       .banner-button-bg {
        left: -351px;
       }
      }
      @media (max-width: 800px) {
        margin-left: 2rem;
        .banner-button-bg {
          left: -260px;
        }
        .banner-button {
          white-space: wrap;
        }
      }
    }
  }
}

.pro-black-friday-v2 {
  background: linear-gradient(0deg, #020D1D, #020D1D),
  linear-gradient(270deg, #3F80F8 7.7%, rgba(2, 13, 29, 0) 51.79%);
  border-left: 6px solid #3F80F8;

  .banner {
    padding-right: 0 !important;
    .banner-content {
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
    }

    h3 {
      color: white;
      font-size: 34px;
      line-height: 32px;
      font-weight: 400;
      font-family: Inter, sans-serif;
      text-transform: uppercase;
      letter-spacing: -0.02em;

      @media (max-width: 1280px) {
        font-size: 28px;
        line-height: 28px;
      }
      @media (max-width: 1000px) {
        font-size: 24px;
        line-height: 26px;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 20px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      margin-left: 200px;
      padding-right: 3px;

      .banner-button {
        background-color: #3F80F8;
        font-size: 24px;
        white-space: nowrap;
        height: 100%;
        border-radius: 0px !important;
        font-family: Manrope, sans-serif;
        line-height: 22px;
        letter-spacing: 0.05em;

        &:hover {
          background-color:#3F80F8;
        }

        @media (max-width: 800px) {
          padding: 0.5rem 1rem !important;
          font-size: 14px
        }
      }
      .banner-button-bg {
        background-image: url('/next-assets/images/banners/pro-black-friday.png');
        position: absolute;
        background-size: contain;
        width: 471px;
        height: 80px;
        top: 0;
        left: -351px;
        background-position: right;

      }
      @media(max-width: 1416px) {
        padding-right: 0px;
      }
      @media (max-width: 1280px) {
       margin-left: 4rem;
       .banner-button-bg {
        left: -351px;
       }
      }
      @media (max-width: 800px) {
        margin-left: 2rem;
        .banner-button-bg {
          left: -260px;
        }
        .banner-button {
          white-space: wrap;
        }
      }
    }
  }
}

.pro-cyber-monday-v1, .pro-cyber-monday-v2 {
  background: linear-gradient(180deg, #081F49 0%, #020B18 100%);

  .banner {
    h3 {
      color: white;
      font-size: 36px;
      line-height: 32px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      margin-top: 1rem;
      @media (max-width: 1212px) {
        font-size: 32px;
        line-height: 28px;
      }
      @media (max-width: 1000px) {
        font-size: 28px;
        line-height: 28px;
      }
      @media (max-width: 800px) {
        font-size: 18px;
        line-height: 20px;
      }
    }

    p {
      font-family: Inter;
      font-size: 10px;
      font-weight: 700;
      line-height: 12px;
      letter-spacing: 0.54em;
      text-align: start;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      padding: 0.25rem 0.5rem;
      width: 80%;
      text-transform: uppercase;

      color: white;
      background: linear-gradient(90deg, #0077FF 0%, rgba(0, 119, 255, 0) 76.37%);
      position: absolute;
      top: 0;
      left: 0;
      @media(max-width: 800px) {
        width: 100%;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      margin-left: 100px;
      height: 100%;

      .banner-button {
        background-color: #0077FF;
        font-size: 24px;
        white-space: nowrap;
        height: 100%;
        margin: 0.5rem 0;
        border-radius: 4px !important;

        &:hover {
          background-color:#0077FF;
        }

        @media (max-width: 800px) {
          padding: 0.5rem 1rem !important;
          font-size: 14px
        }
      }
      .banner-button-bg {
        background-image: url('/next-assets/images/banners/pro-cyber-bg.png');
        position: absolute;
        background-size: contain;
        background-repeat: no-repeat;
        width: 315px;
        height: 80px;
        top: 0;
        left: -200px;
        background-position: right;
      }
      @media (max-width: 1280px) {
       margin-left: 2rem;
       .banner-button-bg {
        left: -160px;
       }
      }
      @media (max-width: 800px) {
        margin-left: 0rem;
        max-width: 100px;
        .banner-button-bg {
          left: -140px;
        }
        .banner-button {
          white-space: wrap;
        }
      }
    }
  }
}

.pro-webinar-v2 {
  background: linear-gradient(180deg, #FFFFFF 0%, #C2DEFF 100%);

  h3 {
    color: #192940;
  }
  p {
    color: #5B7292;
  }

  .banner {
    .banner-content {
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      padding-right: 4rem;
      @media (max-width: 800px) {
        padding-right: 0;
      }
    }

    h3 {
      font-size: 21px;
      margin-bottom: 0.25rem;
      line-height: 24px;
      @media (max-width:1332px) {
        font-size: 18px;
        line-height: 18px;
      }
      @media (max-width: 1000px) {
        font-size: 16px;
        line-height: 16px;
      }
      @media (max-width: 800px) {
        font-size: 12px;
        line-height: 14px;
      }
    }
    p {
      font-size: 16px;
      line-height: 18px;
      padding-right: 4rem;
      @media (max-width:1332px) {
        font-size: 14px;
        line-height: 14px;
      }
      @media (max-width: 930px) {
        line-height: 12px;
      }
      @media (max-width: 800px) {
        padding-right: 1rem;
        font-size: 10px;
        line-height: 10px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button {
        background-color: #479CFF;
        padding: 1rem;
        font-size: 12px;
        box-shadow: 0 0 24px 0 #3f83f8 !important;
        color: white;
        white-space: nowrap;
        text-transform: capitalize;
        &:hover {
          background-color:#3a74da;
        }

        @media (max-width: 1200px) {
          white-space: wrap;
          min-width: 200px;
        }
        @media (max-width: 800px) {
          padding: 0.5rem 1rem;
          font-size: 10px;
          font-weight: 700;
          min-width: 50px;
        }
      }
    }
  }

  .banner-button-bg {
    position: absolute;
    top: 0;
    left: -170px;
    width: 650px;
    height: 80px;
    background-image: url('/next-assets/images/banners/webinar-divider.png');
    background-position: left;
    background-size: contain;
    background-repeat: no-repeat;
    @media (max-width: 1000px) {
      display: none;
    }
  }

  .banner-bg-end {
    background-color: #192940;
    background-image: url('/next-assets/images/banners/pro-webinar-bg-end.png');
    background-position: right;
    background-size: contain;
    background-repeat: no-repeat;
    width: 255px;
    height: 80px;
    @media (max-width: 1200px) {
      margin-right: -60px;
    }
    @media (max-width: 1000px) {
      background-color: transparent;
    }
    @media(min-width: 1920px) {
      width:25vw;
    }
  }
}

.pro-webinar-v1 {
  background: linear-gradient(180deg, #2C4466 0%, #192940 100%);

  .banner {
    .banner-content {
      padding-right: 4rem;
      @media (max-width: 800px) {
        padding-right: 0;
      }
    }
    h3 {
      color: white;
      font-size: 21px;
      margin-bottom: 0.25rem;
      line-height: 24px;
      @media (max-width:1332px) {
        font-size: 18px;
        line-height: 18px;
      }
      @media (max-width: 1000px) {
        font-size: 16px;
        line-height: 16px;
      }
      @media (max-width: 800px) {
        font-size: 12px;
        line-height: 14px;
      }
    }
    p {
      color: #B8CBE5;
      font-size: 16px;
      line-height: 18px;
      padding-right: 4rem;
      @media (max-width:1332px) {
        font-size: 14px;
        line-height: 14px;
      }
      @media (max-width: 930px) {
        line-height: 12px;
      }
      @media (max-width: 800px) {
        padding-right: 1rem;
        font-size: 10px;
        line-height: 10px;
      }
    }
    .button-wrapper {
      align-items: center;
      justify-content: center;
      @media (max-width: 800px) {
        background-color: transparent;
      }
    }

    .banner-button {
      background-color: #479CFF;
      padding: 1rem;
      font-size: 12px;
      box-shadow: 0 0 24px 0 #3f83f8 !important;
      color: white;
      white-space: nowrap;
      text-transform: capitalize;
      &:hover {
        background-color:#3a74da;
      }

      @media (max-width: 1200px) {
        white-space: wrap;
        min-width: 200px;
      }
      @media (max-width: 800px) {
        padding: 0.5rem 1rem;
        font-size: 10px;
        font-weight: 700;
        min-width: 50px;
      }
    }
  }

  .banner-button-bg {
    position: absolute;
    top: 0;
    left: -180px;
    width: 550px;
    height: 80px;
    background-image: url('/next-assets/images/banners/pro-webinar-ryan.png');
    background-position: left;
    background-size: contain;
    background-repeat: no-repeat;
    overflow: hidden;
    @media (max-width: 930px) {
      left: -160px;
    }
    @media (max-width: 800px) {
      display: none;
    }
  }
  .banner-bg-end {
    background-color: white;
    background-image: url('/next-assets/images/banners/pro-webinar-bg-end.png');
    background-position: right;
    background-size: contain;
    background-repeat: no-repeat;
    width: 255px;
    height: 80px;
    @media (max-width: 1500px) {
      background-color: transparent;
    }
    @media(min-width: 2000px) {
      width:25vw;
    }
  }
}

.erx-webinar-v1 {
  background: linear-gradient(0deg, rgba(2, 13, 29, 0.5), rgba(2, 13, 29, 0.5)),
            linear-gradient(90deg, #040419 0%, #063274 100%);

  .banner {
    .banner-content > div {
      display: flex;
      flex-direction: row-reverse;
      height: 100%;
      align-items: center;
      padding-right: 1rem;
      @media (max-width: 800px) {
        padding-right: 0.5rem;
      }
    }
    h3 {
      color: white;
      font-family: Inter;
      font-size: 30px;
      font-weight: 800;
      line-height: 30px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      padding-left: 1rem;

      @media (max-width:1250px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 1000px) {
        font-size: 20px;
        line-height: 20px;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 16px;
        padding-left: 0.5rem;
      }
    }
    p {
      color: #479CFF;
      font-size: 20px;
      line-height: 24px;
      font-weight: 900;
      text-transform: uppercase;
      padding-right: 1rem;
      max-width: fit-content;
      font-family: Inter, sans-serif;
      letter-spacing: 0.22em;
      text-align: center;
      font-family: Inter;
      border-right: 1px solid #71788F;
      height: 100%;

      @media (max-width:1332px) {
        font-size: 14px;
        line-height: 14px;
      }
      @media (max-width: 930px) {
        line-height: 12px;
      }
      @media (max-width: 800px) {
        padding-right: 0.5rem;
        font-size: 10px;
        line-height: 10px;
      }
    }
    .button-wrapper {
      align-items: center;
      justify-content: center;
      @media (max-width: 800px) {
        background-color: transparent;
      }
    }

    .banner-button {
      background-color: #479CFF;
      padding: 1rem;

      color: white;
      white-space: nowrap;
      text-transform: uppercase;
      border-radius: 2px !important;
      font-family: Inter;
      font-size: 20px;
      font-weight: 800;
      line-height: 26px;
      letter-spacing: 0.08em;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;

      &:hover {
        background-color:#3a74da;
      }

      @media (max-width: 1000px) {
        font-size: 16px;
        line-height: 20px
      }

      @media(max-width: 800px) {
        font-size: 12px;
        line-height: 14px;
        white-space: wrap;
        text-align: center;
      }
    }
  }

  .banner-bg-end {
    background-image: url('/next-assets/images/banners/erx-end-bg.png');
    background-position: right;
    background-size: contain;
    background-repeat: no-repeat;
    width: 944px;
    height: 80px;
  }
}

.erx-webinar-v4 {
  background: linear-gradient(0deg, rgba(2, 13, 29, 0.5), rgba(2, 13, 29, 0.5)),
            linear-gradient(90deg, #040419 0%, #063274 100%);

  .banner {
    p {
      color: white;
      padding-left: 1rem;
      font-family: Inter;
      font-size: 30px;
      font-weight: 500;
      line-height: 30px;
      letter-spacing: 0.18em;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      text-transform: uppercase;

      @media (max-width:1310px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 1000px) {
        font-size: 20px;
        line-height: 20px;
      }
      @media (max-width: 800px) {
        font-size: 14px;
        line-height: 14px;
        padding-left: 0.5rem;
      }
    }
    .button-wrapper {
      align-items: center;
      justify-content: center;
      @media (max-width: 800px) {
        background-color: transparent;
      }
    }

    .banner-button {
      background-color: #479CFF;
      padding: 1rem;

      color: white;
      white-space: nowrap;
      text-transform: uppercase;
      border-radius: 2px !important;
      font-family: Inter;
      font-size: 20px;
      font-weight: 800;
      line-height: 26px;
      letter-spacing: 0.08em;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;

      &:hover {
        background-color:#3a74da;
      }

      @media (max-width: 1000px) {
        font-size: 16px;
        line-height: 20px
      }

      @media(max-width: 800px) {
        font-size: 12px;
        line-height: 14px;
        white-space: wrap;
        text-align: center;
      }
    }
  }

  .banner-bg-end {
    background-image: url('/next-assets/images/banners/erx-end-bg.png');
    background-position: right;
    background-size: contain;
    background-repeat: no-repeat;
    width: 944px;
    height: 80px;
  }
}

.erx-webinar-v2 {
  background: white;
  border-left: 5px solid #1A81FA;

  .banner {
    .banner-content .content {
      display: flex;
      flex-direction: column-reverse;
      align-items: flex-start;
    }
    h3 {
      color: rgba(25, 41, 64, 1);
      font-family: Inter;
      font-size: 30px;
      font-weight: 800;
      line-height: 30px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      text-transform: uppercase;

      @media (max-width:1250px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 1000px) {
        font-size: 20px;
        line-height: 20px;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 16px;
      }
    }
    p {
      color: #1A81FA;
      font-family: Inter;
      font-size: 12px;
      font-style: italic;
      font-weight: 900;
      line-height: 18px;
      letter-spacing: 0.4em;
      text-align: center;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      text-transform: uppercase;
      @media (max-width: 800px) {
        font-size: 10px;
        line-height: 10px;
        letter-spacing: 0.2em;
        margin-bottom: 0.5rem !important;
      }
    }
    .button-wrapper {
      align-items: center;
      justify-content: center;
      @media (max-width: 800px) {
        background-color: transparent;
      }
    }

    .banner-button {
      background-color: #479CFF;
      padding: 1rem;

      color: white;
      white-space: nowrap;
      text-transform: uppercase;
      border-radius: 2px !important;
      font-family: Inter;
      font-size: 20px;
      font-weight: 800;
      line-height: 26px;
      letter-spacing: 0.08em;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;

      &:hover {
        background-color:#3a74da;
      }

      @media (max-width: 1000px) {
        font-size: 16px;
        line-height: 20px
      }

      @media(max-width: 800px) {
        font-size: 12px;
        line-height: 14px;
        white-space: wrap;
        text-align: center;
      }
    }
  }

  .banner-bg-end {
    background-image: url('/next-assets/images/banners/erx-end-bg-light.png');
    background-position: right;
    background-size: contain;
    background-repeat: no-repeat;
    width: 944px;
    height: 80px;
  }
}

.pro-eoy-v1 {
  background: linear-gradient(180deg, #F2E4C7 0%, #F9F4E7 100%),
  linear-gradient(180deg, #F2E4C7 0%, #F9F4E7 100%);

  .banner {
    .banner-content {
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      height: 100%;
    }

    h3 {
      color: #000000;
      font-size: 31px;
      line-height: 28px;
      font-weight: 800;
      font-family: Inter, sans-serif;
      @media (max-width: 1210px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 1000px) {
        font-size: 18px;
        line-height: 18px;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 16px;
        margin-bottom: 0.25rem;
      }
    }

    p {
      color: #A66B38;
      font-family: Inter, sans-serif;
      font-size: 16px;
      font-weight: 500;
      line-height: 20px;
      letter-spacing: -0.04em;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      @media (max-width: 1210px) {
        font-size: 14px;
        line-height: 14px;
      }
      @media (max-width: 1000px) {
        font-size: 12px;
        line-height: 12px;
      }
      @media (max-width: 800px) {
        font-size: 10px;
        line-height: 10px;
      }
    }

    .sale-badge {
      font-family: Inter, sans-serif;
      font-size: 18px;
      font-weight: 800;
      line-height: 24px;
      letter-spacing: 0.44em;
      text-align: center;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      white-space: nowrap;
      text-transform: uppercase;
      height: 100%;
      padding-right: 0.5rem;
      border-right: 1px solid rgba(238, 205, 144, 0.1);
      margin-right: 1rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: #A66B38;
      @media (max-width: 1200px) {
        font-size: 16px;
        line-height: 16px;
        white-space: wrap;
        max-width: 150px;
      }
      @media (max-width: 800px) {
        font-size: 10px;
        line-height: 10px;
        padding-right: 0.25rem;
        margin-right: 0.5rem;
        letter-spacing: 0.22em;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button-bg {
        position: absolute;
        background-image: url('/next-assets/images/banners/pro/eoy-star.png');
        background-size: contain;
        background-repeat: no-repeat;
        top: 0;
        width: 59px;
        height: 38px;
        margin-left: -280px;
        @media (max-width: 800px) {
          margin-left: -100px;
        }
      }

      .banner-button {
        background-color: #3F83F8;
        font-size: 22px;
        white-space: nowrap;
        text-transform: uppercase;
        height: 80%;
        padding: 1rem !important;

        &:hover {
          background-color:#3a74da;
        }

        @media (max-width: 1200px) {
          font-size: 18px;
        }
        @media (max-width: 1000px) {
          font-size: 16px;
        }
        @media (max-width: 800px) {
          padding: 0.5rem 1rem !important;
          font-size: 10px;
          white-space: wrap;
        }

      }
    }
  }

  .banner-bg-start {
    width: 366px;
    height: 80px;
    background-image: url('/next-assets/images/banners/pro/pro-eoy-start-bg.png');
    background-position: left;
    background-size: contain;
    background-repeat: no-repeat;
    z-index: 2;
  }
  .banner-bg-end {
    width: 366px;
    height: 80px;
    background-image: url('/next-assets/images/banners/pro/pro-eoy-end-bg.png');
    background-position: right;
    background-size: contain;
    background-repeat: no-repeat;
    z-index: 2;
    @media (max-width: 800px) {
      margin-right: -40px;
    }
  }
}

.pro-eoy-v2 {
  background-color: #020D1D;
  background-image: url('/next-assets/images/banners/pro/pro-eoy-bg.png');
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  border-left: 4px solid #3F80F8;

  .banner {
    .banner-content, .content {
      display: flex;
      flex-direction: row-reverse;
      justify-content: center;
      align-items: center;
      height: 100%;
      gap: 1rem;
    }

    h3 {
      color: white;
      text-transform: uppercase;
      font-family: Inter, sans-serif;
      font-size: 34px;
      font-weight: 400;
      line-height: 32px;
      letter-spacing: -0.02em;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;

      @media (max-width: 1210px) {
        font-size: 24px;
        line-height: 28px;
      }
      @media (max-width: 800px) {
        font-size: 20px;
        line-height: 24px;
        margin-bottom: 0.25rem;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button {
        background-color: #3F83F8;
        font-size: 22px;
        white-space: nowrap;
        border-radius: 2px !important;
        text-transform: uppercase;
        height: 80%;

        &:hover {
          background-color:#3a74da;
        }
        @media (max-width: 1500px) {
          margin-right: 40px;
        }
        @media (max-width: 1200px) {
          font-size: 18px;
        }
        @media (max-width: 1000px) {
          font-size: 16px;
        }
        @media (max-width: 800px) {
          padding: 0.5rem 1rem !important;
          font-size: 12px;
          white-space: wrap;
          margin-right: 20px;
        }
      }
    }
  }
  .banner-bg-start {
    width: 366px;
    height: 80px;
    background-image: url('/next-assets/images/banners/pro/pro-eoy-start-bg-2.png');
    background-position: left;
    background-size: contain;
    background-repeat: no-repeat;
    z-index: 2;
  }
  .banner-bg-end {
    width: 366px;
    height: 80px;
    background-image: url('/next-assets/images/banners/pro/pro-eoy-end-bg-2.png');
    background-position: right;
    background-size: contain;
    background-repeat: no-repeat;
    z-index: 100;
    @media (max-width: 800px) {
      margin-right: -40px;
    }
  }
}

.lead-gen-inauguration {
  background: linear-gradient(90deg, #192030 0%, #01050E 33.07%);

  .banner {
    p {
      display: flex;
      flex-direction: row;
      gap: 0.5rem;
      color: white;
      text-transform: uppercase;
      font-family: Inter, sans-serif;
      font-size: 23px;
      font-weight: 700;
      line-height: 26px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      letter-spacing: 0.1rem;

      @media (max-width: 1500px) {
        padding-left: 1rem;
      }
      @media (max-width: 1280px) {
        font-size: 20px;
      }
      @media (max-width: 1100px) {
        font-size: 18px;
        letter-spacing: 0;
      }
      @media (max-width: 1000px) {
        flex-direction: column;
      }
      @media (max-width: 800px) {
        gap: 0;
        font-size: 14px;
        line-height: 20px;
        white-space: wrap;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button-bg {
        position: absolute;
        background-image: url('/next-assets/images/banners/trump-btn-bg.png');
        background-size: contain;
        background-repeat: no-repeat;
        top: 0;
        width: 417px;
        height: 80px;
        margin-left: -400px;
        @media (max-width: 1300px) {
          margin-left: -300px;
        }
        @media (max-width: 1100px) {
          margin-left: -150px;
        }
        @media (max-width: 900px) {
          margin-left: -30px;
        }
        @media (max-width: 800px) {
          margin-left: 130px;
        }
      }

      .banner-button {
        background-color: #3F83F8;
        font-size: 20px;
        white-space: nowrap;
        border-radius: 2px !important;
        text-transform: uppercase;
        font-weight: 700;
        letter-spacing: 0.2em;
        margin-left: 1.5rem;
        padding: 1rem !important;

        &:hover {
          background-color:#3a74da;
        }
        @media (max-width: 1300px) {
          font-size: 18px;
          white-space: wrap;
          max-width: 300px;
        }
        @media (max-width: 1000px) {
          font-size: 14px;
          max-width: 210px;
        }
        @media (max-width: 800px) {
          padding: 0.5rem 1rem !important;
          max-width: 80px;
          font-size: 12px;
        }
      }
    }
  }
  .banner-bg-start {
    width: 121px;
    height: 80px;
    background-image: url('/next-assets/images/banners/trump-start-bg.png');
    background-position: left;
    background-size: contain;
    background-repeat: no-repeat;
    z-index: 2;
    @media (max-width: 1500px) {
      margin-left: -20px;
    }
  }

}

.pro-trial-v1 {
  background: linear-gradient(180deg, #00406D 0%, #000000 100%);

  .banner {
    h3 {
      color: white;
      font-size: 28px;
      line-height: 28px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      text-transform: uppercase;
      padding-right: 2rem;


      @media (max-width: 1410px) {
        font-size: 26px;
        line-height: 26px;
      }
      @media (max-width: 1220px) {
        font-size: 24px;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 20px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button-bg {
        position: absolute;
        background-image: url('/next-assets/images/banners/pro/pro-trial-v1-btn.png');
        background-size: contain;
        background-position: right;
        width: 436px;
        height: 80px;
        margin-left: -636px;

        @media (max-width: 1220px) {
          margin-left: -436px;
        }
        @media (max-width: 800px) {
          margin-left: -80px;
        }
      }

      .banner-button {
        background-color: #479CFF;
        font-size: 20px;
        white-space: nowrap;
        font-weight: 700;
        min-height: 70%;
        text-transform: uppercase;
        font-family: Manrope, sans-serif;

        border: 1px solid;
        border-image-slice: 1;
        border-image-source: linear-gradient(269.56deg, rgba(255, 255, 255, 0) 0.38%, #FFFFFF 25.19%, #FFFFFF 74.81%, rgba(255, 255, 255, 0) 99.62%);

        &:hover {
          background-color:#3a74da;
        }
        @media (max-width: 1410px) {
          font-size: 18px;
        }
        @media (max-width: 1220px) {
          padding: 0.5rem 1rem !important;
          font-size: 16px;
          white-space: wrap;
        }
        @media (max-width: 800px) {
          font-size: 12px;
        }
      }
    }
  }
}

.pro-trial-v2 {
  background: linear-gradient(180deg, #00406D 0%, #000000 100%);

  .banner {
    h3 {
      color: white;
      font-size: 28px;
      line-height: 28px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      text-transform: uppercase;
      padding-right: 2rem;


      @media (max-width: 1410px) {
        font-size: 26px;
        line-height: 26px;
      }
      @media (max-width: 1220px) {
        font-size: 24px;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 20px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button-bg {
        position: absolute;
        background-image: url('/next-assets/images/banners/pro/pro-trial-v2-btn.png');
        background-size: contain;
        background-position: right;
        width: 768px;
        height: 80px;
        margin-left: -360px;
        @media (max-width: 1220px) {
          margin-left: -260px;
        }
        @media (max-width: 800px) {
          margin-left: 0px;
        }
      }

      .banner-button {
        background-color: #3F83F8;
        font-size: 20px;
        white-space: nowrap;
        font-weight: 700;
        min-height: 70%;
        text-transform: uppercase;
        font-family: Manrope, sans-serif;

        &:hover {
          background-color:#3a74da;
        }
        @media (max-width: 1410px) {
          font-size: 18px;
        }
        @media (max-width: 1220px) {
          padding: 0.5rem 1rem !important;
          font-size: 16px;
          white-space: wrap;
        }
        @media (max-width: 800px) {
          font-size: 12px;
        }
      }
    }
  }
}

.lead-gen-inauguration-v2 {
  background-color: #01050E;
  background-image: url('/next-assets/images/banners/cheat-sheet-bg.png');

  .banner {
    p {
      display: flex;
      flex-direction: row;
      gap: 0.5rem;
      color: white;
      text-transform: uppercase;
      font-family: Inter, sans-serif;
      font-size: 23px;
      font-weight: 700;
      line-height: 26px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      white-space: nowrap;

      @media (max-width: 1600px) {
        padding-left: 100px;
        font-size: 20px;
      }

      @media (max-width: 1100px) {
        font-size: 18px;
        line-height: 22px;
      }
      @media (max-width: 1000px) {
        flex-direction: column;
        gap: 0;
        padding-left: 4rem;
      }
      @media (max-width: 800px) {
        font-size: 14px;
        line-height: 18px;
        white-space: wrap;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button {
        background-color: #3F83F8;
        font-size: 20px;
        white-space: nowrap;
        border-radius: 2px !important;
        text-transform: uppercase;
        height: 80%;
        font-weight: 700;
        letter-spacing: 0.2em;
        margin-left: 1rem;
        padding: 1rem !important;

        &:hover {
          background-color:#3a74da;
        }
        @media (max-width: 1600px) {
          white-space: wrap;
          font-size: 18px;
        }
        @media (max-width: 1000px) {
          font-size: 14px;
        }
        @media (max-width: 800px) {
          max-width: 80px;
          padding: 0.5rem 1rem !important;
          font-size: 12px;
          white-space: wrap;
          letter-spacing: 0;
        }
      }
    }
  }
  .banner-bg-start {
    width: 197px;
    height: 80px;
    background-image: url('/next-assets/images/banners/cheat-sheet-start-bg.png');
    background-position: left;
    background-size: contain;
    background-repeat: no-repeat;
    z-index: 2;
    @media (max-width: 1680px) {
      margin-left: -100px;
    }
    @media (max-width: 1000px) {
      margin-left: -140px;
    }
  }
}

.pro-sale-v1 {
  background:
  linear-gradient(270deg, #3F80F8 7.7%, rgba(2, 13, 29, 0) 51.79%),
  linear-gradient(0deg, #020D1D, #020D1D);

  border-left: 6px solid #3F80F8;

  .banner {
    padding-right: 0 !important;
    .banner-content {
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
    }

    h3 {
      color: white;
      font-size: 34px;
      line-height: 32px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      text-transform: uppercase;
      letter-spacing: 0.1em;

      @media (max-width: 1280px) {
        font-size: 28px;
        line-height: 28px;
      }
      @media (max-width: 1000px) {
        font-size: 24px;
        line-height: 26px;
      }
      @media (max-width: 800px) {
        font-size: 20px;
        line-height: 20px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      margin-left: 200px;
      padding-right: 3px;

      .banner-button {
        background-color: #3F80F8;
        font-size: 24px;
        white-space: nowrap;
        height: 100%;
        border-radius: 0px !important;
        font-family: Manrope, sans-serif;
        line-height: 22px;
        letter-spacing: 0.05em;

        &:hover {
          background-color:#3F80F8;
        }

        @media (max-width: 800px) {
          padding: 0.5rem 1rem !important;
          font-size: 14px
        }
      }
      .banner-button-bg {
        background-image: url('/next-assets/images/banners/pro/pro-sale-v1.png');
        position: absolute;
        background-size: contain;
        width: 471px;
        height: 80px;
        top: 0;
        left: -351px;
        background-position: right;

      }
      @media(max-width: 1416px) {
        padding-right: 0px;
      }
      @media (max-width: 1280px) {
       margin-left: 4rem;
       .banner-button-bg {
        left: -351px;
       }
      }
      @media (max-width: 800px) {
        margin-left: 2rem;
        .banner-button-bg {
          left: -260px;
        }
        .banner-button {
          white-space: wrap;
        }
      }
    }
  }
}

.pro-sale-v2 {
  background: linear-gradient(180deg, #081F49 0%, #020B18 100%);

  .banner {
    h3 {
      color: white;
      font-size: 32px;
      line-height: 32px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      margin-top: 1rem;
      @media (max-width: 1300px) {
        font-size: 28px;
        line-height: 28px;
      }
      @media (max-width: 1160px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 16px;
      }
    }

    p {
      font-family: Inter;
      font-size: 10px;
      font-weight: 700;
      line-height: 12px;
      letter-spacing: 0.54em;
      text-align: start;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      padding: 0.25rem 0.5rem;
      width: 80%;
      text-transform: uppercase;

      color: white;
      background: linear-gradient(90deg, #0077FF 0%, rgba(0, 119, 255, 0) 76.37%);
      position: absolute;
      top: 0;
      left: 0;
      @media(max-width: 800px) {
        width: 100%;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      height: 100%;

      .banner-button {
        background-color: #0077FF;
        font-size: 24px;
        white-space: nowrap;
        height: 100%;
        margin: 0.5rem 0;
        font-weight: 900;
        border-radius: 4px !important;

        &:hover {
          background-color:#0077FF;
        }

        @media (max-width: 800px) {
          padding: 0.5rem 1rem !important;
          font-size: 14px
        }
      }
      @media (max-width: 1280px) {
       margin-left: 2rem;
       .banner-button-bg {
        left: -160px;
       }
      }
      @media (max-width: 800px) {
        margin-left: 0rem;
        max-width: 100px;
        .banner-button-bg {
          left: -140px;
        }
        .banner-button {
          white-space: wrap;
        }
      }
    }
  }
}

.pro-sale-v3 {
  background: linear-gradient(180deg, #00406D 0%, #000000 100%);

  .banner {
    h3 {
      color: white;
      font-size: 30px;
      line-height: 32px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      @media (max-width: 1320px) {
        font-size: 28px;
        line-height: 28px;
      }
      @media (max-width: 1240px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 1100px) {
        font-size: 20px;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 16px;
      }
    }

    p {
      font-family: Inter, sans-serif;
      font-size: 14px;
      line-height: 14px;
      color: white;
      text-transform: capitalize;

      @media (max-width: 1340px) {
        font-size: 12px;
        line-height: 10px;
      }
      @media (max-width: 1200px) {
        font-size: 10px;
        line-height: 10px;
      }
      @media (max-width: 800px) {
        display: none;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      height: 100%;

      .banner-button {
        background-color: #3F83F8;
        font-size: 24px;
        line-height: 28px;
        white-space: nowrap;

        @media (max-width: 1400px) {
          padding: 1rem !important;
          font-size: 20px;
          line-height: 24px;
        }

        @media (max-width: 800px) {
          padding: 0.5rem 1rem !important;
          font-size: 14px;
          line-height: 16px;
          white-space: wrap;
        }
      }

      .banner-button-bg {
        background-image: url('/next-assets/images/banners/edge/edge-v16-btn.png');
        position: absolute;
        background-size: contain;
        width: 700px;
        height: 80px;
        top: 0;
        left: -430px;
        background-position: right;
      }
    }
  }
}

.matt-trial-v3 {
  background-color: #041624;

  .banner {
    .banner-content {
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
    }

    p {
      color: white;
      font-size: 30px;
      font-weight: 700;
      line-height: 28px;
      font-family: Inter, sans-serif;
      margin-right: 1rem;
      text-transform: uppercase;
      letter-spacing: 0.1em;

      @media (max-width: 1232px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 990px) {
        font-size: 16px;
        line-height: 18px;
        // margin-right: 0.5rem;
      }
      @media (max-width: 800px) {
        font-size: 12px;
        line-height: 12px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button {
        background-color: #479CFF;
        font-size: 22px;
        white-space: nowrap;
        font-weight: 700;
        text-transform: uppercase;
        padding: 1.25rem 1rem !important;

        @media (max-width: 1100px) {
          font-size: 18px;
        }

        @media (max-width: 800px) {
          padding: 0.5rem !important;
          white-space: wrap;
          font-size: 12px;
          max-width: 180px;
        }
      }

      .banner-button-bg {
        position: absolute;
        background-image: url('/next-assets/images/banners/matt-tariff-bg.png');
        height: 80px;
        width: 500px;
        left: 0;
        background-position: right;
        margin-left: -500px;

        @media (max-width: 800px) {
          margin-left: -460px;
        }
      }
    }
  }
}

.matt-trial-v2 {
  background-color: #0F1F3D;
  background-image: url('/next-assets/images/banners/trial-bg-v2.png');
  background-position: left;
  background-repeat: no-repeat;
  background-size: cover;

  .banner {
    .banner-content {
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
    }

    h3 {
      color: white;
      font-size: 32px;
      line-height: 28px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      margin-right: 1rem;

      @media (max-width: 1375px) {
        font-size: 28px;
        line-height: 32px;
      }
      @media (max-width: 1232px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 1150px) {
        font-size: 20px;
        line-height: 22px;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 18px;
        margin-right: 0.5rem;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .banner-button {
        background-color: #479CFF;
        font-size: 20px;
        white-space: nowrap;
        font-weight: 700;
        text-transform: uppercase;
        padding: 1.25rem 2rem !important;

        &:hover {
          background-color:#3a74da;
        }

        @media (max-width: 1300px) {
          font-size: 18px;
        }

        @media (max-width: 1100px) {
          padding: 1rem 1rem !important;
          font-size: 16px;
        }

        @media (max-width: 800px) {
          // padding: 0.5rem !important;
          white-space: wrap;
          font-size: 12px;
          max-width: 180px;
        }
      }
      .banner-button-bg {
        position:absolute;
        background-image: url('/next-assets/images/banners/matt-maley-headshot.png');
        height: 80px;
        width: 133px;
        left: 0;
        margin-left: -133px;
        @media (max-width: 800px) {
          display: none;
        }
      }
    }
  }
}

.matt-trial-v1 {
  background-color: #0F1F3D;
  background-image: url('/next-assets/images/banners/trial-bg-v1.png');
  background-position: left;
  background-repeat: no-repeat;
  background-size: cover;

  .banner {
    .banner-content {
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
    }

    h3 {
      color: white;
      font-size: 38px;
      line-height: 28px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      margin-right: 1rem;

      @media (max-width: 1375px) {
        font-size: 32px;
        line-height: 32px;
      }
      @media (max-width: 1232px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 18px;
        margin-right: 0.5rem;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;

      .trial-button {
        background: linear-gradient(180deg, #3F83F8 0%, #00399B 100%);
        font-size: 9px;
        width: 100%;
        text-align: center;
        font-family: Inter, sans-serif;
        font-weight: 800;
        font-size: 9px;
        letter-spacing: 0.2rem;
        text-align: center;
        text-transform: uppercase;
        line-height: 20px;
        @media (max-width: 800px) {
          font-size: 8px;
          line-height: 12px;
          letter-spacing: 0.1rem;
        }
      }

      .banner-button {
        background-color: #479CFF;
        font-size: 22px;
        white-space: nowrap;
        border-top-left-radius: 6px !important;
        border-top-right-radius: 6px !important;
        border-bottom-left-radius: 0% !important;
        border-bottom-right-radius: 0% !important;
        font-weight: 700;
        text-transform: uppercase;
        padding: 0.75rem 1rem !important;
        &:hover {
          background-color:#3a74da;
        }

        @media (max-width: 1100px) {
          font-size: 18px;
        }

        @media (max-width: 800px) {
          padding: 0.5rem !important;
          white-space: wrap;
          font-size: 12px;
          max-width: 180px;
        }
      }
    }
  }
}

.matt-trial-v4 {
  background-color: #031C30;

  .banner {
    .banner-content div {
      display: flex;
      flex-direction: column-reverse;
      align-items: flex-start;
      justify-content: flex-start;
      margin-right: 2rem;
    }

    p {
      color: white;
      font-size: 30px;
      font-weight: 700;
      line-height: 28px;
      font-family: Inter, sans-serif;
      text-transform: uppercase;

      @media (max-width: 1380px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 1100px) {
        font-size: 20px;
        line-height: 20px;
      }
      @media (max-width: 800px) {
        font-size: 14px;
        line-height: 18px;
      }
    }

    h3 {
      color: white;
      font-family: Inter, sans-serif;
      font-weight: 500;
      font-size: 14px;
      line-height: 14px;
      letter-spacing: -2%;
      text-transform: capitalize;
      margin-top: 0.5rem;
      @media (max-width: 1200px) {
        font-size: 12px;
        line-height: 12px;
      }
      @media (max-width: 1100px) {
        font-size: 10px;
        line-height: 10px;
      }
      @media (max-width: 800px) {
        display: none;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;
      margin-left: 2rem;

      @media (max-width: 800px) {
        margin-left: 1rem;
      }

      .banner-button {
        background-color: #479CFF;
        font-size: 22px;
        white-space: nowrap;
        font-weight: 700;
        height: 70%;

        @media (max-width: 1300px) {
          font-size: 18px;
          padding: 1rem 1rem !important;
        }

        @media (max-width: 1100px) {
          font-size: 16px;
        }

        @media (max-width: 800px) {
          padding: 0.5rem !important;
          white-space: wrap;
          font-size: 12px;
          max-width: 180px;
        }
      }

      .banner-button-bg {
        position: absolute;
        background-image: url('/next-assets/images/banners/matt/matt-trial-v4.png');
        height: 80px;
        width: 618px;
        left: 0;
        background-position: right;
        background-size: contain;
        margin-left: -260px;

        @media (max-width: 1300px) {
          margin-left: -220px;
        }

        @media (max-width: 800px) {
          margin-left: -200px;
        }
      }
    }
  }
}

.matt-trial-v5 {
  background-color: #031C30;

  .banner {
    .banner-content div {
      display: flex;
      flex-direction: column-reverse;
      align-items: flex-start;
      justify-content: flex-start;
      margin-right: 2rem;
    }

    p {
      color: white;
      font-size: 30px;
      font-weight: 700;
      line-height: 28px;
      font-family: Inter, sans-serif;
      text-transform: uppercase;

      @media (max-width: 1380px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 1100px) {
        font-size: 20px;
        line-height: 20px;
      }
      @media (max-width: 800px) {
        font-size: 14px;
        line-height: 18px;
      }
    }

    h3 {
      color: white;
      font-family: Inter, sans-serif;
      font-weight: 500;
      font-size: 14px;
      line-height: 14px;
      letter-spacing: -2%;
      text-transform: capitalize;
      margin-top: 0.5rem;
      @media (max-width: 1200px) {
        font-size: 12px;
        line-height: 12px;
      }
      @media (max-width: 1100px) {
        font-size: 10px;
        line-height: 10px;
      }
      @media (max-width: 800px) {
        display: none;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;
      margin-left: 2rem;

      @media (max-width: 800px) {
        margin-left: 1rem;
      }

      .banner-button {
        position: relative;
        border-radius: 4px;
        background: linear-gradient(90deg, #479CFF 0%, #2B5E99 100%);
        font-size: 22px;
        white-space: nowrap;
        font-weight: 800;
        height: 70%;

        @media (max-width: 1300px) {
          font-size: 18px;
          padding: 1rem 1rem !important;
        }

        @media (max-width: 1100px) {
          font-size: 16px;
        }

        @media (max-width: 800px) {
          padding: 0.5rem !important;
          white-space: wrap;
          font-size: 12px;
          min-width: 80px;
        }
      }
      .banner-button::before {
        content: "";
        position: absolute;
        inset: 0;
        padding: 2px;
        border-radius: 4px;
        background: linear-gradient(to right, #FFFFFF, #2B5E99);
        -webkit-mask:
          linear-gradient(#fff 0 0) content-box,
          linear-gradient(#fff 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        z-index: -1;
      }

      .banner-button-bg {
        position: absolute;
        background-image: url('/next-assets/images/banners/matt/matt-trial-v5.png');
        height: 80px;
        width: 682px;
        left: 0;
        background-position: right;
        background-size: contain;
        margin-left: -360px;
        @media (max-width: 1100px) {
          margin-left: -340px;
        }

        @media (max-width: 800px) {
          margin-left: -320px;
        }
      }
    }
  }
}

.lead-gen-v5 {
  background-color: #191C20;
  background-image: url('/next-assets/images/banners/leadgen/lead-gen-v5.png');
  background-position: left;
  background-repeat: no-repeat;
  background-size: cover;

  .banner {
    .banner-content div {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
    }

    p {
      color: white;
      font-size: 28px;
      font-weight: 700;
      line-height: 28px;
      font-family: Inter, sans-serif;
      text-transform: uppercase;
      display: flex;
      flex-direction: row;
      gap: 0.5rem;
      align-items: center;
      margin-top: 0.25rem;

      @media (max-width: 1450px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 1200px) {
        font-size: 20px;
        line-height: 20px;
      }
      @media (max-width: 1040px) {
        font-size: 16px;
        line-height: 18px;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
      }
      @media (max-width: 800px) {
        font-size: 14px;
      }
    }

    h3 {
      color: #E04838;
      font-family: Inter, sans-serif;
      font-weight: 800;
      font-size: 20px;
      line-height: 20px;
      text-transform: uppercase;
      margin-bottom: 0.5rem;
      letter-spacing: 10%;
      @media (max-width: 1200px) {
        font-size: 18px;
        line-height: 18px;
      }
      @media (max-width: 1100px) {
        font-size: 16px;
        line-height: 16px;
      }
      @media (max-width: 800px) {
        font-size: 10px;
        line-height: 10px;
        margin-bottom: 0.25rem;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;
      margin-left: 2rem;

      @media (max-width: 800px) {
        margin-left: 1rem;
      }

      .banner-button {
        position: relative;
        border-radius: 4px;
        font-size: 22px;
        font-weight: 800;
        height: 70%;
        white-space: nowrap;
        background-color: #E04838;
        text-transform: uppercase;

        @media (max-width: 1300px) {
          font-size: 18px;
          padding: 1rem 1rem !important;
        }

        @media (max-width: 1100px) {
          font-size: 16px;
          white-space: wrap;
          line-height: 14px;
        }

        @media (max-width: 800px) {
          font-size: 12px;
        }
      }

      .banner-button-bg {
        position: absolute;
        background-image: url('/next-assets/images/banners/matt/matt-trial-v6.png');
        height: 80px;
        width:100;
        left: 0;
        background-position: right;
        background-size: contain;
        margin-left: -360px;
        @media (max-width: 1100px) {
          margin-left: -340px;
        }

        @media (max-width: 800px) {
          margin-left: -320px;
        }
      }
    }
  }
}

.lead-gen-v3 {
  background: linear-gradient(270deg, #00203B 0%, #001120 100%);

  .banner {
    p {
      display: flex;
      flex-direction: row;
      gap: 0.5rem;
      color: white;
      font-size: 32px;
      line-height: 32px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      text-transform: uppercase;
      @media (max-width: 1320px) {
        font-size: 28px;
        line-height: 28px;
      }
      @media (max-width: 1240px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 1100px) {
        font-size: 20px;
      }
      @media (max-width: 900px) {
        flex-direction: column;
        font-size: 16px;
        line-height: 14px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      height: 100%;

      .banner-button {
        background-color: #3F83F8;
        font-size: 24px;
        line-height: 28px;
        white-space: nowrap;
        text-transform: uppercase;

        @media (max-width: 1400px) {
          font-size: 20px;
          line-height: 24px;
        }
        @media (max-width: 800px) {
          font-size: 14px;
          line-height: 16px;
          white-space: wrap;
        }
      }
    }
  }

  .banner-bg-end {
    background-image: url('/next-assets/images/banners/leadgen/lead-gen-v3.png');
    position: absolute;
    background-size: cover;
    background-repeat: no-repeat;
    width: 673px;
    height: 80px;
    top: 0;
    right: 0;
    background-position: right;

    @media (max-width: 800px) {
      right: -400px !important;
    }
  }
}

.lead-gen-v4 {
  background: #0F1F3D;

  .banner {
    p {
      display: flex;
      flex-direction: row;
      gap: 0.5rem;
      color: white;
      font-size: 30px;
      line-height: 32px;
      font-weight: 700;
      font-family: Inter, sans-serif;
      text-transform: capitalize;
      @media (max-width: 1320px) {
        font-size: 28px;
        line-height: 28px;
      }
      @media (max-width: 1240px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 1100px) {
        font-size: 20px;
      }
      @media (max-width: 900px) {
        flex-direction: column;
        font-size: 16px;
        line-height: 16px;
        gap: 0.25rem;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      height: 100%;

      .banner-button {
        background-color: #3F83F8;
        font-size: 24px;
        line-height: 28px;
        white-space: nowrap;
        text-transform: uppercase;

        @media (max-width: 1400px) {
          font-size: 20px;
          line-height: 24px;
        }
        @media (max-width: 800px) {
          font-size: 14px;
          line-height: 16px;
          white-space: wrap;
        }
      }
      .banner-button-bg {
        background-image: url('/next-assets/images/banners/leadgen/lead-gen-v4b.png');
        position: absolute;
        background-size: cover;
        background-repeat: no-repeat;
        width: 307px;
        height: 80px;
        top: 0;
        left: -210px;
      }
    }
  }

}

.matt-trial-v5 {
  background-color: #031C30;

  .banner {
    .banner-content div {
      display: flex;
      flex-direction: column-reverse;
      align-items: flex-start;
      justify-content: flex-start;
      margin-right: 2rem;
    }

    p {
      color: white;
      font-size: 30px;
      font-weight: 700;
      line-height: 28px;
      font-family: Inter, sans-serif;
      text-transform: uppercase;

      @media (max-width: 1380px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 1100px) {
        font-size: 20px;
        line-height: 20px;
      }
      @media (max-width: 800px) {
        font-size: 14px;
        line-height: 18px;
      }
    }

    h3 {
      color: white;
      font-family: Inter, sans-serif;
      font-weight: 500;
      font-size: 14px;
      line-height: 14px;
      letter-spacing: -2%;
      text-transform: capitalize;
      margin-top: 0.5rem;
      @media (max-width: 1200px) {
        font-size: 12px;
        line-height: 12px;
      }
      @media (max-width: 1100px) {
        font-size: 10px;
        line-height: 10px;
      }
      @media (max-width: 800px) {
        display: none;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;
      margin-left: 2rem;

      @media (max-width: 800px) {
        margin-left: 1rem;
      }

      .banner-button {
        position: relative;
        border-radius: 4px;
        background: linear-gradient(90deg, #479CFF 0%, #2B5E99 100%);
        font-size: 22px;
        white-space: nowrap;
        font-weight: 800;
        height: 70%;

        @media (max-width: 1300px) {
          font-size: 18px;
          padding: 1rem 1rem !important;
        }

        @media (max-width: 1100px) {
          font-size: 16px;
        }

        @media (max-width: 800px) {
          padding: 0.5rem !important;
          white-space: wrap;
          font-size: 12px;
          min-width: 80px;
        }
      }
      .banner-button::before {
        content: "";
        position: absolute;
        inset: 0;
        padding: 2px;
        border-radius: 4px;
        background: linear-gradient(to right, #FFFFFF, #2B5E99);
        -webkit-mask:
          linear-gradient(#fff 0 0) content-box,
          linear-gradient(#fff 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        z-index: -1;
      }

      .banner-button-bg {
        position: absolute;
        background-image: url('/next-assets/images/banners/matt/matt-trial-v5.png');
        height: 80px;
        width: 682px;
        left: 0;
        background-position: right;
        background-size: contain;
        margin-left: -360px;
        @media (max-width: 1100px) {
          margin-left: -340px;
        }

        @media (max-width: 800px) {
          margin-left: -320px;
        }
      }
    }
  }
}

.matt-trial-v6 {
  background-color: #0F1F3D;
  background-image: url('/next-assets/images/banners/matt/matt-trial-v6.png');
  background-position: left;
  background-repeat: no-repeat;
  background-size: cover;


  .banner {
    .banner-content div {
      display: flex;
    }

    p {
      color: white;
      font-size: 34px;
      font-weight: 700;
      line-height: 28px;
      font-family: Inter, sans-serif;
      text-transform: uppercase;

      @media (max-width: 1300px) {
        font-size: 30px;
        line-height: 22px;
      }
      @media (max-width: 1140px) {
        font-size: 28px;
        line-height: 28px;
      }
      @media (max-width: 1000px) {
        font-size: 24px;
        line-height: 22px;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        line-height: 18px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;
      margin-left: 1rem;

      .banner-button {
        position: relative;
        border-radius: 4px;
        font-size: 22px;
        font-weight: 800;
        height: 70%;
        text-transform: uppercase;
        background-color: #479CFF;

        @media (max-width: 1300px) {
          font-size: 18px;
        }

        @media (max-width: 800px) {
          font-size: 14px;
          height: fit-content
        }
      }
    }
  }
}

.lead-gen-v6 {
  background-color: #0F1F3D;
  background-image: url('/next-assets/images/banners/leadgen/lead-gen-v6.png');
  background-position: left;
  background-repeat: no-repeat;
  background-size: cover;

  .banner {
    p {
      color: white;
      font-size: 32px;
      font-weight: 700;
      line-height: 28px;
      font-family: Inter, sans-serif;
      text-transform: uppercase;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      gap: 0.5rem;
      align-items: center;
      margin-top: 0.25rem;

      @media (max-width: 1440px) {
        font-size: 30px;
        line-height: 22px;
      }
      @media (max-width: 1340px) {
        font-size: 26px;
        line-height: 20px;
      }
      @media (max-width: 1200px) {
        font-size: 22px;
        line-height: 20px;
      }
      @media (max-width: 800px) {
        font-size: 16px;
        gap: 0;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;
      margin-left: 2rem;

      @media (max-width: 800px) {
        margin-left: 1rem;
      }

      .banner-button {
        position: relative;
        border-radius: 4px;
        font-size: 22px;
        font-weight: 700;
        height: 70%;
        white-space: nowrap;
        background-color: #3F80F8;
        text-transform: uppercase;

        @media (max-width: 1400px) {
          font-size: 20px;
        }

        @media (max-width: 1200px) {
          font-size: 16px;
          white-space: wrap;
          line-height: 14px;
        }

        @media (max-width: 800px) {
          font-size: 12px;
          height: fit-content;
          min-height: 70%;
        }
      }
    }
  }
}

.pattern-trader-pro-v1, .pattern-trader-pro-v2 {
  background: #000000;
  position: relative;
  overflow: hidden;

  .banner {
    position: relative;
    z-index: 10;
    font-family: Inter, sans-serif;

    .banner-content {
      display: flex;
      align-items: center;
      gap: 1rem;
      position: relative;
      z-index: 20;

      .live-event-section {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        position: relative;

        .live-event {
          text-align: center;
          font-family: Inter, sans-serif;
          font-size: 10px;
          line-height: 12px;
          margin-top: 6px;
          letter-spacing: 0.25em;
          font-weight: 500;
          text-transform: uppercase;
          opacity: 0.8;
          white-space: nowrap;
        }

        @media (max-width: 800px) {
          display: none;
        }

        .live-event-icon {
          width: 20px;
          height: 20px;
          margin-left: -5px;
          background-image: url(/next-assets/images/banners/pattern-trader-pro-icon-v1.png);
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
          filter: drop-shadow(0 0 8px #00f3ff) drop-shadow(0 0 16px #00f3ff88);
          animation: glowCyan 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;

          @media (max-width: 800px) {
            width: 16px;
            height: 16px;
          }
        }

        .divider-line {
          width: 1px;
          height: 70px;
          background: #ffffff40;
          margin-left: 1rem;

          @media (max-width: 800px) {
            margin-left: 0.5rem;
          }
        }
      }

      .main-title {
        font-family: Inter, sans-serif;
        font-size: 33px;
        line-height: 1.14;
        font-weight: 400;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 0.25rem;

        .title-part-1 {
          color: #ffffff;
          font-weight: 700;
        }

        .title-part-2 {
          color: #00f3ff;
          font-weight: 700;
          font-style: italic;
          margin: 0 4px;
          @media (max-width: 800px) {
            margin: 0;
          }
        }

        .title-part-3 {
          color: #ffffff;
          font-weight: 700;
        }

        @media (max-width: 1443px) {
          font-size: 28px;
        }
        @media (max-width: 1270px) {
          font-size: 24px;
        }
        @media (max-width: 1000px) {
          font-size: 20px;
        }
        @media (max-width: 800px) {
          font-size: 16px;
          line-height: 1.2;
        }
      }
    }

    .description {
      font-family: Inter, sans-serif;
      font-weight: 600;
      @media (max-width: 800px) {
        display: none;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;
      z-index: 30;

      .banner-button {
        border-radius: 0.75rem;
        border: 2px solid rgba(255, 255, 255, 0.32);
        background: radial-gradient(45.93% 147.72% at 50% -12.27%, rgba(0, 243, 255, 0.70) 0%, rgba(32, 177, 185, 0.70) 100%), radial-gradient(321.78% 334.63% at 50% 125.45%, #00F3FF 0%, #2AE1EA 100%), linear-gradient(90deg, #00F3FF 0%, #00F3FF 20%, #00F3FF 80%, #00F3FF 100%);
        box-shadow: 0px 12px 44px 0px rgba(252, 214, 255, 0.34) inset, 0px -8px 44px 0px rgba(0, 0, 0, 0.34) inset;
        color: #ffffff;
        text-align: center;
        font-family: Inter, sans-serif;
        font-size: 17px;
        line-height: 19px;
        font-weight: 700;
        text-transform: uppercase;
        white-space: nowrap;
        padding: 0 !important;
        width: 213px;
        height: 44.31px;
        display: flex;
        align-items: center;
        justify-content: center;

        @media (max-width: 1000px) {
          font-size: 15px;
          width: 180px;
          height: 38px;
        }

        @media (max-width: 800px) {
          font-size: 12px;
          line-height: 14px;
          white-space: wrap;
          text-align: center;
          width: 150px;
          height: 32px;
        }
      }
    }
  }

  .banner-bg-end {
    position: absolute;
    right: 0;
    top: 0;
    width: 944px;
    height: 80px;
    background-image: url('/next-assets/images/banners/pattern-trader-pro-bg-v1.png');
    background-position: right;
    background-size: contain;
    background-repeat: no-repeat;
    z-index: 1;

    -webkit-mask: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 0, 0, 0.1) 15%,
      rgba(0, 0, 0, 0.5) 30%,
      rgba(0, 0, 0, 0.8) 50%,
      rgba(0, 0, 0, 1) 70%,
      rgba(0, 0, 0, 1) 100%
    );
    mask: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 0, 0, 0.1) 15%,
      rgba(0, 0, 0, 0.5) 30%,
      rgba(0, 0, 0, 0.8) 50%,
      rgba(0, 0, 0, 1) 70%,
      rgba(0, 0, 0, 1) 100%
    );

    @media (max-width: 1200px) {
      right: -100px !important;
    }
    @media (max-width: 800px) {
      right: -200px !important;
      opacity: 0.8;
    }
  }
}

.pattern-trader-pro-v3 {
  background: #0e1743;
  position: relative;
  overflow: hidden;

  .banner {
    position: relative;
    z-index: 10;
    font-family: Inter, sans-serif;

    .banner-content {
      display: flex;
      align-items: center;
      gap: 1rem;
      position: relative;
      z-index: 20;

      .live-event-section {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        position: relative;

        .live-event {
          color: #FFFFFF;
          text-align: center;
          font-family: Inter, sans-serif;
          font-size: 10px;
          line-height: 12px;
          margin-top: 6px;
          letter-spacing: 0.25em;
          font-weight: 500;
          text-transform: uppercase;
          opacity: 0.8;
          white-space: nowrap;
        }

        @media (max-width: 800px) {
          display: none;
        }

        .live-event-icon {
          width: 25px;
          height: 25px;
          margin-left: -5px;
          background-image: url(/next-assets/images/banners/pattern-trader-pro-icon-v3.png);
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
          filter: drop-shadow(0 0 8px #BF36EC) drop-shadow(0 0 16px #BF36EC88);
          animation: glowPurple 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;

          @media (max-width: 800px) {
            width: 16px;
            height: 16px;
          }
        }

        .divider-line {
          width: 1px;
          height: 70px;
          background: #ffffff40;
          margin-left: 1rem;

          @media (max-width: 800px) {
            margin-left: 0.5rem;
          }
        }
      }

      .main-title {
        font-family: Inter, sans-serif;
        font-size: 33px;
        line-height: 1.14;
        font-weight: 400;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 0.25rem;
        font-weight: 700;

        .title-part-1 {
          color: #BF36EC;
        }

        .title-part-2 {
          color: #ffffff;
        }

        .title-part-3 {
          color: #ffffff;
        }

        @media (max-width: 1443px) {
          font-size: 28px;
        }
        @media (max-width: 1270px) {
          font-size: 24px;
        }
        @media (max-width: 1000px) {
          font-size: 20px;
        }
        @media (max-width: 800px) {
          font-size: 16px;
          line-height: 1.2;
        }
      }
    }

    .description {
      font-family: Inter, sans-serif;
      font-weight: 600;
      @media (max-width: 800px) {
        display: none;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;
      z-index: 30;

      .banner-button {
        border-radius: 0.75rem;
        border: 2px solid rgba(255, 255, 255, 0.32);
        background: radial-gradient(45.93% 147.72% at 50% -12.27%, rgba(192, 62, 235, 0.70) 0%, rgba(255, 255, 255, 0.00) 71.15%), radial-gradient(321.78% 334.63% at 50% 125.45%, #8F22B3 0%, rgba(192, 62, 235, 0.00) 86.54%), linear-gradient(90deg, #BF36EC 0%, #C03EEB 20%, #C03EEB 57.21%, #BF36EC 100%);
        box-shadow: 0px 12px 44px 0px rgba(252, 214, 255, 0.34) inset, 0px -8px 44px 0px rgba(0, 0, 0, 0.34) inset;
        color: #ffffff;
        text-align: center;
        font-family: Inter, sans-serif;
        font-size: 17px;
        line-height: 19px;
        font-weight: 700;
        text-transform: uppercase;
        white-space: nowrap;
        padding: 0 !important;
        width: 213px;
        height: 44.31px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;

        @media (max-width: 1000px) {
          font-size: 15px;
          width: 180px;
          height: 38px;
        }

        @media (max-width: 800px) {
          font-size: 12px;
          line-height: 14px;
          white-space: wrap;
          text-align: center;
          width: 150px;
          height: 32px;
        }
      }
    }
  }

  .banner-bg-end {
    position: absolute;
    right: 0;
    top: 0;
    width: 100%;
    height: 80px;
    background-image: url('/next-assets/images/banners/pattern-trader-pro-bg-v3.png');
    background-position: right;
    background-size: cover;
    background-repeat: no-repeat;
    z-index: 1;
    opacity: 0.4;
    filter: blur(1px);

    -webkit-mask: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 0, 0, 0.1) 15%,
      rgba(0, 0, 0, 0.5) 30%,
      rgba(0, 0, 0, 0.8) 50%,
      rgba(0, 0, 0, 1) 70%,
      rgba(0, 0, 0, 1) 100%
    );
    mask: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 0, 0, 0.1) 15%,
      rgba(0, 0, 0, 0.5) 30%,
      rgba(0, 0, 0, 0.8) 50%,
      rgba(0, 0, 0, 1) 70%,
      rgba(0, 0, 0, 1) 100%
    );

    @media (max-width: 1200px) {
      right: -100px !important;
    }
    @media (max-width: 800px) {
      right: -200px !important;
      opacity: 0.8;
    }
  }
}

.pattern-trader-pro-v4 {
  background: #0e1743;
  position: relative;
  overflow: hidden;

  .banner {
    position: relative;
    z-index: 10;
    font-family: Inter, sans-serif;

    .banner-content {
      display: flex;
      align-items: center;
      gap: 1rem;
      position: relative;
      z-index: 20;
      height: 100%;

      .main-title {
        font-family: Inter, sans-serif;
        font-size: 33px;
        line-height: 1.14;
        font-weight: 400;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 0.25rem;
        font-weight: 700;

        .title-part-1 {
          color: #ffffff;
        }

        .title-part-2 {
          color: #BF36EC;
        }

        .title-part-3 {
          color: #ffffff;
        }

        @media (max-width: 1443px) {
          font-size: 28px;
        }
        @media (max-width: 1270px) {
          font-size: 24px;
        }
        @media (max-width: 1000px) {
          font-size: 20px;
        }
        @media (max-width: 800px) {
          font-size: 16px;
          line-height: 1.2;
        }
      }
    }

    .description {
      font-family: Inter, sans-serif;
      font-weight: 600;
      @media (max-width: 800px) {
        display: none;
      }
      @media (max-width: 1100px) {
        font-size: 12px;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;
      z-index: 30;

      .banner-button {
        border-radius: 0.75rem;
        border: 2px solid rgba(255, 255, 255, 0.32);
        background: radial-gradient(45.93% 147.72% at 50% -12.27%, rgba(192, 62, 235, 0.70) 0%, rgba(255, 255, 255, 0.00) 71.15%), radial-gradient(321.78% 334.63% at 50% 125.45%, #8F22B3 0%, rgba(192, 62, 235, 0.00) 86.54%), linear-gradient(90deg, #BF36EC 0%, #C03EEB 20%, #C03EEB 57.21%, #BF36EC 100%);
        box-shadow: 0px 12px 44px 0px rgba(252, 214, 255, 0.34) inset, 0px -8px 44px 0px rgba(0, 0, 0, 0.34) inset;
        color: #ffffff;
        text-align: center;
        font-family: Inter, sans-serif;
        font-size: 17px;
        line-height: 19px;
        font-weight: 700;
        text-transform: uppercase;
        white-space: nowrap;
        padding: 0 !important;
        width: 213px;
        height: 44.31px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;

        @media (max-width: 1000px) {
          font-size: 15px;
          width: 180px;
          height: 38px;
        }

        @media (max-width: 800px) {
          font-size: 12px;
          line-height: 14px;
          white-space: wrap;
          text-align: center;
          width: 150px;
          height: 32px;
        }
      }
    }
  }

  .tom-gentile-image {
    width: 80px;
    height: 70px;
    background-image: url('/next-assets/images/banners/tom_gentile_v4.png');
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    margin-left: 1rem;
    flex-shrink: 0;
    margin-top: auto;

    @media (max-width: 800px) {
      display: none;
    }
  }

  .banner-bg-end {
    position: absolute;
    right: 0;
    top: 0;
    width: 944px;
    height: 80px;
    background-image: url('/next-assets/images/banners/pattern-trader-pro-bg-v3.png');
    background-position: right;
    background-size: cover;
    background-repeat: no-repeat;
    z-index: 1;
    opacity: 0.4;
    filter: blur(1px);

    -webkit-mask: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 0, 0, 0.1) 15%,
      rgba(0, 0, 0, 0.5) 30%,
      rgba(0, 0, 0, 0.8) 50%,
      rgba(0, 0, 0, 1) 70%,
      rgba(0, 0, 0, 1) 100%
    );
    mask: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 0, 0, 0.1) 15%,
      rgba(0, 0, 0, 0.5) 30%,
      rgba(0, 0, 0, 0.8) 50%,
      rgba(0, 0, 0, 1) 70%,
      rgba(0, 0, 0, 1) 100%
    );

    @media (max-width: 1200px) {
      right: -100px !important;
    }
    @media (max-width: 800px) {
      right: -200px !important;
      opacity: 0.8;
      background-size: cover;
    }
  }
}

.pattern-trader-pro-v3b {
  background: #0e1743;
  position: relative;
  overflow: hidden;

  .banner {
    position: relative;
    z-index: 10;
    font-family: Inter, sans-serif;

    .banner-content {
      align-items: center;
    }

    .content {
      display: flex;
      flex-direction: column-reverse;
      padding-left: 1rem;
      @media (max-width: 800px) {
        padding-left: 0;
      }
    }

    p {
      font-family: Inter, sans-serif;
      font-size: 26px;
      line-height: 1.14;
      font-weight: 400;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 0.25rem;
      font-weight: 700;

      @media (max-width: 1280px) {
        font-size: 22px;
      }
      @media (max-width: 1125px) {
        font-size: 18px;
      }
      @media (max-width: 800px) {
        line-height: 1.2;
      }
    }

    h3 {
      font-family: Inter, sans-serif;
      font-size: 16px;
      font-weight: 600;
      color: white;
      @media (max-width: 1280px) {
        font-size: 14px;
      }
      @media (max-width: 1125px) {
        font-size: 13px;
      }
      @media (max-width: 1000px) {
        display: none;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;
      z-index: 30;

      .banner-button {
        border-radius: 0.75rem;
        border: 2px solid rgba(255, 255, 255, 0.32);
        background: radial-gradient(45.93% 147.72% at 50% -12.27%, rgba(192, 62, 235, 0.70) 0%, rgba(255, 255, 255, 0.00) 71.15%), radial-gradient(321.78% 334.63% at 50% 125.45%, #8F22B3 0%, rgba(192, 62, 235, 0.00) 86.54%), linear-gradient(90deg, #BF36EC 0%, #C03EEB 20%, #C03EEB 57.21%, #BF36EC 100%);
        box-shadow: 0px 12px 44px 0px rgba(252, 214, 255, 0.34) inset, 0px -8px 44px 0px rgba(0, 0, 0, 0.34) inset;
        color: #ffffff;
        text-align: center;
        font-family: Inter, sans-serif;
        font-size: 15px;
        line-height: 19px;
        font-weight: 700;
        text-transform: uppercase;
        white-space: nowrap;
        padding: 0 !important;
        width: 213px;
        height: 44.31px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;

        @media (max-width: 1000px) {
          font-size: 15px;
          width: 180px;
          height: 38px;
        }

        @media (max-width: 800px) {
          font-size: 12px;
          line-height: 14px;
          white-space: wrap;
          text-align: center;
          width: 120px;
        }
      }
    }
  }

  .banner-bg-end {
    position: absolute;
    right: 0;
    top: 0;
    width: 100%;
    height: 80px;
    background-image: url('/next-assets/images/banners/pattern-trader-pro-bg-v3.png');
    background-position: right;
    background-size: cover;
    background-repeat: no-repeat;
    z-index: 1;
    opacity: 0.2;
    filter: blur(1px);

    -webkit-mask: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 0, 0, 0.1) 15%,
      rgba(0, 0, 0, 0.5) 30%,
      rgba(0, 0, 0, 0.8) 50%,
      rgba(0, 0, 0, 1) 70%,
      rgba(0, 0, 0, 1) 100%
    );
    mask: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 0, 0, 0.1) 15%,
      rgba(0, 0, 0, 0.5) 30%,
      rgba(0, 0, 0, 0.8) 50%,
      rgba(0, 0, 0, 1) 70%,
      rgba(0, 0, 0, 1) 100%
    );

    @media (max-width: 1200px) {
      right: -100px !important;
    }
    @media (max-width: 800px) {
      right: -200px !important;
      opacity: 0.8;
    }
  }
}

.pattern-trader-pro-v4b {
  background: #0e1743;
  position: relative;
  overflow: hidden;

  .banner {
    position: relative;
    z-index: 10;
    font-family: Inter, sans-serif;

    .banner-content {
      align-items: center;
    }

    .live-event-section {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      position: relative;

      .live-event {
        color: #FFFFFF;
        text-align: center;
        font-family: Inter, sans-serif;
        font-size: 10px;
        line-height: 12px;
        margin-top: 6px;
        letter-spacing: 0.25em;
        font-weight: 500;
        text-transform: uppercase;
        opacity: 0.8;
        white-space: nowrap;
      }

      @media (max-width: 800px) {
        display: none;
      }

      .live-event-icon {
        width: 25px;
        height: 25px;
        margin-left: -5px;
        background-image: url(/next-assets/images/banners/pattern-trader-pro-icon-v3.png);
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        filter: drop-shadow(0 0 8px #BF36EC) drop-shadow(0 0 16px #BF36EC88);
        animation: glowPurple 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;

        @media (max-width: 800px) {
          width: 16px;
          height: 16px;
        }
      }

      .divider-line {
        width: 1px;
        height: 70px;
        background: #ffffff40;
        margin-left: 1rem;

        @media (max-width: 800px) {
          margin-left: 0.5rem;
        }
      }
    }

    .content {
      display: flex;
      flex-direction: column-reverse;
      padding-left: 1rem;
      @media (max-width: 800px) {
        padding-left: 0;
      }
    }

    p {
      font-family: Inter, sans-serif;
      font-size: 29px;
      line-height: 1.14;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 0.25rem;
      font-weight: 700;

      @media (max-width: 1320px) {
        font-size: 24px;
      }
      @media (max-width: 1175px) {
        font-size: 18px;
      }
      @media (max-width: 1000px) {
        font-size: 16px;
      }
      @media (max-width: 800px) {
        // font-size: 14px;
        line-height: 1.2;
      }
    }

    h3 {
      color: white;
      font-family: Inter, sans-serif;
      font-weight: 600;
      font-size: 16px;

      @media (max-width: 1320px) {
        font-size: 14px;
      }
      @media (max-width: 1175px) {
        font-size: 12px;
      }
      @media (max-width: 1000px) {
        display: 10px;
      }
      @media (max-width: 800px) {
        display: none;
      }
    }

    .button-wrapper {
      align-items: center;
      justify-content: center;
      position: relative;
      z-index: 30;

      .banner-button {
        border-radius: 0.75rem;
        border: 2px solid rgba(255, 255, 255, 0.32);
        background: radial-gradient(45.93% 147.72% at 50% -12.27%, rgba(192, 62, 235, 0.70) 0%, rgba(255, 255, 255, 0.00) 71.15%), radial-gradient(321.78% 334.63% at 50% 125.45%, #8F22B3 0%, rgba(192, 62, 235, 0.00) 86.54%), linear-gradient(90deg, #BF36EC 0%, #C03EEB 20%, #C03EEB 57.21%, #BF36EC 100%);
        box-shadow: 0px 12px 44px 0px rgba(252, 214, 255, 0.34) inset, 0px -8px 44px 0px rgba(0, 0, 0, 0.34) inset;
        color: #ffffff;
        text-align: center;
        font-family: Inter, sans-serif;
        font-size: 15px;
        line-height: 19px;
        font-weight: 700;
        text-transform: uppercase;
        white-space: nowrap;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        width: fit-content;

        @media (max-width: 1000px) {
          font-size: 15px;
          line-height: 16px;
          white-space: wrap;
          width: 250px;
        }

        @media (max-width: 800px) {
          font-size: 12px;
          line-height: 14px;
          text-align: center;
          width: 120px;
        }
      }
    }
  }

  .banner-bg-end {
    position: absolute;
    right: 0;
    top: 0;
    width: 944px;
    height: 80px;
    background-image: url('/next-assets/images/banners/pattern-trader-pro-bg-v3.png');
    background-position: right;
    background-size: cover;
    background-repeat: no-repeat;
    z-index: 1;
    opacity: 0.4;
    filter: blur(1px);

    -webkit-mask: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 0, 0, 0.1) 15%,
      rgba(0, 0, 0, 0.5) 30%,
      rgba(0, 0, 0, 0.8) 50%,
      rgba(0, 0, 0, 1) 70%,
      rgba(0, 0, 0, 1) 100%
    );
    mask: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 0, 0, 0.1) 15%,
      rgba(0, 0, 0, 0.5) 30%,
      rgba(0, 0, 0, 0.8) 50%,
      rgba(0, 0, 0, 1) 70%,
      rgba(0, 0, 0, 1) 100%
    );

    @media (max-width: 1200px) {
      right: -100px !important;
    }
    @media (max-width: 800px) {
      right: -200px !important;
      opacity: 0.2;
      background-size: cover;
    }
  }
}

@keyframes glowCyan {
  0%, 100% {
    filter: drop-shadow(0 0 8px #00f3ff) drop-shadow(0 0 16px #00f3ff88);
  }
  50% {
    filter: drop-shadow(0 0 12px #00f3ff) drop-shadow(0 0 20px #00f3ff) drop-shadow(0 0 24px #00f3ff66);
  }
}

@keyframes glowPurple {
  0%, 100% {
    filter: drop-shadow(0 0 8px #BF36EC) drop-shadow(0 0 16px #BF36EC88);
  }
  50% {
    filter: drop-shadow(0 0 12px #BF36EC) drop-shadow(0 0 20px #BF36EC) drop-shadow(0 0 24px #BF36EC66);
  }
}
