{"name": "bz-mobile", "version": "3.5.9.5", "private": true, "dependencies": {"metro-config": "*", "@coralogix/react-native-sdk": "^1.0.15", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/netinfo": "11.3.1", "@react-native-firebase/analytics": "19.2.2", "@react-native-firebase/app": "19.2.2", "@react-navigation/bottom-tabs": "6.5.20", "@react-navigation/material-top-tabs": "6.6.13", "@react-navigation/native": "6.1.17", "@react-navigation/stack": "6.3.29", "@segment/analytics-react-native": "2.19.1", "@segment/sovran-react-native": "1.1.1", "@taboola/react-native-plugin-3x": "3.1.2", "@testing-library/jest-native": "*", "@testing-library/react-native": "*", "chartbeat-react-native-sdk": "^0.0.22", "connatix-player-sdk-react-native": "^2.4.6", "deprecated-react-native-prop-types": "5.0.0", "expo": "~51.0.31", "expo-apple-authentication": "~6.4.2", "expo-asset": "10.0.10", "expo-auth-session": "5.5.2", "expo-av": "~14.0.7", "expo-blur": "~13.0.2", "expo-build-properties": "~0.12.5", "expo-clipboard": "6.0.3", "expo-constants": "~16.0.2", "expo-crypto": "13.0.2", "expo-dev-client": "~4.0.26", "expo-device": "6.0.2", "expo-document-picker": "~12.0.2", "expo-file-system": "17.0.1", "expo-font": "~12.0.10", "expo-haptics": "13.0.1", "expo-image-manipulator": "12.0.5", "expo-image-picker": "~15.0.7", "expo-intent-launcher": "11.0.1", "expo-linear-gradient": "13.0.2", "expo-linking": "6.3.1", "expo-media-library": "16.0.4", "expo-notifications": "~0.28.16", "expo-random": "14.0.1", "expo-secure-store": "13.0.2", "expo-sharing": "12.0.1", "expo-splash-screen": "~0.27.5", "expo-status-bar": "1.12.1", "expo-store-review": "7.0.2", "expo-structured-headers": "3.8.0", "expo-system-ui": "~3.0.7", "expo-updates": "~0.25.24", "expo-web-browser": "13.0.3", "lodash": "^4.17.21", "lottie-react-native": "7.0.0", "moment": "^2.29.1", "moment-timezone": "^0.5.26", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.74.5", "react-native-actionsheet": "2.4.2", "react-native-collapsible": "1.6.1", "react-native-device-info": "10.13.2", "react-native-disable-battery-optimizations-android": "1.0.7", "react-native-dotenv": "^3.4.11", "react-native-event-listeners": "1.0.3", "react-native-gesture-handler": "2.16.1", "react-native-get-random-values": "1.11.0", "react-native-global-props": "1.1.5", "react-native-google-mobile-ads": "14.1.0", "react-native-iap": "12.15.1", "react-native-image-crop-picker": "0.41.2", "react-native-image-zoom-viewer": "3.0.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-modal": "13.0.1", "react-native-pager-view": "6.3.0", "react-native-paper": "4.9.2", "react-native-permissions": "^4.1.5", "react-native-reanimated": "3.10.1", "react-native-render-html": "6.3.4", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-shimmer-placeholder": "2.0.9", "react-native-tab-view": "3.5.2", "react-native-svg": "15.1.0", "react-native-svg-transformer": "1.5.0", "react-native-track-player": "4.1.1", "react-native-url-polyfill": "1.3.0", "react-native-vector-icons": "10.1.0", "react-native-webrtc": "124.0.3", "react-native-webview": "13.8.6", "react-redux": "8.0.5", "redux-devtools-extension": "^2.13.8", "redux-persist": "6.0.0", "redux-saga": "1.3.0", "stream-chat": "^8.1.3", "stream-chat-expo": "5.6.1", "stream-chat-react": "^10.4.2", "uuid": "^8.3.2", "victory-native": "36.9.2", "patch-package": "^8.0.0", "@miblanchard/react-native-slider": "^2.6.0"}, "devDependencies": {"@babel/core": "^7.12.9", "typescript": "5.4.5"}, "resolutions": {"promise": "8.3.0"}, "scripts": {"expo-cli": "expo-cli", "expo": "expo", "eas": "eas", "eas-build-pre-install": "cd ../../ && pwd && yarn install && yarn patch-package && cp yarn.lock ./apps/bz-mobile/ && cp -R patches ./apps/bz-mobile && mv ./apps/bz-mobile/eas.js ./index.js", "eas-build-post-install": "yarn update-webrtc-bins", "update-webrtc-bins": "if [ -d \"ios/pods\" ]; then \n rm ./ios/Pods/JitsiWebRTC/WebRTC.xcframework/ios-arm64/WebRTC.framework/WebRTC && rm ./ios/Pods/JitsiWebRTC/WebRTC.xcframework/ios-arm64_x86_64-simulator/WebRTC.framework/WebRTC && cp ./libs/WebRTC.xcframework/ios-arm64/WebRTC.framework/WebRTC ./ios/Pods/JitsiWebRTC/WebRTC.xcframework/ios-arm64/WebRTC.framework/ && cp ./libs/WebRTC.xcframework/ios-arm64_x86_64-simulator/WebRTC.framework/WebRTC ./ios/Pods/JitsiWebRTC/WebRTC.xcframework/ios-arm64_x86_64-simulator/WebRTC.framework/ \n fi", "postinstall": "patch-package && rm -r node_modules && cd ../../ && yarn install && npx nx sync-deps bz-mobile && npx nx ensure-symlink bz-mobile", "patch:gradle": "if [ -d \"android\" ]; then \n sed -i\".backup\" \"s/def projectRoot = rootDir.getAbsoluteFile().getParentFile().getAbsolutePath()/def projectRoot = rootDir.getAbsoluteFile().getParentFile().getParentFile().getParentFile().getAbsolutePath()/g\" ./android/app/build.gradle \n fi", "start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios"}}