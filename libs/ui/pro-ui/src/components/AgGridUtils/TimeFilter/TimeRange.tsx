'use client';
import React, { useCallback } from 'react';
import { Button } from 'antd';
import { ButtonProps } from 'antd/lib/button';
import classnames from 'classnames';
// import dayjs, { Dayjs } from 'dayjs';
import { DateTime } from 'luxon';

import styled, { ThemedProps } from '@benzinga/themetron';

import TimePicker, { TimePickerProps } from './TimePicker';

export type TimeString = string | undefined;

export interface TimeRangeInterface {
  isExactTime?: boolean;
  timeFrom?: TimeString;
  timeTo?: TimeString;
}

interface Props {
  addRange: () => void;
  index: number;
  isExactTime: boolean | undefined;
  modifyRange: (index: number, timeFrom?: string, timeTo?: string, isExactTime?: boolean, remove?: boolean) => void;
  ranges: TimeRangeInterface[];
  timeFrom: TimeString;
  timeTo: TimeString;
}

export const TIME_FORMAT = 'HH:mm:ss';
export const DEFAULT_TIME_FROM = '00:00:00';
const DEFAULT_TIME_TO = '23:59:59';
const DEFAULT_OPEN_VALUE = '12:00:00';
export const getTimeNumber = (time: string): number => +time.split(':').join('');

const TimeRange: React.FC<Props> = ({ addRange, index, isExactTime, modifyRange, ranges, timeFrom, timeTo }) => {
  const isFirst = !index;
  const isLast = !(ranges.length - index - 1);
  const showAdd = !!(timeFrom || (timeTo && !isExactTime)) && !(ranges.length - index - 1);
  const remove = (!(timeFrom || (timeTo && !isExactTime)) || !!(ranges.length - index - 1)) && !(isFirst && isLast);

  const handleFromChange = useCallback(
    (date: DateTime | DateTime[] | null, _dateString: string | string[]) => {
      const singleDate = Array.isArray(date) ? date[0] : date;
      const newTimeFrom = singleDate ? singleDate.toFormat(TIME_FORMAT) : undefined;
      const newTimeTo =
        newTimeFrom && timeTo && getTimeNumber(newTimeFrom) <= getTimeNumber(timeTo) ? timeTo : undefined;
      modifyRange(index, newTimeFrom, newTimeTo, isExactTime);
    },

    [modifyRange, isExactTime, timeTo, index],
  );

  const handleToChange = useCallback(
    (date: DateTime | DateTime[] | null, _dateString: string | string[]) => {
      const singleDate = Array.isArray(date) ? date[0] : date;
      const newTimeTo = singleDate ? singleDate.toFormat(TIME_FORMAT) : undefined;
      const newTimeFrom =
        newTimeTo && timeFrom && getTimeNumber(newTimeTo) >= getTimeNumber(timeFrom) ? timeFrom : undefined;
      modifyRange(index, newTimeFrom, newTimeTo, isExactTime);
    },
    [modifyRange, isExactTime, timeFrom, index],
  );

  const handleExactTimeChange = useCallback(() => {
    modifyRange(index, timeFrom, timeTo, !isExactTime);
  }, [modifyRange, index, timeFrom, timeTo, isExactTime]);

  const handleAddRange = useCallback(() => {
    if (showAdd) {
      addRange();
    }
  }, [showAdd, addRange]);

  const handleClearRemove = useCallback(() => {
    modifyRange(index, undefined, undefined, isExactTime, remove);
  }, [modifyRange, isExactTime, index, remove]);

  return (
    <StyledTimeRange className="bz-time-picker">
      <div>
        <StyledTimePicker
          allowClear={false}
          defaultOpenValue={
            timeFrom && DateTime.fromFormat(timeFrom, TIME_FORMAT).isValid
              ? DateTime.fromFormat(timeFrom, TIME_FORMAT)
              : DateTime.fromFormat(DEFAULT_OPEN_VALUE, TIME_FORMAT)
          }
          dropdownClassName={classnames('bz-time-picker-dropdown', 'ag-custom-component-popup')}
          exactTimePicker
          isExactTime={isExactTime}
          onChange={handleFromChange}
          placeholder={DEFAULT_TIME_FROM}
          value={
            timeFrom && DateTime.fromFormat(timeFrom, TIME_FORMAT).isValid
              ? DateTime.fromFormat(timeFrom, TIME_FORMAT)
              : undefined
          }
        />
        <StyledTimePicker
          allowClear={false}
          defaultOpenValue={
            timeTo && DateTime.fromFormat(timeTo, TIME_FORMAT).isValid
              ? DateTime.fromFormat(timeTo, TIME_FORMAT)
              : DateTime.fromFormat(DEFAULT_OPEN_VALUE, TIME_FORMAT)
          }
          dropdownClassName={classnames('bz-time-picker-dropdown', 'ag-custom-component-popup')}
          isExactTime={isExactTime}
          onChange={handleToChange}
          placeholder={DEFAULT_TIME_TO}
          value={
            timeTo && DateTime.fromFormat(timeTo, TIME_FORMAT).isValid
              ? DateTime.fromFormat(timeTo, TIME_FORMAT)
              : undefined
          }
        />
      </div>
      <StyledButtonsContainer>
        <StyledButton double onClick={handleExactTimeChange} size="small">
          {isExactTime ? 'Time Range' : 'Exact Time'}
        </StyledButton>
        {showAdd && (
          <StyledButton onClick={handleAddRange} size="small">
            Add
          </StyledButton>
        )}
        <StyledButton double={!showAdd} onClick={handleClearRemove} size="small">
          {remove ? 'Remove' : 'Clear'}
        </StyledButton>
      </StyledButtonsContainer>
    </StyledTimeRange>
  );
};

export default TimeRange;

const StyledTimeRange = styled.div`
  width: 256px;
  & .bz-time-picker-buttons {
    height: 24px;
    overflow: hidden;
  }
`;

interface StyledTimePickerProps extends TimePickerProps, ThemedProps {
  exactTimePicker?: boolean;
  isExactTime?: boolean;
}

const StyledTimePicker = styled(TimePicker)<StyledTimePickerProps>`
  width: ${props => (props.isExactTime ? '100% !important' : '50% !important')};
  display: ${props => (!props.exactTimePicker && props.isExactTime ? 'none !important' : 'initial')};
  & .ant-picker-input {
    input {
      padding-left: 16px !important;
      padding-right: 14px !important;
      text-align: center !important;
    }
  }
  & .anticon-clock-circle {
    color: ${props => (props.theme.name === 'light' ? props.theme.colors.foreground : '')};
  }
  & input::placeholder {
    color: ${props => (props.theme.name === 'light' ? props.theme.colors.foreground : '')};
  }
`;

interface StyledButtonProps extends ButtonProps {
  double?: boolean;
}

const StyledButton = styled(Button)<StyledButtonProps>`
  width: ${props => (props.double ? '50%' : '25%')};
  & .ant-btn:focus {
    color: inherit !important;
    border-color: inherit !important;
  }
`;

const StyledButtonsContainer = styled.div`
  height: 24px;
  overflow: hidden;
`;
