import * as $protobuf from "protobufjs";
/** Namespace QuoteWSProtos. */
export namespace QuoteWSProtos {

    /** Properties of an AddUpdateQuoteSubscriptionRequest. */
    interface IAddUpdateQuoteSubscriptionRequest {

        /** AddUpdateQuoteSubscriptionRequest symbol */
        symbol?: (string|null);

        /** AddUpdateQuoteSubscriptionRequest subscribeFields */
        subscribeFields?: (string[]|null);

        /** AddUpdateQuoteSubscriptionRequest unsubscribeFields */
        unsubscribeFields?: (string[]|null);
    }

    /** Represents an AddUpdateQuoteSubscriptionRequest. */
    class AddUpdateQuoteSubscriptionRequest implements IAddUpdateQuoteSubscriptionRequest {

        /**
         * Constructs a new AddUpdateQuoteSubscriptionRequest.
         * @param [properties] Properties to set
         */
        constructor(properties?: QuoteWSProtos.IAddUpdateQuoteSubscriptionRequest);

        /** AddUpdateQuoteSubscriptionRequest symbol. */
        public symbol: string;

        /** AddUpdateQuoteSubscriptionRequest subscribeFields. */
        public subscribeFields: string[];

        /** AddUpdateQuoteSubscriptionRequest unsubscribeFields. */
        public unsubscribeFields: string[];

        /**
         * Creates a new AddUpdateQuoteSubscriptionRequest instance using the specified properties.
         * @param [properties] Properties to set
         * @returns AddUpdateQuoteSubscriptionRequest instance
         */
        public static create(properties?: QuoteWSProtos.IAddUpdateQuoteSubscriptionRequest): QuoteWSProtos.AddUpdateQuoteSubscriptionRequest;

        /**
         * Encodes the specified AddUpdateQuoteSubscriptionRequest message. Does not implicitly {@link QuoteWSProtos.AddUpdateQuoteSubscriptionRequest.verify|verify} messages.
         * @param message AddUpdateQuoteSubscriptionRequest message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: QuoteWSProtos.IAddUpdateQuoteSubscriptionRequest, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified AddUpdateQuoteSubscriptionRequest message, length delimited. Does not implicitly {@link QuoteWSProtos.AddUpdateQuoteSubscriptionRequest.verify|verify} messages.
         * @param message AddUpdateQuoteSubscriptionRequest message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: QuoteWSProtos.IAddUpdateQuoteSubscriptionRequest, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an AddUpdateQuoteSubscriptionRequest message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns AddUpdateQuoteSubscriptionRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): QuoteWSProtos.AddUpdateQuoteSubscriptionRequest;

        /**
         * Decodes an AddUpdateQuoteSubscriptionRequest message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns AddUpdateQuoteSubscriptionRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): QuoteWSProtos.AddUpdateQuoteSubscriptionRequest;

        /**
         * Verifies an AddUpdateQuoteSubscriptionRequest message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an AddUpdateQuoteSubscriptionRequest message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns AddUpdateQuoteSubscriptionRequest
         */
        public static fromObject(object: { [k: string]: any }): QuoteWSProtos.AddUpdateQuoteSubscriptionRequest;

        /**
         * Creates a plain object from an AddUpdateQuoteSubscriptionRequest message. Also converts values to other types if specified.
         * @param message AddUpdateQuoteSubscriptionRequest
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: QuoteWSProtos.AddUpdateQuoteSubscriptionRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this AddUpdateQuoteSubscriptionRequest to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a RemoveQuoteSubscriptionRequest. */
    interface IRemoveQuoteSubscriptionRequest {

        /** RemoveQuoteSubscriptionRequest symbol */
        symbol?: (string|null);
    }

    /** Represents a RemoveQuoteSubscriptionRequest. */
    class RemoveQuoteSubscriptionRequest implements IRemoveQuoteSubscriptionRequest {

        /**
         * Constructs a new RemoveQuoteSubscriptionRequest.
         * @param [properties] Properties to set
         */
        constructor(properties?: QuoteWSProtos.IRemoveQuoteSubscriptionRequest);

        /** RemoveQuoteSubscriptionRequest symbol. */
        public symbol: string;

        /**
         * Creates a new RemoveQuoteSubscriptionRequest instance using the specified properties.
         * @param [properties] Properties to set
         * @returns RemoveQuoteSubscriptionRequest instance
         */
        public static create(properties?: QuoteWSProtos.IRemoveQuoteSubscriptionRequest): QuoteWSProtos.RemoveQuoteSubscriptionRequest;

        /**
         * Encodes the specified RemoveQuoteSubscriptionRequest message. Does not implicitly {@link QuoteWSProtos.RemoveQuoteSubscriptionRequest.verify|verify} messages.
         * @param message RemoveQuoteSubscriptionRequest message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: QuoteWSProtos.IRemoveQuoteSubscriptionRequest, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified RemoveQuoteSubscriptionRequest message, length delimited. Does not implicitly {@link QuoteWSProtos.RemoveQuoteSubscriptionRequest.verify|verify} messages.
         * @param message RemoveQuoteSubscriptionRequest message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: QuoteWSProtos.IRemoveQuoteSubscriptionRequest, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a RemoveQuoteSubscriptionRequest message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns RemoveQuoteSubscriptionRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): QuoteWSProtos.RemoveQuoteSubscriptionRequest;

        /**
         * Decodes a RemoveQuoteSubscriptionRequest message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns RemoveQuoteSubscriptionRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): QuoteWSProtos.RemoveQuoteSubscriptionRequest;

        /**
         * Verifies a RemoveQuoteSubscriptionRequest message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a RemoveQuoteSubscriptionRequest message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns RemoveQuoteSubscriptionRequest
         */
        public static fromObject(object: { [k: string]: any }): QuoteWSProtos.RemoveQuoteSubscriptionRequest;

        /**
         * Creates a plain object from a RemoveQuoteSubscriptionRequest message. Also converts values to other types if specified.
         * @param message RemoveQuoteSubscriptionRequest
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: QuoteWSProtos.RemoveQuoteSubscriptionRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this RemoveQuoteSubscriptionRequest to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a QuoteSocketRequest. */
    interface IQuoteSocketRequest {

        /** QuoteSocketRequest addUpdateQuoteSubscriptionRequest */
        addUpdateQuoteSubscriptionRequest?: (QuoteWSProtos.IAddUpdateQuoteSubscriptionRequest|null);

        /** QuoteSocketRequest removeQuoteSubscriptionRequest */
        removeQuoteSubscriptionRequest?: (QuoteWSProtos.IRemoveQuoteSubscriptionRequest|null);

        /** QuoteSocketRequest PONG */
        PONG?: (QuoteWSProtos.IPong|null);
    }

    /** Represents a QuoteSocketRequest. */
    class QuoteSocketRequest implements IQuoteSocketRequest {

        /**
         * Constructs a new QuoteSocketRequest.
         * @param [properties] Properties to set
         */
        constructor(properties?: QuoteWSProtos.IQuoteSocketRequest);

        /** QuoteSocketRequest addUpdateQuoteSubscriptionRequest. */
        public addUpdateQuoteSubscriptionRequest?: (QuoteWSProtos.IAddUpdateQuoteSubscriptionRequest|null);

        /** QuoteSocketRequest removeQuoteSubscriptionRequest. */
        public removeQuoteSubscriptionRequest?: (QuoteWSProtos.IRemoveQuoteSubscriptionRequest|null);

        /** QuoteSocketRequest PONG. */
        public PONG?: (QuoteWSProtos.IPong|null);

        /** QuoteSocketRequest data. */
        public data?: ("addUpdateQuoteSubscriptionRequest"|"removeQuoteSubscriptionRequest"|"PONG");

        /**
         * Creates a new QuoteSocketRequest instance using the specified properties.
         * @param [properties] Properties to set
         * @returns QuoteSocketRequest instance
         */
        public static create(properties?: QuoteWSProtos.IQuoteSocketRequest): QuoteWSProtos.QuoteSocketRequest;

        /**
         * Encodes the specified QuoteSocketRequest message. Does not implicitly {@link QuoteWSProtos.QuoteSocketRequest.verify|verify} messages.
         * @param message QuoteSocketRequest message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: QuoteWSProtos.IQuoteSocketRequest, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified QuoteSocketRequest message, length delimited. Does not implicitly {@link QuoteWSProtos.QuoteSocketRequest.verify|verify} messages.
         * @param message QuoteSocketRequest message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: QuoteWSProtos.IQuoteSocketRequest, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a QuoteSocketRequest message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns QuoteSocketRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): QuoteWSProtos.QuoteSocketRequest;

        /**
         * Decodes a QuoteSocketRequest message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns QuoteSocketRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): QuoteWSProtos.QuoteSocketRequest;

        /**
         * Verifies a QuoteSocketRequest message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a QuoteSocketRequest message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns QuoteSocketRequest
         */
        public static fromObject(object: { [k: string]: any }): QuoteWSProtos.QuoteSocketRequest;

        /**
         * Creates a plain object from a QuoteSocketRequest message. Also converts values to other types if specified.
         * @param message QuoteSocketRequest
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: QuoteWSProtos.QuoteSocketRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this QuoteSocketRequest to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a QuoteSocketResponse. */
    interface IQuoteSocketResponse {

        /** QuoteSocketResponse Quote */
        Quote?: (QuoteWSProtos.IQuoteResponse|null);

        /** QuoteSocketResponse SubscribeQuotesResponse */
        SubscribeQuotesResponse?: (QuoteWSProtos.ISubscribeConfResponse|null);

        /** QuoteSocketResponse UnsubscribeQuotesResponse */
        UnsubscribeQuotesResponse?: (QuoteWSProtos.ISubscribeConfResponse|null);

        /** QuoteSocketResponse PING */
        PING?: (QuoteWSProtos.IPing|null);

        /** QuoteSocketResponse Error */
        Error?: (QuoteWSProtos.IErrorResponse|null);
    }

    /** Represents a QuoteSocketResponse. */
    class QuoteSocketResponse implements IQuoteSocketResponse {

        /**
         * Constructs a new QuoteSocketResponse.
         * @param [properties] Properties to set
         */
        constructor(properties?: QuoteWSProtos.IQuoteSocketResponse);

        /** QuoteSocketResponse Quote. */
        public Quote?: (QuoteWSProtos.IQuoteResponse|null);

        /** QuoteSocketResponse SubscribeQuotesResponse. */
        public SubscribeQuotesResponse?: (QuoteWSProtos.ISubscribeConfResponse|null);

        /** QuoteSocketResponse UnsubscribeQuotesResponse. */
        public UnsubscribeQuotesResponse?: (QuoteWSProtos.ISubscribeConfResponse|null);

        /** QuoteSocketResponse PING. */
        public PING?: (QuoteWSProtos.IPing|null);

        /** QuoteSocketResponse Error. */
        public Error?: (QuoteWSProtos.IErrorResponse|null);

        /** QuoteSocketResponse messageType. */
        public messageType?: ("Quote"|"SubscribeQuotesResponse"|"UnsubscribeQuotesResponse"|"PING"|"Error");

        /**
         * Creates a new QuoteSocketResponse instance using the specified properties.
         * @param [properties] Properties to set
         * @returns QuoteSocketResponse instance
         */
        public static create(properties?: QuoteWSProtos.IQuoteSocketResponse): QuoteWSProtos.QuoteSocketResponse;

        /**
         * Encodes the specified QuoteSocketResponse message. Does not implicitly {@link QuoteWSProtos.QuoteSocketResponse.verify|verify} messages.
         * @param message QuoteSocketResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: QuoteWSProtos.IQuoteSocketResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified QuoteSocketResponse message, length delimited. Does not implicitly {@link QuoteWSProtos.QuoteSocketResponse.verify|verify} messages.
         * @param message QuoteSocketResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: QuoteWSProtos.IQuoteSocketResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a QuoteSocketResponse message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns QuoteSocketResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): QuoteWSProtos.QuoteSocketResponse;

        /**
         * Decodes a QuoteSocketResponse message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns QuoteSocketResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): QuoteWSProtos.QuoteSocketResponse;

        /**
         * Verifies a QuoteSocketResponse message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a QuoteSocketResponse message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns QuoteSocketResponse
         */
        public static fromObject(object: { [k: string]: any }): QuoteWSProtos.QuoteSocketResponse;

        /**
         * Creates a plain object from a QuoteSocketResponse message. Also converts values to other types if specified.
         * @param message QuoteSocketResponse
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: QuoteWSProtos.QuoteSocketResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this QuoteSocketResponse to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a SubscribeConfResponse. */
    interface ISubscribeConfResponse {

        /** SubscribeConfResponse symbol */
        symbol?: (string|null);

        /** SubscribeConfResponse SUCCESS */
        SUCCESS?: (number|null);

        /** SubscribeConfResponse NO_CONTENT */
        NO_CONTENT?: (number|null);

        /** SubscribeConfResponse BAD_REQUEST */
        BAD_REQUEST?: (QuoteWSProtos.IErrorResponse|null);

        /** SubscribeConfResponse UNAUTHORISED */
        UNAUTHORISED?: (QuoteWSProtos.IErrorResponse|null);

        /** SubscribeConfResponse FORBIDDEN */
        FORBIDDEN?: (QuoteWSProtos.IErrorResponse|null);

        /** SubscribeConfResponse INTERNAL_SERVER_ERROR */
        INTERNAL_SERVER_ERROR?: (QuoteWSProtos.IErrorResponse|null);
    }

    /** Represents a SubscribeConfResponse. */
    class SubscribeConfResponse implements ISubscribeConfResponse {

        /**
         * Constructs a new SubscribeConfResponse.
         * @param [properties] Properties to set
         */
        constructor(properties?: QuoteWSProtos.ISubscribeConfResponse);

        /** SubscribeConfResponse symbol. */
        public symbol: string;

        /** SubscribeConfResponse SUCCESS. */
        public SUCCESS: number;

        /** SubscribeConfResponse NO_CONTENT. */
        public NO_CONTENT: number;

        /** SubscribeConfResponse BAD_REQUEST. */
        public BAD_REQUEST?: (QuoteWSProtos.IErrorResponse|null);

        /** SubscribeConfResponse UNAUTHORISED. */
        public UNAUTHORISED?: (QuoteWSProtos.IErrorResponse|null);

        /** SubscribeConfResponse FORBIDDEN. */
        public FORBIDDEN?: (QuoteWSProtos.IErrorResponse|null);

        /** SubscribeConfResponse INTERNAL_SERVER_ERROR. */
        public INTERNAL_SERVER_ERROR?: (QuoteWSProtos.IErrorResponse|null);

        /** SubscribeConfResponse statusCode. */
        public statusCode?: ("SUCCESS"|"NO_CONTENT"|"BAD_REQUEST"|"UNAUTHORISED"|"FORBIDDEN"|"INTERNAL_SERVER_ERROR");

        /**
         * Creates a new SubscribeConfResponse instance using the specified properties.
         * @param [properties] Properties to set
         * @returns SubscribeConfResponse instance
         */
        public static create(properties?: QuoteWSProtos.ISubscribeConfResponse): QuoteWSProtos.SubscribeConfResponse;

        /**
         * Encodes the specified SubscribeConfResponse message. Does not implicitly {@link QuoteWSProtos.SubscribeConfResponse.verify|verify} messages.
         * @param message SubscribeConfResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: QuoteWSProtos.ISubscribeConfResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified SubscribeConfResponse message, length delimited. Does not implicitly {@link QuoteWSProtos.SubscribeConfResponse.verify|verify} messages.
         * @param message SubscribeConfResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: QuoteWSProtos.ISubscribeConfResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a SubscribeConfResponse message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns SubscribeConfResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): QuoteWSProtos.SubscribeConfResponse;

        /**
         * Decodes a SubscribeConfResponse message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns SubscribeConfResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): QuoteWSProtos.SubscribeConfResponse;

        /**
         * Verifies a SubscribeConfResponse message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a SubscribeConfResponse message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns SubscribeConfResponse
         */
        public static fromObject(object: { [k: string]: any }): QuoteWSProtos.SubscribeConfResponse;

        /**
         * Creates a plain object from a SubscribeConfResponse message. Also converts values to other types if specified.
         * @param message SubscribeConfResponse
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: QuoteWSProtos.SubscribeConfResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this SubscribeConfResponse to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a Result. */
    interface IResult {

        /** Result code */
        code?: (string|null);

        /** Result message */
        message?: (string|null);
    }

    /** Represents a Result. */
    class Result implements IResult {

        /**
         * Constructs a new Result.
         * @param [properties] Properties to set
         */
        constructor(properties?: QuoteWSProtos.IResult);

        /** Result code. */
        public code: string;

        /** Result message. */
        public message: string;

        /**
         * Creates a new Result instance using the specified properties.
         * @param [properties] Properties to set
         * @returns Result instance
         */
        public static create(properties?: QuoteWSProtos.IResult): QuoteWSProtos.Result;

        /**
         * Encodes the specified Result message. Does not implicitly {@link QuoteWSProtos.Result.verify|verify} messages.
         * @param message Result message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: QuoteWSProtos.IResult, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified Result message, length delimited. Does not implicitly {@link QuoteWSProtos.Result.verify|verify} messages.
         * @param message Result message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: QuoteWSProtos.IResult, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a Result message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Result
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): QuoteWSProtos.Result;

        /**
         * Decodes a Result message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns Result
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): QuoteWSProtos.Result;

        /**
         * Verifies a Result message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a Result message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Result
         */
        public static fromObject(object: { [k: string]: any }): QuoteWSProtos.Result;

        /**
         * Creates a plain object from a Result message. Also converts values to other types if specified.
         * @param message Result
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: QuoteWSProtos.Result, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Result to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of an ErrorResponse. */
    interface IErrorResponse {

        /** ErrorResponse result */
        result?: (QuoteWSProtos.IResult|null);

        /** ErrorResponse errors */
        errors?: (string[]|null);
    }

    /** Represents an ErrorResponse. */
    class ErrorResponse implements IErrorResponse {

        /**
         * Constructs a new ErrorResponse.
         * @param [properties] Properties to set
         */
        constructor(properties?: QuoteWSProtos.IErrorResponse);

        /** ErrorResponse result. */
        public result?: (QuoteWSProtos.IResult|null);

        /** ErrorResponse errors. */
        public errors: string[];

        /**
         * Creates a new ErrorResponse instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ErrorResponse instance
         */
        public static create(properties?: QuoteWSProtos.IErrorResponse): QuoteWSProtos.ErrorResponse;

        /**
         * Encodes the specified ErrorResponse message. Does not implicitly {@link QuoteWSProtos.ErrorResponse.verify|verify} messages.
         * @param message ErrorResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: QuoteWSProtos.IErrorResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ErrorResponse message, length delimited. Does not implicitly {@link QuoteWSProtos.ErrorResponse.verify|verify} messages.
         * @param message ErrorResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: QuoteWSProtos.IErrorResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an ErrorResponse message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ErrorResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): QuoteWSProtos.ErrorResponse;

        /**
         * Decodes an ErrorResponse message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ErrorResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): QuoteWSProtos.ErrorResponse;

        /**
         * Verifies an ErrorResponse message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an ErrorResponse message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ErrorResponse
         */
        public static fromObject(object: { [k: string]: any }): QuoteWSProtos.ErrorResponse;

        /**
         * Creates a plain object from an ErrorResponse message. Also converts values to other types if specified.
         * @param message ErrorResponse
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: QuoteWSProtos.ErrorResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ErrorResponse to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a QuoteResponse. */
    interface IQuoteResponse {

        /** QuoteResponse quote */
        quote?: (QuoteProtos.IQuote|null);
    }

    /** Represents a QuoteResponse. */
    class QuoteResponse implements IQuoteResponse {

        /**
         * Constructs a new QuoteResponse.
         * @param [properties] Properties to set
         */
        constructor(properties?: QuoteWSProtos.IQuoteResponse);

        /** QuoteResponse quote. */
        public quote?: (QuoteProtos.IQuote|null);

        /**
         * Creates a new QuoteResponse instance using the specified properties.
         * @param [properties] Properties to set
         * @returns QuoteResponse instance
         */
        public static create(properties?: QuoteWSProtos.IQuoteResponse): QuoteWSProtos.QuoteResponse;

        /**
         * Encodes the specified QuoteResponse message. Does not implicitly {@link QuoteWSProtos.QuoteResponse.verify|verify} messages.
         * @param message QuoteResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: QuoteWSProtos.IQuoteResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified QuoteResponse message, length delimited. Does not implicitly {@link QuoteWSProtos.QuoteResponse.verify|verify} messages.
         * @param message QuoteResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: QuoteWSProtos.IQuoteResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a QuoteResponse message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns QuoteResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): QuoteWSProtos.QuoteResponse;

        /**
         * Decodes a QuoteResponse message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns QuoteResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): QuoteWSProtos.QuoteResponse;

        /**
         * Verifies a QuoteResponse message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a QuoteResponse message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns QuoteResponse
         */
        public static fromObject(object: { [k: string]: any }): QuoteWSProtos.QuoteResponse;

        /**
         * Creates a plain object from a QuoteResponse message. Also converts values to other types if specified.
         * @param message QuoteResponse
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: QuoteWSProtos.QuoteResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this QuoteResponse to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a Ping. */
    interface IPing {
    }

    /** Represents a Ping. */
    class Ping implements IPing {

        /**
         * Constructs a new Ping.
         * @param [properties] Properties to set
         */
        constructor(properties?: QuoteWSProtos.IPing);

        /**
         * Creates a new Ping instance using the specified properties.
         * @param [properties] Properties to set
         * @returns Ping instance
         */
        public static create(properties?: QuoteWSProtos.IPing): QuoteWSProtos.Ping;

        /**
         * Encodes the specified Ping message. Does not implicitly {@link QuoteWSProtos.Ping.verify|verify} messages.
         * @param message Ping message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: QuoteWSProtos.IPing, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified Ping message, length delimited. Does not implicitly {@link QuoteWSProtos.Ping.verify|verify} messages.
         * @param message Ping message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: QuoteWSProtos.IPing, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a Ping message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Ping
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): QuoteWSProtos.Ping;

        /**
         * Decodes a Ping message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns Ping
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): QuoteWSProtos.Ping;

        /**
         * Verifies a Ping message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a Ping message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Ping
         */
        public static fromObject(object: { [k: string]: any }): QuoteWSProtos.Ping;

        /**
         * Creates a plain object from a Ping message. Also converts values to other types if specified.
         * @param message Ping
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: QuoteWSProtos.Ping, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Ping to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a Pong. */
    interface IPong {
    }

    /** Represents a Pong. */
    class Pong implements IPong {

        /**
         * Constructs a new Pong.
         * @param [properties] Properties to set
         */
        constructor(properties?: QuoteWSProtos.IPong);

        /**
         * Creates a new Pong instance using the specified properties.
         * @param [properties] Properties to set
         * @returns Pong instance
         */
        public static create(properties?: QuoteWSProtos.IPong): QuoteWSProtos.Pong;

        /**
         * Encodes the specified Pong message. Does not implicitly {@link QuoteWSProtos.Pong.verify|verify} messages.
         * @param message Pong message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: QuoteWSProtos.IPong, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified Pong message, length delimited. Does not implicitly {@link QuoteWSProtos.Pong.verify|verify} messages.
         * @param message Pong message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: QuoteWSProtos.IPong, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a Pong message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Pong
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): QuoteWSProtos.Pong;

        /**
         * Decodes a Pong message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns Pong
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): QuoteWSProtos.Pong;

        /**
         * Verifies a Pong message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a Pong message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Pong
         */
        public static fromObject(object: { [k: string]: any }): QuoteWSProtos.Pong;

        /**
         * Creates a plain object from a Pong message. Also converts values to other types if specified.
         * @param message Pong
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: QuoteWSProtos.Pong, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Pong to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }
}

/** Namespace QuoteProtos. */
export namespace QuoteProtos {

    /** SessionType enum. */
    enum SessionType {
        NA = 0,
        PRE_MARKET = 1,
        REGULAR = 2,
        AFTER_MARKET = 3
    }

    /** TickType enum. */
    enum TickType {
        UNKNOWN = 0,
        VALID = 1,
        IN_VALID = 2
    }

    /** QuoteSource enum. */
    enum QuoteSource {
        UNKNOWN_SOURCE = 0,
        QUOTE_STORE = 1,
        QUOTE_CALCULATOR = 2,
        POLLED_DATA = 3,
        ROLLING_FIELDS = 4,
        RSI_FIELDS = 5,
        CONTENT_EVENT_PUBLISHER = 6,
        SWAP = 7
    }

    /** Properties of a QuoteSeedRequest. */
    interface IQuoteSeedRequest {

        /** QuoteSeedRequest reqId */
        reqId?: (string|null);

        /** QuoteSeedRequest symbols */
        symbols?: (string[]|null);

        /** QuoteSeedRequest seedAll */
        seedAll?: (boolean|null);
    }

    /** Represents a QuoteSeedRequest. */
    class QuoteSeedRequest implements IQuoteSeedRequest {

        /**
         * Constructs a new QuoteSeedRequest.
         * @param [properties] Properties to set
         */
        constructor(properties?: QuoteProtos.IQuoteSeedRequest);

        /** QuoteSeedRequest reqId. */
        public reqId: string;

        /** QuoteSeedRequest symbols. */
        public symbols: string[];

        /** QuoteSeedRequest seedAll. */
        public seedAll: boolean;

        /**
         * Creates a new QuoteSeedRequest instance using the specified properties.
         * @param [properties] Properties to set
         * @returns QuoteSeedRequest instance
         */
        public static create(properties?: QuoteProtos.IQuoteSeedRequest): QuoteProtos.QuoteSeedRequest;

        /**
         * Encodes the specified QuoteSeedRequest message. Does not implicitly {@link QuoteProtos.QuoteSeedRequest.verify|verify} messages.
         * @param message QuoteSeedRequest message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: QuoteProtos.IQuoteSeedRequest, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified QuoteSeedRequest message, length delimited. Does not implicitly {@link QuoteProtos.QuoteSeedRequest.verify|verify} messages.
         * @param message QuoteSeedRequest message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: QuoteProtos.IQuoteSeedRequest, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a QuoteSeedRequest message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns QuoteSeedRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): QuoteProtos.QuoteSeedRequest;

        /**
         * Decodes a QuoteSeedRequest message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns QuoteSeedRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): QuoteProtos.QuoteSeedRequest;

        /**
         * Verifies a QuoteSeedRequest message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a QuoteSeedRequest message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns QuoteSeedRequest
         */
        public static fromObject(object: { [k: string]: any }): QuoteProtos.QuoteSeedRequest;

        /**
         * Creates a plain object from a QuoteSeedRequest message. Also converts values to other types if specified.
         * @param message QuoteSeedRequest
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: QuoteProtos.QuoteSeedRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this QuoteSeedRequest to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a QuoteSeedResponse. */
    interface IQuoteSeedResponse {

        /** QuoteSeedResponse reqId */
        reqId?: (string|null);

        /** QuoteSeedResponse quote */
        quote?: (QuoteProtos.IQuote|null);
    }

    /** Represents a QuoteSeedResponse. */
    class QuoteSeedResponse implements IQuoteSeedResponse {

        /**
         * Constructs a new QuoteSeedResponse.
         * @param [properties] Properties to set
         */
        constructor(properties?: QuoteProtos.IQuoteSeedResponse);

        /** QuoteSeedResponse reqId. */
        public reqId: string;

        /** QuoteSeedResponse quote. */
        public quote?: (QuoteProtos.IQuote|null);

        /**
         * Creates a new QuoteSeedResponse instance using the specified properties.
         * @param [properties] Properties to set
         * @returns QuoteSeedResponse instance
         */
        public static create(properties?: QuoteProtos.IQuoteSeedResponse): QuoteProtos.QuoteSeedResponse;

        /**
         * Encodes the specified QuoteSeedResponse message. Does not implicitly {@link QuoteProtos.QuoteSeedResponse.verify|verify} messages.
         * @param message QuoteSeedResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: QuoteProtos.IQuoteSeedResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified QuoteSeedResponse message, length delimited. Does not implicitly {@link QuoteProtos.QuoteSeedResponse.verify|verify} messages.
         * @param message QuoteSeedResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: QuoteProtos.IQuoteSeedResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a QuoteSeedResponse message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns QuoteSeedResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): QuoteProtos.QuoteSeedResponse;

        /**
         * Decodes a QuoteSeedResponse message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns QuoteSeedResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): QuoteProtos.QuoteSeedResponse;

        /**
         * Verifies a QuoteSeedResponse message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a QuoteSeedResponse message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns QuoteSeedResponse
         */
        public static fromObject(object: { [k: string]: any }): QuoteProtos.QuoteSeedResponse;

        /**
         * Creates a plain object from a QuoteSeedResponse message. Also converts values to other types if specified.
         * @param message QuoteSeedResponse
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: QuoteProtos.QuoteSeedResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this QuoteSeedResponse to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a Quote. */
    interface IQuote {

        /** Quote symbol */
        symbol?: (string|null);

        /** Quote opol */
        opol?: (string|null);

        /** Quote previousClose */
        previousClose?: (number|null);

        /** Quote close */
        close?: (number|null);

        /** Quote change */
        change?: (number|null);

        /** Quote changePercent */
        changePercent?: (number|null);

        /** Quote name */
        name?: (string|null);

        /** Quote ipoDate */
        ipoDate?: (string|null);

        /** Quote exchange */
        exchange?: (string|null);

        /** Quote tradeExchange */
        tradeExchange?: (string|null);

        /** Quote preMarketOpen */
        preMarketOpen?: (number|null);

        /** Quote preMarketOpenTime */
        preMarketOpenTime?: (number|Long|null);

        /** Quote preMarketVolume */
        preMarketVolume?: (number|Long|null);

        /** Quote afterMarketVolume */
        afterMarketVolume?: (number|Long|null);

        /** Quote preMarketOpenChange */
        preMarketOpenChange?: (number|null);

        /** Quote preMarketOpenChangePercent */
        preMarketOpenChangePercent?: (number|null);

        /** Quote source */
        source?: (QuoteProtos.QuoteSource|null);

        /** Quote clearFieldNumbers */
        clearFieldNumbers?: (number[]|null);

        /** Quote marketCap */
        marketCap?: (number|Long|null);

        /** Quote rsi */
        rsi?: (number|null);

        /** Quote atr */
        atr?: (number|null);

        /** Quote priceToEarnings */
        priceToEarnings?: (number|null);

        /** Quote averageVolume */
        averageVolume?: (number|Long|null);

        /** Quote sharesOutstanding */
        sharesOutstanding?: (number|Long|null);

        /** Quote sector */
        sector?: (string|null);

        /** Quote sectorCode */
        sectorCode?: (number|null);

        /** Quote industry */
        industry?: (string|null);

        /** Quote industryCode */
        industryCode?: (number|null);

        /** Quote forwardPERatio */
        forwardPERatio?: (number|null);

        /** Quote pricetoEBITDA */
        pricetoEBITDA?: (number|null);

        /** Quote trailingDividendYield */
        trailingDividendYield?: (number|null);

        /** Quote PBRatio */
        PBRatio?: (number|null);

        /** Quote priceToSales */
        priceToSales?: (number|null);

        /** Quote FCFPerShare */
        FCFPerShare?: (number|null);

        /** Quote buyBackYield */
        buyBackYield?: (number|null);

        /** Quote open */
        open?: (number|null);

        /** Quote price */
        price?: (number|null);

        /** Quote high */
        high?: (number|null);

        /** Quote low */
        low?: (number|null);

        /** Quote time */
        time?: (number|Long|null);

        /** Quote size */
        size?: (number|null);

        /** Quote type */
        type?: (string|null);

        /** Quote dayVolume */
        dayVolume?: (number|Long|null);

        /** Quote askPrice */
        askPrice?: (number|null);

        /** Quote bidPrice */
        bidPrice?: (number|null);

        /** Quote shareFloatDate */
        shareFloatDate?: (string|null);

        /** Quote sharesOutstandingDate */
        sharesOutstandingDate?: (string|null);

        /** Quote shareFloat */
        shareFloat?: (number|Long|null);

        /** Quote updateType */
        updateType?: (QuoteProtos.TickType|null);

        /** Quote subtype */
        subtype?: (string|null);

        /** Quote sessionType */
        sessionType?: (QuoteProtos.SessionType|null);

        /** Quote volume */
        volume?: (number|Long|null);

        /** Quote changePercent1Minute */
        changePercent1Minute?: (number|null);

        /** Quote changePercentClose */
        changePercentClose?: (number|null);

        /** Quote changePercent5Minute */
        changePercent5Minute?: (number|null);

        /** Quote volume1Minute */
        volume1Minute?: (number|Long|null);

        /** Quote relativeVolume1Minute */
        relativeVolume1Minute?: (number|null);

        /** Quote change15Minute */
        change15Minute?: (number|null);

        /** Quote changePercent15Minute */
        changePercent15Minute?: (number|null);

        /** Quote change30Minute */
        change30Minute?: (number|null);

        /** Quote changePercent30Minute */
        changePercent30Minute?: (number|null);

        /** Quote change60Minute */
        change60Minute?: (number|null);

        /** Quote changePercent60Minute */
        changePercent60Minute?: (number|null);

        /** Quote changePercentOpen */
        changePercentOpen?: (number|null);

        /** Quote changeOpen */
        changeOpen?: (number|null);

        /** Quote gics */
        gics?: (number|null);

        /** Quote gicsSector */
        gicsSector?: (number|null);

        /** Quote gicsSectorName */
        gicsSectorName?: (string|null);

        /** Quote gicsIndustryGroup */
        gicsIndustryGroup?: (number|null);

        /** Quote gicsIndustryGroupName */
        gicsIndustryGroupName?: (string|null);

        /** Quote gicsIndustry */
        gicsIndustry?: (number|null);

        /** Quote gicsIndustryName */
        gicsIndustryName?: (string|null);

        /** Quote gicsSubIndustry */
        gicsSubIndustry?: (number|null);

        /** Quote gicsSubIndustryName */
        gicsSubIndustryName?: (string|null);

        /** Quote optionable */
        optionable?: (number|null);

        /** Quote cik */
        cik?: (number|null);

        /** Quote signalName */
        signalName?: (string|null);

        /** Quote lastTradeTime */
        lastTradeTime?: (number|Long|null);

        /** Quote country */
        country?: (string|null);

        /** Quote sharesShort */
        sharesShort?: (number|Long|null);

        /** Quote sharesShortPercentOfFloat */
        sharesShortPercentOfFloat?: (number|null);

        /** Quote sharesShortChangePercent */
        sharesShortChangePercent?: (number|null);

        /** Quote sharesShortReceiptDate */
        sharesShortReceiptDate?: (number|Long|null);

        /** Quote sharesShortSettlementDate */
        sharesShortSettlementDate?: (number|Long|null);

        /** Quote shortInterestRatio */
        shortInterestRatio?: (number|null);

        /** Quote sharesShortValue */
        sharesShortValue?: (number|null);

        /** Quote sharesShortPrevious */
        sharesShortPrevious?: (number|Long|null);

        /** Quote sharesShortPreviousValue */
        sharesShortPreviousValue?: (number|null);

        /** Quote sharesShortPreviousPercentOfFloat */
        sharesShortPreviousPercentOfFloat?: (number|null);

        /** Quote sharesShortChange */
        sharesShortChange?: (number|Long|null);

        /** Quote sentTime */
        sentTime?: (number|Long|null);

        /** Quote volume5Minute */
        volume5Minute?: (number|Long|null);

        /** Quote volume15Minute */
        volume15Minute?: (number|Long|null);

        /** Quote volume30Minute */
        volume30Minute?: (number|Long|null);

        /** Quote volume60Minute */
        volume60Minute?: (number|Long|null);

        /** Quote relativeVolume5Minute */
        relativeVolume5Minute?: (number|null);

        /** Quote relativeVolume90Day */
        relativeVolume90Day?: (number|null);

        /** Quote rvol1m10d */
        rvol1m10d?: (number|null);

        /** Quote rvol5m10d */
        rvol5m10d?: (number|null);

        /** Quote rvol5m30d */
        rvol5m30d?: (number|null);

        /** Quote rvol5m90d */
        rvol5m90d?: (number|null);

        /** Quote averageVolume10Day */
        averageVolume10Day?: (number|Long|null);

        /** Quote averageVolumeChangePercent10Day */
        averageVolumeChangePercent10Day?: (number|null);

        /** Quote high10Day */
        high10Day?: (number|null);

        /** Quote highChangePercent10Day */
        highChangePercent10Day?: (number|null);

        /** Quote low10Day */
        low10Day?: (number|null);

        /** Quote lowChangePercent10Day */
        lowChangePercent10Day?: (number|null);

        /** Quote movingAverage10Day */
        movingAverage10Day?: (number|null);

        /** Quote movingAverageChangePercent10Day */
        movingAverageChangePercent10Day?: (number|null);

        /** Quote averageVolume20Day */
        averageVolume20Day?: (number|Long|null);

        /** Quote averageVolumeChangePercent20Day */
        averageVolumeChangePercent20Day?: (number|null);

        /** Quote high20Day */
        high20Day?: (number|null);

        /** Quote highChangePercent20Day */
        highChangePercent20Day?: (number|null);

        /** Quote low20Day */
        low20Day?: (number|null);

        /** Quote lowChangePercent20Day */
        lowChangePercent20Day?: (number|null);

        /** Quote movingAverage20Day */
        movingAverage20Day?: (number|null);

        /** Quote movingAverageChangePercent20Day */
        movingAverageChangePercent20Day?: (number|null);

        /** Quote averageVolume50Day */
        averageVolume50Day?: (number|Long|null);

        /** Quote averageVolumeChangePercent50Day */
        averageVolumeChangePercent50Day?: (number|null);

        /** Quote high50Day */
        high50Day?: (number|null);

        /** Quote highChangePercent50Day */
        highChangePercent50Day?: (number|null);

        /** Quote low50Day */
        low50Day?: (number|null);

        /** Quote lowChangePercent50Day */
        lowChangePercent50Day?: (number|null);

        /** Quote movingAverage50Day */
        movingAverage50Day?: (number|null);

        /** Quote movingAverageChangePercent50Day */
        movingAverageChangePercent50Day?: (number|null);

        /** Quote averageVolume100Day */
        averageVolume100Day?: (number|Long|null);

        /** Quote averageVolumeChangePercent100Day */
        averageVolumeChangePercent100Day?: (number|null);

        /** Quote high100Day */
        high100Day?: (number|null);

        /** Quote highChangePercent100Day */
        highChangePercent100Day?: (number|null);

        /** Quote low100Day */
        low100Day?: (number|null);

        /** Quote lowChangePercent100Day */
        lowChangePercent100Day?: (number|null);

        /** Quote movingAverage100Day */
        movingAverage100Day?: (number|null);

        /** Quote movingAverageChangePercent100Day */
        movingAverageChangePercent100Day?: (number|null);

        /** Quote averageVolume200Day */
        averageVolume200Day?: (number|Long|null);

        /** Quote averageVolumeChangePercent200Day */
        averageVolumeChangePercent200Day?: (number|null);

        /** Quote high200Day */
        high200Day?: (number|null);

        /** Quote highChangePercent200Day */
        highChangePercent200Day?: (number|null);

        /** Quote low200Day */
        low200Day?: (number|null);

        /** Quote lowChangePercent200Day */
        lowChangePercent200Day?: (number|null);

        /** Quote movingAverage200Day */
        movingAverage200Day?: (number|null);

        /** Quote movingAverageChangePercent200Day */
        movingAverageChangePercent200Day?: (number|null);

        /** Quote movingAverage250Day */
        movingAverage250Day?: (number|null);

        /** Quote movingAverageChangePercent250Day */
        movingAverageChangePercent250Day?: (number|null);

        /** Quote averageVolume90Day */
        averageVolume90Day?: (number|Long|null);

        /** Quote nextEarningsDate */
        nextEarningsDate?: (number|null);

        /** Quote dividend */
        dividend?: (number|null);

        /** Quote dividendYield */
        dividendYield?: (number|null);

        /** Quote dividendExDate */
        dividendExDate?: (number|null);

        /** Quote dividendFrequency */
        dividendFrequency?: (number|Long|null);

        /** Quote forwardDividendYield */
        forwardDividendYield?: (number|null);

        /** Quote roe */
        roe?: (number|null);

        /** Quote roa */
        roa?: (number|null);

        /** Quote roic */
        roic?: (number|null);

        /** Quote currentRatio */
        currentRatio?: (number|null);

        /** Quote grossMargin */
        grossMargin?: (number|null);

        /** Quote netMargin */
        netMargin?: (number|null);

        /** Quote operationMargin */
        operationMargin?: (number|null);

        /** Quote epsMrq */
        epsMrq?: (number|null);

        /** Quote epsTtm */
        epsTtm?: (number|null);

        /** Quote dilutedEpsMrq */
        dilutedEpsMrq?: (number|null);

        /** Quote dilutedEpsTtm */
        dilutedEpsTtm?: (number|null);

        /** Quote low52Week */
        low52Week?: (number|null);

        /** Quote high52Week */
        high52Week?: (number|null);

        /** Quote revenueTtm */
        revenueTtm?: (number|null);

        /** Quote earningsAsOf */
        earningsAsOf?: (number|null);

        /** Quote netProfitMargin */
        netProfitMargin?: (number|null);

        /** Quote price1Week */
        price1Week?: (number|null);

        /** Quote priceChangePercent1Week */
        priceChangePercent1Week?: (number|null);

        /** Quote price1Month */
        price1Month?: (number|null);

        /** Quote priceChangePercent1Month */
        priceChangePercent1Month?: (number|null);

        /** Quote priceHalfYear */
        priceHalfYear?: (number|null);

        /** Quote priceChangePercentHalfYear */
        priceChangePercentHalfYear?: (number|null);

        /** Quote priceQuarter */
        priceQuarter?: (number|null);

        /** Quote priceChangePercentQuarter */
        priceChangePercentQuarter?: (number|null);

        /** Quote price1Year */
        price1Year?: (number|null);

        /** Quote priceChangePercent1Year */
        priceChangePercent1Year?: (number|null);

        /** Quote priceYearToDate */
        priceYearToDate?: (number|null);

        /** Quote priceChangePercentYearToDate */
        priceChangePercentYearToDate?: (number|null);

        /** Quote roeMrq */
        roeMrq?: (number|null);

        /** Quote roaMrq */
        roaMrq?: (number|null);

        /** Quote roicMrq */
        roicMrq?: (number|null);

        /** Quote currentRatioMrq */
        currentRatioMrq?: (number|null);

        /** Quote grossMarginMrq */
        grossMarginMrq?: (number|null);

        /** Quote netMarginMrq */
        netMarginMrq?: (number|null);

        /** Quote operationMarginMrq */
        operationMarginMrq?: (number|null);

        /** Quote previousDayVolume */
        previousDayVolume?: (number|Long|null);

        /** Quote previousDayAtr */
        previousDayAtr?: (number|null);

        /** Quote previousDayAdx */
        previousDayAdx?: (number|null);

        /** Quote previousDayPlusDMSmoothed */
        previousDayPlusDMSmoothed?: (number|null);

        /** Quote previousDayMinusDMSmoothed */
        previousDayMinusDMSmoothed?: (number|null);

        /** Quote previousDayHigh */
        previousDayHigh?: (number|null);

        /** Quote previousDayLow */
        previousDayLow?: (number|null);

        /** Quote adx */
        adx?: (number|null);

        /** Quote percentInstitutionalOwnership */
        percentInstitutionalOwnership?: (number|null);

        /** Quote percentInstitutionalTransactions */
        percentInstitutionalTransactions?: (number|null);

        /** Quote wiim */
        wiim?: (string|null);

        /** Quote wiimAsOf */
        wiimAsOf?: (number|Long|null);

        /** Quote vwap */
        vwap?: (number|null);

        /** Quote vwapChangePercent */
        vwapChangePercent?: (number|null);

        /** Quote vwapChange */
        vwapChange?: (number|null);

        /** Quote rsiAvgGain */
        rsiAvgGain?: (number|null);

        /** Quote rsiAvgLoss */
        rsiAvgLoss?: (number|null);

        /** Quote halted */
        halted?: (string|null);

        /** Quote tradeCount */
        tradeCount?: (number|Long|null);

        /** Quote regularHighCount */
        regularHighCount?: (number|Long|null);

        /** Quote regularLowCount */
        regularLowCount?: (number|Long|null);

        /** Quote preMarketHighCount */
        preMarketHighCount?: (number|Long|null);

        /** Quote preMarketLowCount */
        preMarketLowCount?: (number|Long|null);

        /** Quote afterMarketHighCount */
        afterMarketHighCount?: (number|Long|null);

        /** Quote afterMarketLowCount */
        afterMarketLowCount?: (number|Long|null);

        /** Quote dayHighCount */
        dayHighCount?: (number|Long|null);

        /** Quote dayLowCount */
        dayLowCount?: (number|Long|null);

        /** Quote dayHigh */
        dayHigh?: (number|null);

        /** Quote dayLow */
        dayLow?: (number|null);

        /** Quote preMarketHigh */
        preMarketHigh?: (number|null);

        /** Quote preMarketLow */
        preMarketLow?: (number|null);

        /** Quote afterMarketHigh */
        afterMarketHigh?: (number|null);

        /** Quote afterMarketLow */
        afterMarketLow?: (number|null);

        /** Quote regularHigh */
        regularHigh?: (number|null);

        /** Quote regularLow */
        regularLow?: (number|null);

        /** Quote priceSpikeUpCount */
        priceSpikeUpCount?: (number|Long|null);

        /** Quote priceSpikeDownCount */
        priceSpikeDownCount?: (number|Long|null);

        /** Quote haltedCount */
        haltedCount?: (number|Long|null);

        /** Quote usCongressTotal */
        usCongressTotal?: (number|null);

        /** Quote usCongressRepublicansTotal */
        usCongressRepublicansTotal?: (number|null);

        /** Quote usCongressDemocratsTotal */
        usCongressDemocratsTotal?: (number|null);

        /** Quote usCongressOtherTotal */
        usCongressOtherTotal?: (number|null);

        /** Quote volumeFloatRatio */
        volumeFloatRatio?: (number|null);

        /** Quote atrp */
        atrp?: (number|null);

        /** Quote ytdGainLoss */
        ytdGainLoss?: (number|null);

        /** Quote mtdGainLoss */
        mtdGainLoss?: (number|null);

        /** Quote ytdChange */
        ytdChange?: (number|null);

        /** Quote mtdChange */
        mtdChange?: (number|null);

        /** Quote yearOpen */
        yearOpen?: (number|null);

        /** Quote monthOpen */
        monthOpen?: (number|null);

        /** Quote volumeFloatPercent */
        volumeFloatPercent?: (number|null);

        /** Quote rsi1m */
        rsi1m?: (number|null);

        /** Quote rsi2m */
        rsi2m?: (number|null);

        /** Quote rsi5m */
        rsi5m?: (number|null);

        /** Quote rsi15m */
        rsi15m?: (number|null);

        /** Quote rsi30m */
        rsi30m?: (number|null);

        /** Quote rsi60m */
        rsi60m?: (number|null);

        /** Quote rsiEth1m */
        rsiEth1m?: (number|null);

        /** Quote rsiEth2m */
        rsiEth2m?: (number|null);

        /** Quote rsiEth5m */
        rsiEth5m?: (number|null);

        /** Quote rsiEth15m */
        rsiEth15m?: (number|null);

        /** Quote rsiEth30m */
        rsiEth30m?: (number|null);

        /** Quote rsiEth60m */
        rsiEth60m?: (number|null);

        /** Quote averageDollarVolume10Day */
        averageDollarVolume10Day?: (number|null);

        /** Quote averageVolume60Day */
        averageVolume60Day?: (number|Long|null);

        /** Quote relativeVolumePercent60Day */
        relativeVolumePercent60Day?: (number|null);

        /** Quote rsi1mLowest */
        rsi1mLowest?: (number|null);

        /** Quote rsi2mLowest */
        rsi2mLowest?: (number|null);

        /** Quote rsi5mLowest */
        rsi5mLowest?: (number|null);

        /** Quote rsi15mLowest */
        rsi15mLowest?: (number|null);

        /** Quote rsi30mLowest */
        rsi30mLowest?: (number|null);

        /** Quote rsi60mLowest */
        rsi60mLowest?: (number|null);

        /** Quote rsiEth1mLowest */
        rsiEth1mLowest?: (number|null);

        /** Quote rsiEth2mLowest */
        rsiEth2mLowest?: (number|null);

        /** Quote rsiEth5mLowest */
        rsiEth5mLowest?: (number|null);

        /** Quote rsiEth15mLowest */
        rsiEth15mLowest?: (number|null);

        /** Quote rsiEth30mLowest */
        rsiEth30mLowest?: (number|null);

        /** Quote rsiEth60mLowest */
        rsiEth60mLowest?: (number|null);

        /** Quote rsi1mGreatest */
        rsi1mGreatest?: (number|null);

        /** Quote rsi2mGreatest */
        rsi2mGreatest?: (number|null);

        /** Quote rsi5mGreatest */
        rsi5mGreatest?: (number|null);

        /** Quote rsi15mGreatest */
        rsi15mGreatest?: (number|null);

        /** Quote rsi30mGreatest */
        rsi30mGreatest?: (number|null);

        /** Quote rsi60mGreatest */
        rsi60mGreatest?: (number|null);

        /** Quote rsiEth1mGreatest */
        rsiEth1mGreatest?: (number|null);

        /** Quote rsiEth2mGreatest */
        rsiEth2mGreatest?: (number|null);

        /** Quote rsiEth5mGreatest */
        rsiEth5mGreatest?: (number|null);

        /** Quote rsiEth15mGreatest */
        rsiEth15mGreatest?: (number|null);

        /** Quote rsiEth30mGreatest */
        rsiEth30mGreatest?: (number|null);

        /** Quote rsiEth60mGreatest */
        rsiEth60mGreatest?: (number|null);

        /** Quote rsiCrossOver */
        rsiCrossOver?: (number|null);

        /** Quote ema12d */
        ema12d?: (number|null);

        /** Quote ema26d */
        ema26d?: (number|null);

        /** Quote macd */
        macd?: (number|null);

        /** Quote macdSignalLine */
        macdSignalLine?: (number|null);

        /** Quote macdHistogram */
        macdHistogram?: (number|null);

        /** Quote macdSignalLineBase */
        macdSignalLineBase?: (number|null);

        /** Quote dividendGrowthRate1Year */
        dividendGrowthRate1Year?: (number|null);

        /** Quote dividendGrowthRate3Year */
        dividendGrowthRate3Year?: (number|null);

        /** Quote dividendGrowthRate5Year */
        dividendGrowthRate5Year?: (number|null);

        /** Quote totalDividendTtm */
        totalDividendTtm?: (number|null);

        /** Quote dividendIncreasing */
        dividendIncreasing?: (number|null);

        /** Quote newsBzWireTitle */
        newsBzWireTitle?: (string|null);

        /** Quote newsBzWireNodeId */
        newsBzWireNodeId?: (string|null);

        /** Quote newsBzWireTimestamp */
        newsBzWireTimestamp?: (number|Long|null);

        /** Quote newsPRTitle */
        newsPRTitle?: (string|null);

        /** Quote newsPRNodeId */
        newsPRNodeId?: (string|null);

        /** Quote newsPRTimestamp */
        newsPRTimestamp?: (number|Long|null);

        /** Quote newsSecTitle */
        newsSecTitle?: (string|null);

        /** Quote newsSecNodeId */
        newsSecNodeId?: (string|null);

        /** Quote newsSecTimestamp */
        newsSecTimestamp?: (number|Long|null);

        /** Quote newsBzWireTitleWithNodeId */
        newsBzWireTitleWithNodeId?: (string|null);

        /** Quote newsPRTitleWithNodeId */
        newsPRTitleWithNodeId?: (string|null);

        /** Quote newsSecTitleWithNodeId */
        newsSecTitleWithNodeId?: (string|null);

        /** Quote movingAverage500Day */
        movingAverage500Day?: (number|null);

        /** Quote gapChange */
        gapChange?: (number|null);

        /** Quote gapChangePercent */
        gapChangePercent?: (number|null);

        /** Quote adr14d */
        adr14d?: (number|null);

        /** Quote dilutedEpsMrqSurprisePercent */
        dilutedEpsMrqSurprisePercent?: (number|null);

        /** Quote dilutedEpsTtmSurprisePercent */
        dilutedEpsTtmSurprisePercent?: (number|null);

        /** Quote dilutedRevenueMrqSurprisePercent */
        dilutedRevenueMrqSurprisePercent?: (number|null);

        /** Quote dilutedRevenueTtmSurprisePercent */
        dilutedRevenueTtmSurprisePercent?: (number|null);

        /** Quote epsGrowthTtm */
        epsGrowthTtm?: (number|null);

        /** Quote epsGrowthY2Y */
        epsGrowthY2Y?: (number|null);

        /** Quote epsGrowthQ2Q */
        epsGrowthQ2Q?: (number|null);

        /** Quote epsGrowth5Year */
        epsGrowth5Year?: (number|null);

        /** Quote epsGrowthYearAgoQ */
        epsGrowthYearAgoQ?: (number|null);

        /** Quote revenueGrowthTtm */
        revenueGrowthTtm?: (number|null);

        /** Quote revenueGrowthY2Y */
        revenueGrowthY2Y?: (number|null);

        /** Quote revenueGrowthQ2Q */
        revenueGrowthQ2Q?: (number|null);

        /** Quote revenueGrowth5Year */
        revenueGrowth5Year?: (number|null);

        /** Quote revenueGrowthYearAgoQ */
        revenueGrowthYearAgoQ?: (number|null);

        /** Quote ltDebtEquity */
        ltDebtEquity?: (number|null);

        /** Quote bullsSay */
        bullsSay?: (string|null);

        /** Quote bearsSay */
        bearsSay?: (string|null);

        /** Quote ipoDateNumber */
        ipoDateNumber?: (number|Long|null);

        /** Quote ffoPerShare */
        ffoPerShare?: (number|null);

        /** Quote quickRatioMrq */
        quickRatioMrq?: (number|null);

        /** Quote highAllTime */
        highAllTime?: (number|null);

        /** Quote lowAllTime */
        lowAllTime?: (number|null);

        /** Quote evToEbitda */
        evToEbitda?: (number|null);

        /** Quote totalDebtEquityRatio */
        totalDebtEquityRatio?: (number|null);

        /** Quote dividendPayoutRatio */
        dividendPayoutRatio?: (number|null);

        /** Quote operatingCashFlow */
        operatingCashFlow?: (number|null);

        /** Quote insiderPercentage */
        insiderPercentage?: (number|null);

        /** Quote priceToCashFlowRatio */
        priceToCashFlowRatio?: (number|null);

        /** Quote regularHoursChange */
        regularHoursChange?: (number|null);

        /** Quote regularHoursPercentChange */
        regularHoursPercentChange?: (number|null);

        /** Quote postToPreHoursChange */
        postToPreHoursChange?: (number|null);

        /** Quote postToPreHoursPercentChange */
        postToPreHoursPercentChange?: (number|null);

        /** Quote scannerClose */
        scannerClose?: (number|null);

        /** Quote scannerChange */
        scannerChange?: (number|null);

        /** Quote scannerChangePercent */
        scannerChangePercent?: (number|null);

        /** Quote scannerChangePercentClose */
        scannerChangePercentClose?: (number|null);

        /** Quote scannerPreviousClose */
        scannerPreviousClose?: (number|null);

        /** Quote freeCashFlow */
        freeCashFlow?: (number|null);

        /** Quote minPriceTarget */
        minPriceTarget?: (number|null);

        /** Quote maxPriceTarget */
        maxPriceTarget?: (number|null);

        /** Quote meanPriceTarget */
        meanPriceTarget?: (number|null);

        /** Quote totalNumberOfAnalyst */
        totalNumberOfAnalyst?: (number|null);

        /** Quote averageAnalystRecommendation */
        averageAnalystRecommendation?: (string|null);

        /** Quote pegRatio */
        pegRatio?: (number|null);

        /** Quote priceToFreeCashFlowRatio */
        priceToFreeCashFlowRatio?: (number|null);

        /** Quote distanceToMaxPriceTarget */
        distanceToMaxPriceTarget?: (number|null);

        /** Quote distanceToMinPriceTarget */
        distanceToMinPriceTarget?: (number|null);

        /** Quote distanceToMeanPriceTarget */
        distanceToMeanPriceTarget?: (number|null);

        /** Quote shortTermTrend */
        shortTermTrend?: (string|null);

        /** Quote mediumTermTrend */
        mediumTermTrend?: (string|null);

        /** Quote longTermTrend */
        longTermTrend?: (string|null);

        /** Quote twentySixWeekChange */
        twentySixWeekChange?: (number|null);

        /** Quote fiftyTwoWeekChange */
        fiftyTwoWeekChange?: (number|null);

        /** Quote annualizedStandardDeviation */
        annualizedStandardDeviation?: (number|null);

        /** Quote momentum */
        momentum?: (number|null);

        /** Quote value */
        value?: (number|null);

        /** Quote piotroskiFScore */
        piotroskiFScore?: (number|Long|null);

        /** Quote netIncomeTtm */
        netIncomeTtm?: (number|null);

        /** Quote currentRatioYearAgo */
        currentRatioYearAgo?: (number|null);

        /** Quote roaYearAgo */
        roaYearAgo?: (number|null);

        /** Quote grossMarginYearAgo */
        grossMarginYearAgo?: (number|null);

        /** Quote longTermDebt */
        longTermDebt?: (number|null);

        /** Quote longTermDebtYearAgo */
        longTermDebtYearAgo?: (number|null);

        /** Quote shareIssued */
        shareIssued?: (number|null);

        /** Quote shareIssuedYearAgo */
        shareIssuedYearAgo?: (number|null);

        /** Quote totalAssets */
        totalAssets?: (number|null);

        /** Quote totalAssetsYearAgo */
        totalAssetsYearAgo?: (number|null);

        /** Quote totalAssets2YearAgo */
        totalAssets2YearAgo?: (number|null);

        /** Quote revenueTtmYearAgo */
        revenueTtmYearAgo?: (number|null);

        /** Quote longTermDebtAndCapitalLeaseObligation */
        longTermDebtAndCapitalLeaseObligation?: (number|null);

        /** Quote averageTotalAssets */
        averageTotalAssets?: (number|null);

        /** Quote averageTotalAssetsYearAgo */
        averageTotalAssetsYearAgo?: (number|null);

        /** Quote growth */
        growth?: (number|null);

        /** Quote quality */
        quality?: (number|null);

        /** Quote momentumPercentile */
        momentumPercentile?: (number|null);

        /** Quote valuePercentile */
        valuePercentile?: (number|null);

        /** Quote growthPercentile */
        growthPercentile?: (number|null);

        /** Quote qualityPercentile */
        qualityPercentile?: (number|null);

        /** Quote rateOfReturnDay */
        rateOfReturnDay?: (number|null);

        /** Quote rateOfReturnWeek */
        rateOfReturnWeek?: (number|null);

        /** Quote rateOfReturnMonth */
        rateOfReturnMonth?: (number|null);

        /** Quote dividendOneDay */
        dividendOneDay?: (number|null);

        /** Quote dividendOneWeek */
        dividendOneWeek?: (number|null);

        /** Quote dividendOneMonth */
        dividendOneMonth?: (number|null);

        /** Quote totalLiabilities */
        totalLiabilities?: (number|null);

        /** Quote retainedEarnings */
        retainedEarnings?: (number|null);

        /** Quote ebit */
        ebit?: (number|null);

        /** Quote altmanZScore */
        altmanZScore?: (number|null);

        /** Quote powerEarningGap */
        powerEarningGap?: (boolean|null);

        /** Quote monsterPowerEarningGap */
        monsterPowerEarningGap?: (boolean|null);

        /** Quote monsterGap */
        monsterGap?: (boolean|null);

        /** Quote oelGap */
        oelGap?: (boolean|null);
    }

    /** Represents a Quote. */
    class Quote implements IQuote {

        /**
         * Constructs a new Quote.
         * @param [properties] Properties to set
         */
        constructor(properties?: QuoteProtos.IQuote);

        /** Quote symbol. */
        public symbol: string;

        /** Quote opol. */
        public opol: string;

        /** Quote previousClose. */
        public previousClose: number;

        /** Quote close. */
        public close: number;

        /** Quote change. */
        public change: number;

        /** Quote changePercent. */
        public changePercent: number;

        /** Quote name. */
        public name: string;

        /** Quote ipoDate. */
        public ipoDate: string;

        /** Quote exchange. */
        public exchange: string;

        /** Quote tradeExchange. */
        public tradeExchange: string;

        /** Quote preMarketOpen. */
        public preMarketOpen: number;

        /** Quote preMarketOpenTime. */
        public preMarketOpenTime: (number|Long);

        /** Quote preMarketVolume. */
        public preMarketVolume: (number|Long);

        /** Quote afterMarketVolume. */
        public afterMarketVolume: (number|Long);

        /** Quote preMarketOpenChange. */
        public preMarketOpenChange: number;

        /** Quote preMarketOpenChangePercent. */
        public preMarketOpenChangePercent: number;

        /** Quote source. */
        public source: QuoteProtos.QuoteSource;

        /** Quote clearFieldNumbers. */
        public clearFieldNumbers: number[];

        /** Quote marketCap. */
        public marketCap: (number|Long);

        /** Quote rsi. */
        public rsi: number;

        /** Quote atr. */
        public atr: number;

        /** Quote priceToEarnings. */
        public priceToEarnings: number;

        /** Quote averageVolume. */
        public averageVolume: (number|Long);

        /** Quote sharesOutstanding. */
        public sharesOutstanding: (number|Long);

        /** Quote sector. */
        public sector: string;

        /** Quote sectorCode. */
        public sectorCode: number;

        /** Quote industry. */
        public industry: string;

        /** Quote industryCode. */
        public industryCode: number;

        /** Quote forwardPERatio. */
        public forwardPERatio: number;

        /** Quote pricetoEBITDA. */
        public pricetoEBITDA: number;

        /** Quote trailingDividendYield. */
        public trailingDividendYield: number;

        /** Quote PBRatio. */
        public PBRatio: number;

        /** Quote priceToSales. */
        public priceToSales: number;

        /** Quote FCFPerShare. */
        public FCFPerShare: number;

        /** Quote buyBackYield. */
        public buyBackYield: number;

        /** Quote open. */
        public open: number;

        /** Quote price. */
        public price: number;

        /** Quote high. */
        public high: number;

        /** Quote low. */
        public low: number;

        /** Quote time. */
        public time: (number|Long);

        /** Quote size. */
        public size: number;

        /** Quote type. */
        public type: string;

        /** Quote dayVolume. */
        public dayVolume: (number|Long);

        /** Quote askPrice. */
        public askPrice: number;

        /** Quote bidPrice. */
        public bidPrice: number;

        /** Quote shareFloatDate. */
        public shareFloatDate: string;

        /** Quote sharesOutstandingDate. */
        public sharesOutstandingDate: string;

        /** Quote shareFloat. */
        public shareFloat: (number|Long);

        /** Quote updateType. */
        public updateType: QuoteProtos.TickType;

        /** Quote subtype. */
        public subtype: string;

        /** Quote sessionType. */
        public sessionType: QuoteProtos.SessionType;

        /** Quote volume. */
        public volume: (number|Long);

        /** Quote changePercent1Minute. */
        public changePercent1Minute: number;

        /** Quote changePercentClose. */
        public changePercentClose: number;

        /** Quote changePercent5Minute. */
        public changePercent5Minute: number;

        /** Quote volume1Minute. */
        public volume1Minute: (number|Long);

        /** Quote relativeVolume1Minute. */
        public relativeVolume1Minute: number;

        /** Quote change15Minute. */
        public change15Minute: number;

        /** Quote changePercent15Minute. */
        public changePercent15Minute: number;

        /** Quote change30Minute. */
        public change30Minute: number;

        /** Quote changePercent30Minute. */
        public changePercent30Minute: number;

        /** Quote change60Minute. */
        public change60Minute: number;

        /** Quote changePercent60Minute. */
        public changePercent60Minute: number;

        /** Quote changePercentOpen. */
        public changePercentOpen: number;

        /** Quote changeOpen. */
        public changeOpen: number;

        /** Quote gics. */
        public gics: number;

        /** Quote gicsSector. */
        public gicsSector: number;

        /** Quote gicsSectorName. */
        public gicsSectorName: string;

        /** Quote gicsIndustryGroup. */
        public gicsIndustryGroup: number;

        /** Quote gicsIndustryGroupName. */
        public gicsIndustryGroupName: string;

        /** Quote gicsIndustry. */
        public gicsIndustry: number;

        /** Quote gicsIndustryName. */
        public gicsIndustryName: string;

        /** Quote gicsSubIndustry. */
        public gicsSubIndustry: number;

        /** Quote gicsSubIndustryName. */
        public gicsSubIndustryName: string;

        /** Quote optionable. */
        public optionable: number;

        /** Quote cik. */
        public cik: number;

        /** Quote signalName. */
        public signalName: string;

        /** Quote lastTradeTime. */
        public lastTradeTime: (number|Long);

        /** Quote country. */
        public country: string;

        /** Quote sharesShort. */
        public sharesShort: (number|Long);

        /** Quote sharesShortPercentOfFloat. */
        public sharesShortPercentOfFloat: number;

        /** Quote sharesShortChangePercent. */
        public sharesShortChangePercent: number;

        /** Quote sharesShortReceiptDate. */
        public sharesShortReceiptDate: (number|Long);

        /** Quote sharesShortSettlementDate. */
        public sharesShortSettlementDate: (number|Long);

        /** Quote shortInterestRatio. */
        public shortInterestRatio: number;

        /** Quote sharesShortValue. */
        public sharesShortValue: number;

        /** Quote sharesShortPrevious. */
        public sharesShortPrevious: (number|Long);

        /** Quote sharesShortPreviousValue. */
        public sharesShortPreviousValue: number;

        /** Quote sharesShortPreviousPercentOfFloat. */
        public sharesShortPreviousPercentOfFloat: number;

        /** Quote sharesShortChange. */
        public sharesShortChange: (number|Long);

        /** Quote sentTime. */
        public sentTime: (number|Long);

        /** Quote volume5Minute. */
        public volume5Minute: (number|Long);

        /** Quote volume15Minute. */
        public volume15Minute: (number|Long);

        /** Quote volume30Minute. */
        public volume30Minute: (number|Long);

        /** Quote volume60Minute. */
        public volume60Minute: (number|Long);

        /** Quote relativeVolume5Minute. */
        public relativeVolume5Minute: number;

        /** Quote relativeVolume90Day. */
        public relativeVolume90Day: number;

        /** Quote rvol1m10d. */
        public rvol1m10d: number;

        /** Quote rvol5m10d. */
        public rvol5m10d: number;

        /** Quote rvol5m30d. */
        public rvol5m30d: number;

        /** Quote rvol5m90d. */
        public rvol5m90d: number;

        /** Quote averageVolume10Day. */
        public averageVolume10Day: (number|Long);

        /** Quote averageVolumeChangePercent10Day. */
        public averageVolumeChangePercent10Day: number;

        /** Quote high10Day. */
        public high10Day: number;

        /** Quote highChangePercent10Day. */
        public highChangePercent10Day: number;

        /** Quote low10Day. */
        public low10Day: number;

        /** Quote lowChangePercent10Day. */
        public lowChangePercent10Day: number;

        /** Quote movingAverage10Day. */
        public movingAverage10Day: number;

        /** Quote movingAverageChangePercent10Day. */
        public movingAverageChangePercent10Day: number;

        /** Quote averageVolume20Day. */
        public averageVolume20Day: (number|Long);

        /** Quote averageVolumeChangePercent20Day. */
        public averageVolumeChangePercent20Day: number;

        /** Quote high20Day. */
        public high20Day: number;

        /** Quote highChangePercent20Day. */
        public highChangePercent20Day: number;

        /** Quote low20Day. */
        public low20Day: number;

        /** Quote lowChangePercent20Day. */
        public lowChangePercent20Day: number;

        /** Quote movingAverage20Day. */
        public movingAverage20Day: number;

        /** Quote movingAverageChangePercent20Day. */
        public movingAverageChangePercent20Day: number;

        /** Quote averageVolume50Day. */
        public averageVolume50Day: (number|Long);

        /** Quote averageVolumeChangePercent50Day. */
        public averageVolumeChangePercent50Day: number;

        /** Quote high50Day. */
        public high50Day: number;

        /** Quote highChangePercent50Day. */
        public highChangePercent50Day: number;

        /** Quote low50Day. */
        public low50Day: number;

        /** Quote lowChangePercent50Day. */
        public lowChangePercent50Day: number;

        /** Quote movingAverage50Day. */
        public movingAverage50Day: number;

        /** Quote movingAverageChangePercent50Day. */
        public movingAverageChangePercent50Day: number;

        /** Quote averageVolume100Day. */
        public averageVolume100Day: (number|Long);

        /** Quote averageVolumeChangePercent100Day. */
        public averageVolumeChangePercent100Day: number;

        /** Quote high100Day. */
        public high100Day: number;

        /** Quote highChangePercent100Day. */
        public highChangePercent100Day: number;

        /** Quote low100Day. */
        public low100Day: number;

        /** Quote lowChangePercent100Day. */
        public lowChangePercent100Day: number;

        /** Quote movingAverage100Day. */
        public movingAverage100Day: number;

        /** Quote movingAverageChangePercent100Day. */
        public movingAverageChangePercent100Day: number;

        /** Quote averageVolume200Day. */
        public averageVolume200Day: (number|Long);

        /** Quote averageVolumeChangePercent200Day. */
        public averageVolumeChangePercent200Day: number;

        /** Quote high200Day. */
        public high200Day: number;

        /** Quote highChangePercent200Day. */
        public highChangePercent200Day: number;

        /** Quote low200Day. */
        public low200Day: number;

        /** Quote lowChangePercent200Day. */
        public lowChangePercent200Day: number;

        /** Quote movingAverage200Day. */
        public movingAverage200Day: number;

        /** Quote movingAverageChangePercent200Day. */
        public movingAverageChangePercent200Day: number;

        /** Quote movingAverage250Day. */
        public movingAverage250Day: number;

        /** Quote movingAverageChangePercent250Day. */
        public movingAverageChangePercent250Day: number;

        /** Quote averageVolume90Day. */
        public averageVolume90Day: (number|Long);

        /** Quote nextEarningsDate. */
        public nextEarningsDate: number;

        /** Quote dividend. */
        public dividend: number;

        /** Quote dividendYield. */
        public dividendYield: number;

        /** Quote dividendExDate. */
        public dividendExDate: number;

        /** Quote dividendFrequency. */
        public dividendFrequency: (number|Long);

        /** Quote forwardDividendYield. */
        public forwardDividendYield: number;

        /** Quote roe. */
        public roe: number;

        /** Quote roa. */
        public roa: number;

        /** Quote roic. */
        public roic: number;

        /** Quote currentRatio. */
        public currentRatio: number;

        /** Quote grossMargin. */
        public grossMargin: number;

        /** Quote netMargin. */
        public netMargin: number;

        /** Quote operationMargin. */
        public operationMargin: number;

        /** Quote epsMrq. */
        public epsMrq: number;

        /** Quote epsTtm. */
        public epsTtm: number;

        /** Quote dilutedEpsMrq. */
        public dilutedEpsMrq: number;

        /** Quote dilutedEpsTtm. */
        public dilutedEpsTtm: number;

        /** Quote low52Week. */
        public low52Week: number;

        /** Quote high52Week. */
        public high52Week: number;

        /** Quote revenueTtm. */
        public revenueTtm: number;

        /** Quote earningsAsOf. */
        public earningsAsOf: number;

        /** Quote netProfitMargin. */
        public netProfitMargin: number;

        /** Quote price1Week. */
        public price1Week: number;

        /** Quote priceChangePercent1Week. */
        public priceChangePercent1Week: number;

        /** Quote price1Month. */
        public price1Month: number;

        /** Quote priceChangePercent1Month. */
        public priceChangePercent1Month: number;

        /** Quote priceHalfYear. */
        public priceHalfYear: number;

        /** Quote priceChangePercentHalfYear. */
        public priceChangePercentHalfYear: number;

        /** Quote priceQuarter. */
        public priceQuarter: number;

        /** Quote priceChangePercentQuarter. */
        public priceChangePercentQuarter: number;

        /** Quote price1Year. */
        public price1Year: number;

        /** Quote priceChangePercent1Year. */
        public priceChangePercent1Year: number;

        /** Quote priceYearToDate. */
        public priceYearToDate: number;

        /** Quote priceChangePercentYearToDate. */
        public priceChangePercentYearToDate: number;

        /** Quote roeMrq. */
        public roeMrq: number;

        /** Quote roaMrq. */
        public roaMrq: number;

        /** Quote roicMrq. */
        public roicMrq: number;

        /** Quote currentRatioMrq. */
        public currentRatioMrq: number;

        /** Quote grossMarginMrq. */
        public grossMarginMrq: number;

        /** Quote netMarginMrq. */
        public netMarginMrq: number;

        /** Quote operationMarginMrq. */
        public operationMarginMrq: number;

        /** Quote previousDayVolume. */
        public previousDayVolume: (number|Long);

        /** Quote previousDayAtr. */
        public previousDayAtr: number;

        /** Quote previousDayAdx. */
        public previousDayAdx: number;

        /** Quote previousDayPlusDMSmoothed. */
        public previousDayPlusDMSmoothed: number;

        /** Quote previousDayMinusDMSmoothed. */
        public previousDayMinusDMSmoothed: number;

        /** Quote previousDayHigh. */
        public previousDayHigh: number;

        /** Quote previousDayLow. */
        public previousDayLow: number;

        /** Quote adx. */
        public adx: number;

        /** Quote percentInstitutionalOwnership. */
        public percentInstitutionalOwnership: number;

        /** Quote percentInstitutionalTransactions. */
        public percentInstitutionalTransactions: number;

        /** Quote wiim. */
        public wiim: string;

        /** Quote wiimAsOf. */
        public wiimAsOf: (number|Long);

        /** Quote vwap. */
        public vwap: number;

        /** Quote vwapChangePercent. */
        public vwapChangePercent: number;

        /** Quote vwapChange. */
        public vwapChange: number;

        /** Quote rsiAvgGain. */
        public rsiAvgGain: number;

        /** Quote rsiAvgLoss. */
        public rsiAvgLoss: number;

        /** Quote halted. */
        public halted: string;

        /** Quote tradeCount. */
        public tradeCount: (number|Long);

        /** Quote regularHighCount. */
        public regularHighCount: (number|Long);

        /** Quote regularLowCount. */
        public regularLowCount: (number|Long);

        /** Quote preMarketHighCount. */
        public preMarketHighCount: (number|Long);

        /** Quote preMarketLowCount. */
        public preMarketLowCount: (number|Long);

        /** Quote afterMarketHighCount. */
        public afterMarketHighCount: (number|Long);

        /** Quote afterMarketLowCount. */
        public afterMarketLowCount: (number|Long);

        /** Quote dayHighCount. */
        public dayHighCount: (number|Long);

        /** Quote dayLowCount. */
        public dayLowCount: (number|Long);

        /** Quote dayHigh. */
        public dayHigh: number;

        /** Quote dayLow. */
        public dayLow: number;

        /** Quote preMarketHigh. */
        public preMarketHigh: number;

        /** Quote preMarketLow. */
        public preMarketLow: number;

        /** Quote afterMarketHigh. */
        public afterMarketHigh: number;

        /** Quote afterMarketLow. */
        public afterMarketLow: number;

        /** Quote regularHigh. */
        public regularHigh: number;

        /** Quote regularLow. */
        public regularLow: number;

        /** Quote priceSpikeUpCount. */
        public priceSpikeUpCount: (number|Long);

        /** Quote priceSpikeDownCount. */
        public priceSpikeDownCount: (number|Long);

        /** Quote haltedCount. */
        public haltedCount: (number|Long);

        /** Quote usCongressTotal. */
        public usCongressTotal: number;

        /** Quote usCongressRepublicansTotal. */
        public usCongressRepublicansTotal: number;

        /** Quote usCongressDemocratsTotal. */
        public usCongressDemocratsTotal: number;

        /** Quote usCongressOtherTotal. */
        public usCongressOtherTotal: number;

        /** Quote volumeFloatRatio. */
        public volumeFloatRatio: number;

        /** Quote atrp. */
        public atrp: number;

        /** Quote ytdGainLoss. */
        public ytdGainLoss: number;

        /** Quote mtdGainLoss. */
        public mtdGainLoss: number;

        /** Quote ytdChange. */
        public ytdChange: number;

        /** Quote mtdChange. */
        public mtdChange: number;

        /** Quote yearOpen. */
        public yearOpen: number;

        /** Quote monthOpen. */
        public monthOpen: number;

        /** Quote volumeFloatPercent. */
        public volumeFloatPercent: number;

        /** Quote rsi1m. */
        public rsi1m: number;

        /** Quote rsi2m. */
        public rsi2m: number;

        /** Quote rsi5m. */
        public rsi5m: number;

        /** Quote rsi15m. */
        public rsi15m: number;

        /** Quote rsi30m. */
        public rsi30m: number;

        /** Quote rsi60m. */
        public rsi60m: number;

        /** Quote rsiEth1m. */
        public rsiEth1m: number;

        /** Quote rsiEth2m. */
        public rsiEth2m: number;

        /** Quote rsiEth5m. */
        public rsiEth5m: number;

        /** Quote rsiEth15m. */
        public rsiEth15m: number;

        /** Quote rsiEth30m. */
        public rsiEth30m: number;

        /** Quote rsiEth60m. */
        public rsiEth60m: number;

        /** Quote averageDollarVolume10Day. */
        public averageDollarVolume10Day: number;

        /** Quote averageVolume60Day. */
        public averageVolume60Day: (number|Long);

        /** Quote relativeVolumePercent60Day. */
        public relativeVolumePercent60Day: number;

        /** Quote rsi1mLowest. */
        public rsi1mLowest: number;

        /** Quote rsi2mLowest. */
        public rsi2mLowest: number;

        /** Quote rsi5mLowest. */
        public rsi5mLowest: number;

        /** Quote rsi15mLowest. */
        public rsi15mLowest: number;

        /** Quote rsi30mLowest. */
        public rsi30mLowest: number;

        /** Quote rsi60mLowest. */
        public rsi60mLowest: number;

        /** Quote rsiEth1mLowest. */
        public rsiEth1mLowest: number;

        /** Quote rsiEth2mLowest. */
        public rsiEth2mLowest: number;

        /** Quote rsiEth5mLowest. */
        public rsiEth5mLowest: number;

        /** Quote rsiEth15mLowest. */
        public rsiEth15mLowest: number;

        /** Quote rsiEth30mLowest. */
        public rsiEth30mLowest: number;

        /** Quote rsiEth60mLowest. */
        public rsiEth60mLowest: number;

        /** Quote rsi1mGreatest. */
        public rsi1mGreatest: number;

        /** Quote rsi2mGreatest. */
        public rsi2mGreatest: number;

        /** Quote rsi5mGreatest. */
        public rsi5mGreatest: number;

        /** Quote rsi15mGreatest. */
        public rsi15mGreatest: number;

        /** Quote rsi30mGreatest. */
        public rsi30mGreatest: number;

        /** Quote rsi60mGreatest. */
        public rsi60mGreatest: number;

        /** Quote rsiEth1mGreatest. */
        public rsiEth1mGreatest: number;

        /** Quote rsiEth2mGreatest. */
        public rsiEth2mGreatest: number;

        /** Quote rsiEth5mGreatest. */
        public rsiEth5mGreatest: number;

        /** Quote rsiEth15mGreatest. */
        public rsiEth15mGreatest: number;

        /** Quote rsiEth30mGreatest. */
        public rsiEth30mGreatest: number;

        /** Quote rsiEth60mGreatest. */
        public rsiEth60mGreatest: number;

        /** Quote rsiCrossOver. */
        public rsiCrossOver: number;

        /** Quote ema12d. */
        public ema12d: number;

        /** Quote ema26d. */
        public ema26d: number;

        /** Quote macd. */
        public macd: number;

        /** Quote macdSignalLine. */
        public macdSignalLine: number;

        /** Quote macdHistogram. */
        public macdHistogram: number;

        /** Quote macdSignalLineBase. */
        public macdSignalLineBase: number;

        /** Quote dividendGrowthRate1Year. */
        public dividendGrowthRate1Year: number;

        /** Quote dividendGrowthRate3Year. */
        public dividendGrowthRate3Year: number;

        /** Quote dividendGrowthRate5Year. */
        public dividendGrowthRate5Year: number;

        /** Quote totalDividendTtm. */
        public totalDividendTtm: number;

        /** Quote dividendIncreasing. */
        public dividendIncreasing: number;

        /** Quote newsBzWireTitle. */
        public newsBzWireTitle: string;

        /** Quote newsBzWireNodeId. */
        public newsBzWireNodeId: string;

        /** Quote newsBzWireTimestamp. */
        public newsBzWireTimestamp: (number|Long);

        /** Quote newsPRTitle. */
        public newsPRTitle: string;

        /** Quote newsPRNodeId. */
        public newsPRNodeId: string;

        /** Quote newsPRTimestamp. */
        public newsPRTimestamp: (number|Long);

        /** Quote newsSecTitle. */
        public newsSecTitle: string;

        /** Quote newsSecNodeId. */
        public newsSecNodeId: string;

        /** Quote newsSecTimestamp. */
        public newsSecTimestamp: (number|Long);

        /** Quote newsBzWireTitleWithNodeId. */
        public newsBzWireTitleWithNodeId: string;

        /** Quote newsPRTitleWithNodeId. */
        public newsPRTitleWithNodeId: string;

        /** Quote newsSecTitleWithNodeId. */
        public newsSecTitleWithNodeId: string;

        /** Quote movingAverage500Day. */
        public movingAverage500Day: number;

        /** Quote gapChange. */
        public gapChange: number;

        /** Quote gapChangePercent. */
        public gapChangePercent: number;

        /** Quote adr14d. */
        public adr14d: number;

        /** Quote dilutedEpsMrqSurprisePercent. */
        public dilutedEpsMrqSurprisePercent: number;

        /** Quote dilutedEpsTtmSurprisePercent. */
        public dilutedEpsTtmSurprisePercent: number;

        /** Quote dilutedRevenueMrqSurprisePercent. */
        public dilutedRevenueMrqSurprisePercent: number;

        /** Quote dilutedRevenueTtmSurprisePercent. */
        public dilutedRevenueTtmSurprisePercent: number;

        /** Quote epsGrowthTtm. */
        public epsGrowthTtm: number;

        /** Quote epsGrowthY2Y. */
        public epsGrowthY2Y: number;

        /** Quote epsGrowthQ2Q. */
        public epsGrowthQ2Q: number;

        /** Quote epsGrowth5Year. */
        public epsGrowth5Year: number;

        /** Quote epsGrowthYearAgoQ. */
        public epsGrowthYearAgoQ: number;

        /** Quote revenueGrowthTtm. */
        public revenueGrowthTtm: number;

        /** Quote revenueGrowthY2Y. */
        public revenueGrowthY2Y: number;

        /** Quote revenueGrowthQ2Q. */
        public revenueGrowthQ2Q: number;

        /** Quote revenueGrowth5Year. */
        public revenueGrowth5Year: number;

        /** Quote revenueGrowthYearAgoQ. */
        public revenueGrowthYearAgoQ: number;

        /** Quote ltDebtEquity. */
        public ltDebtEquity: number;

        /** Quote bullsSay. */
        public bullsSay: string;

        /** Quote bearsSay. */
        public bearsSay: string;

        /** Quote ipoDateNumber. */
        public ipoDateNumber: (number|Long);

        /** Quote ffoPerShare. */
        public ffoPerShare: number;

        /** Quote quickRatioMrq. */
        public quickRatioMrq: number;

        /** Quote highAllTime. */
        public highAllTime: number;

        /** Quote lowAllTime. */
        public lowAllTime: number;

        /** Quote evToEbitda. */
        public evToEbitda: number;

        /** Quote totalDebtEquityRatio. */
        public totalDebtEquityRatio: number;

        /** Quote dividendPayoutRatio. */
        public dividendPayoutRatio: number;

        /** Quote operatingCashFlow. */
        public operatingCashFlow: number;

        /** Quote insiderPercentage. */
        public insiderPercentage: number;

        /** Quote priceToCashFlowRatio. */
        public priceToCashFlowRatio: number;

        /** Quote regularHoursChange. */
        public regularHoursChange: number;

        /** Quote regularHoursPercentChange. */
        public regularHoursPercentChange: number;

        /** Quote postToPreHoursChange. */
        public postToPreHoursChange: number;

        /** Quote postToPreHoursPercentChange. */
        public postToPreHoursPercentChange: number;

        /** Quote scannerClose. */
        public scannerClose: number;

        /** Quote scannerChange. */
        public scannerChange: number;

        /** Quote scannerChangePercent. */
        public scannerChangePercent: number;

        /** Quote scannerChangePercentClose. */
        public scannerChangePercentClose: number;

        /** Quote scannerPreviousClose. */
        public scannerPreviousClose: number;

        /** Quote freeCashFlow. */
        public freeCashFlow: number;

        /** Quote minPriceTarget. */
        public minPriceTarget: number;

        /** Quote maxPriceTarget. */
        public maxPriceTarget: number;

        /** Quote meanPriceTarget. */
        public meanPriceTarget: number;

        /** Quote totalNumberOfAnalyst. */
        public totalNumberOfAnalyst: number;

        /** Quote averageAnalystRecommendation. */
        public averageAnalystRecommendation: string;

        /** Quote pegRatio. */
        public pegRatio: number;

        /** Quote priceToFreeCashFlowRatio. */
        public priceToFreeCashFlowRatio: number;

        /** Quote distanceToMaxPriceTarget. */
        public distanceToMaxPriceTarget: number;

        /** Quote distanceToMinPriceTarget. */
        public distanceToMinPriceTarget: number;

        /** Quote distanceToMeanPriceTarget. */
        public distanceToMeanPriceTarget: number;

        /** Quote shortTermTrend. */
        public shortTermTrend: string;

        /** Quote mediumTermTrend. */
        public mediumTermTrend: string;

        /** Quote longTermTrend. */
        public longTermTrend: string;

        /** Quote twentySixWeekChange. */
        public twentySixWeekChange: number;

        /** Quote fiftyTwoWeekChange. */
        public fiftyTwoWeekChange: number;

        /** Quote annualizedStandardDeviation. */
        public annualizedStandardDeviation: number;

        /** Quote momentum. */
        public momentum: number;

        /** Quote value. */
        public value: number;

        /** Quote piotroskiFScore. */
        public piotroskiFScore: (number|Long);

        /** Quote netIncomeTtm. */
        public netIncomeTtm: number;

        /** Quote currentRatioYearAgo. */
        public currentRatioYearAgo: number;

        /** Quote roaYearAgo. */
        public roaYearAgo: number;

        /** Quote grossMarginYearAgo. */
        public grossMarginYearAgo: number;

        /** Quote longTermDebt. */
        public longTermDebt: number;

        /** Quote longTermDebtYearAgo. */
        public longTermDebtYearAgo: number;

        /** Quote shareIssued. */
        public shareIssued: number;

        /** Quote shareIssuedYearAgo. */
        public shareIssuedYearAgo: number;

        /** Quote totalAssets. */
        public totalAssets: number;

        /** Quote totalAssetsYearAgo. */
        public totalAssetsYearAgo: number;

        /** Quote totalAssets2YearAgo. */
        public totalAssets2YearAgo: number;

        /** Quote revenueTtmYearAgo. */
        public revenueTtmYearAgo: number;

        /** Quote longTermDebtAndCapitalLeaseObligation. */
        public longTermDebtAndCapitalLeaseObligation: number;

        /** Quote averageTotalAssets. */
        public averageTotalAssets: number;

        /** Quote averageTotalAssetsYearAgo. */
        public averageTotalAssetsYearAgo: number;

        /** Quote growth. */
        public growth: number;

        /** Quote quality. */
        public quality: number;

        /** Quote momentumPercentile. */
        public momentumPercentile: number;

        /** Quote valuePercentile. */
        public valuePercentile: number;

        /** Quote growthPercentile. */
        public growthPercentile: number;

        /** Quote qualityPercentile. */
        public qualityPercentile: number;

        /** Quote rateOfReturnDay. */
        public rateOfReturnDay: number;

        /** Quote rateOfReturnWeek. */
        public rateOfReturnWeek: number;

        /** Quote rateOfReturnMonth. */
        public rateOfReturnMonth: number;

        /** Quote dividendOneDay. */
        public dividendOneDay: number;

        /** Quote dividendOneWeek. */
        public dividendOneWeek: number;

        /** Quote dividendOneMonth. */
        public dividendOneMonth: number;

        /** Quote totalLiabilities. */
        public totalLiabilities: number;

        /** Quote retainedEarnings. */
        public retainedEarnings: number;

        /** Quote ebit. */
        public ebit: number;

        /** Quote altmanZScore. */
        public altmanZScore: number;

        /** Quote powerEarningGap. */
        public powerEarningGap: boolean;

        /** Quote monsterPowerEarningGap. */
        public monsterPowerEarningGap: boolean;

        /** Quote monsterGap. */
        public monsterGap: boolean;

        /** Quote oelGap. */
        public oelGap: boolean;

        /**
         * Creates a new Quote instance using the specified properties.
         * @param [properties] Properties to set
         * @returns Quote instance
         */
        public static create(properties?: QuoteProtos.IQuote): QuoteProtos.Quote;

        /**
         * Encodes the specified Quote message. Does not implicitly {@link QuoteProtos.Quote.verify|verify} messages.
         * @param message Quote message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: QuoteProtos.IQuote, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified Quote message, length delimited. Does not implicitly {@link QuoteProtos.Quote.verify|verify} messages.
         * @param message Quote message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: QuoteProtos.IQuote, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a Quote message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Quote
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): QuoteProtos.Quote;

        /**
         * Decodes a Quote message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns Quote
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): QuoteProtos.Quote;

        /**
         * Verifies a Quote message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a Quote message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Quote
         */
        public static fromObject(object: { [k: string]: any }): QuoteProtos.Quote;

        /**
         * Creates a plain object from a Quote message. Also converts values to other types if specified.
         * @param message Quote
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: QuoteProtos.Quote, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Quote to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }
}
