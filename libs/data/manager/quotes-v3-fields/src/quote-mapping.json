[{"index": 1, "name": "symbol", "type": "string"}, {"index": 100, "name": "opol", "type": "string"}, {"index": 2, "name": "previousClose", "type": "double"}, {"index": 3, "name": "close", "type": "double"}, {"index": 4, "name": "change", "type": "double"}, {"index": 5, "name": "changePercent", "type": "double"}, {"index": 6, "name": "name", "type": "string"}, {"index": 7, "name": "ipoDate", "type": "string"}, {"index": 8, "name": "exchange", "type": "string"}, {"index": 9, "name": "tradeExchange", "type": "string"}, {"index": 900, "name": "preMarketOpen", "type": "double"}, {"index": 901, "name": "preMarketOpenTime", "type": "int64"}, {"index": 902, "name": "preMarketVolume", "type": "int64"}, {"index": 899, "name": "afterMarketVolume", "type": "int64"}, {"index": 903, "name": "preMarketOpenChange", "type": "double"}, {"index": 904, "name": "preMarketOpenChangePercent", "type": "double"}, {"index": 905, "name": "source", "type": "QuoteSource"}, {"index": 906, "name": "clearFieldNumbers", "type": "int32"}, {"index": 10, "name": "marketCap", "type": "int64"}, {"index": 11, "name": "rsi", "type": "double"}, {"index": 12, "name": "atr", "type": "double"}, {"index": 13, "name": "priceToEarnings", "type": "double"}, {"index": 14, "name": "averageVolume", "type": "int64"}, {"index": 15, "name": "sharesOutstanding", "type": "int64"}, {"index": 16, "name": "sector", "type": "string"}, {"index": 17, "name": "sectorCode", "type": "int32"}, {"index": 18, "name": "industry", "type": "string"}, {"index": 19, "name": "industryCode", "type": "int32"}, {"index": 20, "name": "forwardPERatio", "type": "double"}, {"index": 21, "name": "pricetoEBITDA", "type": "double"}, {"index": 22, "name": "trailingDividendYield", "type": "double"}, {"index": 23, "name": "PBRatio", "type": "double"}, {"index": 24, "name": "priceToSales", "type": "double"}, {"index": 25, "name": "FCFPerShare", "type": "double"}, {"index": 26, "name": "buyBackYield", "type": "double"}, {"index": 27, "name": "open", "type": "double"}, {"index": 28, "name": "price", "type": "double"}, {"index": 29, "name": "high", "type": "double"}, {"index": 30, "name": "low", "type": "double"}, {"index": 31, "name": "time", "type": "int64"}, {"index": 32, "name": "size", "type": "int32"}, {"index": 33, "name": "type", "type": "string"}, {"index": 34, "name": "dayVolume", "type": "int64"}, {"index": 35, "name": "askPrice", "type": "double"}, {"index": 36, "name": "bidPrice", "type": "double"}, {"index": 37, "name": "shareFloatDate", "type": "string"}, {"index": 38, "name": "sharesOutstandingDate", "type": "string"}, {"index": 81, "name": "shareFloat", "type": "int64"}, {"index": 40, "name": "updateType", "type": "TickType"}, {"index": 101, "name": "subtype", "type": "string"}, {"index": 41, "name": "sessionType", "type": "SessionType"}, {"index": 96, "name": "volume", "type": "int64"}, {"index": 50, "name": "changePercent1Minute", "type": "double"}, {"index": 51, "name": "changePercentClose", "type": "double"}, {"index": 52, "name": "changePercent5Minute", "type": "double"}, {"index": 53, "name": "volume1Minute", "type": "int64"}, {"index": 54, "name": "relativeVolume1Minute", "type": "double"}, {"index": 55, "name": "change15Minute", "type": "double"}, {"index": 56, "name": "changePercent15Minute", "type": "double"}, {"index": 57, "name": "change30Minute", "type": "double"}, {"index": 58, "name": "changePercent30Minute", "type": "double"}, {"index": 59, "name": "change60Minute", "type": "double"}, {"index": 60, "name": "changePercent60Minute", "type": "double"}, {"index": 61, "name": "changePercentOpen", "type": "double"}, {"index": 62, "name": "changeOpen", "type": "double"}, {"index": 70, "name": "gics", "type": "int32"}, {"index": 71, "name": "gicsSector", "type": "int32"}, {"index": 72, "name": "gicsSectorName", "type": "string"}, {"index": 73, "name": "gicsIndustryGroup", "type": "int32"}, {"index": 74, "name": "gicsIndustryGroupName", "type": "string"}, {"index": 75, "name": "gicsIndustry", "type": "int32"}, {"index": 76, "name": "gicsIndustryName", "type": "string"}, {"index": 77, "name": "gicsSubIndustry", "type": "int32"}, {"index": 78, "name": "gicsSubIndustryName", "type": "string"}, {"index": 79, "name": "optionable", "type": "int32"}, {"index": 39, "name": "cik", "type": "int32"}, {"index": 80, "name": "signalName", "type": "string"}, {"index": 82, "name": "lastTradeTime", "type": "int64"}, {"index": 83, "name": "country", "type": "string"}, {"index": 110, "name": "sharesShort", "type": "int64"}, {"index": 111, "name": "sharesShortPercentOfFloat", "type": "double"}, {"index": 112, "name": "sharesShortChangePercent", "type": "double"}, {"index": 113, "name": "sharesShortReceiptDate", "type": "int64"}, {"index": 114, "name": "sharesShortSettlementDate", "type": "int64"}, {"index": 115, "name": "shortInterestRatio", "type": "double"}, {"index": 116, "name": "sharesShortValue", "type": "double"}, {"index": 117, "name": "sharesShortPrevious", "type": "int64"}, {"index": 118, "name": "sharesShortPreviousValue", "type": "double"}, {"index": 119, "name": "sharesShortPreviousPercentOfFloat", "type": "double"}, {"index": 120, "name": "sharesShortChange", "type": "int64"}, {"index": 200, "name": "sentTime", "type": "int64"}, {"index": 300, "name": "volume5Minute", "type": "int64"}, {"index": 301, "name": "volume15Minute", "type": "int64"}, {"index": 302, "name": "volume30Minute", "type": "int64"}, {"index": 303, "name": "volume60Minute", "type": "int64"}, {"index": 304, "name": "relativeVolume5Minute", "type": "double"}, {"index": 305, "name": "relativeVolume90Day", "type": "double"}, {"index": 306, "name": "rvol1m10d", "type": "double"}, {"index": 307, "name": "rvol5m10d", "type": "double"}, {"index": 308, "name": "rvol5m30d", "type": "double"}, {"index": 309, "name": "rvol5m90d", "type": "double"}, {"index": 600, "name": "averageVolume10Day", "type": "int64"}, {"index": 601, "name": "averageVolumeChangePercent10Day", "type": "double"}, {"index": 602, "name": "high10Day", "type": "double"}, {"index": 603, "name": "highChangePercent10Day", "type": "double"}, {"index": 604, "name": "low10Day", "type": "double"}, {"index": 605, "name": "lowChangePercent10Day", "type": "double"}, {"index": 606, "name": "movingAverage10Day", "type": "double"}, {"index": 607, "name": "movingAverageChangePercent10Day", "type": "double"}, {"index": 610, "name": "averageVolume20Day", "type": "int64"}, {"index": 611, "name": "averageVolumeChangePercent20Day", "type": "double"}, {"index": 612, "name": "high20Day", "type": "double"}, {"index": 613, "name": "highChangePercent20Day", "type": "double"}, {"index": 614, "name": "low20Day", "type": "double"}, {"index": 615, "name": "lowChangePercent20Day", "type": "double"}, {"index": 616, "name": "movingAverage20Day", "type": "double"}, {"index": 617, "name": "movingAverageChangePercent20Day", "type": "double"}, {"index": 620, "name": "averageVolume50Day", "type": "int64"}, {"index": 621, "name": "averageVolumeChangePercent50Day", "type": "double"}, {"index": 622, "name": "high50Day", "type": "double"}, {"index": 623, "name": "highChangePercent50Day", "type": "double"}, {"index": 624, "name": "low50Day", "type": "double"}, {"index": 625, "name": "lowChangePercent50Day", "type": "double"}, {"index": 626, "name": "movingAverage50Day", "type": "double"}, {"index": 627, "name": "movingAverageChangePercent50Day", "type": "double"}, {"index": 630, "name": "averageVolume100Day", "type": "int64"}, {"index": 631, "name": "averageVolumeChangePercent100Day", "type": "double"}, {"index": 632, "name": "high100Day", "type": "double"}, {"index": 633, "name": "highChangePercent100Day", "type": "double"}, {"index": 634, "name": "low100Day", "type": "double"}, {"index": 635, "name": "lowChangePercent100Day", "type": "double"}, {"index": 636, "name": "movingAverage100Day", "type": "double"}, {"index": 637, "name": "movingAverageChangePercent100Day", "type": "double"}, {"index": 640, "name": "averageVolume200Day", "type": "int64"}, {"index": 641, "name": "averageVolumeChangePercent200Day", "type": "double"}, {"index": 642, "name": "high200Day", "type": "double"}, {"index": 643, "name": "highChangePercent200Day", "type": "double"}, {"index": 644, "name": "low200Day", "type": "double"}, {"index": 645, "name": "lowChangePercent200Day", "type": "double"}, {"index": 646, "name": "movingAverage200Day", "type": "double"}, {"index": 647, "name": "movingAverageChangePercent200Day", "type": "double"}, {"index": 648, "name": "movingAverage250Day", "type": "double"}, {"index": 649, "name": "movingAverageChangePercent250Day", "type": "double"}, {"index": 650, "name": "averageVolume90Day", "type": "int64"}, {"index": 400, "name": "nextEarningsDate", "type": "int32"}, {"index": 401, "name": "dividend", "type": "double"}, {"index": 402, "name": "dividendYield", "type": "double"}, {"index": 403, "name": "dividendExDate", "type": "int32"}, {"index": 404, "name": "dividendFrequency", "type": "int64"}, {"index": 405, "name": "forwardDividendYield", "type": "double"}, {"index": 501, "name": "roe", "type": "double"}, {"index": 502, "name": "roa", "type": "double"}, {"index": 503, "name": "roic", "type": "double"}, {"index": 504, "name": "currentRatio", "type": "double"}, {"index": 505, "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "double"}, {"index": 506, "name": "netMargin", "type": "double"}, {"index": 507, "name": "operation<PERSON>argin", "type": "double"}, {"index": 508, "name": "epsMrq", "type": "double"}, {"index": 509, "name": "epsTtm", "type": "double"}, {"index": 510, "name": "dilutedEpsMrq", "type": "double"}, {"index": 511, "name": "dilutedEpsTtm", "type": "double"}, {"index": 512, "name": "low52Week", "type": "double"}, {"index": 513, "name": "high52Week", "type": "double"}, {"index": 514, "name": "revenueTtm", "type": "double"}, {"index": 515, "name": "earningsAsOf", "type": "int32"}, {"index": 516, "name": "netProfitMargin", "type": "double"}, {"index": 800, "name": "price1Week", "type": "double"}, {"index": 801, "name": "priceChangePercent1Week", "type": "double"}, {"index": 802, "name": "price1Month", "type": "double"}, {"index": 803, "name": "priceChangePercent1Month", "type": "double"}, {"index": 804, "name": "priceHalfYear", "type": "double"}, {"index": 805, "name": "priceChangePercentHalfYear", "type": "double"}, {"index": 806, "name": "priceQuarter", "type": "double"}, {"index": 807, "name": "priceChangePercentQuarter", "type": "double"}, {"index": 808, "name": "price1Year", "type": "double"}, {"index": 809, "name": "priceChangePercent1Year", "type": "double"}, {"index": 810, "name": "priceYearToDate", "type": "double"}, {"index": 811, "name": "priceChangePercentYearToDate", "type": "double"}, {"index": 520, "name": "roeMrq", "type": "double"}, {"index": 521, "name": "roaMrq", "type": "double"}, {"index": 522, "name": "roicMrq", "type": "double"}, {"index": 523, "name": "currentRatioMrq", "type": "double"}, {"index": 524, "name": "grossMarginMrq", "type": "double"}, {"index": 525, "name": "netMarginMrq", "type": "double"}, {"index": 526, "name": "operationMarginMrq", "type": "double"}, {"index": 527, "name": "previousDayVolume", "type": "int64"}, {"index": 528, "name": "previousDayAtr", "type": "double"}, {"index": 529, "name": "previousDayAdx", "type": "double"}, {"index": 530, "name": "previousDayPlusDMSmoothed", "type": "double"}, {"index": 531, "name": "previousDayMinusDMSmoothed", "type": "double"}, {"index": 532, "name": "previousDayHigh", "type": "double"}, {"index": 533, "name": "previousDayLow", "type": "double"}, {"index": 534, "name": "adx", "type": "double"}, {"index": 535, "name": "percentInstitutionalOwnership", "type": "double"}, {"index": 536, "name": "percentInstitutionalTransactions", "type": "double"}, {"index": 700, "name": "wiim", "type": "string"}, {"index": 701, "name": "wiimAsOf", "type": "int64"}, {"index": 702, "name": "vwap", "type": "double"}, {"index": 704, "name": "vwapChangePercent", "type": "double"}, {"index": 705, "name": "vwapChange", "type": "double"}, {"index": 706, "name": "rsiAvgGain", "type": "double"}, {"index": 707, "name": "rsiAvgLoss", "type": "double"}, {"index": 708, "name": "halted", "type": "string"}, {"index": 709, "name": "tradeCount", "type": "int64"}, {"index": 710, "name": "regularHighCount", "type": "int64"}, {"index": 711, "name": "regularLowCount", "type": "int64"}, {"index": 712, "name": "preMarketHighCount", "type": "int64"}, {"index": 713, "name": "preMarketLowCount", "type": "int64"}, {"index": 714, "name": "afterMarketHighCount", "type": "int64"}, {"index": 715, "name": "afterMarketLowCount", "type": "int64"}, {"index": 716, "name": "dayHighCount", "type": "int64"}, {"index": 717, "name": "dayLowCount", "type": "int64"}, {"index": 720, "name": "dayHigh", "type": "double"}, {"index": 721, "name": "dayLow", "type": "double"}, {"index": 722, "name": "preMarketHigh", "type": "double"}, {"index": 723, "name": "preMarketLow", "type": "double"}, {"index": 724, "name": "afterMarketHigh", "type": "double"}, {"index": 725, "name": "afterMarketLow", "type": "double"}, {"index": 726, "name": "regularHigh", "type": "double"}, {"index": 727, "name": "regularLow", "type": "double"}, {"index": 728, "name": "priceSpikeUpCount", "type": "int64"}, {"index": 729, "name": "priceSpikeDownCount", "type": "int64"}, {"index": 730, "name": "haltedCount", "type": "int64"}, {"index": 731, "name": "usCongressTotal", "type": "double"}, {"index": 732, "name": "usCongressRepublicansTotal", "type": "double"}, {"index": 733, "name": "usCongressDemocratsTotal", "type": "double"}, {"index": 734, "name": "usCongressOtherTotal", "type": "double"}, {"index": 735, "name": "volumeFloatRatio", "type": "double"}, {"index": 736, "name": "atrp", "type": "double"}, {"index": 737, "name": "ytdGainLoss", "type": "double"}, {"index": 738, "name": "mtdGainLoss", "type": "double"}, {"index": 695, "name": "ytdChange", "type": "double"}, {"index": 694, "name": "mtdChange", "type": "double"}, {"index": 739, "name": "yearOpen", "type": "double"}, {"index": 753, "name": "monthOpen", "type": "double"}, {"index": 754, "name": "volumeFloatPercent", "type": "double"}, {"index": 740, "name": "rsi1m", "type": "double"}, {"index": 741, "name": "rsi2m", "type": "double"}, {"index": 742, "name": "rsi5m", "type": "double"}, {"index": 743, "name": "rsi15m", "type": "double"}, {"index": 745, "name": "rsi30m", "type": "double"}, {"index": 746, "name": "rsi60m", "type": "double"}, {"index": 747, "name": "rsiEth1m", "type": "double"}, {"index": 748, "name": "rsiEth2m", "type": "double"}, {"index": 749, "name": "rsiEth5m", "type": "double"}, {"index": 750, "name": "rsiEth15m", "type": "double"}, {"index": 751, "name": "rsiEth30m", "type": "double"}, {"index": 752, "name": "rsiEth60m", "type": "double"}, {"index": 755, "name": "averageDollarVolume10Day", "type": "double"}, {"index": 756, "name": "averageVolume60Day", "type": "int64"}, {"index": 757, "name": "relativeVolumePercent60Day", "type": "double"}, {"index": 758, "name": "rsi1mLowest", "type": "double"}, {"index": 759, "name": "rsi2mLowest", "type": "double"}, {"index": 760, "name": "rsi5mLowest", "type": "double"}, {"index": 761, "name": "rsi15mLowest", "type": "double"}, {"index": 762, "name": "rsi30mLowest", "type": "double"}, {"index": 763, "name": "rsi60mLowest", "type": "double"}, {"index": 764, "name": "rsiEth1mLowest", "type": "double"}, {"index": 765, "name": "rsiEth2mLowest", "type": "double"}, {"index": 766, "name": "rsiEth5mLowest", "type": "double"}, {"index": 767, "name": "rsiEth15mLowest", "type": "double"}, {"index": 768, "name": "rsiEth30mLowest", "type": "double"}, {"index": 769, "name": "rsiEth60mLowest", "type": "double"}, {"index": 770, "name": "rsi1mGreatest", "type": "double"}, {"index": 771, "name": "rsi2mGreatest", "type": "double"}, {"index": 772, "name": "rsi5mGreatest", "type": "double"}, {"index": 773, "name": "rsi15mGreatest", "type": "double"}, {"index": 774, "name": "rsi30mGreatest", "type": "double"}, {"index": 775, "name": "rsi60mGreatest", "type": "double"}, {"index": 776, "name": "rsiEth1mGreatest", "type": "double"}, {"index": 777, "name": "rsiEth2mGreatest", "type": "double"}, {"index": 778, "name": "rsiEth5mGreatest", "type": "double"}, {"index": 779, "name": "rsiEth15mGreatest", "type": "double"}, {"index": 780, "name": "rsiEth30mGreatest", "type": "double"}, {"index": 781, "name": "rsiEth60mGreatest", "type": "double"}, {"index": 782, "name": "rsiCrossOver", "type": "double"}, {"index": 718, "name": "ema12d", "type": "double"}, {"index": 719, "name": "ema26d", "type": "double"}, {"index": 699, "name": "macd", "type": "double"}, {"index": 698, "name": "macdSignalLine", "type": "double"}, {"index": 697, "name": "macdHistogram", "type": "double"}, {"index": 696, "name": "macdSignalLineBase", "type": "double"}, {"index": 679, "name": "dividendGrowthRate1Year", "type": "double"}, {"index": 678, "name": "dividendGrowthRate3Year", "type": "double"}, {"index": 677, "name": "dividendGrowthRate5Year", "type": "double"}, {"index": 962, "name": "totalDividendTtm", "type": "double"}, {"index": 963, "name": "dividendIncreasing", "type": "int32"}, {"index": 680, "name": "newsBzWireTitle", "type": "string"}, {"index": 681, "name": "newsBzWireNodeId", "type": "string"}, {"index": 682, "name": "newsBzWireTimestamp", "type": "int64"}, {"index": 683, "name": "newsPRTitle", "type": "string"}, {"index": 684, "name": "newsPRNodeId", "type": "string"}, {"index": 685, "name": "newsPRTimestamp", "type": "int64"}, {"index": 686, "name": "newsSecTitle", "type": "string"}, {"index": 687, "name": "newsSecNodeId", "type": "string"}, {"index": 688, "name": "newsSecTimestamp", "type": "int64"}, {"index": 689, "name": "newsBzWireTitleWithNodeId", "type": "string"}, {"index": 690, "name": "newsPRTitleWithNodeId", "type": "string"}, {"index": 691, "name": "newsSecTitleWithNodeId", "type": "string"}, {"index": 676, "name": "movingAverage500Day", "type": "double"}, {"index": 675, "name": "gapChange", "type": "double"}, {"index": 674, "name": "gapChangePercent", "type": "double"}, {"index": 673, "name": "adr14d", "type": "double"}, {"index": 672, "name": "dilutedEpsMrqSurprisePercent", "type": "double"}, {"index": 671, "name": "dilutedEpsTtmSurprisePercent", "type": "double"}, {"index": 670, "name": "dilutedRevenueMrqSurprisePercent", "type": "double"}, {"index": 669, "name": "dilutedRevenueTtmSurprisePercent", "type": "double"}, {"index": 668, "name": "epsGrowthTtm", "type": "double"}, {"index": 667, "name": "epsGrowthY2Y", "type": "double"}, {"index": 666, "name": "epsGrowthQ2Q", "type": "double"}, {"index": 665, "name": "epsGrowth5Year", "type": "double"}, {"index": 659, "name": "epsGrowthYearAgoQ", "type": "double"}, {"index": 664, "name": "revenueGrowthTtm", "type": "double"}, {"index": 663, "name": "revenueGrowthY2Y", "type": "double"}, {"index": 662, "name": "revenueGrowthQ2Q", "type": "double"}, {"index": 661, "name": "revenueGrowth5Year", "type": "double"}, {"index": 660, "name": "revenueGrowthYearAgoQ", "type": "double"}, {"index": 658, "name": "ltDebtEquity", "type": "double"}, {"index": 812, "name": "bullsSay", "type": "string"}, {"index": 813, "name": "bearsSay", "type": "string"}, {"index": 657, "name": "ipoDateNumber", "type": "int64"}, {"index": 656, "name": "ffoPerShare", "type": "double"}, {"index": 655, "name": "quickRatioMrq", "type": "double"}, {"index": 654, "name": "highAllTime", "type": "double"}, {"index": 653, "name": "lowAllTime", "type": "double"}, {"index": 652, "name": "evToEbitda", "type": "double"}, {"index": 907, "name": "totalDebtEquityRatio", "type": "double"}, {"index": 908, "name": "dividendPayoutRatio", "type": "double"}, {"index": 909, "name": "operatingCashFlow", "type": "double"}, {"index": 910, "name": "insiderPercentage", "type": "double"}, {"index": 911, "name": "priceToCashFlowRatio", "type": "double"}, {"index": 912, "name": "regularHoursChange", "type": "double"}, {"index": 913, "name": "regularHoursPercentChange", "type": "double"}, {"index": 914, "name": "postToPreHoursChange", "type": "double"}, {"index": 915, "name": "postToPreHoursPercentChange", "type": "double"}, {"index": 916, "name": "scannerClose", "type": "double"}, {"index": 917, "name": "scannerChange", "type": "double"}, {"index": 918, "name": "scannerChangePercent", "type": "double"}, {"index": 919, "name": "scannerChangePercentClose", "type": "double"}, {"index": 920, "name": "scannerPreviousClose", "type": "double"}, {"index": 921, "name": "freeCashFlow", "type": "double"}, {"index": 922, "name": "minPriceTarget", "type": "double"}, {"index": 923, "name": "max<PERSON>rice<PERSON>arget", "type": "double"}, {"index": 924, "name": "meanPriceTarget", "type": "double"}, {"index": 925, "name": "totalNumberOfAnalyst", "type": "double"}, {"index": 926, "name": "averageAnalystRecommendation", "type": "string"}, {"index": 927, "name": "pegRatio", "type": "double"}, {"index": 928, "name": "priceToFreeCashFlowRatio", "type": "double"}, {"index": 929, "name": "distanceToMaxPriceTarget", "type": "double"}, {"index": 930, "name": "distanceToMinPriceTarget", "type": "double"}, {"index": 931, "name": "distanceToMeanPriceTarget", "type": "double"}, {"index": 932, "name": "shortTermTrend", "type": "string"}, {"index": 933, "name": "mediumTermTrend", "type": "string"}, {"index": 934, "name": "longTermTrend", "type": "string"}, {"index": 935, "name": "twentySixWeekChange", "type": "double"}, {"index": 936, "name": "fiftyTwoWeekChange", "type": "double"}, {"index": 937, "name": "annualizedStandardDeviation", "type": "double"}, {"index": 938, "name": "momentum", "type": "double"}, {"index": 939, "name": "value", "type": "double"}, {"index": 940, "name": "piotroskiFScore", "type": "int64"}, {"index": 941, "name": "netIncomeTtm", "type": "double"}, {"index": 943, "name": "currentRatioYearAgo", "type": "double"}, {"index": 944, "name": "roaYear<PERSON>go", "type": "double"}, {"index": 945, "name": "grossMarginYearAgo", "type": "double"}, {"index": 946, "name": "longTermDebt", "type": "double"}, {"index": 947, "name": "longTermDebtYearAgo", "type": "double"}, {"index": 948, "name": "shareIssued", "type": "double"}, {"index": 949, "name": "shareIssuedYearAgo", "type": "double"}, {"index": 950, "name": "totalAssets", "type": "double"}, {"index": 951, "name": "totalAssetsYearAgo", "type": "double"}, {"index": 952, "name": "totalAssets2YearAgo", "type": "double"}, {"index": 953, "name": "revenueTtmYearAgo", "type": "double"}, {"index": 954, "name": "longTermDebtAndCapitalLeaseObligation", "type": "double"}, {"index": 955, "name": "averageTotalAssets", "type": "double"}, {"index": 956, "name": "averageTotalAssetsYearAgo", "type": "double"}, {"index": 942, "name": "growth", "type": "double"}, {"index": 957, "name": "quality", "type": "double"}, {"index": 958, "name": "momentumPercentile", "type": "double"}, {"index": 959, "name": "valuePercentile", "type": "double"}, {"index": 960, "name": "growthPercentile", "type": "double"}, {"index": 961, "name": "qualityPercentile", "type": "double"}, {"index": 965, "name": "rateOfReturnDay", "type": "double"}, {"index": 966, "name": "rateOfReturnWeek", "type": "double"}, {"index": 967, "name": "rateOfReturnMonth", "type": "double"}, {"index": 968, "name": "dividendOneDay", "type": "double"}, {"index": 969, "name": "dividendOneWeek", "type": "double"}, {"index": 970, "name": "dividendOneMonth", "type": "double"}, {"index": 971, "name": "totalLiabilities", "type": "double"}, {"index": 972, "name": "retainedEarnings", "type": "double"}, {"index": 973, "name": "ebit", "type": "double"}, {"index": 974, "name": "altmanZScore", "type": "double"}, {"index": 975, "name": "powerEarningGap", "type": "bool"}, {"index": 976, "name": "monsterPowerEarningGap", "type": "bool"}, {"index": 977, "name": "monsterGap", "type": "bool"}, {"index": 978, "name": "oelGap", "type": "bool"}]