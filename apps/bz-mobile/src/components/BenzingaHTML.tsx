import React, { createRef, useCallback, useEffect, useMemo, useState } from 'react';
import {
  Dimensions,
  StyleSheet,
  View,
  Text,
  Platform,
  Linking,
  Modal,
  SafeAreaView,
  Pressable,
  GestureResponderEvent,
  BackHandler,
  ScrollView,
} from 'react-native';

import ImageViewer from 'react-native-image-zoom-viewer';

import * as WebBrowser from 'expo-web-browser';

import HTML, {
  CustomBlockRenderer,
  CustomRendererProps,
  CustomTagRendererRecord,
  CustomTextualRenderer,
  domNodeToHTMLString,
  HTMLContentModel,
  HTMLElementModel,
  IMGElementProps,
  InternalRendererProps,
  MixedStyleDeclaration,
  Node,
  TBlock,
  TNodeChildrenRenderer,
  TPhrasing,
  TText,
  useInternalRenderer,
  useRendererProps,
  RenderHTML,
  MixedStyleRecord,
} from 'react-native-render-html';
import { WebView } from 'react-native-webview';
import { useTheme } from '../theme/themecontext';
import { isTag } from 'domutils';
import LoadingView from './LoadingView';
import QuoteTile from './Quote/QuoteTile';
import Icon, { IconTypes } from './Icon/Icon';
import { WebViewNavigation, WebViewProgressEvent } from 'react-native-webview/lib/WebViewTypes';
import { News } from '@benzinga/advanced-news-manager';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { AccountNavigationStackParamList } from '../navigation/AccountNavigationStack';
import { NewsNavigationStackParamList } from '../navigation/NewsNavigationStack';
import { HomeNavigationStackParamList } from '../navigation/HomeNavigationStack';
import BZTaboolaAd, { BZTBL_AD_POSITION } from './BZTaboolaAd';

interface BenzingaHTMLProps {
  html?: string;
  showQuote?: (ticker: string) => void;
  style?: MixedStyleDeclaration;
  showArticle?: (article: News) => void;
}

interface OnElement extends Node {
  tagName?: string;
}

let alternateRow = false;
let webViewRendered = true;

const BAD_IMG_URLS = ['about:///blank'];

const TableRenderer: CustomBlockRenderer = (props: CustomRendererProps<TBlock>) => {
  const { colors } = useTheme();
  alternateRow = false;
  const html = React.useMemo(() => domNodeToHTMLString(props.tnode.domNode), [props.tnode]);
  const navigation: StackNavigationProp<
    AccountNavigationStackParamList | NewsNavigationStackParamList | HomeNavigationStackParamList
  > = useNavigation();
  const [scrollViewWidth, setScrollViewWidth] = useState(Dimensions.get('window').width);
  const [isFitToScreen, setIsFitToScreen] = useState(true);

  const parseTable = (html: string) => {
    const rows = html.match(/<tr>(.*?)<\/tr>/g) ?? [];
    const columns = rows.map(row => {
      if (row.match(/<td><strong>(.*?)<\/span>/)) {
        const cells = row.match(/<strong>(.*?)<\/strong>/);
        return cells ? cells.map(cell => cell.replace(/<\/?strong>/g, '').trim()) : [];
      } else if (row.match(/<td><strong>(.*?)<\/strong><\/td>/) && !row.match(/<td><\/td>/)) {
        const cells = row.match(/<td><strong>(.*?)<\/strong><\/td>/g);
        return cells ? cells.map(cell => cell.replace(/<\/?td>|<\/?strong>/g, '').trim()) : [];
      }
      const cells = row.match(/<td>(.*?)<\/td>/g);
      return cells ? cells.map(cell => cell.replace(/<\/?td>|<\/?strong>/g, '').trim()) : [];
    });

    return columns;
  };

  const addColumnStyles = useCallback(
    (parseHTML: string) => {
      const parsedTable = parseTable(html);
      const noOfRows = parsedTable.length + 1;
      const columnStyles: number[] = [];
      const screenWidth = Dimensions.get('window').width;
      const useScreenWidth = screenWidth * 0.3;
      if (isFitToScreen) {
        return parseHTML;
      }
      for (let j = 0; j < parsedTable.length; j++) {
        for (let i = 0; i < parsedTable[j].length; i++) {
          let totalLength = 0;
          let maxLength = 0;
          let minLength = 0;
          parsedTable.forEach(item => {
            totalLength = totalLength + (item[i]?.length ?? 0);
            maxLength = maxLength < item[i]?.length ? item[i]?.length : maxLength;
            minLength = minLength > item[i]?.length ? item[i]?.length : minLength;
          });
          if (maxLength <= 4) {
            columnStyles[i] = (maxLength + 1.5) * 10;
          } else if (Math.floor(maxLength / 2) > minLength && (totalLength / (noOfRows / 2)) * 10 < useScreenWidth) {
            columnStyles[i] = useScreenWidth;
          } else {
            const cWidth = (totalLength / (noOfRows / 2)) * 10;
            columnStyles[i] = Math.max(cWidth, useScreenWidth * 0.3);
          }
        }
      }
      for (let i = 0; i < noOfRows; i++) {
        for (const width of columnStyles) {
          // Replace only the first occurrence of <th> with each iteration
          const thindex = parseHTML.indexOf('<th>');
          if (thindex !== -1) {
            parseHTML =
              parseHTML.substring(0, thindex) +
              `<th style="width: ${width}px;">` +
              parseHTML.substring(thindex + '<th>'.length);
          }
          // Replace only the first occurrence of <td> with each iteration
          const index = parseHTML.indexOf('<td>');
          if (index !== -1) {
            parseHTML =
              parseHTML.substring(0, index) +
              `<td style="width: ${width}px;">` +
              parseHTML.substring(index + '<td>'.length);
          }
        }
      }
      return parseHTML;
    },
    [isFitToScreen],
  );

  const showQuote = useCallback(
    (ticker: string) => {
      global.Analytics.event('Navigation', 'Show Ticker', 'Article');
      if (ticker?.includes('/USD')) {
        const _symbol = ticker.split('/USD')[0];
        navigation.navigate('CryptoQuote', {
          symbol: _symbol,
        });
      } else {
        navigation.navigate('Quote', {
          symbol: ticker,
        });
      }
    },
    [navigation],
  );

  const onPress = useCallback(
    (href: string) => {
      if (!webViewRendered) {
        console.log('Web View Rendering!');
        return;
      }
      showQuote(href);
    },
    [showQuote, webViewRendered],
  );

  const customHTMLElementModels = useMemo(
    () => ({
      span: HTMLElementModel.fromCustomModel({
        tagName: 'span',
        contentModel: HTMLContentModel.textual,
      }),
    }),
    [],
  );

  const renderers: CustomTagRendererRecord = useMemo(
    () => ({
      span: SpanRenderer,
      tr: TrRenderer,
    }),
    [],
  );

  const renderersProps = useMemo(
    () => ({
      span: {
        onPress: onPress,
      },
    }),
    [onPress],
  );

  const _tagStyles = useMemo<MixedStyleRecord>(
    () => ({
      td: {
        color: colors.text,
        fontSize: 16,
        marginVertical: 1,
        justifyContent: 'center',
      },
      th: {
        color: colors.text,
      },
      span: {
        fontSize: 14,
      },
    }),
    [],
  );

  const onScrollViewLayout = useCallback(event => {
    const { width } = event.nativeEvent.layout;
    if (width > 0) {
      setScrollViewWidth(width);
    }
  }, []);

  return (
    <View style={{ flex: 1 }}>
      <ScrollView horizontal={true} onLayout={onScrollViewLayout} scrollEnabled={!isFitToScreen}>
        <View style={{ width: isFitToScreen ? scrollViewWidth : 'auto' }}>
          <RenderHTML
            source={{ html: addColumnStyles(html) }}
            contentWidth={scrollViewWidth - 32}
            tagsStyles={_tagStyles}
            renderersProps={renderersProps}
            renderers={renderers}
            customHTMLElementModels={customHTMLElementModels}
          />
        </View>
      </ScrollView>
      <Pressable
        onPress={() => {
          setIsFitToScreen(!isFitToScreen);
        }}
      >
        <View style={{ flexDirection: 'row-reverse', alignItems: 'center' }}>
          <Icon type={IconTypes.MaterialIcons} color={colors.text} name="fit-screen" size={20} />
          <Text style={{ color: colors.text, fontSize: 12, marginRight: 4 }}>Expand</Text>
        </View>
      </Pressable>
    </View>
  );
};

const IframeRenderer: CustomBlockRenderer = (props: CustomRendererProps<TBlock>) => {
  const [loading, setLoading] = useState(true);
  const html = React.useMemo(() => domNodeToHTMLString(props.tnode.domNode), [props.tnode]);
  const uri = React.useMemo(() => {
    const uriRegex = new RegExp('src\\="([^"]*)"', 'g');
    const matches = uriRegex.exec(html);
    return matches && matches.length >= 2 ? matches[1] : '';
  }, [html]);

  return (
    <View style={styles.videoContainer}>
      <WebView
        onLoadEnd={() => {
          setLoading(false);
          webViewRendered = true;
        }}
        onLoadStart={() => {
          if (loading) setLoading(true);
          webViewRendered = false;
        }}
        source={{
          uri: uri,
        }}
        style={styles.webViewContainer}
      />
      {loading ? (
        <View style={styles.loadingContainer}>
          <LoadingView />
        </View>
      ) : null}
    </View>
  );
};

const TweetRenderer = (props: { tweetUrl: string; onTweetLoad: (status: boolean) => void }) => {
  const { onTweetLoad, tweetUrl } = props;
  const [loading, setLoading] = useState(true);
  const [webviewHeight, setWebViewHeight] = useState(0);
  const { colors } = useTheme();
  const webViewRef = createRef<WebView>();

  useEffect(() => {
    return () => {
      if (webViewRef && webViewRef?.current) {
        webViewRef.current?.stopLoading();
      }
    };
  }, []);

  // Handle hardware back press when webview is rendering
  const handleBackButton = () => {
    if (webViewRendered) {
      return false;
    }
    return true;
  };

  useFocusEffect(
    React.useCallback(() => {
      BackHandler.addEventListener('hardwareBackPress', handleBackButton);

      return () => BackHandler.removeEventListener('hardwareBackPress', handleBackButton);
    }, []),
  );

  const handlePageLoad = (event: WebViewProgressEvent) => {
    if (event.nativeEvent.progress === 1) {
      webViewRef.current?.injectJavaScript(`
        const meta = document.createElement('meta');
        meta.setAttribute(
          'content',
          'width=width, initial-scale=1,maximum-scale=1,user-scalable=2.0'
        );
        meta.setAttribute('name', 'viewport');
        document.getElementsByTagName('head')[0].appendChild(meta);
        document.body.style.backgroundColor = "${colors.background}";

        setTimeout(() => {
          var body = document.body,
          html = document.documentElement;
          const height = Math.max(body.getBoundingClientRect().height, html.getBoundingClientRect().height);
          window.location.hash = height;
        }, 5000); // find working event and replace this timeout with that

      `);
    }
  };

  const onNavigationChange = (event: WebViewNavigation) => {
    if (event.url && event.url.includes('#')) {
      const htmlHeight = Number(event.url.split('#')[1]);
      const _webviewHeight = isNaN(htmlHeight) ? 0 : htmlHeight;
      setWebViewHeight(_webviewHeight);
      if (onTweetLoad) {
        onTweetLoad(_webviewHeight > 0);
      }
    }
  };

  const handleShouldStartLoadWithRequest = (event: { url: string }) => {
    if (!event.url.includes('platform.twitter.com')) {
      Linking.openURL(event.url);
      return false;
    }
    return true;
  };

  return (
    <View style={[styles.twitterContainer, { height: webviewHeight ?? 60 }]}>
      <WebView
        ref={webViewRef}
        onLoadEnd={() => {
          setTimeout(() => {
            setLoading(false);
            webViewRendered = true;
          }, 5000);
        }}
        onLoadStart={() => {
          if (!loading) setLoading(true);
          webViewRendered = false;
        }}
        onLoadProgress={handlePageLoad}
        scrollEnabled={false}
        scalesPageToFit={Platform.OS === 'ios'}
        onShouldStartLoadWithRequest={handleShouldStartLoadWithRequest}
        source={{
          uri: tweetUrl,
        }}
        style={[styles.webViewContainer, { height: webviewHeight ?? 0 }]}
        onNavigationStateChange={onNavigationChange}
      />
      {loading || webviewHeight === 0 ? (
        <View style={styles.loadingContainer}>
          <LoadingView />
        </View>
      ) : null}
    </View>
  );
};

const BlockQuoteRenderer: CustomBlockRenderer = (props: CustomRendererProps<TBlock>) => {
  const html = React.useMemo(() => domNodeToHTMLString(props.tnode.domNode), [props.tnode]);

  const [tweetLoaded, setTweetLoaded] = useState(true);

  const extractTweetUrl = (html: string) => {
    let tweetId = html.replace(/(.+)(?=https)/g, '');
    tweetId = tweetId.replace(/(=?">)(.*)/g, '');
    const matches = /([0-9]*)(?:\?ref_src)/g.exec(tweetId);
    if (matches && matches.length >= 2) {
      tweetId = matches[1];
      const tweetUrl =
        `https://platform.twitter.com/embed/Tweet.html?` +
        `dnt=false&` +
        `embedId=twitter-widget-0&` +
        `frame=false&` +
        `hideCard=false&` +
        `hideThread=true&` +
        `id=${tweetId}&` +
        `lang=en`;
      return tweetUrl;
    } else {
      return undefined;
    }
  };

  const tweetUrl = React.useMemo(() => {
    return extractTweetUrl(html);
  }, [html]);

  const fallbackIfTweetLoadFailed = (status: boolean) => {
    setTweetLoaded(status);
  };

  return tweetUrl && tweetLoaded ? (
    <TweetRenderer tweetUrl={tweetUrl} onTweetLoad={fallbackIfTweetLoadFailed} />
  ) : (
    <TNodeChildrenRenderer {...props} />
  );
};

const TheadRenderer: CustomBlockRenderer = (props: CustomRendererProps<TBlock>) => {
  const { colors } = useTheme();
  return (
    <View
      style={{
        flexDirection: 'row',
        backgroundColor: colors.cardSecondary,
      }}
    >
      <TNodeChildrenRenderer {...props} />
    </View>
  );
};

const TrRenderer: CustomBlockRenderer = (props: CustomRendererProps<TBlock>) => {
  const { colors, isDark } = useTheme();
  alternateRow = !alternateRow;

  return (
    <View style={styles.trStyle}>
      <View
        style={[
          styles.flexRow,
          { backgroundColor: alternateRow ? 'transparent' : isDark ? colors.card : colors.background },
        ]}
      >
        <TNodeChildrenRenderer {...props} />
      </View>
      <View style={[styles.trBorder, { backgroundColor: colors.border }]} />
    </View>
  );
};

const ThRenderer: CustomBlockRenderer = (props: CustomRendererProps<TBlock>) => {
  const html = domNodeToHTMLString(props.tnode.domNode);

  if (html?.includes('&#xA0;')) {
    return null;
  }

  return (
    <View style={styles.tdStyle}>
      <TNodeChildrenRenderer {...props} />
    </View>
  );
};

const TdRenderer: CustomBlockRenderer = (props: CustomRendererProps<TBlock>) => {
  let html = domNodeToHTMLString(props.tnode.domNode);

  if (html?.includes('&#xA0;')) {
    html = html.split('&#xA0;')[0];
  }

  return (
    <View style={styles.tdStyle}>
      <TNodeChildrenRenderer {...props} />
    </View>
  );
};

const CaptionRenderer: CustomTextualRenderer = (props: CustomRendererProps<TText | TPhrasing>) => {
  const { colors } = useTheme();
  return (
    <View style={{ flexDirection: 'row', marginVertical: 8 }}>
      <Text style={{ color: colors.text, fontWeight: '600' }}>{(props.tnode as TText).data}</Text>
    </View>
  );
};

const BannerRendered: CustomBlockRenderer = () => {
  return (
    <View style={styles.adContainer}>
      <BZTaboolaAd unitType={BZTBL_AD_POSITION.PAGE_MIDDLE} />
    </View>
  );
};

const SpanRenderer: CustomTextualRenderer = (props: CustomRendererProps<TText | TPhrasing>) => {
  const { colors } = useTheme();
  const customRenderProps = useRendererProps('span');
  const [percentChange, setPercentChange] = useState<number>(0);
  const html = domNodeToHTMLString(props.tnode.domNode);
  const isNotTicker = html.indexOf('class="ticker"') === -1;

  const [symbol] = useState((props.tnode as TText).data);

  if (isNotTicker) {
    return (
      <Text style={{ color: colors.text, marginTop: 8, marginBottom: 4, fontSize: 18, lineHeight: 22 }}>
        <TNodeChildrenRenderer {...props} />
      </Text>
    );
  }

  const matchTicker = html.match(/data-ticker="(.*?)"/);
  const tickerValue = matchTicker ? matchTicker[1] : '';

  return (
    <QuoteTile
      onPress={quote => {
        if (customRenderProps.onPress && webViewRendered) {
          customRenderProps.onPress(quote, 'quote');
        }
      }}
      onChange={(percentChange: number) => {
        setPercentChange(percentChange);
      }}
      symbol={isNotTicker ? symbol : tickerValue}
      hideMarketStatus={true}
      pressableStyle={[
        {
          borderRadius: 2,
          paddingHorizontal: 4,
          borderColor: percentChange < 0 ? colors.red : colors.green,
          backgroundColor: percentChange < 0 ? colors.lightRed : colors.lightGreen,
        },
      ]}
    />
  );
};

const CustomImageRenderer = (props: InternalRendererProps<TBlock>) => {
  const { Renderer, rendererProps } = useInternalRenderer('img', props);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const onPress = () => setIsModalOpen(true);
  const onModalClose = () => setIsModalOpen(false);
  const uri = rendererProps.source.uri;
  const thumbnailSource = {
    ...rendererProps.source,
    uri: uri && BAD_IMG_URLS.includes(uri) ? undefined : uri, // drop bad image urls to avoid crashes
  };
  const CastedRenderer = Renderer as React.FC<IMGElementProps>;

  return (
    <View style={{ alignItems: 'center', marginVertical: 20 }}>
      <CastedRenderer {...rendererProps} source={thumbnailSource} onPress={onPress} />
      <Modal visible={isModalOpen} onRequestClose={onModalClose} animationType="slide">
        <SafeAreaView style={{ flex: 1, backgroundColor: 'black' }}>
          <ImageViewer
            enableSwipeDown
            renderIndicator={() => <View />}
            imageUrls={[{ url: uri || '' }]}
            onCancel={onModalClose}
            saveToLocalByLongPress={false}
            useNativeDriver
          />
          <Pressable onPress={onModalClose} style={{ alignItems: 'center', marginVertical: 20 }}>
            <Icon type={IconTypes.SimpleLineIcons} color={'white'} name="close" size={30} />
          </Pressable>
        </SafeAreaView>
      </Modal>
    </View>
  );
};

const BenzingaHTML = React.memo(({ html, showArticle, showQuote, style }: BenzingaHTMLProps) => {
  const { colors } = useTheme();

  const onPress = useCallback(
    (value: GestureResponderEvent | string, href: string) => {
      if (!webViewRendered) {
        console.log('Web View Rendering!');
        return;
      }
      let matches;
      if ((matches = href.match(/.com\/stock\/([A-z-]+)?\/?#/)) || (matches = href.match(/^\/stock\/([A-z-]+)?\/?#/))) {
        showQuote && showQuote(matches[1]);
      } else if ((matches = href.match(/\/[A-z0-9-]+\/[0-9]+\/[0-9]+\/([0-9]+)\/([A-z-]+)/))) {
        showArticle &&
          showArticle({
            id: Number(matches[1]),
          } as News);
      } else if (
        (matches = href.match(
          /(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\\+.~#?&//=]*)/g,
        ))
      ) {
        const updatedHref =
          href.indexOf('?') === -1 ? `${href}?campaigns_strategy=none` : `${href}&campaigns_strategy=none`;
        WebBrowser.openBrowserAsync(updatedHref);
      } else if (href === 'quote') {
        // setQuote(_);
        showQuote && showQuote(value as string);
      }
    },
    [showQuote, showArticle],
  );

  const customHTMLElementModels = useMemo(
    () => ({
      campaign: HTMLElementModel.fromCustomModel({
        tagName: 'campaign',
        mixedUAStyles: {
          backgroundColor: colors.cardSecondary,
          paddingHorizontal: 8,
          paddingBottom: 8,
          borderWidth: 1,
          borderColor: colors.border,
        },
        contentModel: HTMLContentModel.block,
      }),
      caption: HTMLElementModel.fromCustomModel({
        tagName: 'caption',
        contentModel: HTMLContentModel.textual,
      }),
      banner: HTMLElementModel.fromCustomModel({
        tagName: 'banner',
        contentModel: HTMLContentModel.block,
      }),
      iframe: HTMLElementModel.fromCustomModel({
        tagName: 'iframe',
        contentModel: HTMLContentModel.block,
      }),
      blockquote: HTMLElementModel.fromCustomModel({
        tagName: 'blockquote',
        contentModel: HTMLContentModel.block,
      }),
      span: HTMLElementModel.fromCustomModel({
        tagName: 'span',
        contentModel: HTMLContentModel.textual,
      }),
    }),
    [colors],
  );

  const renderers: CustomTagRendererRecord = useMemo(
    () => ({
      table: TableRenderer,
      thead: TheadRenderer,
      tr: TrRenderer,
      th: ThRenderer,
      td: TdRenderer,
      caption: CaptionRenderer,
      banner: BannerRendered,
      iframe: IframeRenderer,
      blockquote: BlockQuoteRenderer,
      span: SpanRenderer,
      img: CustomImageRenderer,
    }),
    [],
  );

  const renderersProps = useMemo(
    () => ({
      a: {
        onPress: onPress,
      },
      span: {
        onPress: onPress,
      },
    }),
    [onPress],
  );

  const replaceColorAndBgColor = (style: string) => {
    if (!style) return '';

    const _styles = style.split(';');
    const _replacedStyles: string[] = [];
    for (const style of _styles) {
      if (style.indexOf('background-color:') > -1) {
        // bg color exist
        _replacedStyles.push(`background-color: ${colors.cardSecondary}; `);
      } else if (style.indexOf('color:') > -1) {
        // text color exist
        _replacedStyles.push(`color: ${colors.text}; `);
      } else {
        _replacedStyles.push(style);
      }
    }
    return _replacedStyles.join('');
  };

  const cleanColorsOfElements = (element: Node) => {
    if (isTag(element)) {
      element.attribs['style'] = replaceColorAndBgColor(element.attribs['style']);
      if (element.children.length) {
        for (const child of element.children) {
          cleanColorsOfElements(child);
        }
      } else {
        element.attribs['style'] = replaceColorAndBgColor(element.attribs['style']);
      }
    }
    return;
  };

  const onElement = (element: OnElement) => {
    // Remove the first two children of an ol tag.
    if (element.tagName === 'campaign') {
      cleanColorsOfElements(element);
    }
  };

  const domVisitors = useMemo(
    () => ({
      onElement,
    }),
    [onElement],
  );

  const _tagStyles = useMemo<MixedStyleRecord>(
    () => ({
      html: {
        color: colors.text,
      },
      div: {
        color: colors.text,
      },
      p: {
        marginTop: 8,
        marginBottom: 4,
        fontSize: 18,
        lineHeight: 22,
        color: colors.text,
      },
      a: {
        color: colors.linkText,
      },
      img: {
        width: Dimensions.get('window').width,
        height: Dimensions.get('window').width * 0.5625,
        resizeMode: 'contain',
      },
      h2: {
        marginTop: 16,
        marginBottom: 4,
        fontSize: 32,
        lineHeight: 36,
        color: colors.text,
      },
      h3: {
        marginTop: 16,
        marginBottom: 4,
        fontSize: 24,
        lineHeight: 32,
        color: colors.text,
      },
      b: {
        color: colors.text,
      },
      input: {
        color: colors.text,
      },
      td: {
        color: colors.text,
      },
      th: {
        color: colors.text,
      },
      li: {
        color: colors.text,
      },
      tr: {
        textAlign: 'left',
      },
      span: {
        fontSize: 16,
      },
    }),
    [colors.text, Dimensions],
  );

  return (
    <HTML
      baseStyle={style}
      source={{ html: `<div>${html}</div>` }}
      ignoredStyles={['height', 'width']}
      contentWidth={Dimensions.get('window').width - 32}
      renderersProps={renderersProps}
      renderers={renderers}
      customHTMLElementModels={customHTMLElementModels}
      domVisitors={domVisitors}
      tagsStyles={_tagStyles}
    />
  );
});
BenzingaHTML.displayName = 'BenzingaHTML';
export default BenzingaHTML;

const styles = StyleSheet.create({
  flexRow: { flexDirection: 'row' },
  trStyle: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tdStyle: {
    flexDirection: 'column',
    flex: 1,
    justifyContent: 'center',
    alignItems: 'flex-start',
    padding: 2,
    marginLeft: 3,
    marginBottom: 3,
  },
  adContainer: {
    width: Dimensions.get('window').width - 32,
    justifyContent: 'center',
    marginVertical: 10,
    alignItems: 'center',
  },
  webViewContainer: {
    height: '100%',
    width: '100%',
    opacity: 0.99,
    overflow: 'hidden',
  },
  loadingContainer: {
    backgroundColor: 'rgba(0,0,0,0.4)',
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  videoContainer: {
    height: 215,
  },
  twitterContainer: {
    marginVertical: 10,
  },
  trBorder: {
    width: '100%',
    height: 1,
  },
});
