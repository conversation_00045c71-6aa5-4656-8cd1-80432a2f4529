import { MetaProps, Paywall, BENZINGA_LOGO_URL, formatStringToValidJsonString } from '@benzinga/seo';
import Script from 'next/script';
import { has } from 'ramda';
import { fixedEncodeURI } from '@benzinga/utils';

const logoPNG = 'https://www.benzinga.com/next-assets/images/schema-publisher-logo-benzinga.png';

const voiceSearchSchema = {
  '@context': 'https://schema.org/',
  '@type': 'WebPage',
  name: '<PERSON><PERSON>',
  speakable: {
    '@type': 'SpeakableSpecification',
    xpath: ['/html/head/title', "/html/head/meta[@name='description']/@content"],
  },
  url: 'https://www.benzinga.com/',
};

const sitelinkSchema = (paywall?: Paywall) => {
  const schema: Record<string, unknown> = {
    '@context': 'https://schema.org/',
    '@type': 'WebSite',
    name: '<PERSON><PERSON>',
    potentialAction: {
      '@type': 'SearchAction',
      'query-input': 'required name=search_term_string',
      target: 'https://www.benzinga.com/search/fast?cx={search_term_string}',
    },
    url: 'https://www.benzinga.com/',
  };

  if (paywall?.hasAccess) {
    schema.hasPart = {
      '@type': 'WebPageElement',
      cssSelector: '.paywall-content',
      isAccessibleForFree: false,
    };
    schema.isAccessibleForFree = false;
  }
  return schema;
};

interface SchemaInterface {
  data: Record<string, unknown>;
  name: string;
}
const structuredDataCompany = {
  '@context': 'http://schema.org',
  '@type': 'NewsMediaOrganization',
  address: {
    '@type': 'PostalAddress',
    addressCountry: 'US',
    addressLocality: 'Detroit',
    addressRegion: 'MI',
    postalCode: '48226',
    streetAddress: '1 Campus Martius Suite 200',
  },
  brand: [
    {
      '@type': 'Brand',
      name: 'PreMarket Playbook',
      url: 'https://www.benzinga.com/premarket',
    },
    {
      '@type': 'Brand',
      name: 'Benzinga Markets',
      url: 'https://www.benzinga.com/markets/',
    },
    {
      '@type': 'Brand',
      name: 'Benzinga Pro',
      url: 'https://pro.benzinga.com/',
    },
    {
      '@type': 'Brand',
      name: 'Benzinga Cloud: Data & APIs',
      url: 'https://www.benzinga.com/apis',
    },
    {
      '@type': 'Brand',
      name: 'Benzinga Events',
      url: 'https://www.benzinga.com/events',
    },
    {
      '@type': 'Brand',
      name: 'Benzinga Money',
      url: 'https://www.benzinga.com/money/',
    },
  ],
  email: '<EMAIL>',
  image: BENZINGA_LOGO_URL,
  legalName: 'Benzinga',
  logo: logoPNG,
  name: 'Benzinga',
  sameAs: [
    'https://www.facebook.com/pages/Benzingacom/159483118580?v=app_7146470109',
    'https://twitter.com/benzinga',
    'https://www.linkedin.com/company/benzinga',
    'https://plus.google.com/108838891574408087738/posts',
    'https://www.youtube.com/user/BenzingaTV',
  ],
  telephone: '************',
  url: 'https://www.benzinga.com/',
};

const decodeDescription = (text: string) => {
  // return text ? decode(text) : '';
  return text ?? '';
};

const organizationMetaSchema = () => {
  return {
    '@type': 'Organization',
    logo: {
      '@type': 'ImageObject',
      url: `${logoPNG}`,
    },
    name: 'Benzinga',
    url: 'https://www.benzinga.com',
  };
};
const structuredDataPage = ({
  author,
  authorURL,
  canonical,
  dateCreated,
  dateUpdated,
  description,
  image,
  structuredData,
  title,
}: MetaProps) => {
  author = author || 'Benzinga';
  dateCreated = dateCreated || '2010-01-01T00:00:00Z';
  dateUpdated = dateUpdated || '2010-01-01T00:00:00Z';
  image = image || BENZINGA_LOGO_URL;
  const pageType = (structuredData && has('pageType', structuredData) && structuredData.pageType) || 'WebPage';
  const keywords = (structuredData && has('keywords', structuredData) && structuredData.keywords) || [];
  const mentions = (structuredData && has('mentions', structuredData) && structuredData.mentions) || [];

  // ToDo: title should be truncated to 110 chars or less, truncated at word, add ellipse
  title = title?.substring(0, 105);

  const cleanQuote = (text: string | null) => {
    return text ? text.replace(/"/g, '&quot;') : '';
  };

  const convertQuoteToSingle = (text: string | null) => {
    return text ? text.replace(/"/g, `'`) : '';
  };

  // mainEntityOfPage, image - Required for AMP
  // prettier-ignore

  const articleBody = structuredData?.articleBody ? formatStringToValidJsonString(structuredData.articleBody) : '';

  return {
    '@context': 'http://schema.org',
    '@type': pageType,
    author: {
      '@type': 'Person',
      name: cleanQuote(author),
      ...(authorURL && { url: authorURL }),
    },
    creator: {
      '@type': 'Person',
      name: cleanQuote(author),
      ...(authorURL && { url: authorURL }),
    },
    dateCreated: dateCreated ?? null,
    dateModified: dateUpdated ?? null,
    datePublished: dateCreated ?? null,
    description: convertQuoteToSingle(decodeDescription(description)),
    headline: cleanQuote(title)?.replace(/(\r\n|\n|\r)/gm, ' '),
    image: fixedEncodeURI(image.toString()) ?? null,
    keywords: keywords,
    mainEntityOfPage: {
      '@id': canonical,
      '@type': 'WebPage',
    },
    mentions: mentions,
    publisher: organizationMetaSchema(),
    url: canonical,
    ...(structuredData?.articleBody && { articleBody }),
    ...(structuredData?.about && { about: structuredData.about }),
    ...(structuredData?.articleSection && { articleSection: structuredData.articleSection }),
    ...(structuredData?.speakable && { speakable: structuredData.speakable }),
  };
};

export const Schema: React.FC<SchemaInterface> = ({ data, name }) => {
  const schemaName = name + '-schema';
  return (
    <Script
      dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
      id={schemaName}
      key={schemaName}
      type="application/ld+json"
    />
  );
};

interface IRootSchema {
  metaInfo: MetaProps & { paywall?: Paywall };
}

export const RootSchema = ({ metaInfo }: IRootSchema) => {
  return (
    <>
      <Schema data={voiceSearchSchema} name="voice-search" />
      <Schema data={sitelinkSchema(metaInfo?.paywall)} name="sitelink-search" />
      <Schema data={structuredDataCompany} name="structuredDataCompany" />
      <Schema data={structuredDataPage(metaInfo)} name="structuredDataPage" />
    </>
  );
};
