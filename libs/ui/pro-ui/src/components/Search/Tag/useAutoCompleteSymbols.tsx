'use client';
import React from 'react';

import { Session, StockSymbol } from '@benzinga/session';
import { SessionContext } from '@benzinga/session-context';
import { WatchlistManager } from '@benzinga/watchlist-manager';
import { WidgetLinkingManager, isTickerSelectedEvent } from '@benzinga/widget-linking';
import { setIsEqual } from '@benzinga/utils';
import {
  SymbolSearchItem,
  isSymbolSearchItem,
  WatchlistSearchItem,
  isWatchlistSearchItem,
  ScannerSearchItem,
  isScannerSearchItem,
  LinkingSearchItem,
  isLinkingSearchItem,
  isHoldingSearchItem,
  HoldingSearchItem,
  isVerticalSearchItem,
  VerticalSearchItem,
  GptSearchItem,
  isGptSearchItem,
} from '@benzinga/search-modules';
import { ScannerFeed, ScannerManager } from '@benzinga/scanner-manager';
import { SubscribableMultiplexer } from '@benzinga/subscribable';
import { ScannerConfig, ScannerConfigManager } from '@benzinga/scanner-config-manager';
import { HoldingsManager } from '@benzinga/data-manager-holdings';
import { VerticalsManager } from '@benzinga/verticals-manager';
import { GptSearchManager } from '@benzinga/benzinga-gpt-search-manager';

export type SymbolLikeModuleItem =
  | SymbolSearchItem
  | WatchlistSearchItem
  | LinkingSearchItem
  | ScannerSearchItem
  | VerticalSearchItem
  | HoldingSearchItem
  | GptSearchItem;

const uniqueSymbols = (symbols: StockSymbol[]): Set<StockSymbol> => {
  return new Set<StockSymbol>(symbols);
};

export const autoCompleteSymbols = (items: SymbolLikeModuleItem[], session: Session): Set<StockSymbol> =>
  uniqueSymbols(
    items?.flatMap(a => {
      if (isSymbolSearchItem(a)) {
        return [a.data.symbol];
      } else if (isWatchlistSearchItem(a)) {
        return (
          session
            .getManager(WatchlistManager)
            .getStoredWatchlistById(a.data.watchlistId)
            ?.symbols.map(s => s.symbol) ?? []
        );
      } else if (isGptSearchItem(a)) {
        return session.getManager(GptSearchManager).getCachedWithPromptData(a.data.prompt)?.completions ?? [];
      } else if (isVerticalSearchItem(a)) {
        return (
          session.getManager(VerticalsManager).getCachedVerticalByVerticalName(a.data.verticalName)?.verticalSymbols ??
          []
        );
      } else if (isHoldingSearchItem(a)) {
        return session.getManager(HoldingsManager).getCachedHoldingByName(a.data.holdingName)?.holdingSymbols ?? [];
      } else if (isLinkingSearchItem(a)) {
        const sym = session
          .getManager(WidgetLinkingManager)
          .getWidgetLinkFeedByID(a.data.linkId)
          ?.getLink()
          .events.find(isTickerSelectedEvent)?.symbol;
        return sym ? [sym] : [];
      } else if (isScannerSearchItem(a)) {
        const scannerConfigManager = session.getManager(ScannerConfigManager);
        const scannerManager = session.getManager(ScannerManager);
        const config = scannerConfigManager.getConfigs().find(s => s.uuid === a.data.scannerId);
        return config
          ? scannerManager
              .getFeed(config)
              .getPrevious()
              .map(a => a.symbol ?? '') ?? []
          : [];
      }
      return [];
    }),
  );

export const autoCompleteSymbolsUpdateCallback = (
  session: Session,
  items: SymbolLikeModuleItem[],
  update: (symbols: Set<StockSymbol>) => void,
  initSymbolsParams?: Set<StockSymbol>,
): (() => void) => {
  let prevSymbols = initSymbolsParams ?? autoCompleteSymbols(items, session);

  const newSymbols = autoCompleteSymbols(items, session);
  if (newSymbols && !setIsEqual(newSymbols, prevSymbols)) {
    update(newSymbols);
  }

  const scannerItems = items?.filter(isScannerSearchItem);
  const scannerConfigManager = session.getManager(ScannerConfigManager);
  scannerConfigManager.fetchConfigs();

  const getFeedMapping = (scannerConfigs: ScannerConfig[] | undefined): [string, ScannerFeed][] =>
    scannerItems?.flatMap(scannerItem => {
      const scannerConfig = scannerConfigs?.find(s => s.uuid === scannerItem.data.scannerId);
      return scannerConfig
        ? [[scannerItem.data.scannerId, session.getManager(ScannerManager).getFeed(scannerConfig)]]
        : [];
    });

  const scannerSubscribable = new SubscribableMultiplexer(getFeedMapping(scannerConfigManager.getConfigs()));

  const subscription = scannerConfigManager.subscribe(event => {
    switch (event.type) {
      case 'scanner:updated_scanners':
        scannerSubscribable.replace(getFeedMapping(event.scanners));
        break;
    }
  });

  const scannerSubscribableSubscription = scannerSubscribable.subscribe(event => {
    switch (event.type) {
      case 'data_update': {
        if (!event.config.uuid) return;
        const newSymbols = autoCompleteSymbols(items, session);
        if (newSymbols && !setIsEqual(prevSymbols, newSymbols)) {
          prevSymbols = newSymbols;
          update(newSymbols);
        }
      }
    }
  });

  const widgetLinkingSubscription = session.getManager(WidgetLinkingManager).subscribe(event => {
    switch (event.type) {
      case 'linking:links_updated': {
        const newSymbols = autoCompleteSymbols(items, session);
        if (newSymbols && !setIsEqual(prevSymbols, newSymbols)) {
          prevSymbols = newSymbols;
          update(newSymbols);
        }
      }
    }
  });
  const watchlistSubscription = session.getManager(WatchlistManager).subscribe(event => {
    switch (event.type) {
      case 'watchlist:updated_watchlists': {
        const newSymbols = autoCompleteSymbols(items, session);
        if (newSymbols && !setIsEqual(prevSymbols, newSymbols)) {
          prevSymbols = newSymbols;
          update(newSymbols);
        }
      }
    }
  });

  const holdingsManager = session.getManager(HoldingsManager).subscribe(event => {
    switch (event.type) {
      case 'holdings:update_holdings': {
        const newSymbols = autoCompleteSymbols(items, session);
        if (newSymbols && !setIsEqual(prevSymbols, newSymbols)) {
          prevSymbols = newSymbols;
          update(newSymbols);
        }
      }
    }
  });

  const verticalsManager = session.getManager(VerticalsManager).subscribe(event => {
    switch (event.type) {
      case 'verticals:update_verticals': {
        const newSymbols = autoCompleteSymbols(items, session);
        if (newSymbols && !setIsEqual(prevSymbols, newSymbols)) {
          prevSymbols = newSymbols;
          update(newSymbols);
        }
      }
    }
  });

  const gptSearchManager = session.getManager(GptSearchManager).subscribe(event => {
    switch (event.type) {
      case 'gpt-search:update': {
        const newSymbols = autoCompleteSymbols(items, session);
        if (newSymbols && !setIsEqual(prevSymbols, newSymbols)) {
          prevSymbols = newSymbols;
          update(newSymbols);
        }
      }
    }
  });

  return () => {
    scannerSubscribableSubscription.unsubscribe();
    widgetLinkingSubscription.unsubscribe();
    holdingsManager.unsubscribe();
    watchlistSubscription.unsubscribe();
    subscription.unsubscribe();
    verticalsManager.unsubscribe();
    gptSearchManager.unsubscribe();
  };
};

export const useAutoCompleteSymbols = (items: SymbolLikeModuleItem[], callback?: (symbol: StockSymbol[]) => void) => {
  const session = React.useContext(SessionContext);
  const [symbols, setSymbols] = React.useState(() => {
    if (callback) {
      return undefined;
    }
    const newSymbols = autoCompleteSymbols(items, session.getSession());
    return newSymbols ? Array.from(newSymbols) : undefined;
  });

  const cb = callback ?? setSymbols;

  const initSymbols = React.useRef<Set<StockSymbol>>(new Set<StockSymbol>(symbols));

  React.useEffect(() => {
    const onUpdate = (symbols: Set<StockSymbol>) => {
      initSymbols.current = symbols;
      cb(Array.from(symbols));
    };

    return autoCompleteSymbolsUpdateCallback(session.getSession(), items, onUpdate, initSymbols.current);
  }, [cb, items, session]);
  return symbols;
};

export const useAutoCompleteSymbolsCallback = (
  items: SymbolLikeModuleItem[],
  callback: (symbols: Set<StockSymbol>) => void,
) => {
  const session = React.useContext(SessionContext);
  const initSymbols = React.useRef<Set<StockSymbol> | undefined>(undefined);

  if (initSymbols.current === undefined) {
    initSymbols.current = autoCompleteSymbols(items, session.getSession());
    initSymbols.current && callback(initSymbols.current);
  }

  React.useEffect(() => {
    const onUpdate = (symbols: Set<StockSymbol>) => {
      initSymbols.current = symbols;
      callback(symbols);
    };

    return autoCompleteSymbolsUpdateCallback(session.getSession(), items, onUpdate, initSymbols.current);
  }, [callback, items, session]);
};

export const useAutoCompleteSymbolsCallbackList = (
  items: Record<string | number, SymbolLikeModuleItem[]>,
  callback: (symbols: Record<string | number, Set<StockSymbol>>) => void,
) => {
  const session = React.useContext(SessionContext);
  const initSymbols = React.useRef<Record<string | number, Set<StockSymbol>> | undefined>(undefined);

  if (initSymbols.current === undefined) {
    initSymbols.current = Object.entries(items).reduce<Record<string | number, Set<StockSymbol>>>(
      (acc, [k, v]) => ({ ...acc, [k]: autoCompleteSymbols(v ?? [], session.getSession()) }),
      {},
    );
    initSymbols.current && callback(initSymbols.current);
  } else {
    if (Object.keys(initSymbols.current).length !== Object.keys(items).length) {
      initSymbols.current = Object.entries(items).reduce<Record<string | number, Set<StockSymbol>>>(
        (acc, [k, v]) => ({ ...acc, [k]: autoCompleteSymbols(v ?? [], session.getSession()) }),
        {},
      );
      initSymbols.current && callback(initSymbols.current);
    }
  }

  React.useEffect(() => {
    const cleanups = Object.entries(items).map(([k, v]) => {
      const onUpdate = (symbols: Set<StockSymbol>) => {
        if (initSymbols.current) {
          initSymbols.current[k] = symbols;
        } else {
          initSymbols.current = { [k]: symbols };
        }
        callback(initSymbols.current);
      };

      return autoCompleteSymbolsUpdateCallback(session.getSession(), v ?? [], onUpdate, initSymbols.current?.[k]);
    });
    return () => cleanups.forEach(c => c());
  }, [callback, items, session]);
};
