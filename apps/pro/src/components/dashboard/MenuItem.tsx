import React from 'react';
import styled, { css } from '@benzinga/themetron';
import { Dropdown, Menu } from 'antd';
import type { MenuProps } from 'antd';
import { connect } from 'react-redux';
import { isWindowWidthLargerThenSmall } from '../../selectors/uiSelectors';
import { RootState } from '../../redux/types';

interface ReduxState {
  isWindowWidthLargerThenSmall: boolean;
}

interface OwnProps {
  dropdownContent?: React.ReactElement;
  menuHoverable?: boolean;
  placement?: 'right' | 'left';
  onClick?: () => void;
  visible?: boolean;
  className?: string;
  menu?: MenuProps['items'];
  setIsVisible?: (visible: boolean) => void;
}

type Props = ReduxState & OwnProps;

export const MenuItemInternal: React.FC<React.PropsWithChildren<Props>> = props => {
  const menuHoverable = props.menuHoverable ?? true;
  const [visible, setIntVisible] = React.useState(false);

  const actuallyVisible = props.visible ?? visible;
  const { setIsVisible } = props;

  const setVisible = React.useCallback((visible: boolean | ((old: boolean) => boolean)) => {
    setIntVisible(visible);
  }, []);

  const onVisibleChange = React.useCallback(
    (visible: boolean) => {
      setVisible(visible);
      setIsVisible?.(visible);
    },
    [setIsVisible, setVisible],
  );

  const onClick = React.useCallback(() => {
    setVisible(old => !old);
    const onClick = props.onClick;
    onClick?.();
  }, [props.onClick, setVisible]);

  const onHover = React.useCallback(() => {
    setVisible(false);
  }, [setVisible]);

  // const menuRef = React.useRef(null);

  // Hooks.useClickOutside(menuRef, () => {
  //   setVisible(false);
  // });

  const menu = (
    <Menu
      items={props.menu}
      key="mainMenu"
      mode={props.isWindowWidthLargerThenSmall ? 'vertical' : 'inline'}
      onClick={onHover}
      onPointerLeave={() => menuHoverable && setVisible(false)}
      style={{ borderRadius: 0, minWidth: '14rem', width: 'fit-content' }}
    >
      {props?.dropdownContent && props?.dropdownContent}
    </Menu>
  );
  return (
    <MenuItemDiv
      className={props.className}
      isHovered={visible && props.dropdownContent !== undefined}
      onClick={onClick}
      onPointerEnter={() => menuHoverable && setVisible(true)}
      onPointerLeave={() => menuHoverable && setVisible(false)}
    >
      {props.dropdownContent || props.menu ? (
        <Dropdown
          destroyOnHidden
          menu={{ items: props.menu }}
          onOpenChange={onVisibleChange}
          open={actuallyVisible}
          overlayClassName={
            props.placement === 'left'
              ? 'menubar-dropdown-left'
              : props.placement === 'right'
                ? 'menubar-dropdown-right'
                : 'menubar-dropdown-center'
          }
          popupRender={
            props?.dropdownContent && (() => <div style={{ width: 'fit-content' }}>{props?.dropdownContent}</div>)
          }
          trigger={menuHoverable ? undefined : ['click']}
        >
          {props.children && (
            <div
              onClick={event => {
                event.stopPropagation();
              }}
            >
              {props.children}
            </div>
          )}
        </Dropdown>
      ) : (
        props.children
      )}
    </MenuItemDiv>
  );
};

const mapStateToProps = (state: RootState): ReduxState => ({
  isWindowWidthLargerThenSmall: isWindowWidthLargerThenSmall(state),
});

export const MenuItem = connect<ReduxState, Record<string, never>, OwnProps, RootState>(mapStateToProps)(
  MenuItemInternal as any,
) as React.FC<React.PropsWithChildren<OwnProps>>;

const MenuItemDiv = styled.div<{ isHovered?: boolean }>`
  display: flex;
  flex-grow: 0;
  font-size: 1rem;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  width: 2rem;

  .relative {
    height: 16px;
  }

  .ant-dropdown-trigger {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ant-dropdown-open {
    background: none;
  }

  &:hover {
    background-color: ${props => props.theme.colors.backgroundActive};
    svg {
      color: ${props => props.theme.colors.foregroundActive};
    }
  }

  ${props =>
    props.isHovered &&
    css`
      background-color: ${props.theme.colors.backgroundActive};
      svg {
        color: ${props.theme.colors.foregroundActive};
        height: 100%;
        width: 100%;
      }
    `}
`;
