import React, { memo } from 'react';
import styled from '@benzinga/themetron';
import { Impression } from '@benzinga/analytics';

export interface KeyPointsProps {
  className?: string;
  data: string[];
  isPaywalled?: boolean;
  dynamicKeyPoint?: string;
  article?: {
    url: string | undefined;
    nodeId: number | undefined;
  };
  trackImpression?: boolean;
}

export const KeyPoints: React.FC<KeyPointsProps> = memo(
  ({ article, className = '', data, dynamicKeyPoint, isPaywalled }) => {
    return (
      <Impression
        campaign_id={`zinger-key-points-${article?.nodeId}`}
        tag={`zinger-key-points-${article?.nodeId}`}
        unit_type={`zinger-key-points-${article?.nodeId}`}
      >
        <KeyPointsContainer className={`key-points ${className}`}>
          <div className="key-points-header">Zinger Key Points</div>
          <ul>
            {Array.isArray(data) &&
              data.map((item, index) => (
                <li className={`bullet-point ${isPaywalled && index > 0 ? 'blur-sm' : ''}`} key={index}>
                  {item}
                </li>
              ))}
            {dynamicKeyPoint && (
              <li className="bullet-point bullhorn">
                <span className="bell-icon text-xs">
                  <BullhornIcon />
                </span>
                <span className="bullhorn-text" dangerouslySetInnerHTML={{ __html: dynamicKeyPoint }} />
              </li>
            )}
          </ul>
        </KeyPointsContainer>
      </Impression>
    );
  },
);

const KeyPointsContainer = styled.div`
  &.key-points {
    display: flex;
    flex-direction: column;
    border-top: 2px solid ${({ theme }) => theme.colorPalette.blue800};
    border-bottom: 2px solid ${({ theme }) => theme.colorPalette.blue800};
    padding: 8px 0;
    min-height: 100px;

    ul {
      list-style: disc;
      margin-left: 30px;

      li {
        font-size: ${({ theme }) => theme.fontSize.lg};
        font-weight: ${({ theme }) => theme.fontWeight.bold};
        margin-bottom: 6px;
        &:last-of-type {
          margin-bottom: 0;
        }
      }
    }

    .key-points-header {
      margin-right: 15px;
      color: ${({ theme }) => theme.colorPalette.blue800};
      font-size: ${({ theme }) => theme.fontSize.lg};
      font-weight: ${({ theme }) => theme.fontWeight.bold};
      margin-bottom: 5px;
      text-transform: uppercase;
    }
  }
`;

export const LightBlueKeyPoints = styled.div`
  &.key-points-wrapper {
    .key-points {
      background-color: ${({ theme }) => theme.colorPalette.blue50};
      border: unset;
      border-top: 2px solid ${({ theme }) => theme.colorPalette.blue500};
      border-bottom: 2px solid ${({ theme }) => theme.colorPalette.blue500};
      padding: 1rem;

      .key-points-header {
        font-size: 16px;
        color: ${({ theme }) => theme.colorPalette.blue500};
      }

      ul {
        list-style: none;
        margin-left: 10px;

        li.bullet-point {
          color: ${({ theme }) => theme.colorPalette.gray700};
          margin-bottom: 24px;
          position: relative;
          padding-left: 20px;
          font-weight: normal;

          a {
            color: ${({ theme }) => theme.colorPalette.blue500};
          }

          &:last-of-type {
            margin-bottom: 0;
          }

          &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 10px;
            width: 8px;
            height: 8px;
            background-color: ${({ theme }) => theme.colorPalette.blue500};
            border-radius: 50%;
            box-shadow:
              0 0 0px #3f83f81a,
              0 0 1px 5px #3f83f81a;
            transform: unset;
          }

          &:not(:last-of-type):after {
            content: '';
            position: absolute;
            left: 3.5px;
            top: 20px;
            height: calc(100% + 12px);
            width: 1px;
            background-color: ${({ theme }) => theme.colorPalette.blue500};
            opacity: 0.3;
          }

          &.bullhorn {
            color: ${({ theme }) => theme.colorPalette.blue500};
            font-weight: ${({ theme }) => theme.fontWeight.semibold};
            display: flex;
            padding-left: 0;
            margin-left: -5px;
            margin-top: 8px;

            &:before,
            &:after {
              content: none;
            }

            .bell-icon {
              background-color: #daeaff;
              padding: 4px;
              border-radius: 50%;
              margin-right: 0.5rem;
              height: 20px;
              width: 20px;
              display: flex;
              align-items: center;
              justify-content: center;

              svg {
                color: ${({ theme }) => theme.colorPalette.blue500};
              }
            }

            .bullhorn-text {
              margin-top: -5px;
            }
          }
        }
      }
    }
  }
`;

export const DarkKeyPoints = styled.div`
  &.key-points-wrapper {
    .key-points {
      background-color: #0e1d32;
      border: 1px solid #2c3d55;
      padding: 1rem;

      .key-points-header {
        margin-bottom: 1rem;
        color: ${({ theme }) => theme.colorPalette.gray500};
        font-size: ${({ theme }) => theme.fontSize.base};
      }

      ul {
        list-style: none;
        margin-left: 10px;

        li.bullet-point {
          color: ${({ theme }) => theme.colorPalette.gray300};
          position: relative;
          padding-left: 20px;
          font-weight: normal;

          &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background-color: ${({ theme }) => theme.colorPalette.blue500};
            border-radius: 50%;
            box-shadow:
              0 0 0px #3f83f81a,
              0 0 1px 5px #3f83f81a;
          }

          &.bullhorn {
            color: ${({ theme }) => theme.colorPalette.blue500};
            font-weight: ${({ theme }) => theme.fontWeight.semibold};
            display: flex;
            align-items: center;
            padding-left: 0;
            margin-left: -5px;

            a {
              color: ${({ theme }) => theme.colorPalette.blue500};
            }

            &:before {
              content: none;
            }

            .bell-icon {
              background-color: #3f83f81a;
              padding: 4px;
              border-radius: 50%;
              margin-right: 0.5rem;
              height: 20px;
              width: 20px;
              display: flex;
              align-items: center;
              justify-content: center;

              svg {
                color: ${({ theme }) => theme.colorPalette.blue500};
              }
            }
          }
        }
      }
    }
  }
`;

function BullhornIcon() {
  return (
    <svg fill="none" height="12" viewBox="0 0 16 14" width="14" xmlns="http://www.w3.org/2000/svg">
      <path
        clipRule="evenodd"
        d="M11.834 1.533a.71.71 0 00-1.23-.486 6.707 6.707 0 01-4.892 2.12H3.334a2.501 2.501 0 00-2.5 2.5v.666a2.501 2.501 0 002.5 2.5h2.378c1.853 0 3.625.768 4.893 2.12a.71.71 0 001.229-.486V1.534zM14.668 5.5h-1a.5.5 0 000 1h1a.5.5 0 000-1zM12.793 8.667l.707.707a.501.501 0 00.707-.707l-.707-.708a.501.501 0 00-.707.708zM13.5 4.041l.707-.707a.501.501 0 00-.707-.707l-.707.707a.501.501 0 00.707.707z"
        fill="#3F83F8"
        fillRule="evenodd"
      ></path>
      <path
        clipRule="evenodd"
        d="M2.965 9.479l.45 2.722c.093.563.58.977 1.151.977h1.562a.832.832 0 00.746-1.206l-.392-.785a.17.17 0 01-.014-.034L6.063 9.51c-.117-.007-2.976-.017-3.098-.031z"
        fill="#3F83F8"
        fillRule="evenodd"
      ></path>
    </svg>
  );
}

export default KeyPoints;
