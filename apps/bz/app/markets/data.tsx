import { getGlobalSession } from '../../pages/api/session';
import { MarketPageProps } from '../../pages/api/market';
import { BASE_URL } from '../../src/env';
import { BasicNewsManager } from '@benzinga/basic-news-manager';
import { ContentManager } from '@benzinga/content-manager';
import { getRequestInfo } from '../_utils/serverUtils';
import { loadServerSideBlockData } from '@benzinga/blocks-utils';

export const getMarketProps = async () => {
  try {
    const response = await fetch(`${BASE_URL}/api/market`, {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      method: 'GET',
    });

    const data = await response.json();

    const session = getGlobalSession();
    const basicNewsManager = session.getManager(BasicNewsManager);
    const latestNews = await basicNewsManager.simplyQueryNews(
      {},
      {
        excludeAutomated: true,
        excludeSponsored: true,
        limit: 4,
      },
    );

    if (latestNews.ok) {
      data.latestNews = latestNews.ok;
    }

    const contentManager = session.getManager(ContentManager);
    const marketPageProps = await contentManager.getPageWithPath('markets');
    if (marketPageProps.ok) {
      const { cookies, headers } = await getRequestInfo();
      const inContent = marketPageProps.ok?.in_content;
      const inContentBlock = inContent
        ? await loadServerSideBlockData(session, inContent?.blocks ?? [], headers, cookies)
        : [];
      data.inContentBlock = inContentBlock;
    }

    data.metaProps = {
      structuredData: {
        keywords: [`"category: Markets"`],
      },
    };

    return data as MarketPageProps;
  } catch (error) {
    console.error('Error fetching market props:', error);
    return {
      latestNews: [],
      logos: [],
      marketPageTickers: {},
      movers: [],
      rankings: {},
      wiims: [],
    } as unknown as MarketPageProps;
  }
};
