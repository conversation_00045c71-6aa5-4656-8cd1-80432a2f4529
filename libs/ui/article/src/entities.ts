import { NodeLayout } from '@benzinga/content-manager';
import { MetaProps } from '@benzinga/seo';
import { ArticleDFPTags, ArticleData, CampaignStrategy, Campaigns } from '@benzinga/article-manager';
import { NavigationHeaderProps } from '@benzinga/navigation-ui';
import { ButtonVariant } from '@benzinga/core-ui';
import { StoryObject } from '@benzinga/advanced-news-manager';
import { CampaignProps } from './components/Campaign';
import { DeviceType } from '@benzinga/device-utils';
import { TaboolaSettings } from './components/GetBelowArticlePartnerAdBlock';
import { Candle } from '@benzinga/chart-manager';
import { DetailsQuoteType, QuoteAnalysisResponse, RankingDetail } from '@benzinga/quotes-manager';

export interface ArticlePageProps {
  article?: ArticleData | null;
  articleIndex?: number;
  articleScrollViewMoreLink?: string;
  baseUrl?: string;
  campaignStrategy?: CampaignStrategy;
  campaigns?: Campaigns | null;
  deviceType: DeviceType | null;
  disableCampaignifyUnit?: boolean;
  followUpQuestions?: string[] | null;
  disablePartnerAdOnScroll?: boolean;
  disablePaywall?: boolean;
  disableWNSTNWidget?: boolean;
  disableRaptiveReadyOnPageLoad?: boolean;
  disableTopPanel?: boolean;
  errorCode?: number;
  enableConnatixScript?: boolean;
  enablePlaystreamIndiaScript?: boolean;
  headerProps?: NavigationHeaderProps;
  hideSidebar?: boolean;
  hideTaboola?: boolean;
  hideTopPanel?: boolean;
  initialArticleBelowArticle?: ArticleData;
  googleNewsUrlKey?: 'benzinga' | 'benzingaIndia';
  isTaggedPressRelease?: boolean;
  layout: NodeLayout | null;
  loadInfiniteArticles?: boolean;
  loadMoreButtonVariant?: ButtonVariant;
  metaProps: MetaProps | null;
  nid: string;
  pressReleasesByAuthor?: StoryObject[];
  pageTargeting?: ArticleDFPTags | null;
  partialView?: boolean;
  postedInVariant?: 'default' | 'secondary';
  primaryImage?: null | {
    alt: string;
    height: number;
    url: string;
    width: number;
  };
  relatedArticles?: ArticleData[];
  showApiText?: boolean;
  showCommentButton?: boolean;
  showFontAwesomeIcons?: boolean;
  showWhatsAppIcon?: boolean;
  translationMeta?: null | {
    [key: string]: {
      url?: string;
      post_id?: number;
    };
  };
  taboolaSettings?: TaboolaSettings;
  wordCount?: number | null;
  campaignSettings?: Partial<CampaignProps>;
  raptiveEnabled?: boolean;
  chartInfo?: ArticleChartInfo;
  featuredTickers?: ArticleFeaturedTicker[];
  useNewTemplate?: boolean;
  shouldRenderBottomTaboola?: boolean;
  isTemplate?: boolean;
  rankingData?: RankingDetail | null;
}

export interface ArticleChartInfo extends ArticleFeaturedTicker {
  candles: Candle[];
}

export interface ArticleFeaturedTicker {
  candles?: Candle[];
  logoUrl: string | null;
  symbol: string;
  price: number | null;
  changePercent: number | null;
  name: string | null;
  type: DetailsQuoteType;
  analysis?: QuoteAnalysisResponse;
}
