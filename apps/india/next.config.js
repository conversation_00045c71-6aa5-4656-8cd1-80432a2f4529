const { composePlugins, withNx } = require('@nx/next');
const envData = require('./public/env/index.json');
const RELEASE_VERSION = require('./package.json')?.version;
const { GitRevisionPlugin } = require('git-revision-webpack-plugin');
const gitRevisionPlugin = new GitRevisionPlugin();

const env = {
  ...(envData ?? {}),
  GIT_COMMITHASH: process.env.CI_COMMIT_SHA ?? gitRevisionPlugin.commithash() ?? '',
  RELEASE_VERSION,
  RUNTIME_ENV: process.env.RUNTIME_ENV || 'development',
};

/**
 * @type {import('@nx/next/plugins/with-nx').WithNxOptions}
 **/
const nextConfig = {
  assetPrefix: '/india-build',
  //distDir: '../../dist/apps/india/.next',
  env,
  // RULE: Last match WINS
  async headers() {
    // Benzinga-Control - Same as Benzinga TTL for legacy reasons, but we can probably remove
    // Benzinga-TTL - Sets the cache TTL for the returned object
    // Benzinga-stale_while_revalidate - Sets max_stale_while_revalidate, or how long the object should be served while stale
    // Benzinga-stale_if_error - If there are errors, how long can we serve the stale object

    return [
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=300',
          },
          {
            key: 'Benzinga-TTL',
            value: '600s',
          },
        ],
        // 10m Default
        source: '/:path*',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=60',
          },
          {
            key: 'Benzinga-TTL',
            value: '60s',
          },
        ],
        // Homepage, 1m
        source: '/',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=60',
          },
          {
            key: 'Benzinga-TTL',
            value: '60s',
          },
          {
            key: 'Benzinga-stale_while_revalidate',
            value: '60s',
          },
        ],
        // API Cache Defaults
        source: '/api/cache/:path*',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=60',
          },
          {
            key: 'Benzinga-TTL',
            value: '60s',
          },
        ],
        // News API, 60s
        source: '/api/news',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=86400',
          },
          {
            key: 'Benzinga-TTL',
            value: '86400s',
          },
        ],
        // Article Cache, 1d
        // source: '^/:year([0-2][0-9])/:month([0-1][0-9])/:id([0-9]+)/*',
        source: '/:year([0-2][0-9])/:month([0-1][0-9])/:id([0-9]+)/:lastpath*',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=86400',
          },
          {
            key: 'Benzinga-TTL',
            value: '86400s',
          },
        ],
        // Article Cache, 1d
        // source: '^/:cat1([0-9a-zA-Z-_]+)/:year([0-2][0-9])/:month([0-1][0-9])/:id([0-9]+)/*',
        source: '/:cat1([0-9a-zA-Z-_]+)/:year([0-2][0-9])/:month([0-1][0-9])/:id([0-9]+)/:lastpath*',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=86400',
          },
          {
            key: 'Benzinga-TTL',
            value: '86400s',
          },
        ],
        // Article Cache, 1d
        source:
          '/:cat1([0-9a-zA-Z-_]+)/:cat2([0-9a-zA-Z-_]+)/:year([0-2][0-9])/:month([0-1][0-9])/:id([0-9]+)/:lastpath*',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=86400',
          },
          {
            key: 'Benzinga-TTL',
            value: '86400s',
          },
        ],
        // Article Cache, 1d
        source: '/article/:path*{/}?',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=86400',
          },
          {
            key: 'Benzinga-TTL',
            value: '86400s',
          },
        ],
        // Amp Cache, 1d
        source: '/amp/content/:path*{/}?',
      },
    ];
  },

  images: {
    dangerouslyAllowSVG: true,
    domains: ['image-util.benzinga.com', 'assets.coingecko.com', 'cdn.benzinga.com'],
  },

  nx: {
    // Set this to true if you would like to use SVGR
    // See: https://github.com/gregberge/svgr
    svgr: false,
  },

  // RULE: First match wins. 🤷
  async rewrites() {
    return [
      { destination: '/_next/:path*', source: '/india-build/_next/:path*' },
      {
        destination: '/article/:id',
        source: '/pressreleases/:year([0-2][0-9])/:month([0-1][0-9])/([a-z]{0,2})?:id([0-9]+)/:lastpath*',
      },
      {
        destination: '/article/secfilings/:id',
        source: '/secfilings/:year([0-2][0-9])/:month([0-1][0-9])/:id([0-9]+)/:lastpath*',
      },
      {
        destination: '/article/:id',
        source: '/:year([0-2][0-9])/:month([0-1][0-9])/:id([0-9]+)/:lastpath*',
      },
      {
        destination: '/article/:id',
        source: '/content/:id([0-9]+)/:lastpath*',
      },
      {
        destination: '/article/:id',
        source: '/:cat1([0-9a-zA-Z-_]+)/:year([0-2][0-9])/:month([0-1][0-9])/:id([0-9]+)/:lastpath*',
      },
      {
        destination: '/article/:id',
        source:
          '/:cat1([0-9a-zA-Z-_]+)/:cat2([0-9a-zA-Z-_]+)/:year([0-2][0-9])/:month([0-1][0-9])/:id([0-9]+)/:lastpath*',
      },
      {
        destination: '/article/:id',
        source:
          '/:cat1([0-9a-zA-Z-_]+)/:cat2([0-9a-zA-Z-_]+)/:cat3([0-9a-zA-Z-_]+)/:year([0-2][0-9])/:month([0-1][0-9])/:id([0-9]+)/:lastpath*',
      },
      {
        destination: '/recent',
        source: '/latest',
      },
    ];
  },
   transpilePackages: [
    '@ant-design',
    'antd',
    '@coralogix/browser',
    '@ant-design/icons',
    '@ant-design/icons-svg',
    '@ant-design/v5-patch-for-react-19',
    'rc-util',
    'rc-pagination',
    'rc-picker',
    'rc-picker/lib/generate/dayjs',
    'rc-picker/lib/generate',
    'rc-picker/lib',
    'rc-table',
    'rc-tree',
    'react-tweet',
    "rc-cascader",
    "rc-checkbox",
    "rc-collapse",
    "rc-component",
    "@rc-component",
    "@rc-component/util",
    "rc-component/util",
    "rc-dialog",
    "rc-drawer",
    "rc-dropdown",
    "rc-field-form",
    "rc-image",
    "rc-input",
    "rc-input-number",
    "rc-mentions",
    "rc-menu",
    "rc-motion",
    "rc-notification",
    "rc-pagination",
    "rc-picker",
    "rc-progress",
    "rc-rate",
    "rc-resize-observer",
    "rc-segmented",
    "rc-select",
    "rc-slider",
    "rc-steps",
    "rc-switch",
    "rc-table",
    "rc-tabs",
    "rc-textarea",
    "rc-tooltip",
    "rc-tree",
    "rc-tree-select",
    "rc-upload",
    "rc-util",
  ],
  webpack: config => {
    // Grab the existing rule that handles SVG imports
    const fileLoaderRule = config.module.rules.find(rule => rule.test?.test?.('.svg'));

    config.module.rules.push(
      // Reapply the existing rule, but only for svg imports ending in ?url
      {
        ...fileLoaderRule,
        resourceQuery: /url/,
        test: /\.svg$/i, // *.svg?url
      },
      // Convert all other *.svg imports to React components
      {
        issuer: fileLoaderRule.issuer,
        resourceQuery: { not: [...fileLoaderRule.resourceQuery.not, /url/] },
        test: /\.svg$/i, // exclude if *.svg?url
        use: ['@svgr/webpack', 'file-loader'],
      },
    );

    // Modify the file loader rule to ignore *.svg, since we have it handled now.
    fileLoaderRule.exclude = /\.svg$/i;

    return config;
  },
};

const plugins = [
  // Add more Next.js plugins to this list if needed.
  withNx,
];

module.exports = composePlugins(...plugins)(nextConfig);
