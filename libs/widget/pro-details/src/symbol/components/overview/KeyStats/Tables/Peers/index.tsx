import React, { useMemo } from 'react';
import { NO_VALUE, numberShorthand } from '@benzinga/fission';
import { isEmpty, isNil } from '@benzinga/utils';
import { KeyStatRow, Title } from '../shared';
import { StockSymbol } from '@benzinga/session';
import { usePeers } from '@benzinga/securities-manager-hooks';
import { Peer } from '@benzinga/securities-manager';

interface Props {
  clickByTitle?: () => void;
  symbol: StockSymbol;
  peersToPreview: number;
}

const PeersTable: React.FC<Props> = ({ clickByTitle, peersToPreview, symbol }) => {
  const fields = useMemo(
    () => [
      'symbol',
      'name',
      'pe',
      'change',
      'forwardPERatio',
      'marketCap',
      'shareFloat',
      'sharesOutstanding',
      'dividend',
      'dividendYield',
      'exchange',
      'close',
      'changePercent',
    ],
    [],
  );
  const peers = usePeers(symbol, fields) ?? [];
  const hasMoreLink = peers.length >= peersToPreview;
  const renderPeers = (peer: Peer) => (
    <KeyStatRow
      key={peer.symbol}
      values={[peer.symbol, peer.name, numberShorthand(peer.marketCap), numberShorthand(peer.forwardPERatio)]}
    />
  );

  return (
    <div className="KeyStat KeyStat--comparison">
      <Title clickByTitle={clickByTitle} hasMoreLink={hasMoreLink} name="Peers" />

      <div className="KeyStat-table">
        <div className="KeyStat-row">
          <div className="KeyStat-stat">Symbol</div>
          <div className="KeyStat-stat">Name</div>
          <div className="KeyStat-stat">Market Cap</div>
          <div className="KeyStat-stat">P/E</div>
        </div>

        {peers.slice(0, peersToPreview).map(renderPeers)}

        {(isNil(peers) || isEmpty(peers)) && (
          <KeyStatRow key={NO_VALUE} values={[NO_VALUE, NO_VALUE, NO_VALUE, NO_VALUE]} />
        )}
      </div>
    </div>
  );
};

export default PeersTable;
