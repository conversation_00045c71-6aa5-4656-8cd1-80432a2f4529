'use client';
import { noop } from '@benzinga/utils';
import { MenuNodeDef } from '@benzinga/widget-tools';
import { Dropdown } from 'antd';
import React from 'react';
import { Menu } from './ContextMenuElements';

interface Props {
  menu: MenuNodeDef[];
  trigger?: ('click' | 'hover' | 'contextMenu')[];
}

type OnSelectType = React.ComponentProps<typeof Menu>['onClick'];
type ActionMapElement = (() => void) | ActionMap;
type ActionMap = Map<string, ActionMapElement>;

const buildActionMap = (menu: MenuNodeDef[]) => {
  const map: ActionMap = new Map();
  menu.forEach(menuItem => {
    switch (menuItem.type) {
      case 'Item':
        map[menuItem.key] = menuItem.action ?? noop;
        break;
      case 'SubMenu':
        map[menuItem.key] = buildActionMap(menuItem.nodes ?? []);
        break;
      default:
        break;
    }
  });
  return map;
};
const traverseActionMap = (mapNode: ActionMapElement, keyPath: string[]): (() => void) | undefined => {
  if (typeof mapNode === 'function' && keyPath.length === 0) {
    return mapNode;
  } else if (
    (typeof mapNode === 'function' && keyPath.length !== 0) ||
    (typeof mapNode !== 'function' && keyPath.length === 0)
  ) {
    return undefined;
  } else {
    const curr = keyPath.pop();
    return traverseActionMap(mapNode[curr ?? ''], keyPath);
  }
};

export const ContextMenu: React.FC<React.PropsWithChildren<Props>> = props => {
  const actionMap = React.useRef<ActionMap>(new Map());
  actionMap.current = buildActionMap(props.menu);

  const onClickHandler = React.useCallback<NonNullable<OnSelectType>>(info => {
    traverseActionMap(actionMap.current, info.keyPath)?.();
  }, []);

  return (
    <Dropdown
      key={`context`}
      overlayStyle={{
        zIndex: 4500,
      }}
      placement="bottomRight"
      popupRender={() => <Menu nodes={props.menu} onClick={onClickHandler}></Menu>}
      trigger={props.trigger ?? ['contextMenu']}
    >
      {props.children}
    </Dropdown>
  );
};
