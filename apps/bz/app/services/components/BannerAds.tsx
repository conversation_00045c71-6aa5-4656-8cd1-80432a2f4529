'use client';

import React, { lazy, Suspense } from 'react';
import { MoneyBlockProps } from '@benzinga/money';

const WebinarBannerAd = lazy(() => import('@benzinga/ads').then(mod => ({ default: mod.WebinarBannerAd })));
const ProTrialBannerAd = lazy(() => import('@benzinga/ads').then(mod => ({ default: mod.ProTrialBannerAd })));
const ErxGapsBannerAd = lazy(() => import('@benzinga/ads').then(mod => ({ default: mod.ErxGapsBannerAd })));

export const BannerAds = ({ webinarBlock }: { webinarBlock: MoneyBlockProps | null }) => {
  return (
    <Suspense fallback={<div />}>
      <WebinarBannerAd {...webinarBlock} />
      <ProTrialBannerAd />
      <ErxGapsBannerAd />
    </Suspense>
  );
};
