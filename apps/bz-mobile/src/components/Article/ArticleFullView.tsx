import React, { useEffect, useState } from 'react';
import { StyleSheet, Text, Image, View, StyleProp, TextStyle } from 'react-native';
import moment from 'moment';
import BenzingaHTML from '../../components/BenzingaHTML';
import { headingsStyle } from '../../constants/Styles';
import { useTheme } from '../../theme/themecontext';
import { ArticleApiClient } from '../../data-mobile/article/client';
import QuotesChipView from '../../components/QuotesChipView';
import { decode } from 'html-entities';
import { ArticleItem } from '../../screens/Home/HomeScreenTypes';
import { News, NewsImage } from '@benzinga/advanced-news-manager';
import Data from '../../data-mobile/data';
import CustomPressable from '../CustomPressable';
import Icon, { IconTypes } from '../Icon/Icon';
import { useIsLoggedIn } from '../../hooks/useIsLoggedIn';

interface ArticleItemCampaign extends ArticleItem {
  campaign: {
    top: string;
    bottom: string;
    middle: string;
  };
}

interface GlobalSettingsProps {
  ok: {
    starred_articles: number[];
  };
}

interface ArticleFullViewProps {
  article: News | ArticleItemCampaign;
  onPressTicker: (ticker: string) => void;
  images: NewsImage[];
  isFromStarredArticles?: boolean;
  showArticle: (article: News) => void;
}

const ArticleFullView = ({
  article,
  images,
  isFromStarredArticles = false,
  onPressTicker,
  showArticle,
}: ArticleFullViewProps) => {
  const { colors, isDark } = useTheme();
  const [authorByLine, setAuthorByLine] = useState<string | undefined>();
  const [isStarred, setIsStarred] = useState(false);
  const isLoggedIn = useIsLoggedIn();

  useEffect(() => {
    Data.article()
      .getArticle(article.id)
      .then(res => {
        setAuthorByLine(res.ok?.author?.byLine);
      })
      .catch(err => console.log('error draft article', err));
    isFromStarredArticles ? setIsStarred(true) : setStarredArticle();
  }, []);

  const setStarredArticle = async () => {
    try {
      const getGlobalSettings: GlobalSettingsProps | unknown = await Data.user().getGlobalSettings();
      const articleIds = (getGlobalSettings as GlobalSettingsProps).ok?.starred_articles;
      if (articleIds.length) {
        const isArticleStarred = articleIds.filter((item: number) => item === article.id);
        setIsStarred(isArticleStarred.length ? true : false);
      }
    } catch (err) {
      console.log('Error: ', err);
      setIsStarred(false);
    }
  };

  const addArticleToStarred = async (isArticleStarred: boolean) => {
    setIsStarred(isArticleStarred);
    try {
      const getGlobalSettings: GlobalSettingsProps | unknown = await Data.user().getGlobalSettings();
      const articleIds = (getGlobalSettings as GlobalSettingsProps).ok?.starred_articles;

      if (articleIds) {
        if (isArticleStarred) {
          await Data.user().setGlobalSetting('starred_articles', [article.id, ...articleIds]);
        } else {
          const articlesArray = articleIds.filter((item: number) => item !== article.id);
          await Data.user().setGlobalSetting('starred_articles', articlesArray);
        }
      } else {
        if (isArticleStarred) {
          await Data.user().setGlobalSetting('starred_articles', [article.id]);
        }
      }
      if (article.id) {
        Data.tracking().trackSettingsEvent('change', {
          setting_id: 'favouriteArticles',
          setting_type: 'boolean',
          setting_value: article.id.toString(),
        });
      }
    } catch (err) {
      console.log('Error: ', err);
    }
  };

  return (
    <View>
      <Text
        style={[
          headingsStyle.title as StyleProp<TextStyle>,
          { paddingHorizontal: 0, color: isDark ? '#F2F8FF' : '#192940' },
        ]}
      >
        {decode(article.title)}
      </Text>
      <View style={{ flexDirection: 'row' }}>
        {article.author && (
          <Text style={[styles.author, { color: isDark ? '#99AECC' : '#5B7292' }]}>by {article.author}</Text>
        )}
        {authorByLine && (
          <Text style={[styles.author, { color: isDark ? '#99AECC' : '#5B7292' }]}>, {authorByLine}</Text>
        )}
      </View>
      <View style={styles.authorStarContainer}>
        {article.created && (
          <Text style={[styles.author, { color: isDark ? '#99AECC' : '#5B7292' }]}>
            {moment(article.created).fromNow()}
          </Text>
        )}
        {isLoggedIn ? (
          <CustomPressable
            onPress={() => {
              addArticleToStarred(!isStarred);
            }}
          >
            {isStarred ? (
              <Icon type={IconTypes.AntDesign} name="star" size={25} color={colors.borderBlue} />
            ) : (
              <Icon type={IconTypes.AntDesign} name="staro" size={25} color={colors.borderBlue} />
            )}
          </CustomPressable>
        ) : null}
      </View>
      {(article.thumbnail || (images?.length > 2 && images[2]?.url)) && (
        <View style={styles.imgContainer}>
          <Image
            source={{ uri: article.thumbnail ? article.thumbnail : images[2].url }}
            // resizeMode={'contain'}
            style={styles.imageStyle}
          />
        </View>
      )}
      <QuotesChipView quotes={article?.quotes} pressedTicker={onPressTicker} />
      {article && article?.blocks?.length ? (
        <BenzingaHTML
          html={ArticleApiClient.htmlFromBlock(article as ArticleItemCampaign)}
          showArticle={showArticle}
          showQuote={onPressTicker}
          style={styles.body}
        />
      ) : (
        <View />
      )}
    </View>
  );
};

export default ArticleFullView;

const styles = StyleSheet.create({
  author: {
    color: '#555',
    fontSize: 14,
  },
  body: {
    color: '#395173',
    fontSize: 18,
  },
  imgContainer: {
    width: '100%',
    height: 200,
    marginBottom: 16,
    overflow: 'hidden',
    borderRadius: 4,
  },
  imageStyle: {
    width: '100%',
    height: '100%',
    borderRadius: 4,
    marginVertical: 16,
  },
  authorStarContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});
