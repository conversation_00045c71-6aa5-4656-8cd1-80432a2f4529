import { Layout } from '@benzinga/core-ui';
import React from 'react';
//import { RegisterDFPManager } from './ToolsPageMain';
import PageLayout from '../_components/PageLayout';

function ToolsLayout({ children }: { children: React.ReactElement }) {
  return (
    <PageLayout>
      <div className="tool-page-container tools-page">
        {/* <RegisterDFPManager /> */}
        <Layout layoutMain={children} title="Financial Market Tools" />
      </div>
    </PageLayout>
  );
}

export default ToolsLayout;
