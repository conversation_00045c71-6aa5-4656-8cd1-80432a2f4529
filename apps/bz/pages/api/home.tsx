import { DefaultQuotesList } from '@benzinga/navigation-ui';
import { shuffleArray } from '@benzinga/utils';
import { safeTimeout } from '@benzinga/safe-await';
import { HomeProps } from '../../src/components/Home/interface';
import { getGlobalSession } from './session';
import { QuotesManager } from '@benzinga/quotes-manager';
import { ContentManager, WordpressPost, WordpressSidebar } from '@benzinga/content-manager';
import { BasicNewsManager, News, StoryObject } from '@benzinga/basic-news-manager';
import dayjs from 'dayjs';
import { formatImageUrl } from '@benzinga/utils';

const sortTopStoriesFeed = (feeds: News[][]) => {
  const initial: News[] = [];
  const rest: News[] = [];
  feeds.forEach(feed => {
    feed.forEach((node, index) => {
      if (index === 0) {
        initial.push(node);
      } else {
        rest.push(node);
      }
    });
  });
  rest.sort((a, b) => {
    const date1 = dayjs(a.created);
    const date2 = dayjs(b.created);
    return date2.diff(date1) < 0 ? -1 : 1;
  });
  return [...initial, ...rest];
};

interface Feed {
  posts: News[];
  queryKey: string;
  queryValues: number[];
  title: string;
}

const sortPartnerContentForDispersion = (partnerContent: News[]) => {
  const feeds: Feed[] = [
    {
      posts: [],
      queryKey: 'tags',
      queryValues: [142057, 142059, 16786],
      title: 'Alternative Investments',
    },
    {
      posts: [],
      queryKey: 'tags',
      queryValues: [768599],
      title: 'WIIMS',
    },
    {
      posts: [],
      queryKey: 'channels',
      queryValues: [22],
      title: 'Trading Ideas',
    },
    {
      posts: [],
      queryKey: 'channels',
      queryValues: [135733, 144421],
      title: 'Digital Asset and Fintech',
    },
    {
      posts: [],
      queryKey: 'channels',
      queryValues: [16784],
      title: 'Options',
    },
    {
      posts: [],
      queryKey: 'channels',
      queryValues: [135733],
      title: 'NFTs',
    },
    {
      posts: [],
      queryKey: 'channels',
      queryValues: [16888],
      title: 'Earnings',
    },
    {
      posts: [],
      queryKey: 'channels',
      queryValues: [67],
      title: 'Analyst Ratings',
    },
    {
      posts: [],
      queryKey: '',
      queryValues: [],
      title: 'Sponsored Content',
    },
  ];
  const feedChannels = feeds.map(feed => feed.queryValues).flat();
  const feedTags = feeds.map(feed => feed.queryValues).flat();

  for (const post of partnerContent) {
    const hasChannel = post.channels.find(channel => feedChannels.find(feedChannel => feedChannel === channel.tid));
    const hasTag = post?.tags?.find(tag => feedTags.find(feedTag => feedTag === tag.tid));

    const feedId = hasChannel?.tid || hasTag?.tid;
    if (feedId) {
      const feed = feeds.find(feed => feed.queryValues.includes(feedId));
      feed?.posts?.push(post);
    } else {
      const feed = feeds.find(feed => feed.title === 'Sponsored Content');
      feed?.posts?.push(post);
    }
  }

  return feeds?.filter(feed => feed.posts.length > 0);
};

export async function homeData(): Promise<HomeProps> {
  const newsletter = {
    // hubspotFormId: '************************************',
    beehiivFormId: 'c03f46e3-b180-439f-8cf4-103cbf2ac567',
    subtitle: 'The 5-minute newsletter with brief insights on big investment opportunities.',
    title: 'Beat The Market With PreMarket News And Trade Signals',
  };

  const session = getGlobalSession();

  const basicNewsManager = session.getManager(BasicNewsManager);

  // Top
  const topStoriesReq = basicNewsManager.getTopStoriesFromNodeQueue();
  const chartbeatPopularStoriesReq = basicNewsManager.getTopPostsUsingChartbeat({ limit: 50 });
  const latestNewsReq = basicNewsManager.simplyQueryNews(
    {},
    {
      excludeAutomated: true,
      excludeSponsored: true,
      limit: 4,
    },
  );

  // Bottom

  const contentManager = session.getManager(ContentManager);
  //const homePageReq = contentManager.getPost(54813);
  const homePageReq = contentManager.getPost(229472);

  const nftMoneyPostsReq = contentManager.getPosts({
    limit: 4,
    vertical: 825,
  });

  //sidebar
  // const feed = new TradeIdeaFeedManager({ filter: 'ideas', limit: 3 });
  // const tradeIdeasReq = feed.loadTradeIdeas();
  const sidebarArticlesReq = contentManager.getWordpressPost(174447);

  // new TOP STORIES feed requests
  const topCryptoStoriesReq = basicNewsManager.getTopPostsUsingChartbeat({
    limit: 2,
    section: 'cryptocurrency',
  });
  const expertIdeasReq = basicNewsManager.simplyQueryNews(
    {
      tags: [868681],
    },
    { limit: 2 },
  );

  const [
    topStoriesRes,
    chartbeatPopularStoriesRes,
    latestNewsRes,
    // tradeIdeasRes,
    homePageRes,
    sidebarArticlesRes,
    topCryptoStoriesRes,
    expertIdeasRes,
  ] = await Promise.all([
    topStoriesReq,
    chartbeatPopularStoriesReq,
    latestNewsReq,
    // tradeIdeasReq,
    homePageReq,
    sidebarArticlesReq,
    topCryptoStoriesReq,
    expertIdeasReq,
  ]);

  // Inject Partner Content
  const partnerContentRes = await basicNewsManager.simplyQueryNews(
    {
      tags: [802151],
    },
    { limit: 100 },
  );
  let partnerPosts: StoryObject[] = [];
  if (!partnerContentRes.err) {
    partnerPosts = partnerContentRes.ok ?? [];
    shuffleArray(partnerPosts);
  }

  // Add Sponsored post to popular
  const sponsoredPost = {
    ...partnerPosts?.[0],
    sponsored: true,
  };

  if (partnerPosts.length > 3 && partnerPosts?.[0] && latestNewsRes?.ok) {
    latestNewsRes.ok.pop();
    latestNewsRes.ok?.push(sponsoredPost);
  }

  const usedTags: string[] = [];
  const usedTickers: string[] = [];

  let chartbeatPopularStories = chartbeatPopularStoriesRes?.ok?.filter(node => {
    if (!Array.isArray(node.stocks)) return [];
    const stocksResult = node.stocks.every(stock => {
      const notIncluded = !usedTickers.includes(stock?.name);
      if (notIncluded) usedTickers.push(stock?.name);
      return notIncluded;
    });
    const tagsResult = node?.tags?.every(tag => {
      const notIncluded = !usedTags.includes(tag?.name);
      if (notIncluded) usedTags.push(tag?.name);
      return notIncluded;
    });
    return stocksResult && tagsResult;
  });

  const sidebarArticles = sidebarArticlesRes?.ok as WordpressSidebar;
  const latestNews: StoryObject[] = latestNewsRes?.ok || [];

  const topStories = topStoriesRes?.ok
    ? topStoriesRes?.ok.splice(0, 5).map(story => {
        return {
          ...story,
          image: formatImageUrl(story, true, true),
        };
      })
    : [];
  const topStoriesId = topStories.map(node => node.id);
  chartbeatPopularStories = chartbeatPopularStories?.filter(node => !topStoriesId.includes(node.id)) ?? [];

  // Create new Top Stories Feed
  // Feed Order: 1. Top Stories, 2. Crypto, 2. Expert Ideas, Rest - sorted by datetime
  const topStoriesFeed = sortTopStoriesFeed([
    (topStoriesRes?.ok?.splice(0, 4) ?? []) as News[],
    (topCryptoStoriesRes?.ok ?? []) as News[],
    (expertIdeasRes?.ok ?? []) as News[],
  ]);

  // Create map for Sponsored Content to be dispersed
  const partnerContentDisperseMap = sortPartnerContentForDispersion((partnerContentRes?.ok ?? []) as News[]);

  const briefsRes = await safeTimeout(
    basicNewsManager.simplyQueryNews(
      { tags: [799703] },
      {
        displayOutput: 'full',
        limit: 5,
      },
    ),
    3000,
  );

  // sidebar
  const quoteManager = session.getManager(QuotesManager);
  // const quotesReq = quoteManager.getDelayedQuotes(DefaultQuotesList);
  // console.log('DefaultQuotesList', DefaultQuotesList);
  // TODO: Remove hardcoded tickers
  const quotesRes = await quoteManager.getDelayedQuotes(['SPY', 'QQQ', 'SPIKE', 'BTC/USD', 'DIA', 'GLD', 'TLT']);

  const cryptoNewsReq = await safeTimeout(
    basicNewsManager.simplyQueryNews({
      channels: [135733],
    }),
    3000,
  );
  const cryptoNewsResponse = await cryptoNewsReq;

  const crypto = cryptoNewsResponse?.ok ?? [];

  const injected = {};
  // i dont know what this is trying to do?
  // eslint-disable-next-line @typescript-eslint/no-unused-expressions

  partnerPosts.forEach((partnerPost, i) => {
    if (Array.isArray(partnerPost.channels)) {
      const channels = partnerPost.channels.map(channel => channel?.name);
      if (channels.includes('Cryptocurrency') && !injected['Cryptocurrency']) {
        injected['Cryptocurrency'] = true;
        crypto.pop();
        crypto.push(partnerPost);
        partnerPosts.splice(i, 1);
      }
    }
  });

  //const breakingNews = (await session.getManager(BasicNewsManager).getBreakingNewsFormatted())?.ok;

  return {
    briefs: briefsRes?.ok ?? [],
    extraStories: topStoriesRes?.ok ?? [], // spliced above twice to remove 5 stories from top stories list
    featuredNews: topStories,
    headerProps: {
      //breakingNews,
      quotes: quotesRes?.ok ?? null,
    },
    //tradeIdeas: tradeIdeasRes,
    latestNews,
    latestNewsQuery: {
      excludeAutomated: true,
      excludeSponsored: true,
    },
    newsletter,
    parselyPopularStories: chartbeatPopularStories || [],
    partnerContentDisperseMap,
    post: homePageRes?.ok ?? null,
    shows: [],
    sidebarArticles: sidebarArticles ?? null,
    topStoriesFeed,
  };
}

const handler = async (_req, res) => {
  const data = await homeData();
  res.status(200).json(data);
};

export default handler;
