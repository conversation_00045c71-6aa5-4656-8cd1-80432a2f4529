'use client';
import React, { useEffect, useState } from 'react';
import { usePermission } from '@benzinga/user-context';
import { Impression } from '@benzinga/analytics';
import Image from 'next/image';

const variants = {
  crypto: {
    alt: 'Market Page Benzinga Edge Crypto CTA Banner',
    height: 120,
    url: '/next-assets/images/banners/edge/market-page/crypto-cta.png',
    width: 524,
  },
  etf: {
    alt: 'Market Page Benzinga Edge ETF CTA Banner',
    height: 120,
    url: '/next-assets/images/banners/edge/market-page/etf-cta.png',
    width: 524,
  },
  market: {
    alt: 'Market Page Benzinga Edge Market CTA Banner',
    height: 118,
    url: '/next-assets/images/banners/edge/market-page/market-cta.png',
    width: 852,
  },
  ranking: {
    alt: 'Market Page Benzinga Edge Rankings CTA Banner',
    height: 118,
    url: '/next-assets/images/banners/edge/market-page/rankings-cta.png',
    width: 852,
  },
};

export type EdgeCTAVariant = keyof typeof variants;

export const EdgeCTA = ({ variant }: { variant: EdgeCTAVariant }) => {
  const img = variants[variant ?? 'ranking'];
  const url = `https://www.benzinga.com/premium/ideas/benzinga-edge/?utm_campaign=marketpage&utm_adType=marketpagead&utm_ad=${variant}`;
  const hasBzEdge = usePermission('com/read', 'unlimited-calendars');
  const [show, setShow] = useState(true);

  useEffect(() => {
    if (hasBzEdge) {
      setShow(false);
    }
  }, [hasBzEdge]);

  if (!show) return null;

  return (
    <Impression campaign_id="marketpage" unit_type={variant}>
      <a
        className="w-full h-full rounded-md flex items-center justify-center"
        href={url}
        rel="noopener noreferrer"
        target="_blank"
      >
        <Image
          alt={img.alt}
          className="mt-2"
          height={img.height}
          object-fit="contain"
          src={img.url}
          width={img.width}
        ></Image>
      </a>
    </Impression>
  );
};
