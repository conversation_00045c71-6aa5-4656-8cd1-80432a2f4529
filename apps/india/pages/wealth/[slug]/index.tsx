import React from 'react';
import { GetServerSideProps } from 'next';
import { ContentManager, WordpressPage } from '@benzinga/content-manager';
import { getGlobalSession } from '../../api/session';
import { MoneyPostTemplate, moneyMetaInfo } from '@benzinga/money';
import { loadServerSideBlockData } from '@benzinga/blocks-utils';
import { appCode } from '@benzinga/utils';
import { getDeviceInfoFromRequestHeaders } from '@benzinga/device-utils';
import { ArticlePageTemplate } from '@benzinga/templates';
import { Mode } from '@benzinga/article';
import { Quicklinks } from '../../../src/components/Quicklinks';

export interface WealthPageProps {
  wealthPost: WordpressPage;
}

const WealthPost: React.FC<any> = props => {
  return (
    <>
      <Quicklinks />
      <h1 className="text-center text-5xl font-bold mt-10 mb-10">{props?.article?.title}</h1>
      <ArticlePageTemplate
        {...props}
        articleScrollViewMoreLink={'news'}
        campaignStrategy="none"
        disablePartnerAdOnScroll={false}
        disablePaywall={true}
        enableConnatixScript={false}
        enablePlaystreamIndiaScript={true}
        googleNewsUrlKey={'benzingaIndia'}
        hideSidebar={false}
        hideTopPanel={true}
        loadInfiniteArticles={true}
        loadMoreButtonVariant={'flat-ind-blue'}
        postedInVariant={'secondary'}
        showApiText={false}
        showCommentButton={false}
        showFontAwesomeIcons={true}
        showWhatsAppIcon={true}
        taboolaSettings={{
          placementMethod: 'below-article',
          unitKey: 'benzinga-benzingaind',
          unitMode: 'alternating-thumbnails-a' as Mode,
        }}
      />
    </>
  );
};

export default WealthPost;

export const getServerSideProps: GetServerSideProps = async ({ query, req, res }) => {
  try {
    const slug = query?.slug ?? '';
    const wealthSlug = Array.isArray(slug) ? slug[0] : slug;

    const session = getGlobalSession();

    const WealthPostRes = await session.getManager(ContentManager).getPageWithPathBySiteCode(wealthSlug, appCode.india);
    const wealthPost = WealthPostRes.ok;

    if (!wealthPost) {
      res.statusCode = 404;
      return { notFound: true };
    }

    if (wealthPost && wealthPost.success !== false) {
      if (Array.isArray(wealthPost?.blocks)) {
        wealthPost.blocks = await loadServerSideBlockData(session, wealthPost?.blocks, req.headers, req.cookies);
      }

      if (wealthPost?.sidebar?.blocks && Array.isArray(wealthPost?.sidebar?.blocks)) {
        wealthPost.sidebar.blocks = await loadServerSideBlockData(
          session,
          wealthPost?.sidebar?.blocks,
          req.headers,
          req.cookies,
        );
      }
    }

    const deviceInfo = getDeviceInfoFromRequestHeaders(req.headers, req.cookies);
    const deviceType = deviceInfo?.deviceType ?? null;
    const menuResponse = await session.getManager(ContentManager).getNavigation('money_india');
    const menuData = menuResponse?.ok ?? null;
    return {
      props: {
        article: wealthPost,
        deviceType: deviceType,
        headerProps: {
          hideBanner: true,
          hideQuicklinks: true,
          hideQuoteBar: true,
          hideSearchBar: true,
          hideTopBar: true,
          isMainDrawerVisible: true,
          menuData: menuData,
        },
        metaProps: wealthPost ? moneyMetaInfo(wealthPost) : null,
      },
    };
  } catch (error) {
    res.statusCode = 404;
    console.log('error:', error);
    return {
      props: {
        slug: null,
      },
    };
  }
};
