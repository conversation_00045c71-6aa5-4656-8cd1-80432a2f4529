import React from 'react';
import { Layout } from '@benzinga/core-ui';

import { LayoutMain, PremarketPageProps } from './components/LayoutMain';
import { getPremarketProps } from './data';
import PageLayout from '../_components/PageLayout';

export default async function Page() {
  const props = (await getPremarketProps()) as PremarketPageProps;
  return (
    <PageLayout pageProps={props}>
      <Layout layoutMain={<LayoutMain {...props} />} width="full" />
    </PageLayout>
  );
}
