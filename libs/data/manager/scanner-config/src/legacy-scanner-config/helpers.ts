import { FilterObject } from '@benzinga/quotes-v3-fields-manager';
import { FilterIngress, ScannerConfigLegacy, ScannerConfigV2 } from '../entities';
import { ScannerProtos } from '../scanner_protos';

export function legacyToQueryFiltersObject(filters: (string | FilterIngress | FilterObject)[]): FilterObject[] {
  return filters.map(filter => {
    if (typeof filter === 'string') {
      const tokens = filter.split('_');
      const params = tokens.slice(2).join('_').split(',');
      return { field: tokens[0], operator: tokens[1], parameters: params };
    } else {
      return {
        ...filter,
        field: typeof filter.field === 'string' ? filter.field : filter.field.name,
        parameters: filter.parameters.map(p => (p === null ? '' : typeof p === 'number' ? p.toString() : p)),
      };
    }
  });
}

export const toScannerConfig = (config: ScannerConfigLegacy): ScannerConfigV2 => {
  return {
    ...config,
    filters: legacyToQueryFiltersObject(config.filters),
  };
};

export class LegacyFilter {
  isCustom?: boolean;
  field: ScannerProtos.DataField;
  operator: string;
  parameters: string[];
  version = 1; // used as a key, increment when updated

  constructor(
    field: ScannerProtos.DataField,
    operator: string,
    parameters: (number | null)[] | string[],
    isCustom?: boolean,
  ) {
    this.isCustom = isCustom || false;
    this.field = field;
    this.operator = operator;
    this.parameters = parameters.map(p => (p === null ? '' : typeof p === 'number' ? p.toString() : p));
  }

  public static equals(f1: LegacyFilter, filter: LegacyFilter) {
    return f1.field.name === filter.field.name;
  }

  /**
   * Converts a
   */
  static parse(scanner, query: string): LegacyFilter | null {
    const tokens = query.split('_');
    const params = tokens.slice(2).join('_').split(',');
    const dataField = scanner.getDataFieldByName(tokens[0]);
    return dataField ? new LegacyFilter(dataField, tokens[1], params) : null;
  }

  static parseObject(scanner, query: FilterObject): LegacyFilter | null {
    if ((query.field as any) instanceof ScannerProtos.DataField) {
      return new LegacyFilter(
        query.field as unknown as ScannerProtos.DataField,
        query.operator,
        query.parameters,
        query.isCustom,
      );
    } else {
      const dataField = scanner.getDataFieldByName(query.field);
      return dataField ? new LegacyFilter(dataField, query.operator, query.parameters, query.isCustom) : null;
    }
  }

  public toString(): string {
    return `${this.field.name} ${this.operator} ${this.parameters}`;
  }
}
