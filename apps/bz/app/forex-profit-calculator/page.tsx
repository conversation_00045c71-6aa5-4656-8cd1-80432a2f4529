import React from 'react';
import { Layout } from '@benzinga/core-ui';

import { LayoutMain } from './components/LayoutMain';
import { LayoutSidebar } from './components/LayoutSidebar';

import { getPageProps } from './data';
import PageLayout from '../_components/PageLayout';
import { PageMeta } from './meta';
import { Metadata } from 'next';
import { PageType, BENZINGA_LOGO_URL } from '@benzinga/seo';
import Script from 'next/script';

const title = 'Forex Profit Calculator';
const description =
  "Benzinga's forex profit calculator can help you calculate your possible gains and losses based on the result of the trade.";
const canonical = 'https://www.benzinga.com/forex-profit-calculator';

export const metadata: Metadata = {
  alternates: {
    canonical,
  },
  authors: [{ name: '<PERSON><PERSON>' }],
  description,
  openGraph: {
    description,
    images: [{ alt: 'Benzinga Logo', height: 300, url: BENZINGA_LOGO_URL, width: 400 }],
    title,
    type: 'website',
    url: canonical,
  },
  title,
};

export default async function Page() {
  const pageProps = await getPageProps();
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': PageType.Tool,
    author: { '@type': 'Person', name: 'Benzinga' },
    description:
      "Benzinga's forex profit calculator can help you calculate your possible gains and losses based on the result of the trade.",
    name: 'Forex Profit Calculator',
    url: 'https://www.benzinga.com/forex-profit-calculator',
  };
  return (
    <PageLayout pageProps={pageProps}>
      <Script
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
        id="forex-profit-calculator-json-ld"
        type="application/ld+json"
      />

      <PageMeta />
      <Layout
        layoutMain={<LayoutMain post={pageProps.post} />}
        layoutSidebar={<LayoutSidebar sidebar={pageProps.post?.sidebar} />}
      />
    </PageLayout>
  );
}
