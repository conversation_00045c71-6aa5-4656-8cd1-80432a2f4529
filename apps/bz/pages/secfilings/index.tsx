import React, { ReactElement } from 'react';
import { GetServerSideProps, NextPage } from 'next';
import { Meta, MetaProps } from '@benzinga/seo';
import { Layout, LayoutBox } from '@benzinga/core-ui';
import { NEWSAPI_ROOT, SEC_CONTENT_TOKEN } from '../../src/env';
import Error from '../_error';
import { News } from '@benzinga/basic-news-manager';
import { PageType } from '@benzinga/seo';
import { NewsListItemElement } from '@benzinga/news';
import styled from '@benzinga/themetron';
import LazyLoad from 'react-lazyload';

const AdMgid = React.lazy(() => import('../../src/components/Ads/MGID/AdMgid'));

const RaptiveAdPlaceholder = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.RaptiveAdPlaceholder };
  }),
);

const CallToActionForm = React.lazy(() =>
  import('@benzinga/forms-ui').then(module => {
    return { default: module.CallToActionForm };
  }),
);

interface Props {
  articles: News[];
  errorCode?: number;
  metaProps?: MetaProps;
}

const GetLayoutSidebar: React.FC = () => {
  return (
    <>
      <LayoutBox>
        <React.Suspense fallback={<div className="h-[250px] w-[300px] mb-2" />}>
          <RaptiveAdPlaceholder className="w-[300px] mb-2 overflow-hidden" type="static-sidebar" />
        </React.Suspense>
      </LayoutBox>
      <LayoutBox>
        <React.Suspense>
          <CallToActionForm
            // hubspotFormId="************************************"
            beehiivFormId="c03f46e3-b180-439f-8cf4-103cbf2ac567"
            subtitle="Enter your email to get Benzinga's ultimate morning update: The PreMarket Activity Newsletter"
            title="Beat the Market With Our Free Pre-Market Newsletter"
          />
        </React.Suspense>
      </LayoutBox>
    </>
  );
};

const LayoutBelow: React.FC = () => {
  return (
    <LayoutBox>
      <LazyLoad offset={100} once>
        <AdMgid />
      </LazyLoad>
    </LayoutBox>
  );
};

const GetLayoutMain: React.FC<{ articles: News[] }> = ({ articles }) => {
  return (
    <Container>
      <LayoutBox className="border-t border-gray-400 pt-1">
        {Array.isArray(articles) &&
          articles.map((article, index) => {
            return (
              <div className="flex border-b border-bzblue-400 pb-2 pt-2" key={`${article.id}-${index}`}>
                <NewsListItemElement node={article} variant="secondary" />
              </div>
            );
          })}
      </LayoutBox>
      <LayoutBelow />
    </Container>
  );
};

const pageTitle = 'Latest SEC Filings';
const pageDescription = 'THe latest SEC Filings updated realtime';

const SECFilingsPage: NextPage<Props> = ({ articles, errorCode, metaProps }: Props) => {
  if (!articles || !articles.length) {
    return <Error statusCode={errorCode || 503} />;
  }

  return (
    <>
      <Meta description={pageDescription} title={pageTitle} canonical={metaProps?.canonical} />
      <Layout
        layoutMain={GetLayoutMain({ articles }) as ReactElement}
        layoutSidebar={GetLayoutSidebar({}) as ReactElement}
        title={pageTitle}
      />
    </>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ res }) => {
  // Yes yes yes, this is gross.
  // We should be using safefetch and moving this to the data package
  const newsApiUrlRecent = `${NEWSAPI_ROOT}?pageSize=100&sort=created:desc&token=${SEC_CONTENT_TOKEN}`;
  const newsApiHeader = {
    accept: 'application/json',
  };

  try {
    const articleAndCampaignJsonResp = await Promise.all([fetch(newsApiUrlRecent, { headers: newsApiHeader })]);
    const articles = await articleAndCampaignJsonResp[0].json();

    return {
      props: {
        articles: articles,
        metaProps: {
          canonical: 'https://www.benzinga.com/secfilings',
          pageType: PageType.Channel,
        },
        pageTargeting: { BZ_CHANNEL: ['SEC Filings'], BZ_PTYPE: 'channel' },
      },
    };
  } catch (error) {
    res.statusCode = 503;
    console.error('SECFilingsPage Error:', error);
    return {
      props: {
        errorCode: 503,
      },
    };
  }
};

const Container = styled.div`
  .news-block.secondary {
    font-weight: ${({ theme }) => theme.fontWeight.semibold};
    .news-block-headline {
      color: ${({ theme }) => theme.colorPalette.blue700};
    }
  }
`;

export default SECFilingsPage;
