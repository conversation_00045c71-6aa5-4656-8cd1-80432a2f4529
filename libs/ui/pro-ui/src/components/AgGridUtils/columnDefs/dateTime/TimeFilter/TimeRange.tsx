import React, { useCallback } from 'react';
import { Button } from 'antd';
import classnames from 'classnames';
import styled, { ThemedProps } from '@benzinga/themetron';
import { ButtonProps } from 'antd/lib/button';
// import { Dayjs } from 'dayjs';
// import dayjs from 'dayjs';
import { DateTime } from 'luxon';
import TimePicker, { TimePickerProps } from './TimePicker';
import { getTimeNumericValue } from '@benzinga/utils';

export type TimeString = string | undefined;

export interface TimeRangeInterface {
  isExactTime?: boolean;
  timeFrom?: TimeString;
  timeTo?: TimeString;
}

interface Props {
  addRange: () => void;
  index: number;
  isExactTime: boolean | undefined;
  modifyRange: (index: number, timeFrom?: string, timeTo?: string, isExactTime?: boolean, remove?: boolean) => void;
  ranges: TimeRangeInterface[];
  timeFrom: TimeString;
  timeTo: TimeString;
}

export const TIME_FORMAT = 'HH:mm:ss';
export const DEFAULT_TIME_FROM = '00:00:00';
const DEFAULT_TIME_TO = '23:59:59';
const DEFAULT_OPEN_VALUE = '12:00:00';

const TimeRange: React.FC<Props> = ({ addRange, index, isExactTime, modifyRange, ranges, timeFrom, timeTo }) => {
  const isFirst = !index;
  const isLast = !(ranges.length - index - 1);
  const showAdd = !!(timeFrom || (timeTo && !isExactTime)) && !(ranges.length - index - 1);
  const remove = (!(timeFrom || (timeTo && !isExactTime)) || !!(ranges.length - index - 1)) && !(isFirst && isLast);

  const handleFromChange = useCallback(
    (event: DateTime | null) => {
      const newTimeFrom = event ? event.toFormat(TIME_FORMAT) : undefined;
      const newTimeTo =
        newTimeFrom && timeTo && getTimeNumericValue(newTimeFrom) <= getTimeNumericValue(timeTo) ? timeTo : undefined;
      modifyRange(index, newTimeFrom, newTimeTo, isExactTime);
    },

    [modifyRange, isExactTime, timeTo, index],
  );

  const handleToChange = useCallback(
    (event: DateTime | null, _dateString: string) => {
      const newTimeTo = event ? event.toFormat(TIME_FORMAT) : undefined;
      const newTimeFrom =
        newTimeTo && timeFrom && getTimeNumericValue(newTimeTo) >= getTimeNumericValue(timeFrom) ? timeFrom : undefined;
      modifyRange(index, newTimeFrom, newTimeTo, isExactTime);
    },
    [modifyRange, isExactTime, timeFrom, index],
  );

  const handleExactTimeChange = useCallback(() => {
    modifyRange(index, timeFrom, timeTo, !isExactTime);
  }, [modifyRange, index, timeFrom, timeTo, isExactTime]);

  const handleAddRange = useCallback(() => {
    if (showAdd) {
      addRange();
    }
  }, [showAdd, addRange]);

  const handleClearRemove = useCallback(() => {
    modifyRange(index, undefined, undefined, isExactTime, remove);
  }, [modifyRange, isExactTime, index, remove]);

  return (
    <StyledTimeRange className="bz-time-picker">
      <div>
        <StyledTimePicker
          allowClear={false}
          defaultOpenValue={
            timeFrom && DateTime.fromFormat(timeFrom, TIME_FORMAT).isValid
              ? DateTime.fromFormat(timeFrom, TIME_FORMAT)
              : DateTime.fromFormat(DEFAULT_OPEN_VALUE, TIME_FORMAT)
          }
          dropdownClassName={classnames('bz-time-picker-dropdown', 'ag-custom-component-popup')}
          exactTimePicker
          isExactTime={isExactTime}
          onChange={handleFromChange as any}
          placeholder={DEFAULT_TIME_FROM}
          value={
            timeFrom && DateTime.fromFormat(timeFrom, TIME_FORMAT).isValid
              ? DateTime.fromFormat(timeFrom, TIME_FORMAT)
              : undefined
          }
        />
        <StyledTimePicker
          allowClear={false}
          defaultOpenValue={
            timeTo && DateTime.fromFormat(timeTo, TIME_FORMAT).isValid
              ? DateTime.fromFormat(timeTo, TIME_FORMAT)
              : DateTime.fromFormat(DEFAULT_OPEN_VALUE, TIME_FORMAT)
          }
          dropdownClassName={classnames('bz-time-picker-dropdown', 'ag-custom-component-popup')}
          isExactTime={isExactTime}
          onChange={handleToChange as any}
          placeholder={DEFAULT_TIME_TO}
          value={
            timeTo && DateTime.fromFormat(timeTo, TIME_FORMAT).isValid
              ? DateTime.fromFormat(timeTo, TIME_FORMAT)
              : undefined
          }
        />
      </div>
      <StyledButtonsContainer>
        <StyledButton double onClick={handleExactTimeChange} size="small">
          {isExactTime ? 'Time Range' : 'Exact Time'}
        </StyledButton>
        {showAdd && (
          <StyledButton onClick={handleAddRange} size="small">
            Add
          </StyledButton>
        )}
        <StyledButton double={!showAdd} onClick={handleClearRemove} size="small">
          {remove ? 'Remove' : 'Clear'}
        </StyledButton>
      </StyledButtonsContainer>
    </StyledTimeRange>
  );
};

export default TimeRange;

const StyledTimeRange = styled.div`
  width: 256px;
  & .bz-time-picker-buttons {
    height: 24px;
    overflow: hidden;
  }
`;

interface StyledTimePickerProps extends TimePickerProps, ThemedProps {
  exactTimePicker?: boolean;
  isExactTime: boolean | undefined;
}

const StyledTimePicker = styled(TimePicker).attrs<
  StyledTimePickerProps & {
    color?: string;
    inputDisplay?: string;
    inputWidth?: string;
  }
>(props => ({
  color: props.theme.name === 'light' ? props.theme.colors.foreground : '',
  inputDisplay: !props.exactTimePicker && props.isExactTime ? 'none !important' : 'initial',
  inputWidth: props.isExactTime ? '100% !important' : '50% !important',
}))<StyledTimePickerProps>`
  width: ${props => props.inputWidth};
  display: ${props => props.inputDisplay};
  height: 0px;
  &.ant-time-picker-panel-inner {
    position: relative !important;
    top: 60px !important;
  }
  &.ant-time-picker-panel-select-option-selected {
    color: $bz-black !important;
  }
  &.ant-time-picker-panel-select ul {
    padding-bottom: 0 !important;
  }
  &.ant-time-picker-panel-input-wrap {
    text-align: center !important;
    & .ant-time-picker-panel-input {
      text-align: center !important;
      font-size: 16px !important;
      background: none !important;
    }
  }
  & .ant-picker-input {
    input {
      padding-left: 16px !important;
      padding-right: 14px !important;
      text-align: center !important;
    }
  }
  & .anticon-clock-circle {
    color: ${props => props.color} !important;
  }
  & input::placeholder {
    color: ${props => props.color} !important;
  }
`;

interface StyledButtonProps extends ButtonProps {
  double?: boolean;
}

const StyledButton = styled(Button).attrs<StyledButtonProps & { width?: string }>(props => ({
  width: props.double ? '50%' : '25%',
}))<StyledButtonProps>`
  width: ${props => props.width};
  & .ant-btn:focus {
    color: inherit;
    border-color: inherit;
  }
`;

const StyledButtonsContainer = styled.div`
  height: 24px;
  overflow: hidden;
`;
