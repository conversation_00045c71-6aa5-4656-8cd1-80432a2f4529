import { numberShorthand } from '@benzinga/fission';
import { GeneratedPost } from '../components/collections/collectionEntities';
import { Categories } from './entities';
import { getSentiment, numberFormatter, percentChange } from './utils';

interface PostKeys {
  calEst_checked: boolean;
  eps_type: string;
  eps_est: string;
  eps_prior: string;
  eps: string;
  exchange: string;
  importance: number;
  name: string;
  period_year: string;
  period: string;
  revenue_est: string;
  revenue_prior: string;
  revenue: string;
  ticker: string;
  refinitiv_estimate: any;
}

const ESTIMATE = 'the analyst consensus estimate';
const LASTYEAR = 'the same period last year';

const parseOrDefault = (value, fallback) => (!isNaN(parseFloat(value)) ? parseFloat(value) : fallback);

export default (keys: PostKeys): GeneratedPost => {
  const { calEst_checked, eps_type, exchange, importance, name, period, period_year, refinitiv_estimate, ticker } =
    keys;
  const revenue = parseFloat(keys.revenue);
  const revenue_prior = parseFloat(keys.revenue_prior);
  const revenue_est_ex = parseFloat(keys.revenue_est);
  const eps = parseFloat(keys.eps);
  const eps_prior = parseFloat(keys.eps_prior);
  const eps_est_ex = parseFloat(keys.eps_est);

  const gap_est = parseOrDefault(calEst_checked ? eps_est_ex : refinitiv_estimate?.gps, eps_est_ex);
  const ffo_est = parseOrDefault(calEst_checked ? eps_est_ex : refinitiv_estimate?.ffo_est, eps_est_ex);
  const eps_est_ref = parseOrDefault(calEst_checked ? eps_est_ex : refinitiv_estimate?.eps_est, eps_est_ex);

  const eps_est = eps_type === 'GAAP' ? gap_est : eps_type === 'FFO' ? ffo_est : eps_est_ref;
  const revenue_est = parseOrDefault(calEst_checked ? revenue_est_ex : refinitiv_estimate?.revenue_est, revenue_est_ex);

  const longname = `${name} (${exchange}: ${ticker})`;

  let short_eps_adverb: string;
  let eps_verb: string;
  let short_eps_verb: string;
  let eps_change: string;
  let short_eps_change: string;
  let revenue_verb: string;
  let short_revenue_verb: string;
  let revenue_change: string;
  let short_revenue_change: string;

  if (eps) {
    const loss = eps < 0;
    const ceps = numberFormatter({ short: false, value: eps });
    const sceps = numberFormatter({ value: eps });

    short_eps_adverb = '';

    if (eps_est) {
      const eceps = numberFormatter({ short: false, value: eps_est });
      const seceps = numberFormatter({ value: eps_est });

      if (ceps === eceps) {
        eps_verb = `reported quarterly ${loss ? 'losses' : 'earnings'} of ${ceps} per share which met ${ESTIMATE}. `;
        short_eps_verb = `${eps_type === 'FFO' ? 'FFO' : eps_type === 'Adj' ? 'Adj. EPS' : 'EPS'} ${sceps}`;
        short_eps_adverb = ', Inline';
      } else {
        const up = eps > eps_est;
        const pc = `${percentChange(eps, eps_est)} percent`;
        eps_verb = `reported quarterly ${loss ? 'losses' : 'earnings'} of ${ceps} per share which ${
          up ? 'beat' : 'missed'
        } ${ESTIMATE} of ${eceps} by ${pc}. `;
        short_eps_verb = `${eps_type === 'FFO' ? 'FFO' : eps_type === 'Adj' ? 'Adj. EPS' : 'EPS'} ${sceps} ${
          up ? 'Beats' : 'Misses'
        } ${seceps} Estimate`;
      }
    } else {
      // earnings with no estimates
      eps_verb = `reported quarterly ${loss ? 'losses' : 'earnings'} of ${ceps} per share. `;
      short_eps_verb = `${eps_type === 'FFO' ? 'FFO' : eps_type === 'Adj' ? 'Adj. EPS' : 'EPS'} ${sceps}`;
    }

    if (eps_prior) {
      const peps = numberFormatter({ short: false, value: eps_prior });

      if (eps_prior === eps) {
        eps_change = `This is unchanged from ${LASTYEAR}. `;

        short_eps_change = eps_est ? '' : ', Same YoY';
      } else {
        const up = eps > eps_prior;
        const prior_loss = eps_prior < 0;
        const pc = `${percentChange(eps, eps_prior)} percent`;
        eps_change = `This is a ${pc} ${up ? 'increase' : 'decrease'} over ${
          prior_loss ? 'losses' : 'earnings'
        } of ${peps} per share from ${LASTYEAR}. `;

        short_eps_change = eps_est ? '' : ` ${up ? 'Up' : 'Down'} From ${peps} YoY`;
      }
    } else {
      eps_change = '';
      short_eps_change = '';
    }
  } else {
    eps_verb = null;
    short_eps_verb = null;
  }

  if (revenue) {
    const rev = numberFormatter({ fixed: 3, short: false, value: revenue });
    const srev = numberFormatter({ fixed: 3, value: revenue });

    if (revenue_est) {
      const erev = numberFormatter({ fixed: 3, short: false, value: revenue_est });
      const serev = numberFormatter({ fixed: 3, value: revenue_est });

      if (srev === serev) {
        revenue_verb = `reported quarterly sales of ${erev} which met ${ESTIMATE}. `;

        if (eps === eps_est) {
          short_revenue_verb = `Sales ${srev}, Both Inline`;
          short_eps_adverb = '';
        } else {
          short_revenue_verb = `Sales ${srev} Inline`;
        }
      } else {
        const up = revenue > revenue_est;
        const pc = `${numberShorthand(percentChange(revenue, revenue_est), 2)} percent`;

        revenue_verb = `reported quarterly sales of ${rev} which ${
          up ? 'beat' : 'missed'
        } ${ESTIMATE} of ${erev} by ${pc}. `;
        short_revenue_verb = `Sales ${srev} ${up ? 'Beat' : 'Miss'} ${serev} Estimate`;
      }
    } else {
      revenue_verb = `reported ${rev} in sales this quarter.`;
      short_revenue_verb = `Sales ${srev}`;
      // revenue with no estimates
    }

    if (revenue_prior) {
      const prev = numberFormatter({ fixed: 3, short: false, value: revenue_prior });
      const sprev = numberFormatter({ fixed: 3, value: revenue_prior });

      if (revenue_prior === revenue) {
        revenue_change = `This is the same as ${LASTYEAR}.`;

        short_revenue_change = revenue_est ? '' : ', Same YoY';
      } else {
        const pc = `${numberShorthand(percentChange(revenue, revenue_prior), 2)} percent`;
        const up = revenue > revenue_prior;
        revenue_change = `This is a ${pc} ${up ? 'increase' : 'decrease'} over sales of ${prev} ${LASTYEAR}. `;

        short_revenue_change = revenue_est ? '' : ` ${up ? 'Up' : 'Down'} From ${sprev} YoY`;
      }
    } else {
      revenue_change = '';
      short_revenue_change = '';
    }
  } else {
    revenue_verb = null;
  }

  let title = '';
  let body = '';

  if (eps_verb) {
    body = `${longname} ${eps_verb}${eps_change}`;
    title = `${name} ${period} ${short_eps_verb}${short_eps_adverb}${short_eps_change}`;

    if (revenue_verb) {
      body = `${body} The company ${revenue_verb} ${revenue_change}`;
      title = `${title}, ${short_revenue_verb}${short_revenue_change}`;
    }
  } else {
    if (revenue_verb) {
      body = `${longname} ${revenue_verb} ${revenue_change}`;
      title = `${name} ${period} ${short_revenue_verb}${short_revenue_change}`;
    } else {
      body = `${longname} will report on ${period} ${period_year}. `;
      title = `${name} ${period} ${period_year}`;
    }
  }

  const categories = [Categories.NEWS, Categories.EARNINGS];
  if (importance === 5) {
    categories.push(Categories.HOT);
  }

  if (title.toLowerCase().includes('beat')) {
    categories.push(Categories.EARNINGS_BEATS);
  }
  if (title.toLowerCase().includes('miss')) {
    categories.push(Categories.EARNINGS_MISS);
  }
  const sentiment = getSentiment(eps, eps_est, revenue, revenue_est);

  return {
    body,
    categories,
    sentiment,
    title,
  };
};
