// import { Dayjs } from 'dayjs';
import { DateTime } from 'luxon';
import * as React from 'react';
import DatePicker from './DatePicker';
import type { PickerProps } from 'antd/es/date-picker/generatePicker';

export type TimePickerProps = Omit<PickerProps<DateTime>, 'picker'>;

const TimePicker = React.forwardRef<any, TimePickerProps>((props, ref) => {
  return <DatePicker {...props} mode={undefined} picker="time" ref={ref} />;
});

TimePicker.displayName = 'TimePicker';

export default TimePicker;
