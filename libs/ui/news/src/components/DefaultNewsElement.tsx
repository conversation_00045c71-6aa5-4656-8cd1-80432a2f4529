'use client';
import React from 'react';
import { StoryObject } from '@benzinga/advanced-news-manager';
import { appEnvironment, appName, formatImageUrl } from '@benzinga/utils';
import { isSponsoredArticle, getSponsoredContentLabel } from '@benzinga/article-manager';
import { PostCard, PostCardProps } from './PostCard';
import classNames from 'classnames';
import Hooks from '@benzinga/hooks';

const nodeUrl = node => {
  return appEnvironment().isApp(appName.india)
    ? node.url?.replace('https://www.benzinga.com/', `${appEnvironment().config().url}/`)
    : node.url;
};

export const DefaultNewsElement: React.FC<{
  node: StoryObject & { isHighlighted?: boolean };
  postCardProps?: PostCardProps;
}> = ({ node, postCardProps = {} }) => {
  const [didImpress, setDidImpress] = React.useState<boolean>(false);
  const [isHighlighted, setIsHighlighted] = React.useState<boolean>(node?.isHighlighted ?? false);

  const ref = React.useRef<HTMLDivElement | null>(null);
  const entry = Hooks.useIntersectionObserver(ref as React.RefObject<Element>, {});
  const isVisible = !!entry?.isIntersecting;

  const sponsoredData = getSponsoredContentLabel(node);
  const url = nodeUrl(node);

  React.useEffect(() => {
    if (isVisible && !didImpress) {
      setDidImpress(true);
    } else if (!isVisible && didImpress) {
      setIsHighlighted(false);
      node.isHighlighted = false;
    }
  }, [didImpress, isVisible, node]);

  return (
    <div className={classNames('newsfeed-card text-black', { fade: isVisible, highlight: isHighlighted })} ref={ref}>
      <PostCard
        created={node.created}
        dateType="med"
        description={node.teaser ?? ''}
        hideEmptyThumb={false}
        id={node.id}
        image={formatImageUrl(node)}
        isOnlyProPost={!node.isBzPost && node.isBzProPost}
        layout="feed"
        size="nano"
        sponsored={sponsoredData.isSponsored}
        sponsoredLabel={sponsoredData?.label}
        stocks={node.stocks}
        title={node.title}
        url={url}
        {...(postCardProps ?? {})}
        showCommentsIcon={sponsoredData.isSponsored ? false : postCardProps.showCommentsIcon}
      />
    </div>
  );
};

export default DefaultNewsElement;
