import React, { Suspense } from 'react';
import { useTranslation } from 'react-i18next';
import { DateTime } from 'luxon';

import i18n, { LocaleType, setLanguageByHost } from '@benzinga/translate';
import { formatLarge } from '@benzinga/utils';
import { BasicNewsManager, News, StoryObject } from '@benzinga/basic-news-manager';
import { SectionTitle, StatBoxesContainer } from '@benzinga/core-ui';

import { getQuoteData } from '../../api/quote';
import { getGlobalSession } from '../../api/session';
import { EarningsSummary } from '../../api/quote/[ticker]/earnings';

import { StatBox } from '../../../src/components/Quote/V2/StatBox';

import { QuoteProfile } from '../../../src/entities/quoteEntity';
import { parseArticleBodyToBlocks } from '@benzinga/article-manager';
import { InternalNewsManager } from '@benzinga/internal-news-manager';
import DrilldownFooter from '../../../src/components/Quote/V2/DrilldownFooter';
import { QuoteLayout } from '../../../src/components/Quote/V2/QuoteLayout';
import EarningsFAQ from '../../../src/components/Quote/FAQ/EarningsFAQ';
import QuoteCalendar from '../../../src/components/Quote/QuoteCalendar';
import ZacksHeadScript from '../../../src/components/Quote/V2/ZacksHeadScript';
import getQuoteV2PageData, {
  QuoteV2PageProps,
  getQuoteFeedTypeByLocale,
  getQuoteNotFoundData,
} from '../../../src/quoteV2Utils';
import EarningsChart from '../../../src/components/Quote/V2/EarningsCharts';
import { QuoteAltSuggestions } from '../../../src/components/Quote/QuoteAltSuggestions';
import { sanitizeHTML } from '@benzinga/frontend-utils';

const RaptiveAdPlaceholder = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.RaptiveAdPlaceholder };
  }),
);

interface EarningsQuoteV2PageProps extends QuoteV2PageProps {
  earningsNews: StoryObject[] | null;
  relevantEarningsNews: News[] | null;
}

const QuoteEarningsPage: React.FC<EarningsQuoteV2PageProps> = ({
  activeTab,
  advertiserProfile,
  altSuggestions,
  isAdvertiser,
  isETF,
  metaProps,
  peersChartData,
  profile,
  showGovLink,
  symbol,
}) => {
  const { t } = useTranslation(['quote', 'common'], { i18n });

  if (altSuggestions) {
    return <QuoteAltSuggestions {...altSuggestions} symbol={symbol} />;
  }

  return (
    <QuoteLayout
      advertiserProfile={advertiserProfile}
      defaultPath={activeTab}
      isAdvertiser={isAdvertiser}
      profile={profile}
      showGovLink={showGovLink}
      symbol={symbol}
    >
      <ZacksHeadScript />
      <div className="main-div flex flex-col gap-y-8">
        <div className="flex flex-col lg:flex-row">
          <div className="flex flex-col">
            <div className="w-full md:flex">
              <div className="w-full">
                <div className="pb-4">
                  <div className="flex flex-col gap-2 mb-8">
                    <SectionTitle level={1} uppercase={false}>
                      {metaProps.title}
                    </SectionTitle>
                    <p className="section-description">
                      {t('Quote.Paragraphs.earnings', { stockName: profile.richQuoteData?.name })}
                    </p>
                  </div>
                </div>
                <EarningsSummaryBox earningsSummary={profile.earningsSummary} profile={profile} />
              </div>
              <Suspense fallback={<div />}>
                <RaptiveAdPlaceholder
                  className="w-[300px] min-w-[300px] h-[250px] md:ml-4"
                  disableMargins={true}
                  onlyDesktop={true}
                  type="static-sidebar"
                />
              </Suspense>
            </div>
            <div>
              <p className="mb-2 text-sm">
                {t('Quote.Earnings.analyze-earnings', { companyName: profile.richQuoteData?.name })}
              </p>
              <QuoteCalendar
                calendar="earnings"
                hideFilters
                initialData={profile.earningsSummary?.earnings}
                symbol={symbol}
              />
            </div>
            <div className="my-8">
              <SectionTitle level={3} size="2xl" uppercase={false}>
                {t('Quote.Earnings.Charts.company-earnings-per-share', {
                  companyName: profile.richQuoteData?.name,
                  symbol,
                })}
              </SectionTitle>
              <React.Suspense fallback={<div />}>
                <EarningsChart data={profile?.earningsSummary?.earnings ?? []} type="eps" />
              </React.Suspense>
            </div>
            <div className="mt-4 md:my-4">
              <SectionTitle level={3} size="2xl" uppercase={false}>
                {t('Quote.Earnings.Charts.company-revenue', { companyName: profile.richQuoteData?.name, symbol })}
              </SectionTitle>
              <React.Suspense fallback={<div />}>
                <EarningsChart data={profile?.earningsSummary?.earnings ?? []} type="revenue" />
              </React.Suspense>
            </div>
          </div>
        </div>
        <RaptiveAdPlaceholder className="w-[300px] min-w-[300px]" onlyMobile={true} type="content-small" />
        <DrilldownFooter
          advertiserProfile={advertiserProfile}
          isAdvertiser={isAdvertiser}
          isETF={isETF}
          leftSection={
            <>
              <SectionTitle level={3} size="2xl">
                {t('Quote.FAQ.faq')}
              </SectionTitle>
              <EarningsFAQ earningsSummary={profile.earningsSummary} quote={profile.richQuoteData} />
              <p>
                {/* eslint-disable-next-line @next/next/no-html-link-for-pages */}
                {t('Buttons.browse', { ns: 'common' })}{' '}
                {/* eslint-disable-next-line @next/next/no-html-link-for-pages */}
                <a href="/calendars/earnings">{t('Quote.Earnings.earnings-estimates-eps')}</a>{' '}
                {t('Quote.Helpers.on-all-stocks')}
              </p>
            </>
          }
          peers={profile.quotes}
          peersChartData={peersChartData}
          symbol={symbol}
          type={profile.schedule.type}
        />
      </div>
    </QuoteLayout>
  );
};

export default QuoteEarningsPage;

export const getServerSideProps = async ({ query: { ticker }, req, res }) => {
  if (req?.headers?.host) {
    await setLanguageByHost(req?.headers?.host); // set language for server side translations
  }

  const symbol: string = sanitizeHTML(ticker) || '';
  const activeTab = 'earnings';

  const pageData = await getQuoteV2PageData(
    symbol,
    activeTab,
    getQuoteFeedTypeByLocale(i18n.language as LocaleType),
    req?.headers?.host,
  );

  if (pageData?.props?.errorCode) {
    res.statusCode = pageData.props.errorCode;
    return pageData;
  }

  if ('props' in pageData) {
    const typedProps = pageData.props as EarningsQuoteV2PageProps;
    const isEstimateAvailable = !!typedProps?.profile?.earningsSummary?.next?.eps_est;

    const now = new Date();
    const quarter = Math.floor(now.getMonth() / 3);
    const firstDate = new Date(now.getFullYear(), quarter * 3, 1);
    firstDate.setDate(firstDate.getDate() - 1);
    const afterTimestamp = firstDate.toISOString().split('T')[0];

    const session = getGlobalSession();
    const internalNewsManager = session.getManager(InternalNewsManager);
    const relevantEarningsNewsRes = await internalNewsManager.getStories(
      {
        lhs: {
          expression: ['Tags.tid', 'any', [isEstimateAvailable ? 787692 : 787678]],
          operator: 'expression',
        },
        operator: 'and',
        rhs: {
          expression: ['Tickers.name', 'any', [symbol]],
          operator: 'expression',
        },
      },
      {
        after: afterTimestamp,
        displayOutput: 'full',
        limit: 1,
      },
    );

    const relevantEarningsNews: News[] | null = (relevantEarningsNewsRes?.ok as News[]) ?? null;
    if (Array.isArray(relevantEarningsNews)) {
      for (const news of relevantEarningsNews as News[]) {
        try {
          const blocks = await parseArticleBodyToBlocks(session, news.body);
          news.blocks = blocks.filter(block => {
            return !block.innerHTML?.match(/To track all earnings releases/);
          });
        } catch (e) {
          console.error('ERROR:: Earnings error parse article to blocks');
        }
      }
    }

    const basicNewsManager = session.getManager(BasicNewsManager);
    const earningsNewsRes = await basicNewsManager.simplyQueryNews({ channels: 16888 }, { limit: 5 });

    typedProps.earningsNews = earningsNewsRes?.ok ?? [];
    typedProps.relevantEarningsNews = relevantEarningsNews || null;
  }

  return pageData;
};

const formatDate = (date: string) => {
  return date ? DateTime.fromISO(date).setLocale(i18n.language).toFormat('MMM d') : 'N/A';
};

const EarningsSummaryBox: React.FC<{ earningsSummary: EarningsSummary; profile: QuoteProfile }> = ({
  earningsSummary,
  profile,
}) => {
  const { t } = useTranslation('quote', { i18n });
  const isEstimateAvalible = earningsSummary.next?.eps_est ? true : false;
  const relevantEarnings = isEstimateAvalible ? earningsSummary?.next : earningsSummary?.last;
  const financialsAnnual = profile?.fundamentalsAnnual?.financials && profile.fundamentalsAnnual.financials[0];
  const totalRevenue = financialsAnnual?.incomeStatement?.totalRevenue;
  return (
    <StatBoxesContainer>
      {relevantEarnings && (
        <StatBox
          title={t('Quote.Earnings.earnings-date')}
          value={relevantEarnings ? formatDate(relevantEarnings.date) : '–'}
        />
      )}
      {isEstimateAvalible ? (
        <>
          <StatBox
            title={t('Quote.Earnings.eps-estimate')}
            value={relevantEarnings?.eps_est ? `$${relevantEarnings.eps_est}` : '–'}
          />
          <StatBox
            title={t('Quote.Earnings.quarterly-revenue-estimate')}
            value={relevantEarnings?.revenue_est ? `$${formatLarge(Number(relevantEarnings.revenue_est))}` : '–'}
          />
        </>
      ) : (
        <>
          <StatBox title={t('Quote.Earnings.eps')} value={relevantEarnings?.eps ? `$${relevantEarnings.eps}` : '–'} />
          <StatBox
            title={t('Quote.Earnings.quarterly-revenue')}
            value={relevantEarnings?.revenue ? `$${formatLarge(Number(relevantEarnings.revenue))}` : '–'}
          />
        </>
      )}
      <StatBox
        title={
          financialsAnnual?.asOf
            ? `${t('Quote.Earnings.annual-revenue')}<br />(${t('Helpers.as-of')} ${formatDate(financialsAnnual?.asOf)})`
            : t('Quote.Earnings.annual-revenue')
        }
        value={totalRevenue ? `$${formatLarge(totalRevenue)}` : '–'}
      />
    </StatBoxesContainer>
  );
};
