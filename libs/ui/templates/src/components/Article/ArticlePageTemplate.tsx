'use client';
import React, { Suspense, useMemo } from 'react';
import { NextPage } from 'next';

import { ErrorBoundary } from '@benzinga/core-ui';
import { AuthContainer } from '@benzinga/auth-ui';

import { checkDeviceType } from '@benzinga/device-utils';
import Hooks from '@benzinga/hooks';

import {
  useHasSession,
  useIsUserBenzingaContributor,
  useIsUserEditor,
  useIsUserLoggedIn,
} from '@benzinga/user-context';
import { SessionContext } from '@benzinga/session-context';
import { useBenzingaEdge } from '@benzinga/edge';
import { ArticleData, ArticleManager, DraftArticle, comtexPublishers } from '@benzinga/article-manager';

import Error from '../Error/Error';

import { LoadingDraftScreen, StyledLoadingDraft } from './LoadingDraftScreen';
import { ArticleLayout } from './ArticleLayout';
import { ArticlePageProps } from './entities';

const PlayStreamScript = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.PlayStreamScript };
  }),
);

// const ConnatixInitialHeadScript = React.lazy(() =>
//   import('@benzinga/ads').then(module => {
//     return { default: module.ConnatixInitialHeadScript };
//   }),
// );

// const ArticleInfiniteScrollStories = React.lazy(() =>
//   import('./ArticleInfiniteScrollStories').then(module => {
//     return { default: module.ArticleInfiniteScrollStories };
//   }),
// );

export const ArticlePageTemplate: NextPage<ArticlePageProps> = ({
  article,
  articleScrollViewMoreLink,
  baseUrl,
  campaigns,
  campaignSettings,
  campaignStrategy,
  deviceType,
  disablePartnerAdOnScroll = false,
  disablePaywall,
  disableWNSTNWidget,
  //enableConnatixScript = false,
  enablePlaystreamIndiaScript = false,
  errorCode,
  featuredTickers,
  followUpQuestions,
  googleNewsUrlKey = 'benzinga',
  hideSidebar = false,
  hideTaboola = false,
  hideTopPanel,
  isTaggedPressRelease,
  isTemplate = false,
  layout,
  loadInfiniteArticles = true,
  loadMoreButtonVariant,
  nid,
  postedInVariant,
  pressReleasesByAuthor,
  rankingData,
  raptiveEnabled,
  shouldRenderBottomTaboola,
  showApiText,
  showCommentButton,
  showFontAwesomeIcons,
  showWhatsAppIcon,
  taboolaSettings,
  useNewTemplate = false,
  wordCount,
}: ArticlePageProps) => {
  const sessionLoaded = useHasSession();

  const session = React.useContext(SessionContext);

  const [isLoadingDraftArticle, setIsLoadingDraftArticle] = React.useState<boolean>(!article || false);
  const [draftArticle, setDraftArticle] = React.useState<DraftArticle>();
  const [articleData, setArticleData] = React.useState<ArticleData | undefined | null>(article);

  const isLoggedIn = Hooks.useHydrate(useIsUserLoggedIn(), false);
  const isEditor = Hooks.useHydrate(useIsUserEditor(), false);
  const isBenzingaContributor = Hooks.useHydrate(useIsUserBenzingaContributor(), false);
  const hasAdLight = useBenzingaEdge().adLightEnabled;

  const isBot = Hooks.useHydrate(checkDeviceType().isBot(), false);

  React.useEffect(() => {
    // Reload page
    // https://upmostly.com/tutorials/settimeout-in-react-components-using-hooks
    const timer = setTimeout(
      () => {
        // eslint-disable-next-line no-restricted-globals
        location.reload();
      },
      (Math.floor(Math.random() * 11) + 22) * 60 * 1000,
    );
    return () => clearTimeout(timer);
  }, []);

  React.useEffect(() => {
    if (sessionLoaded && (isEditor || isBenzingaContributor) && !article && !draftArticle) {
      setIsLoadingDraftArticle(true);
      session
        .getManager(ArticleManager)
        .getDraftArticle(nid)
        .then(res => {
          // TODO: We need to load the draft article into the components when available
          // We should also probably add an indicator if the current draft is the same as the published version
          // Would be cool to use this to show diffs: https://github.com/praneshr/react-diff-viewer
          // console.log("ARTICLE DRAFT: ", res, articleData)
          if (res?.ok) {
            setDraftArticle(res.ok);
            setArticleData(res.ok as unknown as ArticleData);
          }
        })
        .finally(() => setIsLoadingDraftArticle(false))
        .catch(() => setIsLoadingDraftArticle(false));
    }
  }, [article, draftArticle, isBenzingaContributor, isEditor, nid, session, sessionLoaded]);

  const handleLogin = React.useCallback(() => {
    console.log('LOGGED IN');
  }, []);

  if (article && comtexPublishers.includes(article?.type)) {
    return (<Error statusCode={410} />) as React.ReactElement;
  }

  if ((isEditor || isBenzingaContributor) && !articleData?.nodeId) {
    const loginBox = (
      <StyledLoadingDraft className="login-box">
        <h2>Log in to Preview Draft</h2>
        <AuthContainer authMode="login" onLogin={handleLogin} placement="contributor-login" />
      </StyledLoadingDraft>
    );

    if (isLoadingDraftArticle) {
      return <LoadingDraftScreen title="Loading draft" />;
    } else if (isLoggedIn && draftArticle) {
      return <LoadingDraftScreen title="Checking permissions" />;
    } else if (!isLoggedIn) {
      return loginBox;
    }

    return <Error statusCode={errorCode ?? 404} />;
  }

  if (!articleData) {
    return (<Error statusCode={errorCode ?? 404} />) as React.ReactElement;
  }

  //console.log('Completing build for article page: ' + nid);

  return (
    <>
      {/* {!hasAdLight && enableConnatixScript && <ConnatixInitialHeadScript />} */}
      {enablePlaystreamIndiaScript ? <PlayStreamScript scriptType="article" /> : null}
      {/*{isIndiaApp ? <PlayStreamScript scriptType="article" /> : null}*/}
      <ErrorBoundary name="article-layout">
        <Suspense fallback={<h1>Loading Article...</h1>}>
          <ArticleLayout
            {...{
              articleData,
              articleIndex: 0,
              articleScrollViewMoreLink,
              baseUrl,
              campaignSettings,
              campaignStrategy,
              campaigns,
              deviceType: deviceType || null,
              disablePartnerAdOnScroll,
              disablePaywall,
              disableWNSTNWidget,
              executePageviewEvent: false,
              executeReplaceHistory: false,
              featuredTickers,
              followUpQuestions,
              googleNewsUrlKey,
              hasAdLight,
              hideSidebar,
              hideTaboola,
              hideTopPanel,
              isBenzingaContributor,
              isBot,
              isDraft: !!draftArticle,
              isEditor,
              isTaggedPressRelease,
              isTemplate,
              layout,
              loadInfiniteArticles: loadInfiniteArticles,
              loadMoreButtonVariant,
              postedInVariant,
              pressReleasesByAuthor,
              rankingData,
              raptiveEnabled,
              shouldRenderBottomTaboola,
              showApiText,
              showCommentButton,
              showFontAwesomeIcons,
              showPartnerAd: true,
              showWhatsAppIcon,
              taboolaSettings,
              useNewTemplate,
              wordCount: wordCount ?? undefined,
            }}
          />
        </Suspense>
      </ErrorBoundary>
      {/* {!isBot && loadInfiniteArticles && (
        <React.Suspense fallback={<div />}>
          <ArticleInfiniteScrollStories articleData={articleData} showCommentButton={showCommentButton} />
        </React.Suspense>
      )} */}
    </>
  );
};
