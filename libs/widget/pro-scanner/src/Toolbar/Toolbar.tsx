'use client';
import React from 'react';
import { SessionContext } from '@benzinga/session-context';
import { Dropdown, Menu, Popover, Button } from 'antd';
import { DownOutlined, ReloadOutlined, PauseCircleOutlined, PlaySquareOutlined } from '@ant-design/icons';
import { REFRESH_INTERVALS, ScannerSearchItem } from '@benzinga/scanner-config-manager';
import styled, { TC } from '@benzinga/themetron';
import { SettingsModal } from '../SettingsModal';
import { TrackingManager } from '@benzinga/tracking-manager';
import { PermissionedComponent } from '@benzinga/user-context';
import { SearchBar } from './searchBar';
import { ScannerWidgetManifest } from '../widget';
import { useWidgetParameters } from '@benzinga/widget-tools';
import { ScannerConfigSelector } from './configSelector';
import { Setting<PERSON>, Lock } from '@benzinga/themed-icons';
import { useScannerFeedContext } from '../ScannerContext';

const Toolbar: React.FC<{
  onPauseClick: () => void;
  onRefresh?: (params: { key: string }) => void;
  paused: boolean;
  isScreener?: boolean;
}> = React.memo(props => {
  const session = React.useContext(SessionContext);
  const scanner = useScannerFeedContext();
  const widgetParams = useWidgetParameters(ScannerWidgetManifest);
  const scannerConfig = widgetParams.parameters.config;
  const setScannerConfig = widgetParams.setter.config;

  const menu = React.useMemo(
    () => (
      <Menu
        items={REFRESH_INTERVALS.map(interval => {
          return {
            key: interval.key,
            label:
              scannerConfig.refreshInterval !== interval.key ? (
                <P>{interval.label}</P>
              ) : (
                <SelectedDiv>{interval.label}</SelectedDiv>
              ),
          };
        })}
        onClick={props.onRefresh}
      />
    ),
    [props.onRefresh, scannerConfig.refreshInterval],
  );

  const handleRefreshClick = () => {
    session.getManager(TrackingManager).trackWidgetEvent('refresh_data', 'scanner_widget', {});
    scanner.refresh();
  };

  const [settingsVisible, setSettingsVisible] = React.useState(false);

  const showSettings = React.useCallback(() => {
    setSettingsVisible(true);
  }, []);

  const dontShowSettings = React.useCallback(() => {
    setSettingsVisible(false);
  }, []);

  const changeTags = React.useCallback(
    (tags: ScannerSearchItem[]) => {
      setScannerConfig(config => ({ ...config, tags }));
    },
    [setScannerConfig],
  );

  return (
    <>
      <MenuCt style={React.useMemo(() => ({ display: props.isScreener ? 'none' : 'flex' }), [props.isScreener])}>
        <SearchBar changeTags={changeTags} newsConfigSelector={ScannerConfigSelector} tags={scannerConfig.tags} />

        <PermissionedComponent permission={{ action: 'bzpro/scanner/open', resource: '#' }}>
          {access => (
            <Dropdown dropdownRender={() => menu} trigger={['click']}>
              <StyledButton>
                {!access && <Locked />}
                {formatRefreshLabel(scannerConfig.refreshInterval)}
                <DownOutlined />
              </StyledButton>
            </Dropdown>
          )}
        </PermissionedComponent>

        <Popover content={<Tooltip>Refresh data immediately</Tooltip>} placement="bottom">
          <StyledButton onClick={handleRefreshClick}>
            <ReloadOutlined />
          </StyledButton>
        </Popover>

        <StyledButton onClick={() => props.onPauseClick()}>
          {props.paused ? <PlaySquareOutlined /> : <PauseCircleOutlined />}
        </StyledButton>
        <StyledButton key="settings" onClick={showSettings}>
          <Settings title="Scanner Settings" />
        </StyledButton>
        {/*<UploadButton />*/}
        {/*<CopyButton settings={props.scannerConfig}/>*/}
      </MenuCt>
      {scanner && <SettingsModal onOk={dontShowSettings} visible={settingsVisible} />}
    </>
  );
});

const StyledButton = styled(Button)`
  height: 100%;
  border: none !important;
  border-right: 1px solid ${props => props.theme.colors.border} !important;
  :disabled {
    background: transparent !important;
  }
  span {
    color: ${props => props.theme.colors.foregroundInactive} !important;
  }
`;

const Tooltip = styled.div`
  padding: 5px 10px !important;
`;

const REFRESH_INTERVALS_BY_KEY = new Map(REFRESH_INTERVALS.map(f => [f.key, f]));

function formatRefreshLabel(updateInterval?: number) {
  if (!updateInterval || updateInterval === -1) {
    return 'Refresh, Never';
  } else if (updateInterval > 0) {
    return `Refresh, ${updateInterval}s`;
  } else {
    const refreshInterval = REFRESH_INTERVALS_BY_KEY.get(updateInterval);
    if (!refreshInterval) {
      console.error(`Unknown refresh interval ${updateInterval}`);
      return 'Refresh, Unknown-ERR';
    }
    return `Refresh, ${refreshInterval.label}`;
  }
}

const MenuCt = styled(TC.StretchRow)`
  display: flex;
  flex-wrap: nowrap;
  overflow: auto;
  justify-content: space-between;
  width: 100%;
`;

const P = styled.div`
  color: ${props => props.theme.colors.foreground} !important;
`;

const SelectedDiv = styled.div`
  font-weight: bold;
  padding: 0;
  margin: 0;
`;

const Locked = styled(Lock)`
  fill: ${props => props.theme.colors.accent};
  font-size: 12px;
`;

export default Toolbar;
