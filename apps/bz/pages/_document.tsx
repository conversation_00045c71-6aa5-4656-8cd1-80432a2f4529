import React from 'react';
import Document, { DocumentContext, Head, Html, Main, NextScript } from 'next/document';
import Script from 'next/script';
import { ServerStyleSheet } from 'styled-components';
import { dom } from '@fortawesome/fontawesome-svg-core';
import { BZHeader } from '@benzinga/next-utils';
import { Schema } from '@benzinga/seo';
import { ChartbeatAnalytics } from '@benzinga/analytics';

interface Props {
  hostname: string;
  nextPath: string;
  styleTags: React.ReactElement<Record<any, unknown>, string | React.JSXElementConstructor<any>>[];
}

export default class BZDocument extends Document<Props> {
  static async getInitialProps(ctx: DocumentContext) {
    const sheet = new ServerStyleSheet();
    const nextPath = ctx.asPath;
    const originalRenderPage = ctx.renderPage;

    const hostname = ctx.req?.headers.host;

    try {
      ctx.renderPage = () =>
        originalRenderPage({
          enhanceApp: App => props => sheet.collectStyles(<App {...props} />),
        });
      const initialProps = await Document.getInitialProps(ctx);
      return {
        ...initialProps,
        hostname,
        nextPath,
        styles: (
          <>
            {initialProps.styles}
            {sheet.getStyleElement()}
          </>
        ),
      };
    } finally {
      sheet.seal();
    }
  }

  render() {
    const languageFlag = this.props.__NEXT_DATA__.props.pageProps.metaProps?.hrefLanguage
      ? this.props.__NEXT_DATA__.props.pageProps.metaProps?.hrefLanguage
      : 'en';

    const tags = this.props.__NEXT_DATA__.props?.pageProps?.article?.tags || [];
    const channels = this.props.__NEXT_DATA__.props?.pageProps?.article?.channels || [];
    const authorName = this.props.__NEXT_DATA__.props?.pageProps?.article?.name || '';
    const sections = channels.concat(tags)?.map(({ name }: { name: string }) => name) || [];

    // This is UGLY. This should all be defined in the layout selected.
    if (this.props.nextPath.includes('/amp/')) {
      return (
        <Html lang={languageFlag}>
          <Head></Head>
          <body>
            <Main />
            <NextScript />
          </body>
        </Html>
      );
    } else {
      return (
        <Html lang={languageFlag}>
          <Head>
            <BZHeader locale={this.props.__NEXT_DATA__.props.pageProps.metaProps?.language} />
            {this.props.styleTags}
            {(this.props.__NEXT_DATA__.props.pageProps.metaProps?.hrefLangs || [])
              .filter(link => {
                const langs = ['x-default', 'en'];
                if (languageFlag !== 'en') langs.push(languageFlag);
                return langs.includes(link.hrefLang);
              })
              .map(link => (
                <link href={link.href} hrefLang={link.hrefLang} key={link.hrefLang} rel="alternate" />
              ))}

            <link crossOrigin="use-credentials" href="https://accounts.benzinga.com" rel="preconnect" />
            <link href="https://cdn.benzinga.com" rel="preconnect" />
            <link crossOrigin="use-credentials" href="https://cdn.segment.com" rel="preconnect" />
            <link href="https://ads.adthrive.com" rel="dns-prefetch" />
            <link href="https://data-api-next.benzinga.com" rel="dns-prefetch" />
            <link href="https://www.googletagservices.com" rel="dns-prefetch" />
            <link href="https://www.googletagmanager.com" rel="dns-prefetch" />
            <link href="https://securepubads.g.doubleclick.net/tag/js/gpt.js" rel="dns-prefetch" />
            <link as="script" href="https://securepubads.g.doubleclick.net/tag/js/gpt.js" rel="preload" />
            <link as="script" href="https://www.google-analytics.com/analytics.js" rel="preload" />
            <style>{dom.css()}</style>
            <ChartbeatAnalytics
              authorName={authorName}
              chartBeatDomain={'benzinga.com'}
              sections={sections}
              uid={process.env.CHARTBEAT_KEY as string}
            />
            <script key="windowOneSignal" type="text/javascript">
              {`window.OneSignal = window.OneSignal || [];`}
            </script>
            {/* <script
              async
              crossOrigin="anonymous"
              src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-0540403401982416"
            ></script> */}
            {/* Disabling this as it causes flicker and click (search) issues with the site */}
            {/* <link as="script" href="https://static.chartbeat.com/js/chartbeat_mab.js" rel="preload" />
            <Script
              async={true}
              id="chartbeatMAB"
              src="https://static.chartbeat.com/js/chartbeat_mab.js"
              strategy="beforeInteractive"
            ></Script> */}
          </Head>
          <body>
            <Schema data={this.props.__NEXT_DATA__.props.pageProps?.metaProps?.schema} name="newsArticle-schema" />
            <Main />
            <NextScript />
          </body>
        </Html>
      );
    }
  }
}
