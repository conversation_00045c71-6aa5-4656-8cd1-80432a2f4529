import { PageType } from '@benzinga/seo';
import { Meta } from '@benzinga/seo';
//import { RegisterDFPManager } from '../_components/meta/DFPManager';

const metaInfo = () => {
  const canonical = `https://www.benzinga.com/forex-profit-calculator`;
  return {
    author: '<PERSON><PERSON>',
    canonical,
    description:
      "Benzinga's forex profit calculator can help you calculate your possible gains and losses based on the result of the trade.",
    dimensions: {
      authorName: 'Benz<PERSON>',
      channels: ['Forex'],
      contentType: 'tool',
    },
    image:
      'https://cdn.benzinga.com/files/imagecache/bz2_opengraph_meta_image_400x300/sites/all/themes/bz2/images/bz-icon.png',
    pageType: PageType.Tool,
    title: 'Forex Profit Calculator',
  };
};

export const PageMeta: React.FC<any> = () => {
  return (
    <>
      {/* <RegisterDFPManager targetingArguments={{ BZ_CHANNEL: ['Forex'], BZ_PTYPE: 'tool' }} /> */}
      <Meta {...metaInfo()} />
    </>
  );
};
