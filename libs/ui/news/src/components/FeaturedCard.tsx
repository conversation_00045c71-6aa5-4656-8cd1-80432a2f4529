import React from 'react';
import { StoryObject } from '@benzinga/advanced-news-manager';
import { News } from '@benzinga/basic-news-manager';
import { appEnvironment, appName } from '@benzinga/utils';
import { getSponsoredContentLabel } from '@benzinga/article-manager';
import { PostCard, PostCardLayout, PostCardSize, PostCardProps } from './PostCard';
import type { FetchPriority, LoadingValue, PreloadOptions } from '@benzinga/image';
import styled from '@benzinga/themetron';

interface FeaturedCardProps extends Partial<PostCardProps> {
  layout?: PostCardLayout;
  loading?: LoadingValue;
  node: StoryObject | News;
  size?: PostCardSize;
  sizes?: string;
  relativePath?: boolean;
  imageWidth?: number;
  preloadOptions?: PreloadOptions;
  fetchPriority?: FetchPriority;
}

export const FeaturedCard = ({
  fetchPriority,
  hidePublishDate = false,
  imagePreload = false,
  imageWidth,
  layout = 'default',
  loading,
  node,
  preloadOptions,
  relativePath,
  showCommentsIcon,
  size = 'default',
  sizes,
}: FeaturedCardProps) => {
  if (!node) return <div />;

  const sponsoredData = getSponsoredContentLabel(node);

  const nodeUrl = relativePath
    ? node.url?.replace('https://www.benzinga.com/', '/')
    : appEnvironment().isApp(appName.india)
      ? node.url?.replace('https://www.benzinga.com/', `${appEnvironment().config().url}/`)
      : node.url;

  const created = 'createdAt' in node ? node.createdAt.toString() : node.created;
  const description = 'teaserText' in node ? node.teaserText : node.teaser;
  return (
    <Container data-action="Featured Card Click">
      <PostCard
        created={created}
        description={description ?? ''}
        fetchPriority={fetchPriority}
        hidePublishDate={hidePublishDate}
        id={node.id}
        image={node.image as string}
        imagePreload={imagePreload}
        imageWidth={imageWidth}
        isOnlyProPost={!(node as StoryObject).isBzPost && (node as StoryObject).isBzProPost}
        layout={layout}
        loading={loading}
        preloadOptions={preloadOptions}
        showCommentsIcon={showCommentsIcon && !sponsoredData.isSponsored}
        size={size}
        sizes={sizes}
        sponsored={sponsoredData.isSponsored}
        sponsoredLabel={sponsoredData.label}
        stocks={node.stocks}
        styles={{ titleLineHeight: 'snug' }}
        title={node.title}
        url={nodeUrl}
      />
    </Container>
  );
};

const Container = styled.div`
  @media screen and (max-width: 800px) {
    .post-title {
      margin-top: -4px;
    }
  }
`;
