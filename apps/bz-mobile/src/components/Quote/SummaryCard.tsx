import React, { useCallback, useMemo, useState } from 'react';

import { StyleProp, StyleSheet, Text, View, ViewStyle } from 'react-native';
import { formatPrice, formatPercent } from '../../services/format';
import { variables, changeColor } from '../../constants/Styles';
import { changeIcon } from '../../services/quote';

import { Price } from '../../components/Quote/Price';
import Data from '../../data-mobile/data';
import { useTheme } from '../../theme/themecontext';
import CryptoSubscriptedName from '../CryptoSubscriptedName';
// eslint-disable-next-line @nx/enforce-module-boundaries
import Hooks from '@benzinga/hooks';
import { QuoteEvent, QuoteFeedEvent } from '@benzinga/quotes-manager';

interface QuoteSummaryCardProps {
  backgroundStyle: StyleProp<ViewStyle>;
  style: StyleProp<ViewStyle>;
  symbol: string;
}

const QuoteSummaryCard = ({ backgroundStyle, style, symbol }: QuoteSummaryCardProps) => {
  const getQuoteFeed = useCallback(() => {
    return Data.quotes().createQuoteFeed(symbol);
  }, [symbol]);

  const [, quoteFeed] = Hooks.useSubscriber(getQuoteFeed(), (event: QuoteFeedEvent) => {
    setQuote((event as QuoteEvent).quote);
  });

  const [quote, setQuote] = useState(quoteFeed?.getQuote());
  const detail = useMemo(() => quoteFeed?.getDetail(), [quoteFeed]);
  const { colors } = useTheme();

  return (
    <View style={[styles.quote, backgroundStyle]}>
      <View style={[styles.quotePrimaryInfo, style]}>
        <View style={styles.quoteInfoView}>
          <View style={styles.twoColumn}>
            <CryptoSubscriptedName symbol={symbol} textStyle={styles.quoteSymbol} />
            {quote ? <Price price={quote.lastTradePrice as number} style={styles.quotePrice} /> : <View />}
          </View>
          {detail ? (
            <Text
              numberOfLines={2}
              style={[
                styles.quoteName,
                {
                  color: colors.dimmedText,
                },
              ]}
            >
              {detail.companyName}
            </Text>
          ) : (
            <View />
          )}
        </View>
        {quote ? (
          <View style={[styles.quotePricingView]}>
            <View style={styles.quoteChange as StyleProp<ViewStyle>}>
              <View style={styles.twoColumn}>
                <Text
                  style={[
                    {
                      color: changeColor(quote.percentChange),
                    },
                    styles.quoteFormat,
                  ]}
                >
                  {quote.percentChange ? formatPercent(quote.percentChange) : '—'}%
                </Text>
                <Text
                  style={[
                    {
                      color: changeColor(quote.percentChange),
                    },
                    styles.quoteFormat,
                  ]}
                >
                  {changeIcon(quote.percentChange)}
                  {quote.change ? `$${formatPrice(quote.change)}` : ''}
                </Text>
              </View>
            </View>
          </View>
        ) : (
          <View />
        )}
      </View>
    </View>
  );
};

export default QuoteSummaryCard;

const styles = StyleSheet.create({
  quote: {
    // width: 156,
    marginBottom: 16,

    minWidth: 156,
    shadowColor: variables.colors.black,
    shadowOffset: { height: 1, width: 0 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },

  quoteAction: {
    flex: 0,
    display: 'flex',
    padding: 16,
    flexDirection: 'column',
    paddingLeft: 8,
    justifyContent: 'space-around',
  },

  quoteDetailedInfo: {
    padding: 8,
  },

  quoteInfoView: {
    flex: 1,
    maxWidth: '100%',
    padding: 8,
    paddingTop: 2,
  },

  quoteChange: {
    fontFamily: variables.fonts.graphikMedium,
    textAlign: 'right',
  },
  quoteName: {
    // width: '100%',
    color: '#555',

    fontSize: 12,

    // marginTop: 4,
    height: 30,
    maxWidth: 150,
  },
  quoteFormat: {
    fontFamily: variables.fonts.graphikMedium,
    fontSize: 13.5,
  },
  row: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quotePrice: {
    fontFamily: variables.fonts.graphikSemiBold,
    alignSelf: 'flex-end',
    fontSize: 16,
    textAlign: 'right',
  },
  super: {
    fontSize: 10,
    fontFamily: variables.fonts.graphikLight,
    lineHeight: 22 * 1.1,
    textAlignVertical: 'top',
  },
  quotePricingView: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    padding: 8,
  },
  superPercent: {
    // fontSize:10, lineHeight:22 * 1.1, textAlignVertical: 'top',
    // fontFamily: variables.fonts.graphikLight,
  },
  quotePrimaryInfo: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: 'white',
  },
  twoColumn: {
    display: 'flex',
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
  },

  quoteSymbol: {
    fontSize: 20,
    marginRight: 10,
  },
});
