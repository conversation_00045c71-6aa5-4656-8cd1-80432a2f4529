import { Session } from '@benzinga/session';
import { Subscribable, SubscribableEventType, Subscription } from '@benzinga/subscribable';
import { RefreshInterval, ScannerConfig } from '@benzinga/scanner-config-manager';
import { ScannerSocket } from './socket';
import { QuoteProtos, ScannerProtos } from './scanner_protos';
import { configToQuery } from './utils';
import { delta, getNextQuotes } from './compare';
import { ScannerRequest } from './request';
import { QuotesV3FieldsManager } from '@benzinga/quotes-v3-fields-manager';
import { oneRequestAtATime } from '@benzinga/utils';

interface ScannerFeedRowUpdateEvent {
  rows: QuoteProtos.IQuote[];
  config: ScannerConfig;
  totalInstruments: number;
  transactions: ScannerProtos.IRowUpdatesV2;
  type: 'data_update';
}

export type ScannerFeedEvent =
  | ScannerFeedRowUpdateEvent
  | Exclude<SubscribableEventType<ScannerSocket>, { type: 'row_update' }>;

export class ScannerFeed extends Subscribable<ScannerFeedEvent> {
  private config: ScannerConfig;
  private session: Session;
  private request: ScannerRequest;

  private socket: ScannerSocket;
  private socketSubscription?: Subscription<ScannerSocket>;
  private refreshTimer?: ReturnType<typeof setInterval>;

  private getQuotesOneAtATime = oneRequestAtATime(() => this.getQuotes());
  private previousQuotes: QuoteProtos.IQuote[] = [];
  private fireOnZeroSubscription: () => void;

  constructor(session: Session, config: ScannerConfig, request: ScannerRequest, onZeroSubscriptions: () => void) {
    super();
    this.config = config;
    this.session = session;
    this.request = request;
    this.fireOnZeroSubscription = onZeroSubscriptions;
    this.socket = new ScannerSocket(session, {
      query: configToQuery(config, session).ok ?? { limit: 1 },
      updateType: ScannerProtos.ScannerConfig.UpdateType.SNAPSHOT_DELTAS,
    });

    const sub = this.session.getManager(QuotesV3FieldsManager).subscribe(event => {
      switch (event.type) {
        case 'quotes_fields:update':
          this.socket = new ScannerSocket(session, {
            query: configToQuery(config, this.session).ok ?? { limit: 1 },
            updateType: ScannerProtos.ScannerConfig.UpdateType.SNAPSHOT_DELTAS,
          });
          if (this.socketSubscription) {
            this.socketSubscription.unsubscribe();
            this.socketSubscription = this.socket.subscribe(this.onScannerMessage);
          }
          this.refresh();
      }
    });

    session.onPreSessionClose(() => sub.unsubscribe());
  }

  public refresh = () => {
    switch (this.config.refreshInterval) {
      case RefreshInterval['Real-time']:
        break;
      case RefreshInterval['Never']:
      case RefreshInterval['10s']:
      case RefreshInterval['1m']:
      case RefreshInterval['30s']:
      default:
        this.getQuotesOneAtATime();
        break;
    }
  };

  public getConfig = () => this.config;
  public getPrevious = () => this.previousQuotes;

  protected onFirstSubscription() {
    switch (this.config.refreshInterval) {
      case RefreshInterval['Real-time']:
        this.socketSubscription = this.socket.subscribe(this.onScannerMessage);
        break;
      case RefreshInterval['Never']:
        this.getQuotesOneAtATime();
        break;
      case RefreshInterval['10s']:
      case RefreshInterval['1m']:
      case RefreshInterval['30s']:
      default:
        this.getQuotesOneAtATime();
        this.refreshTimer = setInterval(() => this.getQuotesOneAtATime(), this.config.refreshInterval * 1000);
        break;
    }
  }

  protected onZeroSubscriptions() {
    this.socketSubscription?.unsubscribe();
    this.refreshTimer && clearInterval(this.refreshTimer);
    this.previousQuotes = [];
    this.fireOnZeroSubscription();
  }

  private onScannerMessage = (event: SubscribableEventType<ScannerSocket>) => {
    switch (event.type) {
      case 'on-status-change':
        this.dispatch(event);
        if (event.status === 'reconnected') {
          // reset the previous quotes if we reconnect the socket
          // socket will send us the full snapshot at the start
          const rows_update: ScannerProtos.IRowUpdatesV2 = {
            removes: this.previousQuotes.map((_, idx) => ({ previousIndex: idx })),
            total: 0,
          };
          this.previousQuotes = [];
          this.dispatch({
            config: this.config,
            rows: this.previousQuotes,
            totalInstruments: 0,
            transactions: rows_update,
            type: 'data_update',
          });
        }
        break;
      case 'row_update':
        this.previousQuotes = getNextQuotes(this.previousQuotes, event.rows);
        this.dispatch({
          config: this.config,
          rows: this.previousQuotes,
          totalInstruments: event.rows.total ?? 0,
          transactions: event.rows,
          type: 'data_update',
        });
        break;
      default:
        this.dispatch(event);
        break;
    }
  };

  private getQuotes = async () => {
    const quotes = await this.request.getScannerInstruments(this.config);
    if (quotes.ok) {
      const prevQuotes = this.previousQuotes;
      this.previousQuotes = quotes.ok.instruments ?? [];
      this.dispatch({
        config: this.config,
        rows: this.previousQuotes,
        totalInstruments: quotes.ok.totalInstruments ?? 0,
        transactions: delta(prevQuotes, this.previousQuotes, this.session),
        type: 'data_update',
      });
    }
  };
}
