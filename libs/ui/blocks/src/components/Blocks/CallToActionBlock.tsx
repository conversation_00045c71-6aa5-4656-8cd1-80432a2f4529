import React from 'react';
import { CallToAction, CallToActionProps } from '@benzinga/ui';
import { extractImageSize } from '@benzinga/image';
import styled from '@benzinga/themetron';
import { AttrsData } from '@benzinga/content-manager';

const CallToActionForm = React.lazy(() =>
  import('@benzinga/forms-ui').then(module => {
    return { default: module.CallToActionForm };
  }),
);

export interface CallToActionBlockProps {
  attrs: any;
  campaign: any;
}

export const CallToActionBlock: React.FC<CallToActionBlockProps> = ({ attrs, campaign }) => {
  const getMediaTypeData = (type: string, data: AttrsData) => {
    if (type === 'video') {
      return {
        buttonText: data?.cta_button_text || '',
        hubspotFormId: '',
        subtitle: data?.cta_description || '',
        title: data?.cta_title || '',
        videoID: data?.video_url || '',
      };
    }
    return;
  };

  const renderOtherActions = () => {
    if (attrs?.data?.media_type) {
      const mediaData = getMediaTypeData(attrs?.data?.media_type, attrs.data);
      if (!mediaData) return null;
      return (
        <CallToActionWrapper>
          <CallToActionForm {...mediaData} />
        </CallToActionWrapper>
      );
    }
    return null;
  };

  if (!campaign) return renderOtherActions();

  const image = extractImageSize(campaign?.image);

  return (
    <CallToAction
      button_text={campaign.button_text}
      description={campaign.description}
      disclosure={attrs?.data?.disclosure ?? campaign?.disclosure ?? ''}
      id={campaign.id}
      image_url={image?.url}
      image_width={campaign.image_width}
      layout={attrs?.data?.display}
      title={campaign.title}
      url={campaign.link}
    />
  );
};

const CallToActionWrapper = styled.div`
  .cta-link-button {
    color: #ffffff;
  }
`;

export const CallToActionElement: React.FC<CallToActionProps> = props => {
  return <CallToAction {...props} />;
};
