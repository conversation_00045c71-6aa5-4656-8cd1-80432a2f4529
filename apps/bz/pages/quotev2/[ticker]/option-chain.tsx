import styled from 'styled-components';
import i18n, { LocaleType, setLanguageByHost } from '@benzinga/translate';
import getQuoteV2PageData, { getQuoteFeedTypeByLocale, QuoteV2PageProps } from '../../../src/quoteV2Utils';
import { QuoteLayout } from '../../../src/components/Quote/V2/QuoteLayout';
import OptionChainTable from '../../../src/components/Quote/optionChain/OptionChainTable';
import OptionChainFilter from '../../../src/components/Quote/optionChain/OptionChainFilter';
import { getOptionChain } from '../../api/quote/[ticker]/option-chain';
import { organizeOptionDataReturn } from '@benzinga/data-option-chain';
import { useTranslation } from 'react-i18next';
import { SectionTitle } from '@benzinga/core-ui';
import { useOptionChain } from '@benzinga/hooks';
import OptionTabs from '../../../src/components/Quote/optionChain/OptionTabs';
import { sanitizeHTML } from '@benzinga/frontend-utils';

const OptionChainRenderer = ({
  metaProps,
  optionChainData,
  profile,
  symbol,
}: Pick<QuoteV2PageProps, 'profile' | 'symbol' | 'metaProps'> & { optionChainData: organizeOptionDataReturn }) => {
  const stockPrice = profile?.richQuoteData?.ethPrice;

  const { expiries, filteredData, initialRange, moneyness, onExpiryChange, onMoneynessChange, onRangeChange, range } =
    useOptionChain({ optionChainData, stockPrice, symbol });

  const { companyStandardName } = profile?.richQuoteData || '';
  const { t } = useTranslation('quote', { i18n });

  const showSymbolDetail = true;

  return (
    <>
      <OptionTabs companyStandardName={companyStandardName} symbol={symbol} />

      <div className="main-div">
        <OptionChainHeader>
          {showSymbolDetail && (
            <div className="symbol-detail">
              <div>
                <div className="flex flex-col gap-2">
                  <SectionTitle level={1} uppercase={false}>
                    {metaProps.title}
                  </SectionTitle>
                  <p className="section-description">{metaProps.description}</p>
                </div>
              </div>
            </div>
          )}
          <div className="left-side-header-part">
            <span>{t('Quote.OptionChain.in-the-money')}</span>
          </div>
        </OptionChainHeader>
        <OptionChainFilter
          expiries={expiries}
          moneyNess={moneyness}
          onExpiryChange={onExpiryChange}
          onMoneynessChange={onMoneynessChange}
          onRangeChange={onRangeChange}
          range={range}
          rangeLimits={initialRange}
        />
        <OptionChainTable data={filteredData} />
      </div>
    </>
  );
};

const OptionChainPage: React.FC<QuoteV2PageProps> = ({
  activeTab,
  advertiserProfile,
  isAdvertiser,
  metaProps,
  optionChainData,
  profile,
  showGovLink,
  symbol,
}) => {
  return (
    <QuoteLayout
      advertiserProfile={advertiserProfile}
      defaultPath={activeTab}
      isAdvertiser={isAdvertiser}
      profile={profile}
      showGovLink={showGovLink}
      symbol={symbol}
    >
      {optionChainData && optionChainData.expiries?.length ? (
        <OptionChainRenderer
          metaProps={metaProps}
          optionChainData={optionChainData}
          profile={profile}
          symbol={symbol}
        />
      ) : (
        <NoDataContainer>
          <h2 className="noDataText">No option chain data available. Please try again later</h2>
        </NoDataContainer>
      )}
    </QuoteLayout>
  );
};

export const getServerSideProps = async ({ query: { ticker }, req, res }) => {
  if (req?.headers?.host) {
    await setLanguageByHost(req?.headers?.host); // set language for server side translations
  }

  const symbol: string = sanitizeHTML(ticker) || '';
  const activeTab = 'option-chain';
  const pageData = await getQuoteV2PageData(
    symbol,
    activeTab,
    getQuoteFeedTypeByLocale(i18n.language as LocaleType),
    req?.headers?.host,
  );

  if (pageData?.props?.errorCode) {
    res.statusCode = pageData.props.errorCode;
    return pageData;
  }

  const optionChainResp = (await getOptionChain(symbol)) || [];

  if (optionChainResp.ok) {
    if ('props' in pageData) {
      return {
        props: {
          ...pageData.props,
          optionChainData: optionChainResp.ok,
        },
      };
    }
  }

  return pageData;
};
export default OptionChainPage;

const NoDataContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  padding: 0 1rem;
  width: 100%;
  min-height: 300px;
  & > .noDataText {
    padding: 1rem;
    color: ${({ theme }) => theme.colorPalette.gray500};
  }
`;

const OptionChainHeader = styled.div`
  padding: 1rem 0;
  display: flex;
  justify-content: space-between;
  width: 100%;
  border-bottom: 1px solid ${({ theme }) => theme.colorPalette.gray100};

  .symbol-detail {
    display: flex;
    flex-direction: column;
    div {
      display: flex;
      justify-content: flex-start;

      h2 {
        color: ${({ theme }) => theme.colorPalette.gray700};
        font-size: 20px;
        font-style: normal;
        font-weight: 700;
        line-height: 32px;
      }
      span {
        border-radius: 4px;
        background: ${({ theme }) => theme.colorPalette.gray50};
        color: ${({ theme }) => theme.colorPalette.bzGrayDarkTone10};
        font-size: 14px;
        font-style: normal;
        font-weight: 700;
        line-height: 32px; /* 228.571% */

        padding: 0px 6px;
      }
    }
  }

  .left-side-header-part {
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    flex: 1 1 0%;
    white-space: nowrap;

    span {
      padding: 0px 8px;
      border-radius: 3px 0px 0px 3px;
      border-left: 3px solid ${({ theme }) => theme.colorPalette.blue500};
      background: var(--Blue-05, rgba(63, 131, 248, 0.05));
      color: ${({ theme }) => theme.colorPalette.gray700};
      font-size: 14px;
      font-style: normal;
      font-weight: 700;
      line-height: 24px;
    }
  }

  @media screen and (max-width: 800px) {
    grid-template-columns: 1fr;

    .symbol-detail {
      div {
        flex-direction: column;
        width: fit-content;
      }
    }
    .left-side-header-part {
      justify-content: flex-start;
      margin-top: 8px;
    }
  }
`;
