/* eslint-disable @next/next/no-html-link-for-pages */
import React from 'react';

import styled from '@benzinga/themetron';
import { SectionTitle } from '@benzinga/core-ui';

import TradeIdeas from '../../../src/components/TradeIdeas';
import { QuoteLayout } from '../../../src/components/Quote/V2/QuoteLayout';
import getQuoteV2PageData, { QuoteV2PageProps, getQuoteFeedTypeByLocale } from '../../../src/quoteV2Utils';
import i18n, { LocaleType, setLanguageByHost } from '@benzinga/translate';
import { QuoteAltSuggestions } from '../../../src/components/Quote/QuoteAltSuggestions';
import { sanitizeHTML } from '@benzinga/frontend-utils';

const QuoteIdeasPage: React.FC<QuoteV2PageProps> = ({
  activeTab,
  advertiserProfile,
  altSuggestions,
  isAdvertiser,
  metaProps,
  profile,
  showGovLink,
  symbol,
}) => {
  if (altSuggestions) {
    return <QuoteAltSuggestions {...altSuggestions} symbol={symbol} />;
  }

  return (
    <QuoteLayout
      advertiserProfile={advertiserProfile}
      defaultPath={activeTab}
      isAdvertiser={isAdvertiser}
      profile={profile}
      showGovLink={showGovLink}
      symbol={symbol}
    >
      {/* Beef this up. */}
      <QuoteIdeasPageWrapper className="main-div pt-0 p-4">
        <div className="flex flex-col gap-2">
          <SectionTitle level={1} uppercase={false}>
            {metaProps.title}
          </SectionTitle>
          <p className="section-description">{metaProps.description}</p>
        </div>
        <TradeIdeas symbol={symbol} />
      </QuoteIdeasPageWrapper>
    </QuoteLayout>
  );
};

export default QuoteIdeasPage;

export const getServerSideProps = async ({ query: { ticker }, req, res }) => {
  if (req?.headers?.host) {
    await setLanguageByHost(req?.headers?.host); // set language for server side translations
  }

  const symbol: string = sanitizeHTML(ticker) || '';
  const activeTab = 'ideas';
  const pageData = await getQuoteV2PageData(
    symbol,
    activeTab,
    getQuoteFeedTypeByLocale(i18n.language as LocaleType),
    req?.headers?.host,
  );

  if (pageData?.props?.errorCode) {
    res.statusCode = pageData.props.errorCode;
    return pageData;
  }

  return pageData;
};

const QuoteIdeasPageWrapper = styled.div`
  .trade-idea-portal-container {
    margin-top: 0px !important;
  }
`;
