import React from 'react';

import { SectionTitle } from '@benzinga/core-ui';
import { Ticker, TickerLink } from '@benzinga/ticker-ui';

import { getQuoteData } from '../../api/quote';
import { QuoteLayout } from '../../../src/components/Quote/V2/QuoteLayout';
import ETFHoldingsSummaryBoxes from '../../../src/components/Quote/V2/ETFHoldingsSummaryBoxes';
import ETFFAQ from '../../../src/components/Quote/FAQ/ETFFAQ';
import DrilldownFooter from '../../../src/components/Quote/V2/DrilldownFooter';
import getQuoteV2PageData, {
  QuoteV2PageProps,
  getQuoteFeedTypeByLocale,
  getQuoteNotFoundData,
} from '../../../src/quoteV2Utils';
import { Table } from '@benzinga/table';
import { useTranslation } from 'react-i18next';
import i18n, { LocaleType, setLanguageByHost } from '@benzinga/translate';
import { QuoteAltSuggestions } from '../../../src/components/Quote/QuoteAltSuggestions';
import { sanitizeHTML } from '@benzinga/frontend-utils';

const QuoteGuidancePage: React.FC<QuoteV2PageProps> = ({
  activeTab,
  advertiserProfile,
  altSuggestions,
  isAdvertiser,
  metaProps,
  peersChartData,
  profile,
  showGovLink,
  symbol,
}) => {
  const { t } = useTranslation('quote', { i18n });

  if (altSuggestions) {
    return <QuoteAltSuggestions {...altSuggestions} symbol={symbol} />;
  }

  const fundHolders = profile?.etfFund?.fund_top_holders || [];

  return (
    <QuoteLayout
      advertiserProfile={advertiserProfile}
      defaultPath={activeTab}
      isAdvertiser={isAdvertiser}
      profile={profile}
      showGovLink={showGovLink}
      symbol={symbol}
    >
      <div className="main-div flex flex-col gap-y-8">
        {profile.etfFund && <ETFHoldingsSummaryBoxes etfFund={profile.etfFund} />}
        <div className="flex flex-col gap-2">
          <SectionTitle level={1} uppercase={false}>
            {metaProps.title}
          </SectionTitle>
          <p className="section-description">{metaProps.description}</p>
        </div>
        <Table columnsDef={HoldingsColumnsDef} rowData={fundHolders} />
        <DrilldownFooter
          advertiserProfile={advertiserProfile}
          isAdvertiser={isAdvertiser}
          isETF={true}
          leftSection={
            <>
              <SectionTitle level={3} size="2xl">
                {t('Quote.FAQ.faq')}
              </SectionTitle>
              <ETFFAQ profile={profile} />
            </>
          }
          peers={profile.quotes}
          peersChartData={peersChartData}
          symbol={symbol}
          type={profile.schedule.type}
        />
      </div>
    </QuoteLayout>
  );
};

interface Holding {
  symbol: string;
  name: string;
  sharepercentage: number;
}

const HoldingsColumnsDef = [
  {
    cellRenderer: ({ data }: { data: Holding }) => {
      return (
        <Ticker symbol={data.symbol} targetElement={<TickerLink symbol={data.symbol}>{data.symbol}</TickerLink>} />
      );
    },
    colSpan: 1,
    field: 'symbol',
    headerName: 'Ticker',
  },
  {
    colSpan: 2,
    field: 'name',
    headerName: 'Name',
    valueFormatter: ({ data }: { data: Holding }) => {
      return data.name || '—';
    },
  },
  {
    colSpan: 1,
    field: 'sharepercentage',
    headerName: 'Share %',
    valueFormatter: ({ data }: { data: Holding }) => {
      return Number(data.sharepercentage) ? `${Number(data.sharepercentage).toFixed(2)}%` : '—';
    },
  },
];

export default QuoteGuidancePage;

export const getServerSideProps = async ({ query: { ticker }, req, res }) => {
  if (req?.headers?.host) {
    setLanguageByHost(req?.headers?.host); // set language for server side translations
  }

  const symbol: string = sanitizeHTML(ticker) || '';
  const activeTab = 'holdings';

  const pageData = await getQuoteV2PageData(
    symbol,
    activeTab,
    getQuoteFeedTypeByLocale(i18n.language as LocaleType),
    req?.headers?.host,
  );

  if (!pageData || pageData?.props?.errorCode) {
    res.statusCode = pageData?.props?.errorCode || 503;
  }

  return pageData;
};
