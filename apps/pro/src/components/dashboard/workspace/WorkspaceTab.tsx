import { equals } from 'ramda';
import React, { ChangeEvent, KeyboardEvent, MouseEvent, useRef, useEffect } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { ThunkDispatch } from 'redux-thunk';

import { Span } from '@benzinga/fission';
import styled, { css } from '@benzinga/themetron';
import classnames from 'classnames';
import { pathOr } from 'ramda';
import { isUndefined } from 'ramda-adjunct';
import { UAParser } from 'ua-parser-js';

import { changeWorkspaceName, moveWorkspaceTab, switchWorkspace } from '../workspaceActions';
import { Workspace, WorkspaceId } from '../../../components/dashboard/workspaceEntity';
import { RootAction, RootState } from '../../../redux/types';
import { selectActiveWorkspaceId, selectWorkspaceById } from '../workspaceSelectors';
import { calculateTextWidth } from '../../../utils/html';
import { useDrag, useDrop } from 'react-dnd';
import type { Identifier } from 'dnd-core';
import { selectWidgets } from '../../../selectors/widgetSelectors';
import { WidgetsById } from '../../../entities/widgetEntity';
import { calculateWorkspaceName } from '../workspaceUtils';
import { Close, Edit } from '@benzinga/valentyn-icons';
import { BZ } from '@benzinga/themed-icons';

interface WorkspaceNameProps {
  $active: boolean;
}

const WorkspaceName = styled(Span)<WorkspaceNameProps>`
  ${({ $active, theme }) => {
    return css`
      color: ${theme && theme.colors
        ? $active
          ? theme.colors.foregroundActive
          : theme.colors.foregroundActive
        : 'white'};
      font-weight: ${$active ? 500 : 300};
      text-overflow: ellipsis;
      overflow: hidden;
    `;
  }}
`;

interface OwnProps {
  active: boolean;
  editable: boolean;
  index?: number;
  isCollapsed?: boolean;
  icon?: React.ReactNode;
  isDraggable?: boolean;
  onTabClick: () => void;
  removeWorkspace: (workspaceId: WorkspaceId) => void;
  workspace: Workspace;
}

interface ReduxState {
  activeWorkspaceId: WorkspaceId;
  name: string;
  widgetsById: WidgetsById;
}
interface DispatchableActions {
  changeWorkspaceName: typeof changeWorkspaceName;
  moveWorkspaceTab: typeof moveWorkspaceTab;
  switchWorkspace: typeof switchWorkspace;
}

type Props = OwnProps & ReduxState & DispatchableActions;

interface State {
  readonly editingName: boolean;
  readonly inputWidth: string;
  readonly isDraggedOver: boolean;
  readonly workspaceName: string;
}

const WorkspaceTab: React.FC<Props> = props => {
  const nameInput = useRef<HTMLInputElement>(null);
  const ref = useRef<HTMLLIElement>(null);
  const { name: wName, workspace } = props;

  const [state, setState] = React.useState<State>({
    editingName: false,
    inputWidth: 'auto',
    isDraggedOver: false,
    workspaceName: '',
  });

  useEffect(() => {
    if (state.editingName) return;
    setState(state => ({
      ...state,
      workspaceName: calculateWorkspaceName(workspace, props.widgetsById),
    }));
  }, [wName, workspace, state.editingName, props.widgetsById]);

  const saveName = React.useCallback(() => {
    const changeWorkspaceName = props.changeWorkspaceName;
    changeWorkspaceName(props.activeWorkspaceId, state.workspaceName.trim());
    setState(preState => ({ ...preState, editingName: false }));
  }, [props.activeWorkspaceId, props.changeWorkspaceName, state.workspaceName]);

  const setWorkspaceActive = React.useCallback(() => {
    const switchWorkspace = props.switchWorkspace;
    switchWorkspace(props.workspace.workspaceId);
  }, [props.switchWorkspace, props.workspace.workspaceId]);

  const handleDrag = React.useCallback(() => {
    if (!props.active) {
      setWorkspaceActive();
    }
  }, [props.active, setWorkspaceActive]);

  React.useEffect(() => {
    if (state.editingName && nameInput.current) {
      nameInput.current.focus();
      nameInput.current.setSelectionRange(0, nameInput.current.value.length);
    }
  }, [state.editingName]);

  const switchToThisWorkspaceOnEdit = React.useCallback(
    (event: MouseEvent<HTMLButtonElement | HTMLLIElement> | KeyboardEvent<HTMLInputElement>) => {
      event.stopPropagation();
      setWorkspaceActive();
    },
    [setWorkspaceActive],
  );

  const switchToThisWorkspace = React.useCallback(() => {
    const onTabClick = props.onTabClick;
    setWorkspaceActive();
    if (props.isCollapsed) {
      onTabClick();
    }
  }, [props.isCollapsed, props.onTabClick, setWorkspaceActive]);

  const handleRemoveWorkspace = React.useCallback(
    (event: MouseEvent<HTMLButtonElement | HTMLLIElement>) => {
      const removeWorkspace = props.removeWorkspace;
      event.stopPropagation();
      removeWorkspace(workspace.workspaceId);
    },
    [props.removeWorkspace, workspace.workspaceId],
  );

  const handleDrop = React.useCallback(() => {
    const moveWorkspaceTab = props.moveWorkspaceTab;
    if (!isUndefined(props.index)) {
      setState(preState => ({ ...preState, isDraggedOver: false }));
      moveWorkspaceTab(props.index);
    }
  }, [props.index, props.moveWorkspaceTab]);

  const handleInputClick = React.useCallback(
    (e: MouseEvent<HTMLInputElement>) => {
      if (props.isCollapsed) {
        e.stopPropagation();
      }
    },
    [props.isCollapsed],
  );

  const calculateInputWidth = React.useCallback(() => {
    if (!nameInput) {
      // set a sane default incase the input isn't
      return `${state.workspaceName.length * 7}px`;
    }
    return `${nameInput.current && calculateTextWidth(nameInput.current.value, nameInput.current)}px`;
  }, [state.workspaceName.length]);

  const toggleNameEdit = React.useCallback(
    (event: MouseEvent<HTMLButtonElement | HTMLLIElement> | KeyboardEvent<HTMLInputElement>) => {
      setState(preState => ({
        ...preState,
        editingName: preState ? !preState.editingName : false,
        inputWidth: calculateInputWidth(),
        workspaceName: workspace.name,
      }));
      if (props.isCollapsed) {
        switchToThisWorkspaceOnEdit(event);
      }
    },
    [calculateInputWidth, props.isCollapsed, switchToThisWorkspaceOnEdit, workspace.name],
  );

  const handleKeyDown = React.useCallback(
    (event: KeyboardEvent<HTMLInputElement>) => {
      if (equals(event.key, 'Enter')) {
        saveName();
      }
      if (equals(event.key, 'Escape')) {
        toggleNameEdit(event);
      }
    },
    [saveName, toggleNameEdit],
  );

  const onWorkspaceNameChange = React.useCallback(
    (event: ChangeEvent<HTMLInputElement>) => {
      const workspaceName = event.currentTarget.value;
      const inputWidth = calculateInputWidth();
      setState(preState => ({ ...preState, inputWidth: inputWidth, workspaceName: workspaceName }));
    },
    [calculateInputWidth],
  );

  const tabOnMouseDown = React.useCallback(
    (event: MouseEvent<HTMLLIElement>) => {
      switch (event.button) {
        case 1: // middle click
          handleRemoveWorkspace(event);
          break;
        case 0: // left click
        case 2: // right click
          break;

        default:
          break;
      }
    },
    [handleRemoveWorkspace],
  );

  const [{ handlerId }, drop] = useDrop<void, void, { handlerId: Identifier | null }>({
    accept: 'workspace',
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      };
    },
  });

  const [{ isDragging }, drag] = useDrag({
    canDrag: props.isDraggable ?? true,
    collect: (monitor: any) => ({
      isDragging: monitor.isDragging(),
    }),
    type: 'workspace',
  });

  //drag and drop hooks configured.
  drag(drop(ref));

  const tabOnContextMenu = React.useCallback((event: MouseEvent<HTMLLIElement>) => {
    event.preventDefault();
  }, []);

  const { active, isCollapsed } = props;
  const { editingName, inputWidth, workspaceName } = state;

  const ieFallbackStyle = equals(new UAParser().getBrowser().name, 'IE') ? { background: 'transparent' } : undefined;
  const tabClasses = classnames('WorkspaceTab', {
    'TUTORIAL_WorkspaceTab-Active': active,
    'is-active': active,
    'is-collapsed': isCollapsed,
    'is-dragging': isDragging,
    'is-editing': editingName,
  });

  const removable = !editingName;

  return (
    <li
      className={tabClasses}
      data-handler-id={handlerId}
      draggable
      onClick={switchToThisWorkspace}
      onContextMenu={tabOnContextMenu}
      onDoubleClick={toggleNameEdit}
      onDrag={handleDrag}
      onDrop={handleDrop}
      onMouseDown={tabOnMouseDown}
      ref={ref}
      title={workspaceName}
    >
      <div className="WorkspaceTab-icon">{props.icon ?? <BZ />}</div>
      <div className="WorkspaceTab-name" data-test="workspace-name" style={ieFallbackStyle}>
        <input
          hidden={!editingName}
          onBlur={saveName}
          onChange={onWorkspaceNameChange}
          onClick={handleInputClick}
          onKeyDown={handleKeyDown}
          ref={nameInput}
          style={{ width: inputWidth }}
          type="text"
          value={workspaceName}
        />
        <WorkspaceName $active={active} hidden={editingName}>
          {workspaceName}
        </WorkspaceName>
      </div>
      <div className="WorkspaceTab-controls">
        {removable && (
          <button
            className="WorkspaceTab-close u-closeIcon TUTORIAL_WorkspaceTab-Close"
            onClick={handleRemoveWorkspace}
          >
            <StyledIcon as={Close} title="Remove Workspace" />
          </button>
        )}
        {props.editable && (
          <button className="WorkspaceTab-edit TUTORIAL_WorkspaceTab-Edit" onClick={toggleNameEdit}>
            <StyledIcon as={Edit} title="Edit Workspace Name" />
          </button>
        )}
      </div>
      {active ? <div className="WorkspaceTab-highlight" /> : <div className="WorkspaceTab-highlightInactive" />}
    </li>
  );
};

const StyledIcon = styled.svg`
  path {
    fill: ${props => props.theme.colors.foregroundInactive};
  }
`;

const mapStateToProps = (state: RootState, ownProps: OwnProps): ReduxState => ({
  activeWorkspaceId: selectActiveWorkspaceId(state),
  name: pathOr('', ['name'], selectWorkspaceById(state, { workspaceId: ownProps.workspace.workspaceId })), // TODO: create workspace entity for property access
  widgetsById: selectWidgets(state),
});

const mapDispatchToProps = (dispatch: ThunkDispatch<RootState, void, RootAction>): DispatchableActions =>
  bindActionCreators(
    {
      changeWorkspaceName,
      moveWorkspaceTab,
      switchWorkspace,
    },
    dispatch,
  );

const WorkspaceTabContainer = connect<ReduxState, DispatchableActions, OwnProps, RootState>(
  mapStateToProps,
  mapDispatchToProps,
)(WorkspaceTab as any) as React.FC<OwnProps>;

WorkspaceTabContainer.displayName = 'WorkspaceTabContainer';

export default WorkspaceTabContainer;
