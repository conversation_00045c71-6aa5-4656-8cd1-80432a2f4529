'use client';
import React from 'react';
import { Button, Text, Rate } from '@benzinga/core-ui';
import { SmartLink } from '@benzinga/analytics';
import { BzImage } from '@benzinga/image';
import styled from '@benzinga/themetron';
import Hooks from '@benzinga/hooks';
import { CallToActionButton } from './index';
export type ProductBasedCTALayout = 'default' | 'vertical' | 'stacked';

export interface ProductBasedCTAProps {
  image?: any;
  button_text: string;
  description?: string;
  highlight_list?: string[] | [];
  image_url?: string;
  layout?: ProductBasedCTALayout;
  review?: number;
  review_url?: string;
  title: string;
  url: string;
}

export const ProductBasedCTA: React.FC<ProductBasedCTAProps> = ({
  button_text,
  description,
  highlight_list,
  image_url,
  layout,
  review,
  review_url,
  title,
  url,
}) => {
  const { width } = Hooks.useWindowSize();

  const variant = width < 800 || layout === 'vertical' ? 'vertical' : 'default';

  return (
    <CampaignCallToActionWrapper className={`call-to-action ${variant} ${layout}`}>
      <div className="call-to-action-container">
        <SmartLink href={url}>
          <CallToActionWrapper className="call-to-action-wrapper">
            <div className="promo-image-wrap">
              {image_url && (
                <CallToActionImageWrapper className="image-wrapper">
                  <div>
                    <BzImage alt={title} height={75} src={image_url} width={120} />
                  </div>
                </CallToActionImageWrapper>
              )}
            </div>
            <div className="call-to-action-copy">
              <div className="call-to-action-copy-content">
                <div>
                  <CallToActionTitle>{title}</CallToActionTitle>
                  {description && (
                    <Text
                      className="call-to-action-description"
                      dangerouslySetInnerHTML={{ __html: description }}
                      lines={5}
                    />
                  )}
                  {highlight_list && highlight_list.length > 0 && (
                    <div className="call-to-action-highlight-list-wrapper">
                      <ul className="call-to-action-highlight-list">
                        {highlight_list.map((item, index) => (
                          <li key={index}>{item}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
                <div className="cta-review">
                  {(review && review > 0) || review_url ? (
                    <div className="item-review">
                      <Button className="w-full review-button">
                        <>
                          {review && review > 0 && (
                            <div className="review-number">
                              <span>{review}</span>/5
                            </div>
                          )}
                          <div className="review-detail">
                            {review && review > 0 && (
                              <Rate allowHalf className="rating-component" readOnly={true} value={review} />
                            )}
                            {review_url && (
                              <a className="w-full review-link" href={review_url}>
                                <span>Read Review</span>
                              </a>
                            )}
                          </div>
                        </>
                      </Button>
                    </div>
                  ) : (
                    <></>
                  )}
                  <CallToActionButton text={button_text} variant="flat-blue" />
                </div>
              </div>
            </div>
          </CallToActionWrapper>
        </SmartLink>
      </div>
    </CampaignCallToActionWrapper>
  );
};

const CampaignCallToActionWrapper = styled.div`
  margin: 16px 0;
  .call-to-action-container {
    border: solid 1px ${({ theme }) => theme.colors.border};
    border-radius: ${({ theme }) => theme.borderRadius.md};
    box-shadow: ${({ theme }) => theme.shadow.default};
    background-color: white;
    padding: 0.25rem 0;
    &:hover {
      box-shadow: ${({ theme }) => theme.shadow.md};
    }
  }
  &.default {
    .call-to-action-copy {
      .cta-button {
        display: none;
      }
    }
  }
  &.vertical {
    height: 100%;

    .call-to-action-container {
      margin: 0;
      height: calc(100% - 25px);
      > a {
        display: block;
        height: 100%;
      }
    }
    .call-to-action-wrapper {
      display: inline-flex;
      flex-direction: column;
      height: 100%;
    }
    .call-to-action-copy {
      flex-direction: column;

      height: 100%;
    }
    .call-to-action-copy-content {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
    .image-wrapper {
      border-bottom: solid 1px ${({ theme }) => theme.colors.border};
      border-right: none;
      flex-direction: column;
      max-width: 100%;
      min-width: 100%;
      width: 100%;
      margin-bottom: 0;
    }
    .item-review {
      margin-top: 0.5rem;
      .review-button {
        padding: 0;
      }
      .review-number {
        color: #5b636f;
        background: rgb(225, 235, 250);
        padding: 0.65rem 1rem;
        display: flex;
        align-items: center;
        margin-right: 0.2rem;
      }
      .review-detail {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        width: 100%;
        align-items: center;
        .review-link {
          width: auto;
          text-align: right;
          padding: 0.25rem;
        }
      }
    }
  }
  &.stacked {
    .promo-image-wrap {
      display: flex;
    }
    margin: 0;
    @media (max-width: 767px) {
      margin: 8px 0;
    }
    .image-wrapper {
      .cta-button {
        display: none;
      }
    }
    .call-to-action-copy {
      width: 100%;
      .call-to-action-copy-content {
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
        @media (max-width: 800px) {
          flex-direction: column;
        }
      }
      width: 100%;
      .cta-review {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        min-width: 200px;
        .cta-button {
          display: block;
          margin-top: 0.5rem;
          @media (max-width: 800px) {
            max-width: 100%;
          }
        }
        .item-review {
          width: 100%;
          .review-button {
            padding: 0;
            display: block;
            > span {
              display: flex;
              justify-content: space-between;
              padding: 0;
            }
          }

          .review-number {
            font-weight: 600;
            color: #5b636f;
            background: rgb(225, 235, 250);
            padding: 0.25rem 1rem;
            display: flex;
            align-items: center;
            span {
              font-size: 2rem;
              font-weight: 600;
              font-size: 1.5rem;
            }
          }
          .review-detail {
            display: flex;
            flex-direction: column;
            padding: 0.25rem 0.5rem;
            align-items: center;
          }
        }
      }
    }
  }
  .call-to-action-highlight {
    font-size: 1rem;
    color: rgb(45, 45, 45);
    margin-top: 0.5rem;
    border-radius: ${({ theme }) => theme.borderRadius.sm};
  }

  .call-to-action-highlight-list-wrapper {
    border-radius: ${({ theme }) => theme.borderRadius.sm};
    margin-top: 0.5rem;

    .call-to-action-highlight-list {
      color: rgb(45, 45, 45);
      margin-left: 18px;
      list-style-type: disc;
      list-style: none;
      padding: 0;

      li:before {
        content: '✓';
        color: #3adb76;
        font-weight: 700;
        margin: 0 5px 0 -16px;
      }
    }
  }
`;

const CallToActionWrapper = styled.div`
  display: inline-flex;
  flex-direction: row;
  width: 100%;
  position: relative;
  .promo-image-wrap {
    border-right: solid 1px ${({ theme }) => theme.colors.border};
  }

  .image-wrapper {
    max-width: 280px;
    min-width: 280px;
    margin: 0;
  }
  .call-to-action-copy {
    display: inline-flex;
    align-items: center;
    padding: 1rem;
    text-align: left;
    .call-to-action-description {
      color: ${({ theme }) => theme.colors.foreground};
    }
  }
  .cta-button {
    button {
      font-size: 14px;
      font-weight: 600;
      display: inline-flex;
      text-decoration: none;
      align-items: center;
      justify-content: center;
      line-height: 1rem;
      box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 2px 0px;
      border: 1px solid rgb(225, 235, 250);
      border-radius: 4px;
      padding: 0.5rem 0.75rem;
      transition:
        background-color 0.25s ease-in-out 0s,
        color 0.25s ease-in-out 0s,
        border-color 0.25s ease-in-out 0s;
    }
  }
`;

const CallToActionTitle = styled.div`
  color: #2ca2d1;
  font-size: ${({ theme }) => theme.fontSize['2xl']};
  font-weight: ${({ theme }) => theme.fontWeight.bold};
  line-height: 2rem;
  margin-bottom: 0.5rem;
`;

const CallToActionImageWrapper = styled.div`
  padding: 1rem;
  display: inline-flex;
  align-items: center;
  width: 100%;
  flex-direction: row;
  div {
    width: 100%;
    text-align: center;
  }
`;
