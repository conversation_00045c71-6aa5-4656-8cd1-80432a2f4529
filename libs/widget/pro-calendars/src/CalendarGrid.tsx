'use client';
import React, { startTransition, useEffect } from 'react';
import { GridOptions, <PERSON>rid<PERSON><PERSON>, RowDataUpdatedEvent } from '@ag-grid-community/core';
import {
  BenzingaGrid,
  GridExportParams,
  gridOptions as gridOptionsUtils,
  GridTransaction,
  MoreItemsBubbleBanner,
  SendLinkContext,
  useViewportData,
} from '@benzinga/pro-ui';
import { TableParameters } from '@benzinga/ag-grid-utils';
import { UnpackedArray, ValueOf, isEmpty } from '@benzinga/utils';
import { calendarDefinitions, calendarLinkContextColIds, getTickerFieldFromCalendarType } from './calendarDefinitions';
import Hooks from '@benzinga/hooks';
import {
  CalendarModuleItem,
  CalendarType,
  DataType,
  DateRange,
  DatePresetId,
  useCalendarData,
  useDateRangeHook,
} from '@benzinga/calendar-manager-hooks';
import { useGlobalSetting } from '@benzinga/widget-tools';
import { CalendarWidgetManifest } from './widget';
import { HoldingsQuote } from '@benzinga/quotes-v3-holdings-manager';
import { useColDef } from './hooks/useColDef';
import { useScannerData } from './hooks/useScannerData';

interface Props {
  calendarType: CalendarType;
  dateRange?: DateRange | DatePresetId;
  dateSearchField: string;
  filters: CalendarModuleItem[];
  setResetGridFilters?: (resetGridFilters: VoidFunction) => void;
  table: TableParameters;
  onGridLayoutChanged(parameters: Partial<TableParameters>): void;
}

enum ScrollDirection {
  bottom = 'bottom',
  top = 'top',
}

interface State {
  firstUpdatedIndex: number;
  hasShowBanner: boolean;
  refreshGrid: number;
  exportParams: GridExportParams;
  symbolColIDs?: string[];
  scrollDirection?: ScrollDirection;
  unseenEventsAmount: number;
}

export const CalendarGrid: React.FC<Props> = props => {
  const gridApi = React.useRef<GridApi | null>(null);
  const { filterVisibleSymbols, getVisibleData, initOnScrollEnd, onTransactionApplied } = useViewportData();
  const colDefs = useColDef(props.table, props.calendarType);
  const [state, setState] = React.useState<State>({
    exportParams: {
      filename: `BZ Pro ${calendarDefinitions[props.calendarType]?.name ?? ''} Calendar`,
    },
    firstUpdatedIndex: -1,
    hasShowBanner: false,
    refreshGrid: 0,
    scrollDirection: undefined,
    symbolColIDs: calendarLinkContextColIds[props.calendarType],
    unseenEventsAmount: 0,
  });

  const [transaction] = React.useState(() => new GridTransaction<UnpackedArray<ValueOf<DataType>>>());
  const { dateFilterPreference } = useGlobalSetting(CalendarWidgetManifest).settings;
  const dateRange = useDateRangeHook(props.calendarType, props.filters, props.dateRange, dateFilterPreference);

  const quoteFields = React.useMemo(() => new Set(['symbol']) as Set<keyof HoldingsQuote>, []);

  const limitBasedOffCalendarType = React.useMemo(() => {
    switch (props.calendarType) {
      case 'optionsActivity':
      case 'sec':
        return 1e5;
      default:
        return 1e4;
    }
  }, [props.calendarType]);

  const dataHook = useCalendarData(
    props.calendarType,
    props.filters,
    dateRange,
    quoteFields,
    transaction,
    props.dateSearchField,
    limitBasedOffCalendarType,
  );

  const { assembleScannerInitialData } = useScannerData(gridApi, dataHook);

  const subscribeVisibleData = React.useCallback(
    (incomeSymbols?: string[]) => {
      const { fields, symbols } = getVisibleData(gridApi?.current);
      dataHook.updateSubscriptionData(incomeSymbols ?? symbols, new Set([...fields]) as Set<keyof HoldingsQuote>);
    },
    [dataHook, getVisibleData],
  );

  const subscribeVisibleColumns = React.useCallback(() => {
    const { fields } = getVisibleData(gridApi?.current);

    if (fields?.length > 1) {
      dataHook.updateSubscriptionFields(new Set([...fields]) as Set<keyof HoldingsQuote>);
    }
  }, [dataHook, getVisibleData]);

  const onScrollEnd = (data, { direction }) => {
    const fields = new Set([...data.fields]) as Set<keyof HoldingsQuote>;

    if (direction === 'vertical') {
      if (data?.removedSymbols?.length) {
        dataHook.removeSubscriptionData(data.removedSymbols);
      }
      dataHook.updateSubscriptionData(data.symbols, fields);
    } else {
      dataHook.updateSubscriptionFields(fields);
    }
  };

  const updateAddedItemSubscriptions = items => {
    if (items?.length) {
      const symbols = items.map(item => item.ticker).filter(symbol => !!symbol);
      const visibleSymbols = filterVisibleSymbols(gridApi?.current, symbols);

      if (visibleSymbols?.length) {
        subscribeVisibleData(visibleSymbols);
      }
    }
  };

  useEffect(() => {
    if (props.table.columns?.length) {
      assembleScannerInitialData();
      subscribeVisibleColumns();
    }
  }, [props.table.columns, assembleScannerInitialData, subscribeVisibleColumns]);

  const latestEvents = React.useRef<UnpackedArray<ValueOf<DataType>>[]>([]);
  Hooks.useSubscriber(dataHook ?? undefined, event => {
    switch (event.type) {
      case 'init': {
        assembleScannerInitialData();
        onTransactionApplied(gridApi?.current, subscribeVisibleData);
        initOnScrollEnd(gridApi?.current, onScrollEnd);
        break;
      }
      case 'added':
        latestEvents.current = event.items;
        updateAddedItemSubscriptions(event.items);
        break;
    }
  });

  const getRowStyle = (params: {
    data: UnpackedArray<ValueOf<DataType>>;
  }): {
    [cssProperty: string]: string;
  } => {
    if (latestEvents.current.some(a => a.id === params.data.id)) {
      return {
        'animation-duration': '5s',
        'animation-name': 'notification',
        'animation-timing-function': 'ease-in-out',
        'background-color': '',
      };
    } else {
      return {};
    }
  };

  const [gridOptions] = React.useState<GridOptions>(() => ({
    defaultColDef: {
      filterParams: {
        newRowsAction: 'keep',
      },
    },
    enableRangeSelection: true,
    getRowId: props => {
      if (!props.data.id) return;
      return props.data.id;
    },
    getRowStyle: getRowStyle,
    headerHeight: 30,
    immutableData: true,
    onGridReady: params => {
      if (params) {
        gridApi.current = params.api;
      }
    },
    processCellForClipboard: params => {
      if (params.column.getColId() === 'dividend_yield') {
        if (params.value === undefined && !isEmpty(params.value)) {
          const num = parseFloat(params.value);
          if (isNaN(num)) {
            return '--';
          }
          return num !== 0 ? `${(num * 100).toFixed(2)}%` : '--';
        }
      }
      return params.value;
    },
    rowBuffer: 20,
    rowHeight: 20,
    rowSelection: 'multiple',
    sideBar: gridOptionsUtils.sideBar,
    statusBar: gridOptionsUtils.statusBar,
  }));

  React.useEffect(() => {
    const definition = calendarDefinitions[props.calendarType];
    setState(old => ({
      ...old,
      exportParams: {
        filename: `BZ Pro ${definition?.name ?? ''} Calendar`,
      },
      symbolColIDs: calendarLinkContextColIds[props.calendarType],
    }));
  }, [props.calendarType]);

  // const resetGridFilters: VoidFunction = React.useCallback(() => {
  //   if (gridApi.current) {
  //     const columns = gridApi.current.getColumns();
  //     columns?.forEach(column => gridApi.current?.destroyFilter(column.getColId()));
  //     gridApi.current.onFilterChanged();
  //   }
  // }, []);

  const scrollTo = React.useCallback(
    (scrollDirection: ScrollDirection) => () => {
      setState(old => {
        if (!gridApi.current || old.firstUpdatedIndex === -1) {
          return old;
        }
        if (gridApi.current.getColumnState().filter(c => c.sort).length === 0) {
          gridApi.current.ensureIndexVisible(old.firstUpdatedIndex, scrollDirection);
        }

        return {
          ...old,
          firstUpdatedIndex: -1,
          hasShowBanner: false,
          scrollDirection: undefined,
          unseenEventsAmount: 0,
        };
      });
    },
    [],
  );

  const renderNewCalendarNotification = () => {
    const { hasShowBanner, scrollDirection, unseenEventsAmount } = state;

    if (!hasShowBanner || !scrollDirection || unseenEventsAmount === 0) {
      return null;
    }

    const newStoryText = unseenEventsAmount === 1 ? '1 new event' : `${unseenEventsAmount} new events`;

    return (
      <MoreItemsBubbleBanner onClick={scrollTo(scrollDirection)} right="22" top="30">
        {newStoryText}
      </MoreItemsBubbleBanner>
    );
  };

  const handleRowDataUpdate = React.useCallback((event: RowDataUpdatedEvent) => {
    const unseenEventsAmount = latestEvents.current.length;
    if (!unseenEventsAmount) {
      return;
    }
    const firstDisplayedRow = event.api.getFirstDisplayedRow();
    const lastDisplayedRow = event.api.getLastDisplayedRow();
    const rowsIds: string[] = [];
    event.api.forEachNodeAfterFilterAndSort(node => rowsIds.push(node.id ?? ''));
    const firstUpdatedIndex = rowsIds.findIndex(id => latestEvents.current.some(a => a.id === id));
    const isNeedToScroll = firstUpdatedIndex < firstDisplayedRow || firstUpdatedIndex > lastDisplayedRow;
    if (firstUpdatedIndex === -1 || !isNeedToScroll) {
      return;
    }
    const scrollDirection = firstUpdatedIndex < firstDisplayedRow ? ScrollDirection.top : ScrollDirection.bottom;
    setState(old => ({
      ...old,
      firstUpdatedIndex,
      hasShowBanner: true,
      scrollDirection,
      unseenEventsAmount: old.unseenEventsAmount + unseenEventsAmount,
    }));
  }, []);

  const handleColumnResized = React.useCallback(() => {
    setTimeout(() => {
      gridApi.current?.redrawRows();
    }, 0);
  }, []);

  const { calendarType, onGridLayoutChanged, table } = props;
  const autoSelectTickerOnSelect = React.useMemo(() => getTickerFieldFromCalendarType(calendarType), [calendarType]);
  const defaultColDef = React.useMemo(() => calendarDefinitions[calendarType].defaultColDef, [calendarType]);

  const sendLink = React.useContext(SendLinkContext);
  return (
    <>
      <BenzingaGrid
        autoSelectTickerOnSelect={autoSelectTickerOnSelect}
        autosizeColumns={true}
        columnDefs={colDefs}
        defaultColDef={defaultColDef}
        exportParams={state.exportParams}
        gridLayout={table ?? []}
        gridOptions={gridOptions}
        onColumnResized={handleColumnResized}
        onGridLayoutChanged={onGridLayoutChanged}
        onRowDataUpdated={handleRowDataUpdate}
        onSymbolClick={sendLink.onSymbolClick}
        refreshGrid={state.refreshGrid}
        rowClass="TUTORIAL_ag_header_Calendar"
        suppressRowTransform
        symbolColIDs={state.symbolColIDs}
        tooltipShowDelay={500}
        transaction={transaction}
      />
      {renderNewCalendarNotification()}
    </>
  );
};

export const CalendarGridMemo = React.memo(CalendarGrid);
CalendarGridMemo.displayName = 'CalendarGrid';
