import { ScannerWidgetIteration, ScannerWidgetAllIterations } from './entity';
import {
  DEFAULT_SCANNER_FILTERS,
  ScannerConfigV2,
  ScannerConfigLegacy,
  toScannerConfig,
} from '@benzinga/scanner-config-manager';
import { ScannerProtos } from '@benzinga/scanner-manager';
import { LoaderConvertor, WidgetSettingsLoaderFromLoader } from '@benzinga/widget-tools';
import { DefaultTableParameters } from '@benzinga/ag-grid-utils';

export const ScannerWidgetMigrator = async (
  configs: LoaderConvertor<ScannerWidgetAllIterations>,
): Promise<WidgetSettingsLoaderFromLoader<ScannerWidgetIteration>> => {
  switch (configs.version) {
    case 4: {
      const getSavedConfig = (widgetId: string): ScannerConfigV2 => {
        const DEFAULT_SCANNER_CONFIG: ScannerConfigV2 = {
          columns: [
            { name: 'symbol', width: 100 },
            { name: 'price', width: 120 },
            { name: 'change', width: 120 },
            { name: 'changePercent', width: 100 },
            { name: 'marketCap', width: 120 },
            { name: 'dayVolume', width: 120 },
          ],
          filters: DEFAULT_SCANNER_FILTERS,
          limit: 100,
          refreshInterval: 60,
          sortDir: ScannerProtos.SortDir.DESC,
          sortField: 'changePercent',
          source: 'stocks',
        };

        const configJson = window.localStorage.getItem(widgetId);
        try {
          if (configJson === null) {
            return DEFAULT_SCANNER_CONFIG;
          }
          const configIngress: ScannerConfigLegacy = JSON.parse(configJson);
          const config = toScannerConfig(configIngress);
          if (!config.filters) {
            config.filters = DEFAULT_SCANNER_FILTERS;
          }
          if (!config.sortField) {
            config.sortField = 'changePercent';
          }
          return config;
        } catch (err) {
          console.warn('Failed to load config', { configJson });
          return DEFAULT_SCANNER_CONFIG;
        }
      };

      return ScannerWidgetMigrator({
        ...configs,
        version: 5,
        widgets: configs.widgets.map(widget => ({
          ...widget,
          parameters: {
            ...widget.parameters,
            filtersMenuCollapsed: false,
            flightMode: false,
            widgetScan: getSavedConfig(widget.widgetId),
          },
        })),
      });
    }
    case 5: {
      return ScannerWidgetMigrator({
        ...configs,
        version: 6,
        widgets: configs.widgets.map(widget => ({
          ...widget,
          parameters: {
            ...widget.parameters,
            sendGroup: null,
          },
        })),
      });
    }
    case 6:
      return ScannerWidgetMigrator({
        ...configs,
        version: 7,
        widgets: configs.widgets.map(widget => {
          const {
            hideFiltersBar: _hideFiltersBar,
            hideToolbar: _hideToolbar,
            stopColumnHighlighting: _stopColumnHighlighting,
            ...widgetScan
          } = widget.parameters.widgetScan;
          return {
            ...widget,
            parameters: {
              ...widget.parameters,
              widgetScan: widgetScan,
            },
          };
        }),
      });
    case 7:
      return ScannerWidgetMigrator({
        ...configs,
        version: 8,
        widgets: configs.widgets.map(widget => {
          const widgetScan = widget.parameters.widgetScan;
          return {
            ...widget,
            parameters: {
              ...widget.parameters,

              gridLayout: {
                ...DefaultTableParameters,
                columns: widgetScan.columns.map(column => ({ colId: column.name, width: column.width })),
              },
            },
          };
        }),
      });
    case 8:
      return ScannerWidgetMigrator({
        ...configs,
        version: 9,
        widgets: configs.widgets.map(widget => {
          return {
            ...widget,
            parameters: {
              ...widget.parameters,
              tags: [],
            },
          };
        }),
      });
    case 9:
      return ScannerWidgetMigrator({
        ...configs,
        version: 10,
        widgets: configs.widgets.map(widget => {
          const { gridLayout, tags, widgetScan, ...rest } = widget.parameters;
          return {
            ...widget,
            parameters: {
              ...rest,
              config: {
                ...widgetScan,
                tableParameters: {
                  ...DefaultTableParameters,
                  ...gridLayout,
                  columns: widgetScan.columns.map(column => ({ colId: column.name, width: column.width })),
                },
                tags,
              },
            },
          };
        }),
      });
    case 10:
      return configs;
  }
};
