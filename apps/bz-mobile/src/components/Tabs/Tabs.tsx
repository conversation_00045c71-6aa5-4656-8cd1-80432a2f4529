import * as React from 'react';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { useTheme } from '../../theme/themecontext';
import { ParamListBase, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { NewsNavigationStackParamList } from '../../navigation/NewsNavigationStack';
import { StyleProp, StyleSheet, Text, View, ViewStyle } from 'react-native';
import { WP } from '../../services';
import BZTabBar from './BZTabBar';

interface TabsProps {
  fullWidth?: boolean;
  tabs: string[];
  renderTabComponent: (
    tab: string,
    props: { navigation: StackNavigationProp<NewsNavigationStackParamList>; route: RouteProp<ParamListBase, string> },
    index?: number,
  ) => React.ReactNode;
  onTabPress?: (tab: string) => void;
  fromTradeIdeas?: boolean;
  labelBackgroundStyle?: StyleProp<ViewStyle>;
  lazy?: boolean;
  onSwipeEnabled?: () => boolean;
}

const Tabs: React.FC<TabsProps> = ({
  fromTradeIdeas = false,
  fullWidth,
  labelBackgroundStyle,
  lazy = true,
  onSwipeEnabled,
  renderTabComponent,
  tabs,
}) => {
  const Tab = createMaterialTopTabNavigator();
  const { colors } = useTheme();
  const styles = withColors(colors);

  return (
    <Tab.Navigator
      tabBar={props => <BZTabBar fullWidth {...props} />}
      tabBarPosition={'top'}
      screenOptions={
        fromTradeIdeas
          ? {
              lazy: lazy,
              tabBarLabel({ children, focused }) {
                return (
                  <View style={{ flexDirection: 'row' }}>
                    <View
                      style={[
                        styles.tabBarLabelContainer,
                        {
                          backgroundColor: focused ? colors.newCard : 'transparent',
                        },
                        labelBackgroundStyle,
                      ]}
                    >
                      <Text
                        style={[
                          styles.tabBarLabelText,
                          {
                            color: focused ? colors.text : '#929EAE',
                          },
                        ]}
                      >
                        {children}
                      </Text>
                    </View>
                    {tabs.length > 2
                      ? !focused && children !== tabs[tabs.length - 1] && <View style={styles.lineStyle} />
                      : null}
                  </View>
                );
              },
              tabBarIndicatorStyle: { backgroundColor: 'transparent' },
              tabBarStyle: { backgroundColor: 'transparent', marginHorizontal: 15 },
              tabBarScrollEnabled: false,
              swipeEnabled: onSwipeEnabled ? onSwipeEnabled() : true,
            }
          : {
              lazy: lazy,
              tabBarActiveTintColor: colors.text,
              tabBarInactiveTintColor: colors.text,
              tabBarScrollEnabled: fullWidth ? false : true,
              tabBarIndicatorStyle: { backgroundColor: colors.linkText },
              tabBarLabelStyle: { fontSize: 16, fontWeight: '500', margin: 0 },
              tabBarItemStyle: [fullWidth ? null : { width: 'auto' }, { marginVertical: 0 }],
              swipeEnabled: onSwipeEnabled ? onSwipeEnabled() : true,
            }
      }
    >
      {tabs.map((tab: string, index: number) => {
        return <Tab.Screen children={props => renderTabComponent(tab, props, index)} key={tab} name={tab} />;
      })}
    </Tab.Navigator>
  );
};

export default Tabs;

const withColors = (colors: Record<string, string>) => {
  return StyleSheet.create({
    tabBarLabelContainer: {
      borderRadius: 24,
      justifyContent: 'center',
      alignItems: 'center',
      height: 32,
      width: WP('30.5'),
      paddingVertical: 6,
      paddingHorizontal: 16,
    },
    tabBarLabelText: {
      fontSize: 14,
      fontWeight: '600',
    },
    lineStyle: {
      height: 12,
      width: 1,
      backgroundColor: colors.grayishBlue,
      alignSelf: 'center',
    },
  });
};
