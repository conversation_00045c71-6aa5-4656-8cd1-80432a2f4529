import { useMemo, useContext, useRef, useCallback } from 'react';
import { usePathname } from 'next/navigation';
import { MetaProps } from '@benzinga/seo';
import { SessionContext } from '@benzinga/session-context';
import { Term, WordpressPage, WordpressPost } from '@benzinga/content-manager';
import { sophiManager } from '@benzinga/ads-utils';
import { runningClientSide } from '@benzinga/utils';

interface SophiTrackingData {
  [key: string]: any;
  page_type: string;
  page_section?: string;
}

export interface UseSophiPageViewOptions {
  pageType: string;
  section?: string;
  trackingData?: Partial<SophiTrackingData>;
  isArticlePage?: boolean;
  disabled?: boolean;
}

export interface SophiDecision {
  outcome?: {
    wallVisibility?: 'always' | 'never' | string;
    wallType?: string;
  };
}

export interface SophiPageTrackingOptions {
  meta?: MetaProps;
  post?: WordpressPage | WordpressPost;
  article?: any;
  term?: Term;
  disabled?: boolean;
  pageTargeting?: {
    BZ_PTYPE?: string;
    BZ_CHANNEL?: string | string[];
  };
}

export const useSophiTracking = ({
  article,
  disabled = false,
  meta,
  pageTargeting,
  post,
  term,
}: SophiPageTrackingOptions) => {
  const pathname = usePathname();
  const hasTrackedRef = useRef<boolean>(false);
  const session = useContext(SessionContext);

  const pageType = useMemo(() => {
    if (article) return 'article';
    if (pathname === '/') return 'homepage';
    if (meta?.pageType) return meta?.pageType;
    if (pageTargeting?.BZ_PTYPE) return pageTargeting.BZ_PTYPE;
    if (post) {
      //if (post.slug === 'benzinga-home-page') return 'homepage';
      return post.post_type || 'page';
    }
    return 'page';
  }, [article, pathname, meta?.pageType, pageTargeting?.BZ_PTYPE, post]);

  const section = useMemo((): string => {
    if (pathname === '/') return 'homepage';
    if (article?.channels?.[0]?.name) return article.channels[0].name;
    if (term?.name) return term?.name;
    if (pageTargeting?.BZ_CHANNEL) {
      if (Array.isArray(pageTargeting?.BZ_CHANNEL)) return pageTargeting?.BZ_CHANNEL[0];
      return pageTargeting.BZ_CHANNEL;
    }

    if (meta?.structuredData?.keywords) {
      const sectionKeyword = meta.structuredData.keywords.find(keyword => keyword.includes('section:'));
      if (sectionKeyword) {
        const sectionExists = sectionKeyword.match(/section:\s*([^"]+)/);
        if (sectionExists && sectionExists[1]) {
          return sectionExists[1].trim();
        }
      }
      const categoryKeyword = meta.structuredData.keywords.find(keyword => keyword.includes('category:'));
      if (categoryKeyword) {
        const categoryExists = categoryKeyword.match(/category:\s*([^"]+)/);
        if (categoryExists && categoryExists[1]) {
          return categoryExists[1].trim();
        }
      }
    }

    if (meta?.title) return meta?.title;
    return '';
  }, [pathname, article?.channels, term?.name, pageTargeting?.BZ_CHANNEL, meta?.structuredData?.keywords, meta?.title]);

  const trackingData = useMemo(() => {
    const baseData = {
      page_referrer: runningClientSide() ? document.referrer : '',
      page_title: meta?.title || '',
      page_type: pageType,
      page_url: meta?.canonical,
    };

    if (article) {
      return {
        ...baseData,
        article_author: article.author?.name || '',
        article_id: String(article.nodeId || ''),
        article_published_date: article.createdAt,
        article_title: article.title || '',
      };
    }

    if (post) {
      return {
        ...baseData,
        author: post.author?.name || '',
        id: String(post.id || ''),
        title: post.title || '',
      };
    }

    return baseData;
  }, [article, post, meta?.title, meta?.canonical, pageType]);

  const trackPageView = useCallback(() => {
    if (disabled || hasTrackedRef.current) return;

    sophiManager.trackPageView(pageType === 'article', section);

    const baseTrackingData: SophiTrackingData = {
      ...trackingData,
    };

    if (section) {
      baseTrackingData.page_section = section;
    }

    sophiManager.trackWithData(session, 'page_view', baseTrackingData);
    hasTrackedRef.current = true;
  }, [session, pageType, section, trackingData, disabled]);

  return {
    pageType,
    section,
    trackPageView,
    trackingData,
  };
};
