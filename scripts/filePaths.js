const filePaths = [
  "apps/bz/app/_components/root/AdditionalScripts.tsx",
  "apps/bz/app/_components/root/RegisterDomCss.tsx",
  "apps/bz/app/_components/root/RegisterEffects.tsx",
  "apps/bz/app/_components/root/RootHeader.tsx",
  "apps/bz/app/_components/root/RootTrackers.tsx",
  "apps/bz/app/_components/root/WrapProviders.tsx",
  "apps/bz/app/v2/tools/ToolsPageMain.tsx",
  "apps/bz/pages/_app.tsx",
  "apps/bz/pages/_error.tsx",
  "apps/bz/pages/index.tsx",
  "apps/bz/pages/razreport.tsx",
  "apps/bz/pages/reports.tsx",
  "apps/bz/pages/account/delete-account.tsx",
  "apps/bz/pages/account/index.tsx",
  "apps/bz/pages/alternative-investments/[type].tsx",
  "apps/bz/pages/apis/get-started.tsx",
  "apps/bz/pages/applications/[application].tsx",
  "apps/bz/pages/briefs/index.tsx",
  "apps/bz/pages/calendars/[calendar].tsx",
  "apps/bz/pages/dividends/index.tsx",
  "apps/bz/pages/earnings/index.tsx",
  "apps/bz/pages/exclusives/index.tsx",
  "apps/bz/pages/fda-calendar/index.tsx",
  "apps/bz/pages/gov-trades/index.tsx",
  "apps/bz/pages/gov-trades/members/index.tsx",
  "apps/bz/pages/gov-trades/members/[name]/index.tsx",
  "apps/bz/pages/gov-trades/securities/index.tsx",
  "apps/bz/pages/gov-trades/securities/[ticker]/index.tsx",
  "apps/bz/pages/login/close.tsx",
  "apps/bz/pages/login/google.tsx",
  "apps/bz/pages/login/index.tsx",
  "apps/bz/pages/margin-calculator/index.tsx",
  "apps/bz/pages/podcasts/[showSlug]/index.tsx",
  "apps/bz/pages/products/[vertical].tsx",
  "apps/bz/pages/quote/[ticker]/analyst-ratings.tsx",
  "apps/bz/pages/quote/[ticker]/earnings.tsx",
  "apps/bz/pages/quote/[ticker]/index.tsx",
  "apps/bz/pages/quote/[ticker]/insider-trades.tsx",
  "apps/bz/pages/recent/index.tsx",
  "apps/bz/pages/register/index.tsx",
  "apps/bz/pages/report/[symbol].tsx",
  "apps/bz/pages/search/index.tsx",
  "apps/bz/pages/short-interest/index.tsx",
  "apps/bz/pages/trade-ideas/[trade].tsx",
  "apps/bz/pages/trade-ideas/index.tsx",
  "apps/bz/pages/trade-ideas/trader/[trader].tsx",
  "apps/bz/pages/trade-ideas/traders/review/index.tsx",
  "apps/bz/pages/welcome/index.tsx",
  "apps/bz/src/components/News.tsx",
  "apps/bz/src/components/NewsMenu.tsx",
  "apps/bz/src/components/Page.tsx",
  "apps/bz/src/components/Peer.tsx",
  "apps/bz/src/components/Peers.tsx",
  "apps/bz/src/components/TradeIdeas.tsx",
  "apps/bz/src/components/About/BenzingaMoney/BenzingaMoney.tsx",
  "apps/bz/src/components/Account/Billing/CreditCardForm.tsx",
  "apps/bz/src/components/Account/Billing/index.tsx",
  "apps/bz/src/components/Account/Chat/index.tsx",
  "apps/bz/src/components/Ads/Adtoniq/AdToniq.tsx",
  "apps/bz/src/components/GovTrades/GovSearchBar.tsx",
  "apps/bz/src/components/GovTrades/TraderRow.tsx",
  "apps/bz/src/components/GovTrades/HeaderTabs/index.tsx",
  "apps/bz/src/components/Quote/CryptoProfile.tsx",
  "apps/bz/src/components/Quote/Partners.tsx",
  "apps/bz/src/components/Quote/PressRelease.tsx",
  "apps/bz/src/components/Quote/QuoteProfile.tsx",
  "apps/bz/src/components/Quote/QuoteSlider.tsx",
  "apps/bz/src/components/Quote/QuoteTabs.tsx",
  "apps/bz/src/components/Quote/V2/AboutQuote.tsx",
  "apps/bz/src/components/Quote/V2/CompareSection.tsx",
  "apps/bz/src/components/Quote/V2/FinancialsSection.tsx",
  "apps/bz/src/components/Quote/V2/MobileGoogleAd.tsx",
  "apps/bz/src/components/Quote/V2/News.tsx",
  "apps/bz/src/components/Quote/V2/OverviewSectionHeader.tsx",
  "apps/bz/src/components/Quote/V2/Peer.tsx",
  "apps/bz/src/components/Quote/V2/Peers.tsx",
  "apps/bz/src/components/Quote/V2/QuoteHeader.tsx",
  "apps/bz/src/components/Quote/V2/QuoteNewsSection.tsx",
  "apps/bz/src/components/Quote/V2/VideoBlocks.tsx",
  "apps/bz/src/components/Quote/V2/BullVsBear/index.tsx",
  "apps/bz/src/components/Quote/V2/CompareCharts/Chart.tsx",
  "apps/bz/src/components/Quote/V2/CompareCharts/CompareChart.tsx",
  "apps/bz/src/components/Quote/V2/CompareCharts/deChart.tsx",
  "apps/bz/src/components/Quote/V2/CompareCharts/epsChart.tsx",
  "apps/bz/src/components/Quote/V2/CompareCharts/pbChart.tsx",
  "apps/bz/src/components/Quote/V2/CompareCharts/peChart.tsx",
  "apps/bz/src/components/Quote/V2/CompareCharts/roeChart.tsx",
  "apps/bz/src/components/Quote/V2/CompareCharts/SymbolMenu.tsx",
  "apps/bz/src/components/Sidebars/Widgets/ZingTotalsSidebarWidget.tsx",
  "apps/bz/src/components/widgets/SmartAssetWidget.tsx",
  "apps/bz/utils/PageTracker.tsx",
  "apps/bz-mobile/src/app/App.tsx",
  "apps/bz-mobile/src/components/AlertCreateView.tsx",
  "apps/bz-mobile/src/components/AnalystRatingsTabs.tsx",
  "apps/bz-mobile/src/components/AppleLogin.tsx",
  "apps/bz-mobile/src/components/ArticleQuoteHover.tsx",
  "apps/bz-mobile/src/components/AuthView.tsx",
  "apps/bz-mobile/src/components/BenzingaHTML.tsx",
  "apps/bz-mobile/src/components/BZBannerAd.tsx",
  "apps/bz-mobile/src/components/CreateWatchlistButton.tsx",
  "apps/bz-mobile/src/components/CustomizePriceAlert.tsx",
  "apps/bz-mobile/src/components/CustomModal.tsx",
  "apps/bz-mobile/src/components/GoogleLogin.tsx",
  "apps/bz-mobile/src/components/LottieAnimationView.tsx",
  "apps/bz-mobile/src/components/Navigation.tsx",
  "apps/bz-mobile/src/components/PriceAlertItem.tsx",
  "apps/bz-mobile/src/components/PriceAlerts.tsx",
  "apps/bz-mobile/src/components/QuoteScreener.tsx",
  "apps/bz-mobile/src/components/QuoteView.tsx",
  "apps/bz-mobile/src/components/SearchModal.tsx",
  "apps/bz-mobile/src/components/SearchModalItem.tsx",
  "apps/bz-mobile/src/components/SquawkBar.tsx",
  "apps/bz-mobile/src/components/useSubscriber.tsx",
  "apps/bz-mobile/src/components/WatchlistAlerts.tsx",
  "apps/bz-mobile/src/components/WatchlistNews.tsx",
  "apps/bz-mobile/src/components/WatchlistsManager.tsx",
  "apps/bz-mobile/src/components/WatchlistsView.tsx",
  "apps/bz-mobile/src/components/WatchlistTabs.tsx",
  "apps/bz-mobile/src/components/ZingFAQ.tsx",
  "apps/bz-mobile/src/components/__test__/AppleLogin.spec.tsx",
  "apps/bz-mobile/src/components/__test__/BZBannerAd.spec.tsx",
  "apps/bz-mobile/src/components/__test__/CustomModal.spec.tsx",
  "apps/bz-mobile/src/components/__test__/GoogleLogin.spec.tsx",
  "apps/bz-mobile/src/components/Article/ArticleFullView.tsx",
  "apps/bz-mobile/src/components/chat-components/InputBoxThread.tsx",
  "apps/bz-mobile/src/components/chat-components/MessageFooter.tsx",
  "apps/bz-mobile/src/components/chat-components/ReactionPicker.tsx",
  "apps/bz-mobile/src/components/chat-components/UserPicker.tsx",
  "apps/bz-mobile/src/components/chat-components/UserSearch.tsx",
  "apps/bz-mobile/src/components/chat-components/__test__/InputBoxThread.spec.tsx",
  "apps/bz-mobile/src/components/chat-components/__test__/UserSearch.spec.tsx",
  "apps/bz-mobile/src/components/chat-components/ChannelList/ChannelsAccordion.tsx",
  "apps/bz-mobile/src/components/chat-components/ChannelList/__test__/ChannelsAccordion.spec.tsx",
  "apps/bz-mobile/src/components/DevTools/index.tsx",
  "apps/bz-mobile/src/components/Ideas/Card.tsx",
  "apps/bz-mobile/src/components/Ideas/TradeIdeaFeed.tsx",
  "apps/bz-mobile/src/components/Market/Indices.tsx",
  "apps/bz-mobile/src/components/Notification/NotificationFrequencySlider.tsx",
  "apps/bz-mobile/src/components/Notification/SliderRadios.tsx",
  "apps/bz-mobile/src/components/OnBoarding/LinkPortfolioModal.tsx",
  "apps/bz-mobile/src/components/OnBoarding/NewWatchlistQuoteView.tsx",
  "apps/bz-mobile/src/components/PaginatedList/index.tsx",
  "apps/bz-mobile/src/components/Quote/CryptoChartView.tsx",
  "apps/bz-mobile/src/components/Quote/QuoteTab.tsx",
  "apps/bz-mobile/src/components/Quote/QuoteTile.tsx",
  "apps/bz-mobile/src/components/Quote/SearchBar.tsx",
  "apps/bz-mobile/src/components/Quote/SummaryCard.tsx",
  "apps/bz-mobile/src/components/Quote/__test__/QuoteTab.spec.tsx",
  "apps/bz-mobile/src/components/Rewards/RewardFeed.tsx",
  "apps/bz-mobile/src/components/VideosPage/VideoThumbnail.tsx",
  "apps/bz-mobile/src/components/Watchlist/AccordionWatchlist.tsx",
  "apps/bz-mobile/src/components/Watchlist/AlertTable.tsx",
  "apps/bz-mobile/src/components/Watchlist/CreateWatchlistModal.tsx",
  "apps/bz-mobile/src/components/Watchlist/WatchlistQuotesView.tsx",
  "apps/bz-mobile/src/navigation/MainTabNavigator.tsx",
  "apps/bz-mobile/src/screens/ArticleScreen.tsx",
  "apps/bz-mobile/src/screens/CryptoQuoteScreen.tsx",
  "apps/bz-mobile/src/screens/MarketDataScreen.tsx",
  "apps/bz-mobile/src/screens/NewsFeedScreen.tsx",
  "apps/bz-mobile/src/screens/NotificationScreen.tsx",
  "apps/bz-mobile/src/screens/Pricing.tsx",
  "apps/bz-mobile/src/screens/QuoteScreen.tsx",
  "apps/bz-mobile/src/screens/SearchScreen.tsx",
  "apps/bz-mobile/src/screens/Splash.tsx",
  "apps/bz-mobile/src/screens/UserWatchlistsScreen.tsx",
  "apps/bz-mobile/src/screens/WebModalScreen.tsx",
  "apps/bz-mobile/src/screens/__test__/QuoteScreen.spec.tsx",
  "apps/bz-mobile/src/screens/Account/AccountScreen.tsx",
  "apps/bz-mobile/src/screens/Account/BenzingaSquawkPlanScreen.tsx",
  "apps/bz-mobile/src/screens/Account/MySubscriptionsScreen.tsx",
  "apps/bz-mobile/src/screens/Account/NotificationSettingsScreen.tsx",
  "apps/bz-mobile/src/screens/Account/UpdateContactInfoScreen.tsx",
  "apps/bz-mobile/src/screens/Auth/LoginScreen.tsx",
  "apps/bz-mobile/src/screens/Auth/SignUpScreen.tsx",
  "apps/bz-mobile/src/screens/Calendar/ConferenceCalls/index.tsx",
  "apps/bz-mobile/src/screens/Calendar/Dividends/index.tsx",
  "apps/bz-mobile/src/screens/Calendar/Economic/index.tsx",
  "apps/bz-mobile/src/screens/Calendar/FDA/index.tsx",
  "apps/bz-mobile/src/screens/Calendar/MergersAcquisitions/index.tsx",
  "apps/bz-mobile/src/screens/Calendar/UnusualOptions/index.tsx",
  "apps/bz-mobile/src/screens/Chat/ChannelListScreen.tsx",
  "apps/bz-mobile/src/screens/Chat/ChannelScreen.tsx",
  "apps/bz-mobile/src/screens/Chat/ChannelSearchScreen.tsx",
  "apps/bz-mobile/src/screens/Chat/DraftsScreen.tsx",
  "apps/bz-mobile/src/screens/Chat/MentionsSearch.tsx",
  "apps/bz-mobile/src/screens/Chat/MessageSearchScreen.tsx",
  "apps/bz-mobile/src/screens/Chat/NewMessageScreen.tsx",
  "apps/bz-mobile/src/screens/Chat/ScreenHeader.tsx",
  "apps/bz-mobile/src/screens/Chat/TargettedMessageChannelScreen.tsx",
  "apps/bz-mobile/src/screens/Chat/ThreadScreen.tsx",
  "apps/bz-mobile/src/screens/Chat/__test__/ChannelScreen.spec.tsx",
  "apps/bz-mobile/src/screens/Chat/__test__/TargettedMessageChannelScreen.spec.tsx",
  "apps/bz-mobile/src/screens/Chat/__test__/ThreadScreen.spec.tsx",
  "apps/bz-mobile/src/screens/Home/HomeScreen.tsx",
  "apps/bz-mobile/src/screens/Home/components/HomeRewardsView.tsx",
  "apps/bz-mobile/src/screens/Ideas/ShareTradeIdeasScreen.tsx",
  "apps/bz-mobile/src/screens/Ideas/TradeIdeaScreen.tsx",
  "apps/bz-mobile/src/screens/Ideas/TradeIdeasScreen.tsx",
  "apps/bz-mobile/src/screens/Ideas/UserTradeIdeasScreen.tsx",
  "apps/bz-mobile/src/screens/News/NewsCommonList.tsx",
  "apps/bz-mobile/src/screens/News/NewsScreen.tsx",
  "apps/bz-mobile/src/screens/News/__test__/NewsCommonList.spec.tsx",
  "apps/bz-mobile/src/screens/OnBoarding/CreateFirstWatchlistScreen.tsx",
  "apps/bz-mobile/src/screens/OnBoarding/ExploreScreen.tsx",
  "apps/bz-mobile/src/screens/OnBoarding/NewWatchlist.tsx",
  "apps/bz-mobile/src/screens/OnBoarding/PostNewWatchlistScreen.tsx",
  "apps/bz-mobile/src/screens/OnBoarding/WelcomeAlertScreen.tsx",
  "apps/bz-mobile/src/screens/OnBoarding/WelcomePostScreen.tsx",
  "apps/bz-mobile/src/screens/OnBoarding/WelcomeScreen.tsx",
  "apps/bz-mobile/src/screens/Quote/MoversScreen.tsx",
  "apps/bz-mobile/src/screens/Rewards/RewardsScreen.tsx",
  "apps/bz-mobile/src/screens/Rewards/ZingTotalTitle.tsx",
  "apps/bz-mobile/src/screens/videoPages/index.tsx",
  "apps/bz-mobile/src/screens/Watchlist/WatchlistEditScreen.tsx",
  "apps/bz-mobile/src/screens/Watchlist/WatchlistScreen.tsx",
  "apps/bz-mobile/src/squwak/index.tsx",
  "apps/bz-mobile/src/theme/themecontext.tsx",
  "apps/india/pages/_app.tsx",
  "apps/india/pages/login/google.tsx",
  "apps/india/pages/login/index.tsx",
  "apps/india/pages/webstories/index.tsx",
  "apps/india/src/components/Page.tsx",
  "apps/india/src/components/Quicklinks.tsx",
  "apps/india/src/components/Newsletter/OptinMonsterNewsLetter.tsx",
  "apps/money/src/components/Page.tsx",
  "apps/newsdesk-tools/src/main.tsx",
  "apps/newsdesk-tools/src/app/components/Autocomplete.tsx",
  "apps/newsdesk-tools/src/app/components/RatingsFileUpload.tsx",
  "apps/newsdesk-tools/src/app/components/Site.tsx",
  "apps/newsdesk-tools/src/app/components/chat/ChannelComponent.tsx",
  "apps/newsdesk-tools/src/app/components/chat/ChatComponent.tsx",
  "apps/newsdesk-tools/src/app/components/grid/GridHotkeys.tsx",
  "apps/newsdesk-tools/src/app/components/logos/Logos.tsx",
  "apps/pro/src/routes.web.tsx",
  "apps/pro/src/components/ContextsInitializer.tsx",
  "apps/pro/src/components/HotkeyGate.tsx",
  "apps/pro/src/components/RedirectRoute.tsx",
  "apps/pro/src/components/SideBar.tsx",
  "apps/pro/src/components/Site.tsx",
  "apps/pro/src/components/bottombar/MiniSnapshot.tsx",
  "apps/pro/src/components/dashboard/AdBanner.tsx",
  "apps/pro/src/components/dashboard/Dashboard.tsx",
  "apps/pro/src/components/dashboard/MarketingWins.tsx",
  "apps/pro/src/components/dashboard/MenuItem.tsx",
  "apps/pro/src/components/dashboard/PlatformBanner.tsx",
  "apps/pro/src/components/dashboard/PlatformBar.tsx",
  "apps/pro/src/components/dashboard/release.tsx",
  "apps/pro/src/components/dashboard/WidgetMenuItem.tsx",
  "apps/pro/src/components/dashboard/WidgetMenuItems.tsx",
  "apps/pro/src/components/dashboard/createWorkspace/createWorkspace.tsx",
  "apps/pro/src/components/dashboard/createWorkspace/components/background.tsx",
  "apps/pro/src/components/dashboard/help/help.tsx",
  "apps/pro/src/components/dashboard/help/helpContent.tsx",
  "apps/pro/src/components/dashboard/homeMenu/homeMenu.tsx",
  "apps/pro/src/components/dashboard/welcome/welcome.tsx",
  "apps/pro/src/components/dashboard/welcome/welcomeButtonBox.tsx",
  "apps/pro/src/components/dashboard/welcome/welcomeWorkspace.tsx",
  "apps/pro/src/components/dashboard/workspace/DeletionList.tsx",
  "apps/pro/src/components/dashboard/workspace/EmptyWorkspace.tsx",
  "apps/pro/src/components/dashboard/workspace/Workspace.tsx",
  "apps/pro/src/components/dashboard/workspace/WorkspaceNav.tsx",
  "apps/pro/src/components/dashboard/workspace/WorkspaceTab.tsx",
  "apps/pro/src/components/home/<USER>",
  "apps/pro/src/components/home/<USER>",
  "apps/pro/src/components/ui/ContactSupport.tsx",
  "apps/pro/src/components/ui/homepageNewWidget.tsx",
  "apps/pro/src/components/ui/Popout.tsx",
  "apps/pro/src/components/ui/SquawkChannel.tsx",
  "apps/pro/src/components/ui/SquawkChannelsContainer.tsx",
  "apps/pro/src/components/user/Reset.tsx",
  "apps/pro/src/components/user/preferences/Settings.tsx",
  "apps/pro/src/components/user/preferences/account/About.tsx",
  "apps/pro/src/components/user/preferences/alerts/AlertsCellEmail.tsx",
  "apps/pro/src/components/user/preferences/alerts/AlertsCellEmailHeader.tsx",
  "apps/pro/src/components/user/preferences/alerts/AlertsCellName.tsx",
  "apps/pro/src/components/user/preferences/alerts/AlertsCellSound.tsx",
  "apps/pro/src/components/user/preferences/alerts/AlertsCellSoundHeader.tsx",
  "apps/pro/src/components/user/preferences/alerts/AlertsTable.tsx",
  "apps/pro/src/components/user/preferences/billing/StripeForm.tsx",
  "apps/pro/src/components/user/preferences/billing/UserSubscription.tsx",
  "apps/pro/src/components/user/preferences/billing/Billing/Billing.tsx",
  "apps/pro/src/components/user/preferences/layouts/Layouts.tsx",
  "apps/pro/src/components/user/preferences/layouts/components/AutoSavedLayoutsGrid/AutoSavedLayoutsGrid.tsx",
  "apps/pro/src/components/user/preferences/layouts/components/buttons/buttons.tsx",
  "apps/pro/src/components/user/preferences/layouts/components/SavedLayoutsGrid/SavedLayoutsGrid.tsx",
  "apps/pro/src/components/utils/PushRegistrationModal.tsx",
  "apps/pro/src/components/widgets/WidgetContainer.tsx",
  "apps/pro/src/components/widgets/WidgetWindowBar.tsx",
  "apps/pro/src/components/widgets/none/widget.tsx",
  "apps/pro/src/layout/LayoutsGate.tsx",
  "apps/pro/src/logging/LoggingGate.tsx",
  "apps/pro/src/widget-linking/WidgetLinkingGate.tsx",
  "apps/proto/pages/_app.tsx",
  "apps/proto/pages/widget-test/index.tsx",
  "apps/proto/pages/widgets/[widget].tsx",
  "apps/proto/pages/widgets/index.tsx",
  "apps/proto/pages/widgets/calendars/index.tsx",
  "apps/proto/pages/widgets/chart/index.tsx",
  "apps/proto/pages/widgets/chartv2/index.tsx",
  "apps/proto/pages/widgets/compliance/index.tsx",
  "apps/proto/pages/widgets/deficit/index.tsx",
  "apps/proto/pages/widgets/earningscal/index.tsx",
  "apps/proto/pages/widgets/newsfeed/index.tsx",
  "apps/proto/pages/widgets/tickertape/index.tsx",
  "apps/proto/src/components/Page.tsx",
  "apps/proto/src/components/WidgetConfig/index.tsx",
  "apps/widgets/pro-calendar/src/app/app.tsx",
  "apps/widgets/pro-insiders/src/app/app.tsx",
  "libs/react-utils/data-hooks/chart-manager/src/useChart.tsx",
  "libs/react-utils/data-hooks/chart-manager/src/useChartData.tsx",
  "libs/react-utils/data-hooks/chart-manager/src/useCharts.tsx",
  "libs/react-utils/data-hooks/chart-manager/src/useChartsDrawings.tsx",
  "libs/react-utils/data-hooks/chart-manager/src/useSavedCharts.tsx",
  "libs/react-utils/data-hooks/content-manager/src/useSponsoredContent.tsx",
  "libs/react-utils/data-hooks/crypto-manager/src/useCrypto.tsx",
  "libs/react-utils/data-hooks/fund-manager/src/useFund.tsx",
  "libs/react-utils/data-hooks/livestream-manager/src/useConnatixLivestream.tsx",
  "libs/react-utils/data-hooks/news-manager/src/useGetLatestStory.tsx",
  "libs/react-utils/data-hooks/news-manager/src/useGetMarketMoversNews.tsx",
  "libs/react-utils/data-hooks/news-manager/src/useGetStories.tsx",
  "libs/react-utils/data-hooks/news-manager/src/useLatestStory.tsx",
  "libs/react-utils/data-hooks/news-manager/src/useNewsUserConfigs.tsx",
  "libs/react-utils/data-hooks/news-manager/src/useSources.tsx",
  "libs/react-utils/data-hooks/news-manager/src/useSourcesGroups.tsx",
  "libs/react-utils/data-hooks/news-manager/src/useSubscribeToStories.tsx",
  "libs/react-utils/data-hooks/quotes-manager/src/useDelayedQuote.tsx",
  "libs/react-utils/data-hooks/quotes-manager/src/useDetailedQuote.tsx",
  "libs/react-utils/data-hooks/quotes-manager/src/useLogo.tsx",
  "libs/react-utils/data-hooks/quotes-manager/src/useQuoteHoldingsSubscription.tsx",
  "libs/react-utils/data-hooks/quotes-manager/src/useQuoteSubscription.tsx",
  "libs/react-utils/data-hooks/quotes-manager/src/useQuoteSubscriptionDetail.tsx",
  "libs/react-utils/data-hooks/quotes-manager/src/useTickerDetails.tsx",
  "libs/react-utils/data-hooks/scanner-manager/src/useScannerColumnableDefs.tsx",
  "libs/react-utils/data-hooks/scanner-manager/src/useScannerConfigs.tsx",
  "libs/react-utils/data-hooks/scanner-manager/src/useScannerDefs.tsx",
  "libs/react-utils/data-hooks/scanner-manager/src/useScannerFilterableDefs.tsx",
  "libs/react-utils/data-hooks/securities-manager/src/useFinancials.tsx",
  "libs/react-utils/data-hooks/securities-manager/src/useMultiSymbolFinancials.tsx",
  "libs/react-utils/data-hooks/securities-manager/src/useOwnership.tsx",
  "libs/react-utils/data-hooks/securities-manager/src/usePeers.tsx",
  "libs/react-utils/data-hooks/time-manager/src/useTime.tsx",
  "libs/react-utils/data-hooks/watchlist-manager/src/useWatchlist.tsx",
  "libs/react-utils/data-hooks/watchlist-manager/src/useWatchlistHoldingsQuotes.tsx",
  "libs/react-utils/data-hooks/watchlist-manager/src/useWatchlistLoading.tsx",
  "libs/react-utils/data-hooks/watchlist-manager/src/useWatchlists.tsx",
  "libs/react-utils/hooks/src/components/NoFirstRender.tsx",
  "libs/react-utils/hooks/src/hooks/useClickOutside.tsx",
  "libs/react-utils/hooks/src/hooks/useEffectDidMount.tsx",
  "libs/react-utils/hooks/src/hooks/useEffectDidUpdate.tsx",
  "libs/react-utils/hooks/src/hooks/useEffectReadyState.tsx",
  "libs/react-utils/hooks/src/hooks/useElementPosition.tsx",
  "libs/react-utils/hooks/src/hooks/useElementSize.tsx",
  "libs/react-utils/hooks/src/hooks/useEventCallback.tsx",
  "libs/react-utils/hooks/src/hooks/useEventListener.tsx",
  "libs/react-utils/hooks/src/hooks/useForceUpdate.tsx",
  "libs/react-utils/hooks/src/hooks/useGetSnapshotBeforeUpdate.tsx",
  "libs/react-utils/hooks/src/hooks/useHoverOutside.tsx",
  "libs/react-utils/hooks/src/hooks/useIsFirstRender.tsx",
  "libs/react-utils/hooks/src/hooks/useIsFirstRenderRef.tsx",
  "libs/react-utils/hooks/src/hooks/useIsMounted.tsx",
  "libs/react-utils/hooks/src/hooks/useIsomorphicLayoutEffect.tsx",
  "libs/react-utils/hooks/src/hooks/useLatest.tsx",
  "libs/react-utils/hooks/src/hooks/useLocalStorage.tsx",
  "libs/react-utils/hooks/src/hooks/useMemoWithPrev.tsx",
  "libs/react-utils/hooks/src/hooks/useMountUnmountRef.tsx",
  "libs/react-utils/hooks/src/hooks/useMutationObserver.tsx",
  "libs/react-utils/hooks/src/hooks/useOnVisibilityChange.tsx",
  "libs/react-utils/hooks/src/hooks/usePrevious.tsx",
  "libs/react-utils/hooks/src/hooks/usePreviousRef.tsx",
  "libs/react-utils/hooks/src/hooks/useRefElementSize.tsx",
  "libs/react-utils/hooks/src/hooks/useRefFunction.tsx",
  "libs/react-utils/hooks/src/hooks/useScrollBarVisible.tsx",
  "libs/react-utils/hooks/src/hooks/useStateCallback.tsx",
  "libs/react-utils/hooks/src/hooks/useStateRef.tsx",
  "libs/react-utils/hooks/src/hooks/useSubscription.tsx",
  "libs/react-utils/pro-tools/src/pro-context.tsx",
  "libs/react-utils/pro-tools/src/useActiveWorkspaceSubscriber.tsx",
  "libs/react-utils/pro-tools/src/workspace-context.tsx",
  "libs/react-utils/themetron/src/BeeZ.tsx",
  "libs/react-utils/themetron/src/components/components/dropdownMenu.tsx",
  "libs/react-utils/themetron/src/components/layout/constrainedFloater.tsx",
  "libs/react-utils/user-context/src/hijackComponent.tsx",
  "libs/react-utils/user-context/src/loggedInComponent.tsx",
  "libs/react-utils/user-context/src/loginPrompt.tsx",
  "libs/react-utils/user-context/src/loginRegister.tsx",
  "libs/react-utils/user-context/src/permissionedComponent.tsx",
  "libs/react-utils/user-context/src/permissionPrompt.tsx",
  "libs/react-utils/user-context/src/upSellPrompt.tsx",
  "libs/react-utils/user-context/src/hooks/useClearBrowserStorage.tsx",
  "libs/react-utils/user-context/src/hooks/useHasSession.tsx",
  "libs/react-utils/user-context/src/hooks/useHasUserTrialed.tsx",
  "libs/react-utils/user-context/src/hooks/useIsUserBenzingaContributor.tsx",
  "libs/react-utils/user-context/src/hooks/useIsUserEditor.tsx",
  "libs/react-utils/user-context/src/hooks/useIsUserLoggedIn.tsx",
  "libs/react-utils/user-context/src/hooks/useIsUserPaywalled.tsx",
  "libs/react-utils/user-context/src/hooks/useIsUserTrialing.tsx",
  "libs/react-utils/user-context/src/hooks/useLayouts.tsx",
  "libs/react-utils/user-context/src/hooks/usePermission.tsx",
  "libs/react-utils/user-context/src/hooks/usePermissions.tsx",
  "libs/react-utils/user-context/src/hooks/useUser.tsx",
  "libs/react-utils/user-context/src/hooks/useUserHasActiveSubscription.tsx",
  "libs/react-utils/user-context/src/hooks/useUserHasCanceledSubscription.tsx",
  "libs/react-utils/user-context/src/hooks/useUserHasSubscription.tsx",
  "libs/react-utils/user-context/src/hooks/useUserSubscriptions.tsx",
  "libs/react-utils/widget-tools/src/globalSettingsContext.tsx",
  "libs/react-utils/widget-tools/src/parameterContext.tsx",
  "libs/react-utils/widget-tools/src/toolbarHooks/flightMode.tsx",
  "libs/ui/ads/src/components/AmplyWidget.tsx",
  "libs/ui/ads/src/components/GoogleAdUnit.tsx",
  "libs/ui/ads/src/components/InvestingChannel.tsx",
  "libs/ui/ads/src/components/MobileAppBannerAd.tsx",
  "libs/ui/ads/src/components/PlayStreamScript.tsx",
  "libs/ui/ads/src/components/Connatix/ConnatixLive.tsx",
  "libs/ui/ads/src/components/Connatix/index.tsx",
  "libs/ui/ads/src/components/MediaAlpha/MediaAlphaInsuranceQuotes.tsx",
  "libs/ui/ads/src/components/Primis/index.tsx",
  "libs/ui/ads/src/components/Raptive/RaptiveAdPlaceholder.tsx",
  "libs/ui/ads/src/components/Raptive/RaptiveHead.tsx",
  "libs/ui/ads/src/components/RotatingBanner/MoneyLineBannerAd.tsx",
  "libs/ui/ads/src/components/RotatingBanner/ProJuly4thBannerAd.tsx",
  "libs/ui/ads/src/components/RotatingBanner/ProMemorialDayBannerAd.tsx",
  "libs/ui/ads/src/components/RotatingBanner/ProStreamlineBannerAd.tsx",
  "libs/ui/ads/src/components/Taboola/TaboolaPlacement.tsx",
  "libs/ui/ads/src/components/Transparently/index.tsx",
  "libs/ui/ads-utils/src/lib/hooks/usePageTargeting.tsx",
  "libs/ui/alternative-investments/src/components/Offerings.tsx",
  "libs/ui/alternative-investments/src/components/OfferingsCarousel.tsx",
  "libs/ui/alternative-investments/src/components/OfferingsFilter.stories.tsx",
  "libs/ui/alternative-investments/src/components/PlatformDetails.tsx",
  "libs/ui/alternative-investments/src/components/AltOfferingForm/index.tsx",
  "libs/ui/article/src/components/AdContent.tsx",
  "libs/ui/article/src/components/AdvertiserDisclosure.tsx",
  "libs/ui/article/src/components/ArticleCommentsDrawer.tsx",
  "libs/ui/article/src/components/ArticleCommentsEmbedScript.tsx",
  "libs/ui/article/src/components/ArticleLayoutMain.tsx",
  "libs/ui/article/src/components/ArticleMainPicture.tsx",
  "libs/ui/article/src/components/ArticlePopup.tsx",
  "libs/ui/article/src/components/AuthorInfo.tsx",
  "libs/ui/article/src/components/Ads/ExCo/index.tsx",
  "libs/ui/article/src/components/Ads/Taboola/index.tsx",
  "libs/ui/article/src/components/Ads/Taboola/NewTaboola.tsx",
  "libs/ui/article/src/components/Blocks/CampaignifyBlock.tsx",
  "libs/ui/article/src/components/Widgets/EditorBox.tsx",
  "libs/ui/article/src/components/Widgets/EditorialPushNotificationModal.tsx",
  "libs/ui/article/src/hooks/useCampaign.tsx",
  "libs/ui/article/src/hooks/useInfiniteArticles.tsx",
  "libs/ui/atomics/src/components/addToWatchlist.tsx",
  "libs/ui/auth/src/components/AuthPortal.tsx",
  "libs/ui/auth/src/components/AuthModal/index.tsx",
  "libs/ui/auth/src/components/AuthModal/ReCaptcha.tsx",
  "libs/ui/auth/src/components/CaptchadLogin/index.tsx",
  "libs/ui/auth/src/components/Login/index.tsx",
  "libs/ui/auth/src/components/Register/index.tsx",
  "libs/ui/blocks/src/components/LayoutAdmin.tsx",
  "libs/ui/blocks/src/components/Blocks/CarouselEventsBlock.tsx",
  "libs/ui/blocks/src/components/Blocks/CarouselNewsBlock.tsx",
  "libs/ui/blocks/src/components/Blocks/CoinListBlock.tsx",
  "libs/ui/blocks/src/components/Blocks/CoinMarqueeBlock.tsx",
  "libs/ui/blocks/src/components/Blocks/ConnatixLiveBlock.tsx",
  "libs/ui/blocks/src/components/Blocks/GoogleAdBlock.tsx",
  "libs/ui/blocks/src/components/Blocks/ListingPreviewBlock.tsx",
  "libs/ui/blocks/src/components/Blocks/PostFeedBlock.tsx",
  "libs/ui/blocks/src/components/Blocks/TwitterBlock.tsx",
  "libs/ui/blocks/src/components/Blocks/WidgetBlock.tsx",
  "libs/ui/bz-onboarding/src/components/OnboardingHubspotForm.tsx",
  "libs/ui/bz-onboarding/src/components/OnboardingWatchlist.tsx",
  "libs/ui/bz-onboarding/src/components/PersonalNewsFeed.tsx",
  "libs/ui/calculators/src/components/BudgetCalculator/index.tsx",
  "libs/ui/calculators/src/components/CapitalExpenseCalculator/index.tsx",
  "libs/ui/calculators/src/components/CapitalRateCalculator/index.tsx",
  "libs/ui/calculators/src/components/ForexProfitCalculator/index.tsx",
  "libs/ui/calculators/src/components/HYSACalculator/index.tsx",
  "libs/ui/calculators/src/components/LemonadeCompensationCalculator/index.tsx",
  "libs/ui/calculators/src/components/MarginCalculator/index.tsx",
  "libs/ui/calculators/src/components/MortgageCalculator/index.tsx",
  "libs/ui/calculators/src/components/OptionsProfitCalculator/index.tsx",
  "libs/ui/calculators/src/components/WorkersCompensationCalculator/index.tsx",
  "libs/ui/calendars/src/components/AnalystRatingCalendar/index.tsx",
  "libs/ui/calendars/src/components/CalendarsWidget/index.tsx",
  "libs/ui/calendars/src/components/CalendarsWidget/Table.tsx",
  "libs/ui/calendars/src/components/ConferenceCallCalendar/index.tsx",
  "libs/ui/calendars/src/components/DividendsCalendar/index.tsx",
  "libs/ui/calendars/src/components/EarningsCalendar/index.tsx",
  "libs/ui/calendars/src/components/EconomicsCalendar/index.tsx",
  "libs/ui/calendars/src/components/FDACalendar/index.tsx",
  "libs/ui/calendars/src/components/GuidanceCalendar/index.tsx",
  "libs/ui/calendars/src/components/InsiderTradesCalendar/index.tsx",
  "libs/ui/calendars/src/components/InsiderTradesCalendar/InsiderTooltipCell.tsx",
  "libs/ui/calendars/src/components/IPOCalendar/index.tsx",
  "libs/ui/calendars/src/components/lib/components/ClickableStatusBarComponent.tsx",
  "libs/ui/calendars/src/components/lib/components/DateFilterBar.tsx",
  "libs/ui/calendars/src/components/lib/components/ExtendedDatePicker.tsx",
  "libs/ui/calendars/src/components/lib/components/Layout.tsx",
  "libs/ui/calendars/src/components/lib/components/Loader.tsx",
  "libs/ui/calendars/src/components/lib/components/SearchField.tsx",
  "libs/ui/calendars/src/components/lib/components/ServerSideCalendar.tsx",
  "libs/ui/calendars/src/components/lib/components/StockTable.tsx",
  "libs/ui/calendars/src/components/lib/components/Tabs.tsx",
  "libs/ui/calendars/src/components/lib/filters/PercentCompareFilter.tsx",
  "libs/ui/calendars/src/components/lib/filters/PriceCompareFilter.tsx",
  "libs/ui/calendars/src/components/lib/filters/QuarterPeriodCompareFilter.tsx",
  "libs/ui/calendars/src/components/MACalendar/index.tsx",
  "libs/ui/calendars/src/components/OptionsCalendar/index.tsx",
  "libs/ui/calendars/src/components/RetailCalendar/index.tsx",
  "libs/ui/calendars/src/components/ShortInterestCalendar/index.tsx",
  "libs/ui/calendars/src/components/SpacCalendar/index.tsx",
  "libs/ui/calendars/src/components/StockSplitsCalendar/index.tsx",
  "libs/ui/charts/src/components/MortgageLoanCompare/index.tsx",
  "libs/ui/charts/src/components/TradingViewWidget/index.tsx",
  "libs/ui/core/src/components/Collapse/index.tsx",
  "libs/ui/core/src/components/Dialog/Dialog.stories.tsx",
  "libs/ui/core/src/components/Dialog/index.tsx",
  "libs/ui/core/src/components/Dropdown/index.tsx",
  "libs/ui/core/src/components/FeedButton/index.tsx",
  "libs/ui/core/src/components/FollowButtons/FollowAuthorButton.tsx",
  "libs/ui/core/src/components/FollowButtons/FollowChannelButton.tsx",
  "libs/ui/core/src/components/Grid/index.tsx",
  "libs/ui/core/src/components/Inputs/Checkbox/Checkbox.stories.tsx",
  "libs/ui/core/src/components/Inputs/CheckboxGroup/index.tsx",
  "libs/ui/core/src/components/Inputs/Input/Input.stories.tsx",
  "libs/ui/core/src/components/Inputs/RadioGroup/index.tsx",
  "libs/ui/core/src/components/Inputs/Slider/index.tsx",
  "libs/ui/core/src/components/Layout/index.tsx",
  "libs/ui/core/src/components/Modal/Modal.tsx",
  "libs/ui/core/src/components/Pagination/index.tsx",
  "libs/ui/core/src/components/Rate/index.tsx",
  "libs/ui/core/src/components/SelectMenu/SelectMenu.stories.tsx",
  "libs/ui/core/src/components/Tabs/index.tsx",
  "libs/ui/core/src/components/Tabs/Tabs.stories.tsx",
  "libs/ui/core/src/components/Tooltip/index.tsx",
  "libs/ui/core/src/utils/Portal.tsx",
  "libs/ui/core/src/utils/ResizeDetector.tsx",
  "libs/ui/crypto/src/components/CryptoConversionCalculator/FooterCurrencyRate.tsx",
  "libs/ui/crypto/src/components/CryptoConversionCalculator/index.tsx",
  "libs/ui/crypto/src/components/CryptoDotCom/CoinList/index.tsx",
  "libs/ui/crypto/src/components/CryptoDotCom/CoinMarquee/index.tsx",
  "libs/ui/crypto/src/components/MoonOrBust/index.tsx",
  "libs/ui/entity/src/components/EntityCard.tsx",
  "libs/ui/filter/src/components/MultiSelectGroup/index.tsx",
  "libs/ui/filter/src/hooks/useFilters.tsx",
  "libs/ui/filter/src/hooks/useQueryParams.tsx",
  "libs/ui/forms/src/components/AltsForm/index.tsx",
  "libs/ui/forms/src/components/CallToActionForm/EmailCapture.tsx",
  "libs/ui/forms/src/components/CallToActionForm/HubspotForm.tsx",
  "libs/ui/forms/src/components/CallToActionForm/index.tsx",
  "libs/ui/forms/src/components/SurveyBox/index.tsx",
  "libs/ui/image/src/lib/useIntersection.tsx",
  "libs/ui/logos/src/components/BenzingaLogo/index.tsx",
  "libs/ui/miller-columns/src/components/MillerColumns/MillerColumns.tsx",
  "libs/ui/money/src/components/Blocks/AskTheExpert.tsx",
  "libs/ui/money/src/components/Blocks/BZLogoSearch.tsx",
  "libs/ui/money/src/components/Blocks/ConnatixLiveBlock.tsx",
  "libs/ui/money/src/components/Blocks/DisclosureBlock.tsx",
  "libs/ui/money/src/components/Blocks/NewsCardsBlock.tsx",
  "libs/ui/money/src/components/Blocks/ProductsListBlock.tsx",
  "libs/ui/money/src/components/Blocks/WorkersCompensationDataFormBlock.tsx",
  "libs/ui/money/src/components/Campaign/CampaignPopup.tsx",
  "libs/ui/money/src/components/Campaign/FloatingPopup.tsx",
  "libs/ui/money/src/components/Campaign/Placeholder.tsx",
  "libs/ui/money/src/components/DentalInsurance/InsurancePlan.tsx",
  "libs/ui/money/src/components/FinancialAdvisorInfoCard/index.tsx",
  "libs/ui/money/src/components/Futures/index.tsx",
  "libs/ui/money/src/components/GetQuotesForm/index.tsx",
  "libs/ui/money/src/components/LargeMediaCardLayout/AddToCalendarButton.tsx",
  "libs/ui/money/src/components/LargeMediaCardLayout/index.tsx",
  "libs/ui/money/src/components/MoneyHeaderNavigation/index.tsx",
  "libs/ui/money/src/components/MoneyServicesList/index.tsx",
  "libs/ui/money/src/components/MoneyServicesList/MoneyServiceListItem.tsx",
  "libs/ui/money/src/components/NewsListWithPagination/index.tsx",
  "libs/ui/money/src/components/NewsWithTabs/index.tsx",
  "libs/ui/money/src/components/PartnerProducts/index.tsx",
  "libs/ui/money/src/components/Popup/index.tsx",
  "libs/ui/money/src/components/TableOfContents/index.tsx",
  "libs/ui/money/src/components/Templates/MoneyPageTemplate.tsx",
  "libs/ui/money/src/components/Templates/NewsTemplate.tsx",
  "libs/ui/money/src/components/TestimonialCard/index.tsx",
  "libs/ui/money/src/context/AffiliateContext.tsx",
  "libs/ui/navigation/src/components/AccountMenu.tsx",
  "libs/ui/navigation/src/components/BenzingaProButton.tsx",
  "libs/ui/navigation/src/components/QuoteBox.tsx",
  "libs/ui/navigation/src/components/BreakingNewsBanner/index.tsx",
  "libs/ui/navigation/src/components/FusionHeader/FusionHeaderBurger.tsx",
  "libs/ui/navigation/src/components/Header/HeaderDesktop.tsx",
  "libs/ui/navigation/src/components/Header/HeaderMobile.tsx",
  "libs/ui/navigation/src/components/Header/index.tsx",
  "libs/ui/navigation/src/components/Header/QuoteBar.tsx",
  "libs/ui/navigation/src/components/Header/QuotesList.tsx",
  "libs/ui/navigation/src/components/Header/Variants/Simple/SimpleHeader.tsx",
  "libs/ui/navigation/src/components/Header/Variants/Simple/SimpleHeaderMenu.tsx",
  "libs/ui/navigation/src/components/Header/Variants/Simple/SimpleHeaderMobile.tsx",
  "libs/ui/navigation/src/components/Header/Variants/Simple/SimpleSearch.tsx",
  "libs/ui/news/src/components/DefaultNewsElement.tsx",
  "libs/ui/news/src/components/NewsFeed.tsx",
  "libs/ui/news/src/components/BenzingaBriefNewsElement/index.tsx",
  "libs/ui/news/src/components/ContentFeed/index.tsx",
  "libs/ui/news/src/components/LatestNewsList/index.tsx",
  "libs/ui/news/src/components/NewsListWithLoadMore/index.tsx",
  "libs/ui/news/src/components/PostCard/PostElapsed.tsx",
  "libs/ui/pro-ui/src/components/AgGridUtils/agGridUtils.tsx",
  "libs/ui/pro-ui/src/components/AgGridUtils/NotesTooltipCellRenderer.tsx",
  "libs/ui/pro-ui/src/components/AgGridUtils/PriceAlertTooltipCellRenderer.tsx",
  "libs/ui/pro-ui/src/components/AgGridUtils/TimeFilter/TimeFilter.tsx",
  "libs/ui/pro-ui/src/components/CashtagPopover/index.tsx",
  "libs/ui/pro-ui/src/components/ConfigSelector/ConfigSelector.tsx",
  "libs/ui/pro-ui/src/components/ConfigSelector/ConfigSelectorModal.tsx",
  "libs/ui/pro-ui/src/components/ContextMenu/ContextMenu.tsx",
  "libs/ui/pro-ui/src/components/Feedback/FeedbackForm.tsx",
  "libs/ui/pro-ui/src/components/Feedback/index.tsx",
  "libs/ui/pro-ui/src/components/Grid/grid.tsx",
  "libs/ui/pro-ui/src/components/InfoBar/InfoBar.tsx",
  "libs/ui/pro-ui/src/components/InfoBar/InfoBarInner.tsx",
  "libs/ui/pro-ui/src/components/InfoBar/Modules/MarketingWinsModule.tsx",
  "libs/ui/pro-ui/src/components/InfoBar/Modules/NewsdeskNotificationsModule.tsx",
  "libs/ui/pro-ui/src/components/InfoBar/Modules/TickerQuotesModule.tsx",
  "libs/ui/pro-ui/src/components/MarketingWinsModal/MarketingWinsModal.tsx",
  "libs/ui/pro-ui/src/components/NoResults/index.tsx",
  "libs/ui/pro-ui/src/components/NotesDialog/NotesDialog.tsx",
  "libs/ui/pro-ui/src/components/PriceAlertDialog/PriceAlertDialog.tsx",
  "libs/ui/pro-ui/src/components/Search/context.tsx",
  "libs/ui/pro-ui/src/components/Search/Expression/ExpressionSearch.stories.tsx",
  "libs/ui/pro-ui/src/components/Search/Expression/ExpressionSearch.tsx",
  "libs/ui/pro-ui/src/components/Search/Expression/Modules/KeywordExpressionModules.tsx",
  "libs/ui/pro-ui/src/components/Search/ExpressionBuilder/ExpressionBuilder.tsx",
  "libs/ui/pro-ui/src/components/Search/ExpressionBuilder/ExpressionBuilderContext.tsx",
  "libs/ui/pro-ui/src/components/Search/ExpressionBuilder/ExpressionBuilderSections.tsx",
  "libs/ui/pro-ui/src/components/Search/Modules/AuthorModules.tsx",
  "libs/ui/pro-ui/src/components/Search/Modules/FundModules.tsx",
  "libs/ui/pro-ui/src/components/Search/Modules/KeywordModules.tsx",
  "libs/ui/pro-ui/src/components/Search/Modules/LinkingModules.tsx",
  "libs/ui/pro-ui/src/components/Search/Modules/ScannerModules.tsx",
  "libs/ui/pro-ui/src/components/Search/Modules/SymbolModules.tsx",
  "libs/ui/pro-ui/src/components/Search/Modules/WatchlistModules.tsx",
  "libs/ui/pro-ui/src/components/Search/SearchBar/SearchBar.tsx",
  "libs/ui/pro-ui/src/components/Search/Tag/TagSearch.stories.tsx",
  "libs/ui/pro-ui/src/components/Search/Tag/TagSearch.tsx",
  "libs/ui/pro-ui/src/components/Search/Tag/useAutoCompleteSymbols.tsx",
  "libs/ui/pro-ui/src/components/Sparkline/Sparkline.tsx",
  "libs/ui/pro-ui/src/components/Ticker/MultipleTickerHover.tsx",
  "libs/ui/pro-ui/src/components/Ticker/MultipleTickers.tsx",
  "libs/ui/pro-ui/src/components/Ticker/TickerSnippet.tsx",
  "libs/ui/pro-ui/src/components/Ticker/TickerTooltipCellRenderer.tsx",
  "libs/ui/pro-ui/src/components/Ticker/Modules/TickerNote.tsx",
  "libs/ui/pro-ui/src/components/Ticker/Ticker/Ticker.tsx",
  "libs/ui/pro-ui/src/components/TickerColor/TickerColor.tsx",
  "libs/ui/pro-ui/src/components/TickerHover/hover.tsx",
  "libs/ui/pro-ui/src/components/TickerHover/popover.tsx",
  "libs/ui/pro-ui/src/components/TickerHover/modules/header.tsx",
  "libs/ui/pro-ui/src/components/TickerHover/modules/note.tsx",
  "libs/ui/pro-ui/src/components/TickerHover/modules/sparkline.tsx",
  "libs/ui/pro-ui/src/components/TimeFilter/index.tsx",
  "libs/ui/pro-ui/src/components/WidgetLinking/icon.tsx",
  "libs/ui/pro-ui/src/components/WidgetLinking/useTickerLinking.tsx",
  "libs/ui/pro-ui/src/components/WidgetLinking/SearchLinking/OptionalTagSearchLinked.tsx",
  "libs/ui/pro-ui/src/components/WidgetLinking/SearchLinking/TagSearchLinked.tsx",
  "libs/ui/pro-ui/src/components/WidgetLinking/WidgetLinkingPicker/GroupEditModal.tsx",
  "libs/ui/pro-ui/src/components/WidgetLinking/WidgetLinkingPicker/GroupSelectDropdown.tsx",
  "libs/ui/pro-ui/src/components/WidgetLinking/WidgetLinkingPicker/SendLinkContext.tsx",
  "libs/ui/pro-ui/src/components/WidgetLinking/WidgetLinkingPicker/WidgetLinkingPicker.tsx",
  "libs/ui/pro-ui/src/utils/MiniSnapshot.tsx",
  "libs/ui/product/src/CompareProductTable/index.tsx",
  "libs/ui/product/src/UsersChoiceCard/index.tsx",
  "libs/ui/quotes/src/components/QuoteCard/index.tsx",
  "libs/ui/quotes/src/components/StockCommodities/index.tsx",
  "libs/ui/quotes/src/components/StockList/index.tsx",
  "libs/ui/quotes/src/components/StockMovers/index.tsx",
  "libs/ui/quotes/src/components/StockMoversTable/index.tsx",
  "libs/ui/quotes/src/components/StockQuotesTable/index.tsx",
  "libs/ui/reviews/src/components/ReviewEntries.tsx",
  "libs/ui/reviews/src/components/SubmitReviewBox.tsx",
  "libs/ui/search/src/Search/index.tsx",
  "libs/ui/table/src/components/Loader.tsx",
  "libs/ui/table/src/components/Table/index.tsx",
  "libs/ui/table/src/components/TableVirtualized/index.tsx",
  "libs/ui/templates/src/components/Article/ArticleInfiniteScrollStories.tsx",
  "libs/ui/templates/src/components/Article/ArticleLayout.tsx",
  "libs/ui/templates/src/components/Article/ArticleLayoutSidebar.tsx",
  "libs/ui/templates/src/components/Article/ArticlePageTemplate.tsx",
  "libs/ui/templates/src/components/Article/PartialArticleLayout.tsx",
  "libs/ui/templates/src/components/Author/AuthorTemplate.tsx",
  "libs/ui/templates/src/components/Error/Error.tsx",
  "libs/ui/templates/src/components/Profile/ProfileLayout.tsx",
  "libs/ui/ticker/src/components/TickerCard/index.tsx",
  "libs/ui/ticker/src/components/TickerCardCarousel/index.tsx",
  "libs/ui/ticker/src/components/TickerSearch/index.tsx",
  "libs/ui/ticker/src/components/TickerSearch/TickerSearch.stories.tsx",
  "libs/ui/ticker/src/components/TickerSelect/index.tsx",
  "libs/ui/ticker/src/components/TickerSelect/TickerSelect.stories.tsx",
  "libs/ui/trade-ideas/src/components/FinancialAssetAllocationSelect/FinancialAssetAllocationSelect.stories.tsx",
  "libs/ui/trade-ideas/src/components/FinancialAssetPositionSelect/FinancialAssetPositionSelect.stories.tsx",
  "libs/ui/trade-ideas/src/components/FinancialAssetPositionSelect/index.tsx",
  "libs/ui/trade-ideas/src/components/TradeDurationSelect/index.tsx",
  "libs/ui/trade-ideas/src/components/TradeDurationSelect/TradeDurationSelect.stories.tsx",
  "libs/ui/trade-ideas/src/components/TradeIdeaArticleFeed/index.tsx",
  "libs/ui/trade-ideas/src/components/TradeIdeaList/index.tsx",
  "libs/ui/trade-ideas/src/components/TradeIdeaListItem/FlagButton.tsx",
  "libs/ui/trade-ideas/src/components/TradeIdeaListItem/index.tsx",
  "libs/ui/trade-ideas/src/components/TradeIdeaListItem/MenuButton.tsx",
  "libs/ui/trade-ideas/src/components/TradeIdeaListItem/ShareButton.tsx",
  "libs/ui/trade-ideas/src/components/TradeIdeaListItem/TipZingButton.tsx",
  "libs/ui/trade-ideas/src/components/TradeIdeaListItem/TradeIdeaListItemComments.tsx",
  "libs/ui/trade-ideas/src/components/TradeIdeaPostPortal/index.tsx",
  "libs/ui/trade-ideas/src/components/TradeIdeasWidget/index.tsx",
  "libs/ui/trade-ideas/src/components/ZingStatistics/ZingReferral.tsx",
  "libs/ui/ui/src/components/CallToAction/DisclosureBox.tsx",
  "libs/ui/ui/src/components/Carousel/index.tsx",
  "libs/ui/ui/src/components/FusionHelpCard/index.tsx",
  "libs/ui/ui/src/components/FusionServiceCards/FusionServiceCard.tsx",
  "libs/ui/ui/src/components/GalleryCarousel/index.tsx",
  "libs/ui/ui/src/components/Movers/index.tsx",
  "libs/ui/ui/src/components/Plan/index.tsx",
  "libs/ui/ui/src/components/ShareButtons/index.tsx",
  "libs/ui/ui/src/components/ShowsAndPodcasts/index.tsx",
  "libs/ui/ui/src/components/Tabs/TabContent.tsx",
  "libs/ui/ui/src/components/Tabs/TabNavItem.tsx",
  "libs/ui/ui/src/components/YieldTable/index.tsx",
  "libs/ui/user/src/components/CreditCard/index.tsx",
  "libs/ui/user/src/components/UpdateAvatar/index.tsx",
  "libs/ui/watchlist-ui/src/components/AddToWatchlist/index.tsx",
  "libs/ui/widgets/src/components/WatchlistCreateBox/index.tsx",
  "libs/ui/widgets/src/components/WatchlistDropdown/index.tsx",
  "libs/ui/widgets/src/components/WatchlistModal/index.tsx",
  "libs/ui/widgets/src/components/WatchlistMovers/index.tsx",
  "libs/ui/widgets/src/components/WatchlistMovers/WatchListMovers.stories.tsx",
  "libs/ui/widgets/src/components/WatchlistSelect/WatchlistSelect.stories.tsx",
  "libs/utils/analytics/src/ClickTracker.tsx",
  "libs/utils/analytics/src/GeoTracker.tsx",
  "libs/utils/analytics/src/Impression.tsx",
  "libs/utils/analytics/src/PageTracker.tsx",
  "libs/utils/analytics/src/SmartLink/index.tsx",
  "libs/utils/seo/src/Meta.tsx",
  "libs/visualization/analyst-ratings/src/analyst-ratings.tsx",
  "libs/visualization/guage/src/types/averageAnalystRatings.tsx",
  "libs/visualization/heatmap/src/heatmap.tsx",
  "libs/visualization/iqchart/src/internal.tsx",
  "libs/visualization/iqchart/src/IQChart.tsx",
  "libs/visualization/iqchart/src/useChartiqSwitchListener.tsx",
  "libs/visualization/plotly/src/BzPlotly.tsx",
  "libs/visualization/plotly/src/useBzPlotly.tsx",
  "libs/visualization/sankey/src/types/incomeStatement.tsx",
  "libs/visualization/trading-view-charting-libarary/src/TradingViewChart.tsx",
  "libs/visualization/trading-view-charting-libarary/src/useChartInitialize.tsx",
  "libs/visualization/trading-view-charting-libarary/src/layout/drawingLayouts.tsx",
  "libs/visualization/trading-view-charting-libarary/src/layout/drawingTooltip.tsx",
  "libs/visualization/trading-view-charting-libarary/src/layout/layouts.tsx",
  "libs/visualization/trading-view-charting-libarary/src/layout/rename.tsx",
  "libs/visualization/trading-view-charting-libarary/src/layout/saveTooltip.tsx",
  "libs/widget/chartwidget/src/components/Chart.tsx",
  "libs/widget/chartwidget/src/components/TimePeriodToolbar.tsx",
  "libs/widget/chartwidget/src/hooks/useCompanyProfile.tsx",
  "libs/widget/chartwidget/src/hooks/useConferenceCall.tsx",
  "libs/widget/chartwidget/src/hooks/useDividendData.tsx",
  "libs/widget/chartwidget/src/hooks/useEarningsData.tsx",
  "libs/widget/chartwidget/src/hooks/useNewsData.tsx",
  "libs/widget/chartwidget/src/hooks/usePressReleases.tsx",
  "libs/widget/chartwidget/src/hooks/usePriceHistory.tsx",
  "libs/widget/chartwidget/src/hooks/useSECFilings.tsx",
  "libs/widget/chartwidget/src/hooks/useTickerInfo.tsx",
  "libs/widget/chartwidget/src/lib/build-chart-widget.tsx",
  "libs/widget/chartwidget/src/lib/quote-widget.tsx",
  "libs/widget/pro-bz-chart/src/toolbar.tsx",
  "libs/widget/pro-bz-chart/src/widget.tsx",
  "libs/widget/pro-calendars/src/CalendarGrid.tsx",
  "libs/widget/pro-calendars/src/CalendarSettings.tsx",
  "libs/widget/pro-calendars/src/CalendarToolbar.tsx",
  "libs/widget/pro-calendars/src/component.tsx",
  "libs/widget/pro-calendars/src/DatePicker.tsx",
  "libs/widget/pro-calendars/src/widget.tsx",
  "libs/widget/pro-calendars/src/components/NoWatchlists/NoWatchlists.tsx",
  "libs/widget/pro-chart/src/globalSettings.tsx",
  "libs/widget/pro-chart/src/widget.tsx",
  "libs/widget/pro-chat/src/StreamSettings.tsx",
  "libs/widget/pro-chat/src/StreamWidget.tsx",
  "libs/widget/pro-chat/src/UpdateNicknameForm.tsx",
  "libs/widget/pro-chat/src/components/ChatMuteModal.tsx",
  "libs/widget/pro-chat/src/components/MessageInfoContext.tsx",
  "libs/widget/pro-chat/src/components/StreamApp.tsx",
  "libs/widget/pro-chat/src/components/StreamChat.tsx",
  "libs/widget/pro-chat/src/components/StreamWidgetContext.tsx",
  "libs/widget/pro-chat/src/components/useTriggers.tsx",
  "libs/widget/pro-chat/src/components/Attachment/Gallery.tsx",
  "libs/widget/pro-chat/src/components/Attachment/Image.tsx",
  "libs/widget/pro-chat/src/components/Attachment/ImageModal.tsx",
  "libs/widget/pro-chat/src/components/Channel/Channels.tsx",
  "libs/widget/pro-chat/src/components/Channel/ChannelsList.tsx",
  "libs/widget/pro-chat/src/components/Message/Message.tsx",
  "libs/widget/pro-chat/src/components/Message/MessageActions.tsx",
  "libs/widget/pro-chat/src/components/Message/MessageBody.tsx",
  "libs/widget/pro-chat/src/components/Message/MessageInputText.tsx",
  "libs/widget/pro-chat/src/components/Message/MessageTools.tsx",
  "libs/widget/pro-chat/src/components/MessageInput/MessageInput.tsx",
  "libs/widget/pro-chat/src/components/Quote/QuoteMessage.tsx",
  "libs/widget/pro-chat/src/components/Search/Search.tsx",
  "libs/widget/pro-chat/src/components/Thread/ThreadHeader.tsx",
  "libs/widget/pro-details/src/widget.tsx",
  "libs/widget/pro-details/src/fund/FundDetails.tsx",
  "libs/widget/pro-details/src/symbol/symbolDetails.tsx",
  "libs/widget/pro-details/src/symbol/components/header/header.tsx",
  "libs/widget/pro-details/src/symbol/components/header/title.tsx",
  "libs/widget/pro-details/src/symbol/components/header/alertsFactory/Wiim/index.tsx",
  "libs/widget/pro-details/src/symbol/components/news/newsFeed.tsx",
  "libs/widget/pro-details/src/symbol/components/overview/LightweightChart.tsx",
  "libs/widget/pro-details/src/symbol/components/overview/overview.tsx",
  "libs/widget/pro-details/src/symbol/components/overview/KeyStats/Tables/visualizations/index.tsx",
  "libs/widget/pro-details/src/symbol/crypto/CryptoDetails.tsx",
  "libs/widget/pro-details/src/symbol/stock/insiders.tsx",
  "libs/widget/pro-details/src/symbol/stock/peers.tsx",
  "libs/widget/pro-details/src/symbol/stock/stockDetails.tsx",
  "libs/widget/pro-details/src/symbol/stock/keyData/index.tsx",
  "libs/widget/pro-gpt/src/widget.tsx",
  "libs/widget/pro-gpt/src/components/DrawerMenu.tsx",
  "libs/widget/pro-gpt/src/components/GptChatArea.tsx",
  "libs/widget/pro-gpt/src/components/GptMessageArea.tsx",
  "libs/widget/pro-gpt/src/components/MessageArticles.tsx",
  "libs/widget/pro-gpt/src/components/MessageInlineArticle.tsx",
  "libs/widget/pro-gpt/src/components/MessageTickers.tsx",
  "libs/widget/pro-graph/src/NoSymbol.tsx",
  "libs/widget/pro-graph/src/widget.tsx",
  "libs/widget/pro-graph/src/components/editor.tsx",
  "libs/widget/pro-graph/src/components/graphConfigSelector.tsx",
  "libs/widget/pro-graph/src/components/SearchBar.tsx",
  "libs/widget/pro-graph/src/components/list/Collapse.tsx",
  "libs/widget/pro-graph/src/components/list/listItem.tsx",
  "libs/widget/pro-insiders/src/Insiders.tsx",
  "libs/widget/pro-insiders/src/InsidersGrid.tsx",
  "libs/widget/pro-insiders/src/toolbar.tsx",
  "libs/widget/pro-movers/src/Movers.tsx",
  "libs/widget/pro-movers/src/MoversGrid.tsx",
  "libs/widget/pro-movers/src/useMoversData.tsx",
  "libs/widget/pro-movers/src/widget.tsx",
  "libs/widget/pro-newsfeed/src/component.tsx",
  "libs/widget/pro-newsfeed/src/widget.tsx",
  "libs/widget/pro-newsfeed/src/components/content.tsx",
  "libs/widget/pro-newsfeed/src/components/FilterField.tsx",
  "libs/widget/pro-newsfeed/src/components/moreStoriesBanner.tsx",
  "libs/widget/pro-newsfeed/src/components/screenerFilters.tsx",
  "libs/widget/pro-newsfeed/src/components/story.tsx",
  "libs/widget/pro-newsfeed/src/components/storyContainer.tsx",
  "libs/widget/pro-newsfeed/src/components/storyHeadlines.tsx",
  "libs/widget/pro-newsfeed/src/components/storyTools.tsx",
  "libs/widget/pro-newsfeed/src/components/table.tsx",
  "libs/widget/pro-newsfeed/src/components/tableContainer.tsx",
  "libs/widget/pro-newsfeed/src/components/theme/theme.tsx",
  "libs/widget/pro-newsfeed/src/components/theme/themeList.tsx",
  "libs/widget/pro-newsfeed/src/components/theme/themePicker.tsx",
  "libs/widget/pro-newsfeed/src/components/theme/themeSlider.tsx",
  "libs/widget/pro-newsfeed/src/components/toolbar/DatePicker.tsx",
  "libs/widget/pro-newsfeed/src/components/toolbar/newsConfigSelector.tsx",
  "libs/widget/pro-newsfeed/src/components/toolbar/toolbar.tsx",
  "libs/widget/pro-newsfeed/src/context/feedContext.tsx",
  "libs/widget/pro-newsfeed/src/marketMoversNews/modal.tsx",
  "libs/widget/pro-newsfeed/src/utils/useGlobalSettings.tsx",
  "libs/widget/pro-notification/src/widget.tsx",
  "libs/widget/pro-notification/src/notifications/Container.tsx",
  "libs/widget/pro-notification/src/notifications/PanelBody.tsx",
  "libs/widget/pro-notification/src/notifications/PanelHeader.tsx",
  "libs/widget/pro-notification/src/notifications/TempSpinner.tsx",
  "libs/widget/pro-research/src/widget.tsx",
  "libs/widget/pro-scanner/src/ColumnsPanel.tsx",
  "libs/widget/pro-scanner/src/FiltersPanel.tsx",
  "libs/widget/pro-scanner/src/globalSettings.tsx",
  "libs/widget/pro-scanner/src/Scanner.tsx",
  "libs/widget/pro-scanner/src/ScannerContext.tsx",
  "libs/widget/pro-scanner/src/ScannerGrid.tsx",
  "libs/widget/pro-scanner/src/SettingsModal.tsx",
  "libs/widget/pro-scanner/src/filters/CheckboxGroup.tsx",
  "libs/widget/pro-scanner/src/filters/DateSelector.tsx",
  "libs/widget/pro-scanner/src/filters/FilterBox.tsx",
  "libs/widget/pro-scanner/src/filters/Filters.tsx",
  "libs/widget/pro-scanner/src/filters/RangeInput.tsx",
  "libs/widget/pro-scanner/src/filters/SelectFilter.tsx",
  "libs/widget/pro-scanner/src/filters/WatchlistFilter.tsx",
  "libs/widget/pro-scanner/src/filters2/FilterSearch.tsx",
  "libs/widget/pro-scanner/src/notifications/NotificationsPanel.tsx",
  "libs/widget/pro-scanner/src/Toolbar/ConfigSelect.tsx",
  "libs/widget/pro-scanner/src/Toolbar/createConfig.tsx",
  "libs/widget/pro-scanner/src/Toolbar/FiltersBar.tsx",
  "libs/widget/pro-scanner/src/Toolbar/FiltersBarSummary.tsx",
  "libs/widget/pro-scanner/src/Toolbar/Toolbar.tsx",
  "libs/widget/pro-signals/src/grid.tsx",
  "libs/widget/pro-signals/src/notifications.tsx",
  "libs/widget/pro-signals/src/screener.tsx",
  "libs/widget/pro-signals/src/signals.tsx",
  "libs/widget/pro-signals/src/signalsSearchbar.tsx",
  "libs/widget/pro-signals/src/toolbar.tsx",
  "libs/widget/pro-watchlist/src/alerts.tsx",
  "libs/widget/pro-watchlist/src/create.tsx",
  "libs/widget/pro-watchlist/src/delete.tsx",
  "libs/widget/pro-watchlist/src/edit.tsx",
  "libs/widget/pro-watchlist/src/grid.tsx",
  "libs/widget/pro-watchlist/src/import.tsx",
  "libs/widget/pro-watchlist/src/link.tsx",
  "libs/widget/pro-watchlist/src/toolbar.tsx",
  "libs/widget/pro-watchlist/src/widget.tsx",
  "libs/widget/pro-widget-utils/src/iframe-widget-helper.tsx",
  "libs/widget/pro-widget-utils/src/iframe-widget-with-optional-single-search.tsx",
  "libs/widget/pro-widget-utils/src/iframe-widget-with-single-search.tsx",
  "libs/widget/scanner/src/lib/ShortInterestScanner.tsx",
  "libs/widget/scanner/src/lib/SimpleScanner.tsx",
  "libs/widget/sensa-market/src/widget.tsx",
  "libs/widget/ticker-finder/src/lib/TickerFinder.tsx",
  'apps/bz/app/_components/root/RootTrackers.tsx',
  'apps/bz/pages/_app.tsx',
  'apps/bz/pages/account/index.tsx',
  'apps/bz/pages/apis/get-started.tsx',
  'apps/bz/pages/applications/[application].tsx',
  'apps/bz/pages/calendars/[calendar].tsx',
  'apps/bz/pages/dividends/index.tsx',
  'apps/bz/pages/earnings/index.tsx',
  'apps/bz/pages/login/google.tsx',
  'apps/bz/pages/login/index.tsx',
  'apps/bz/pages/margin-calculator/index.tsx',
  'apps/bz/pages/products/[vertical].tsx',
  'apps/bz/pages/register/index.tsx',
  'apps/bz/pages/report/[symbol].tsx',
  'apps/bz/pages/search/index.tsx',
  'apps/bz/pages/trade-ideas/traders/review/index.tsx',
  'apps/bz/pages/welcome/index.tsx',
  'apps/bz/src/components/News.tsx',
  'apps/bz/src/components/Page.tsx',
  'apps/bz/src/components/Account/Billing/CreditCardForm.tsx',
  'apps/bz/src/components/Home/components/HomeTabs.tsx',
  'apps/bz/src/components/Quote/Partners.tsx',
  'apps/bz/src/components/Quote/PressRelease.tsx',
  'apps/bz/src/components/Quote/QuoteTabs.tsx',
  'apps/bz/src/components/Quote/V2/News.tsx',
  'apps/bz/src/components/Quote/V2/CompareCharts/SymbolMenu.tsx',
  'apps/bz/src/components/RTChart/PublicComChart.tsx',
  'apps/bz/src/components/Sidebars/Widgets/ZingTotalsSidebarWidget.tsx',
  'apps/bz/src/ContextApi/context.ts',
  'apps/bz/utils/PageTracker.tsx',
  'apps/bz-mobile/src/app/App.tsx',
  'apps/bz-mobile/src/components/AppleLogin.tsx',
  'apps/bz-mobile/src/components/CreateWatchlistButton.tsx',
  'apps/bz-mobile/src/components/PriceAlerts.tsx',
  'apps/bz-mobile/src/components/QuoteView.tsx',
  'apps/bz-mobile/src/components/SquawkBar.tsx',
  'apps/bz-mobile/src/components/WatchlistTabs.tsx',
  'apps/bz-mobile/src/components/DevTools/index.tsx',
  'apps/bz-mobile/src/components/Ideas/Card.tsx',
  'apps/bz-mobile/src/components/Ideas/TradeIdeaFeed.tsx',
  'apps/bz-mobile/src/components/Quote/QuoteTab.tsx',
  'apps/bz-mobile/src/components/Quote/QuoteTile.tsx',
  'apps/bz-mobile/src/components/Quote/SummaryCard.tsx',
  'apps/bz-mobile/src/components/Rewards/RewardFeed.tsx',
  'apps/bz-mobile/src/components/Rewards/ZingReferral.tsx',
  'apps/bz-mobile/src/components/Watchlist/WatchlistQuotesView.tsx',
  'apps/bz-mobile/src/hooks/useInAppPurchase.ts',
  'apps/bz-mobile/src/screens/NotificationScreen.tsx',
  'apps/bz-mobile/src/screens/QuoteScreen.tsx',
  'apps/bz-mobile/src/screens/Account/AccountScreen.tsx',
  'apps/bz-mobile/src/screens/Account/NotificationSettingsScreen.tsx',
  'apps/bz-mobile/src/screens/Ideas/ShareTradeIdeasScreen.tsx',
  'apps/bz-mobile/src/screens/Ideas/TradeIdeaScreen.tsx',
  'apps/bz-mobile/src/screens/Ideas/TradeIdeasScreen.tsx',
  'apps/bz-mobile/src/screens/News/NewsCommonList.tsx',
  'apps/bz-mobile/src/screens/OnBoarding/WelcomeAlertScreen.tsx',
  'apps/bz-mobile/src/screens/OnBoarding/WelcomeScreen.tsx',
  'apps/bz-mobile/src/screens/Rewards/RewardsScreen.tsx',
  'apps/bz-mobile/src/screens/Rewards/ZingTotalTitle.tsx',
  'apps/bz-mobile/src/stream-chat-utils/index.ts',
  'apps/bz-mobile/src/theme/themecontext.tsx',
  'apps/bz-mobile/src/theme/useColorScheme.ts',
  'apps/india/pages/_app.tsx',
  'apps/india/pages/login/google.tsx',
  'apps/india/pages/login/index.tsx',
  'apps/india/src/components/Page.tsx',
  'apps/money/pages/_app.tsx',
  'apps/money/src/components/Page.tsx',
  'apps/newsdesk-tools/src/app/components/Site.tsx',
  'apps/newsdesk-tools/src/app/components/chat/ChannelComponent.tsx',
  'apps/newsdesk-tools/src/app/components/chat/ChatComponent.tsx',
  'apps/newsdesk-tools/src/app/components/logos/Logos.tsx',
  'apps/pro/src/components/ContextsInitializer.tsx',
  'apps/pro/src/components/HotkeyGate.tsx',
  'apps/pro/src/components/SideBar.tsx',
  'apps/pro/src/components/dashboard/AdBanner.tsx',
  'apps/pro/src/components/dashboard/Dashboard.tsx',
  'apps/pro/src/components/dashboard/MenuItem.tsx',
  'apps/pro/src/components/dashboard/PlatformBanner.tsx',
  'apps/pro/src/components/dashboard/PlatformBar.tsx',
  'apps/pro/src/components/dashboard/WidgetMenuItem.tsx',
  'apps/pro/src/components/dashboard/WidgetMenuItems.tsx',
  'apps/pro/src/components/dashboard/createWorkspace/createWorkspace.tsx',
  'apps/pro/src/components/dashboard/createWorkspace/components/preset.tsx',
  'apps/pro/src/components/dashboard/createWorkspace/components/widgetItem.tsx',
  'apps/pro/src/components/dashboard/help/help.tsx',
  'apps/pro/src/components/dashboard/help/helpButtonBox.tsx',
  'apps/pro/src/components/dashboard/help/helpContent.tsx',
  'apps/pro/src/components/dashboard/help/moreHelp.tsx',
  'apps/pro/src/components/dashboard/homeMenu/homeMenu.tsx',
  'apps/pro/src/components/dashboard/platformBarItems/hotkeyReference.tsx',
  'apps/pro/src/components/dashboard/welcome/welcome.tsx',
  'apps/pro/src/components/dashboard/welcome/welcomeButtonBox.tsx',
  'apps/pro/src/components/dashboard/welcome/welcomeWorkspace.tsx',
  'apps/pro/src/components/dashboard/workspace/EmptyWorkspace.tsx',
  'apps/pro/src/components/dashboard/workspace/Workspace.tsx',
  'apps/pro/src/components/dashboard/workspace/WorkspaceNav.tsx',
  'apps/pro/src/components/dashboard/workspace/WorkspaceTab.tsx',
  'apps/pro/src/components/home/<USER>',
  'apps/pro/src/components/ui/Popout.tsx',
  'apps/pro/src/components/ui/SquawkChannel.tsx',
  'apps/pro/src/components/ui/SquawkChannelsContainer.tsx',
  'apps/pro/src/components/ui/modal/PurchaseConfirmation.tsx',
  'apps/pro/src/components/user/Reset.tsx',
  'apps/pro/src/components/user/preferences/Preferences.tsx',
  'apps/pro/src/components/user/preferences/Settings.tsx',
  'apps/pro/src/components/user/preferences/account/About.tsx',
  'apps/pro/src/components/user/preferences/billing/StripeForm.tsx',
  'apps/pro/src/components/user/preferences/billing/Billing/Billing.tsx',
  'apps/pro/src/components/user/preferences/layouts/Layouts.tsx',
  'apps/pro/src/components/user/preferences/layouts/components/AutoSavedLayoutsGrid/AutoSavedLayoutsGrid.tsx',
  'apps/pro/src/components/user/preferences/layouts/components/buttons/buttons.tsx',
  'apps/pro/src/components/user/preferences/layouts/components/SavedLayoutsGrid/SavedLayoutsGrid.tsx',
  'apps/pro/src/components/widgets/WidgetContainer.tsx',
  'apps/pro/src/layout/LayoutsGate.tsx',
  'apps/proto/pages/_app.tsx',
  'apps/proto/pages/widgets/chart/index.tsx',
  'apps/proto/pages/widgets/newsfeed/index.tsx',
  'apps/proto/src/components/Page.tsx',
  'apps/proto/src/components/WidgetConfig/index.tsx',
  'apps/widgets/pro-insiders/src/app/app.tsx',
  'libs/react-utils/data-hooks/calendar-manager/src/useAnalystRatings.ts',
  'libs/react-utils/data-hooks/calendar-manager/src/useCalendarData.ts',
  'libs/react-utils/data-hooks/news-manager/src/useGetLatestStory.tsx',
  'libs/react-utils/data-hooks/news-manager/src/useGetMarketMoversNews.tsx',
  'libs/react-utils/data-hooks/watchlist-manager/src/useWatchlistLoading.tsx',
  'libs/react-utils/data-hooks/watchlist-manager/src/useWatchlistsSymbols.tsx',
  'libs/react-utils/data-hooks/watchlist-manager/src/useWatchlistSymbols.tsx',
  'libs/react-utils/hooks/src/hooks/useCompare.ts',
  'libs/react-utils/hooks/src/hooks/useElementPosition.tsx',
  'libs/react-utils/hooks/src/hooks/useElementSize.tsx',
  'libs/react-utils/hooks/src/hooks/useEventCallback.tsx',
  'libs/react-utils/hooks/src/hooks/useForceUpdate.tsx',
  'libs/react-utils/hooks/src/hooks/useLocalStorage.tsx',
  'libs/react-utils/hooks/src/hooks/useMountUnmountRef.tsx',
  'libs/react-utils/hooks/src/hooks/useRefElementSize.tsx',
  'libs/react-utils/hooks/src/hooks/useScrollBarVisible.tsx',
  'libs/react-utils/hooks/src/hooks/useStateCallback.tsx',
  'libs/react-utils/hooks/src/hooks/useStateRef.tsx',
  'libs/react-utils/hooks/src/hooks/useSubscription.tsx',
  'libs/react-utils/pro-tools/src/pro-context.tsx',
  'libs/react-utils/pro-tools/src/workspace-context.tsx',
  'libs/react-utils/session-context/src/context.tsx',
  'libs/react-utils/themetron/src/components/components/dropdownMenu.tsx',
  'libs/react-utils/themetron/src/components/layout/constrainedFloater.tsx',
  'libs/react-utils/user-context/src/context.tsx',
  'libs/react-utils/user-context/src/hijackComponent.tsx',
  'libs/react-utils/user-context/src/loggedInComponent.tsx',
  'libs/react-utils/user-context/src/loginPrompt.tsx',
  'libs/react-utils/user-context/src/loginRegister.tsx',
  'libs/react-utils/user-context/src/permissionedComponent.tsx',
  'libs/react-utils/user-context/src/permissionPrompt.tsx',
  'libs/react-utils/user-context/src/upSellPrompt.tsx',
  'libs/react-utils/user-context/src/hooks/useIsUserLoggedIn.tsx',
  'libs/react-utils/widget-tools/src/context.tsx',
  'libs/react-utils/widget-tools/src/globalSettingsContext.tsx',
  'libs/react-utils/widget-tools/src/parameterContext.tsx',
  'libs/react-utils/widget-tools/src/toolbarContext.tsx',

  'libs/ui/ads/src/components/GoogleAdUnit.tsx',
  'libs/ui/ads/src/components/InvestingChannel.tsx',
  'libs/ui/ads/src/components/MediaAlpha/MediaAlphaInsuranceQuotes.tsx',
  'libs/ui/ads/src/components/Primis/index.tsx',
  'libs/ui/ads/src/components/Taboola/TaboolaPlacement.tsx',
  'libs/ui/ads/src/components/Transparently/index.tsx',
  'libs/ui/ads-utils/src/lib/raptive/useRaptiveAdManager.ts',
  'libs/ui/alternative-investments/src/components/Offerings.tsx',
  'libs/ui/alternative-investments/src/components/OfferingsFilter.stories.tsx',
  'libs/ui/alternative-investments/src/components/AltOfferingForm/index.tsx',
  'libs/ui/amp/src/components/Campaigns/AmpWinStockOrCrypto.tsx',
  'libs/ui/article/src/context.ts',
  'libs/ui/article/src/components/AdvertiserDisclosure.tsx',
  'libs/ui/article/src/components/ArticlePopup.tsx',
  'libs/ui/article/src/components/Ads/Taboola/NewTaboola.tsx',
  'libs/ui/article/src/components/Widgets/EditorBox.tsx',
  'libs/ui/article/src/components/Widgets/EditorialPushNotificationModal.tsx',
  'libs/ui/article/src/hooks/useInfiniteArticles.tsx',
  'libs/ui/atomics/src/components/addToWatchlist.tsx',
  'libs/ui/auth/src/components/AuthPortal.tsx',
  'libs/ui/auth/src/components/AuthModal/index.tsx',
  'libs/ui/auth/src/components/AuthModal/ReCaptcha.tsx',
  'libs/ui/auth/src/components/CaptchadLogin/index.tsx',
  'libs/ui/auth/src/components/Login/index.tsx',
  'libs/ui/auth/src/components/Register/index.tsx',
  'libs/ui/blocks/src/context.ts',
  'libs/ui/blocks/src/components/LayoutAdmin.tsx',
  'libs/ui/bz-onboarding/src/components/OnboardingHubspotForm.tsx',
  'libs/ui/bz-onboarding/src/components/PersonalNewsFeed.tsx',
  'libs/ui/calculators/src/components/SelectContainer.tsx',
  'libs/ui/calculators/src/components/CapitalExpenseCalculator/index.tsx',
  'libs/ui/calculators/src/components/CapitalRateCalculator/index.tsx',
  'libs/ui/calculators/src/components/ForexProfitCalculator/index.tsx',
  'libs/ui/calculators/src/components/HYSACalculator/index.tsx',
  'libs/ui/calculators/src/components/MarginCalculator/index.tsx',
  'libs/ui/calculators/src/components/MortgageCalculator/index.tsx',
  'libs/ui/calculators/src/components/WorkersCompensationCalculator/index.tsx',
  'libs/ui/calendars/src/components/AnalystRatingCalendar/index.tsx',
  'libs/ui/calendars/src/components/CalendarsWidget/index.tsx',
  'libs/ui/calendars/src/components/CalendarsWidget/Table.tsx',
  'libs/ui/calendars/src/components/FDACalendar/index.tsx',
  'libs/ui/calendars/src/components/InsiderTradesCalendar/InsiderTooltipCell.tsx',
  'libs/ui/calendars/src/components/lib/components/ClickableStatusBarComponent.tsx',
  'libs/ui/calendars/src/components/lib/components/StockTable.tsx',
  'libs/ui/calendars/src/components/ShortInterestCalendar/index.tsx',
  'libs/ui/calendars/src/components/SpacCalendar/index.tsx',
  'libs/ui/core/src/components/Dialog/index.tsx',
  'libs/ui/core/src/components/Dropdown/index.tsx',
  'libs/ui/core/src/components/Grid/index.tsx',
  'libs/ui/core/src/components/Icon/index.tsx',
  'libs/ui/core/src/components/Inputs/CheckboxGroup/index.tsx',
  'libs/ui/core/src/components/Inputs/Input/index.tsx',
  'libs/ui/core/src/components/Inputs/RadioGroup/index.tsx',
  'libs/ui/core/src/components/Inputs/Slider/index.tsx',
  'libs/ui/core/src/components/Inputs/StepNumberInput/index.tsx',
  'libs/ui/core/src/components/Modal/Modal.tsx',
  'libs/ui/core/src/components/Skeleton/index.tsx',
  'libs/ui/core/src/components/StepNumberInput/index.tsx',
  'libs/ui/core/src/components/Tabs/index.tsx',
  'libs/ui/core/src/components/Tooltip/index.tsx',
  'libs/ui/core/src/utils/ResizeDetector.tsx',
  'libs/ui/filter/src/components/MultiSelectGroup/index.tsx',
  'libs/ui/filter/src/components/RangeSelectGroup/index.tsx',
  'libs/ui/filter/src/components/SingleSelectGroup/index.tsx',
  'libs/ui/filter/src/hooks/useFilters.tsx',
  'libs/ui/forms/src/components/AltsForm/index.tsx',
  'libs/ui/forms/src/components/CallToActionForm/EmailCapture.tsx',
  'libs/ui/forms/src/components/CallToActionForm/index.tsx',
  'libs/ui/image/src/lib/image.tsx',
  'libs/ui/image/src/lib/useIntersection.tsx',
  'libs/ui/miller-columns/src/components/MillerColumns/MillerColumns.tsx',
  'libs/ui/money/src/components/Blocks/BZLogoSearch.tsx',
  'libs/ui/money/src/components/Blocks/ProductsListBlock.tsx',
  'libs/ui/money/src/components/Campaign/CampaignPopup.tsx',
  'libs/ui/money/src/components/Campaign/FloatingPopup.tsx',
  'libs/ui/money/src/components/Futures/index.tsx',
  'libs/ui/money/src/components/Popup/index.tsx',
  'libs/ui/money/src/components/TableOfContents/index.tsx',
  'libs/ui/money/src/context/AffiliateContext.tsx',
  'libs/ui/navigation/src/components/QuoteBox.tsx',
  'libs/ui/navigation/src/components/TopBar.tsx',
  'libs/ui/navigation/src/components/BreakingNewsBanner/hooks/useInterval.ts',
  'libs/ui/navigation/src/components/Header/HeaderMobile.tsx',
  'libs/ui/navigation/src/components/Header/index.tsx',
  'libs/ui/navigation/src/components/Header/QuotesList.tsx',
  'libs/ui/news/src/components/NewsFeed.tsx',
  'libs/ui/news/src/components/BenzingaBriefNewsElement/index.tsx',
  'libs/ui/news/src/components/ContentFeed/index.tsx',
  'libs/ui/news/src/components/LatestNewsList/index.tsx',
  'libs/ui/news/src/components/NewsListWithLoadMore/index.tsx',
  'libs/ui/pro-ui/src/components/AgGridUtils/MultipleTickersCellRender.tsx',
  'libs/ui/pro-ui/src/components/AgGridUtils/NotesTooltipCellRenderer.tsx',
  'libs/ui/pro-ui/src/components/AgGridUtils/PriceAlertTooltipCellRenderer.tsx',
  'libs/ui/pro-ui/src/components/AgGridUtils/TimeFilter/TimeFilter.tsx',
  'libs/ui/pro-ui/src/components/AgGridUtils/TimeFilter/TimeRange.tsx',
  'libs/ui/pro-ui/src/components/ConfigSelector/ConfigSelector.tsx',
  'libs/ui/pro-ui/src/components/ConfigSelector/ConfigSelectorModal.tsx',
  'libs/ui/pro-ui/src/components/ContextMenu/ContextMenu.tsx',
  'libs/ui/pro-ui/src/components/Grid/grid.tsx',
  'libs/ui/pro-ui/src/components/InfoBar/InfoBarInner.tsx',
  'libs/ui/pro-ui/src/components/InfoBar/Modules/useNewsdeskNotifications.ts',
  'libs/ui/pro-ui/src/components/NoResults/index.tsx',
  'libs/ui/pro-ui/src/components/NotesDialog/NotesDialog.tsx',
  'libs/ui/pro-ui/src/components/Preferences/switchboxHorizontal.tsx',
  'libs/ui/pro-ui/src/components/Preferences/toggleNew.tsx',
  'libs/ui/pro-ui/src/components/PriceAlertDialog/PriceAlertDialog.tsx',
  'libs/ui/pro-ui/src/components/Search/context.tsx',
  'libs/ui/pro-ui/src/components/Search/Expression/ExpressionSearch.tsx',
  'libs/ui/pro-ui/src/components/Search/ExpressionBuilder/ExpressionBuilder.tsx',
  'libs/ui/pro-ui/src/components/Search/ExpressionBuilder/ExpressionBuilderContext.tsx',
  'libs/ui/pro-ui/src/components/Search/ExpressionBuilder/ExpressionBuilderSections.tsx',
  'libs/ui/pro-ui/src/components/Search/hooks/useAutocompleteDataHooks.ts',
  'libs/ui/pro-ui/src/components/Search/SearchBar/SearchBar.tsx',
  'libs/ui/pro-ui/src/components/Search/SearchBar/Tools/ClipboardTool.tsx',
  'libs/ui/pro-ui/src/components/Search/Tag/TagSearch.tsx',
  'libs/ui/pro-ui/src/components/Tab/Tab.tsx',
  'libs/ui/pro-ui/src/components/Ticker/MultipleTickerHover.tsx',
  'libs/ui/pro-ui/src/components/Ticker/MultipleTickers.tsx',
  'libs/ui/pro-ui/src/components/Ticker/TickerContext.tsx',
  'libs/ui/pro-ui/src/components/Ticker/TickerSnippet.tsx',
  'libs/ui/pro-ui/src/components/Ticker/TickerTooltipCellRenderer.tsx',
  'libs/ui/pro-ui/src/components/Ticker/Modules/TickerHeader.tsx',
  'libs/ui/pro-ui/src/components/Ticker/Ticker/Ticker.tsx',
  'libs/ui/pro-ui/src/components/TickerColor/TickerColor.tsx',
  'libs/ui/pro-ui/src/components/TickerHover/context.tsx',
  'libs/ui/pro-ui/src/components/TickerHover/hover.tsx',
  'libs/ui/pro-ui/src/components/TickerHover/popover.tsx',
  'libs/ui/pro-ui/src/components/TickerHover/modules/header.tsx',
  'libs/ui/pro-ui/src/components/TimeFilter/index.tsx',
  'libs/ui/pro-ui/src/components/TimeFilter/TimeRange.tsx',
  'libs/ui/pro-ui/src/components/WidgetLinking/SearchLinking/OptionalTagSearchLinked.tsx',
  'libs/ui/pro-ui/src/components/WidgetLinking/SearchLinking/TagSearchLinked.tsx',
  'libs/ui/pro-ui/src/components/WidgetLinking/WidgetLinkingPicker/GroupEditModal.tsx',
  'libs/ui/pro-ui/src/components/WidgetLinking/WidgetLinkingPicker/GroupSelectDropdown.tsx',
  'libs/ui/pro-ui/src/components/WidgetLinking/WidgetLinkingPicker/SendLinkContext.tsx',
  'libs/ui/product/src/UsersChoiceCard/index.tsx',
  'libs/ui/quotes/src/components/StockCommodities/index.tsx',
  'libs/ui/quotes/src/components/StockMovers/index.tsx',
  'libs/ui/quotes/src/components/StockMoversTable/index.tsx',
  'libs/ui/reviews/src/components/SubmitReviewBox.tsx',
  'libs/ui/search/src/Search/index.tsx',
  'libs/ui/table/src/components/Table/index.tsx',
  'libs/ui/table/src/components/TableVirtualized/index.tsx',
  'libs/ui/templates/src/components/Article/ArticlePageTemplate.tsx',
  'libs/ui/templates/src/components/Profile/ProfileLayout.tsx',
  'libs/ui/ticker/src/components/TickerSearch/index.tsx',
  'libs/ui/ticker/src/components/TickerSearch/TickerSearch.stories.tsx',
  'libs/ui/ticker/src/components/TickerSelect/index.tsx',
  'libs/ui/ticker/src/components/TickerSelect/TickerSelect.stories.tsx',
  'libs/ui/trade-ideas/src/components/FinancialAssetAllocationSelect/FinancialAssetAllocationSelect.stories.tsx',
  'libs/ui/trade-ideas/src/components/FinancialAssetAllocationSelect/index.tsx',
  'libs/ui/trade-ideas/src/components/FinancialAssetPositionSelect/FinancialAssetPositionSelect.stories.tsx',
  'libs/ui/trade-ideas/src/components/FinancialAssetPositionSelect/index.tsx',
  'libs/ui/trade-ideas/src/components/TradeDurationSelect/index.tsx',
  'libs/ui/trade-ideas/src/components/TradeDurationSelect/TradeDurationSelect.stories.tsx',
  'libs/ui/trade-ideas/src/components/TradeIdeaList/index.tsx',
  'libs/ui/trade-ideas/src/components/TradeIdeaListItem/FlagButton.tsx',
  'libs/ui/trade-ideas/src/components/TradeIdeaListItem/index.tsx',
  'libs/ui/trade-ideas/src/components/TradeIdeaListItem/TipZingButton.tsx',
  'libs/ui/trade-ideas/src/components/TradeIdeaListItem/TradeIdeaListItemComments.tsx',
  'libs/ui/trade-ideas/src/components/TradeIdeaPostPortal/index.tsx',
  'libs/ui/ui/src/components/Carousel/index.tsx',
  'libs/ui/ui/src/components/Movers/index.tsx',
  'libs/ui/ui/src/components/Plan/index.tsx',
  'libs/ui/ui/src/components/ShareButtons/index.tsx',
  'libs/ui/ui/src/components/Tabs/TabContext.tsx',
  'libs/ui/ui/src/components/YieldTable/index.tsx',
  'libs/ui/user/src/components/CreditCard/index.tsx',
  'libs/ui/watchlist-ui/src/components/AddToWatchlist/index.tsx',
  'libs/ui/watchlist-ui/src/components/AddToWatchlist/WatchlistsDropdown.tsx',
  'libs/ui/widgets/src/components/WatchlistCreateBox/index.tsx',
  'libs/ui/widgets/src/components/WatchlistMovers/index.tsx',
  'libs/ui/widgets/src/components/WatchlistSelect/index.tsx',
  'libs/ui/widgets/src/components/WatchlistSelect/WatchlistSelect.stories.tsx',
  'libs/utils/analytics/src/context.ts',
  'libs/utils/analytics/src/PageTracker.tsx',
  'libs/utils/analytics/src/SmartLink/index.tsx',
  'libs/visualization/analyst-ratings/src/analyst-ratings.tsx',
  'libs/visualization/iqchart/src/internal.tsx',
  'libs/visualization/iqchart/src/IQChart.tsx',
  'libs/visualization/plotly/src/BzPlotly.tsx',
  'libs/visualization/plotly/src/useBzLayout.ts',
  'libs/visualization/trading-view-charting-libarary/src/TradingViewChart.tsx',
  'libs/visualization/trading-view-charting-libarary/src/useChartInitialize.tsx',
  'libs/visualization/trading-view-charting-libarary/src/layout/drawingLayouts.tsx',
  'libs/visualization/trading-view-charting-libarary/src/layout/layouts.tsx',
  'libs/visualization/trading-view-charting-libarary/src/layout/layoutSave.tsx',
  'libs/visualization/trading-view-charting-libarary/src/layout/rename.tsx',
  'libs/widget/chartwidget/src/components/TimePeriodToolbar.tsx',
  'libs/widget/chartwidget/src/lib/quote-widget.tsx',
  'libs/widget/pro-bz-chart/src/content.tsx',
  'libs/widget/pro-bz-chart/src/toolbar.tsx',
  'libs/widget/pro-bz-chart/src/widget.tsx',
  'libs/widget/pro-calendars/src/CalendarGrid.tsx',
  'libs/widget/pro-calendars/src/CalendarSearchbar.tsx',
  'libs/widget/pro-calendars/src/CalendarSettings.tsx',
  'libs/widget/pro-calendars/src/CalendarToolbar.tsx',
  'libs/widget/pro-calendars/src/component.tsx',
  'libs/widget/pro-calendars/src/DatePicker.tsx',
  'libs/widget/pro-calendars/src/widget.tsx',
  'libs/widget/pro-chart/src/globalSettings.tsx',
  'libs/widget/pro-chart/src/widget.tsx',
  'libs/widget/pro-chat/src/ChatMediaPreferences.tsx',
  'libs/widget/pro-chat/src/StreamSettings.tsx',
  'libs/widget/pro-chat/src/StreamWidget.tsx',
  'libs/widget/pro-chat/src/components/MessageInfoContext.tsx',
  'libs/widget/pro-chat/src/components/StreamChannelContext.tsx',
  'libs/widget/pro-chat/src/components/StreamChat.tsx',
  'libs/widget/pro-chat/src/components/StreamSettingContext.tsx',
  'libs/widget/pro-chat/src/components/StreamWidgetContext.tsx',
  'libs/widget/pro-chat/src/components/useTriggers.tsx',
  'libs/widget/pro-chat/src/components/Attachment/ImageModal.tsx',
  'libs/widget/pro-chat/src/components/Channel/ChannelsList.tsx',
  'libs/widget/pro-chat/src/components/Message/Message.tsx',
  'libs/widget/pro-chat/src/components/Message/MessageInputText.tsx',
  'libs/widget/pro-chat/src/components/Message/MessageTools.tsx',
  'libs/widget/pro-chat/src/components/MessageInput/MessageInput.tsx',
  'libs/widget/pro-chat/src/components/Search/Search.tsx',
  'libs/widget/pro-chat/src/components/Thread/ThreadHeader.tsx',
  'libs/widget/pro-details/src/widget.tsx',
  'libs/widget/pro-details/src/symbol/components/header/header.tsx',
  'libs/widget/pro-details/src/symbol/components/news/newsTab.tsx',
  'libs/widget/pro-details/src/symbol/components/overview/LightweightChart.tsx',
  'libs/widget/pro-details/src/symbol/components/overview/overview.tsx',
  'libs/widget/pro-details/src/symbol/crypto/CryptoDetails.tsx',
  'libs/widget/pro-details/src/symbol/stock/calendar.tsx',
  'libs/widget/pro-details/src/symbol/stock/calendarTab.tsx',
  'libs/widget/pro-details/src/symbol/stock/insiders.tsx',
  'libs/widget/pro-details/src/symbol/stock/news.tsx',
  'libs/widget/pro-details/src/symbol/stock/peers.tsx',
  'libs/widget/pro-details/src/symbol/stock/stockDetails.tsx',
  'libs/widget/pro-details/src/symbol/stock/financials/SymbolDetailsFinancials.tsx',
  'libs/widget/pro-gpt/src/widget.tsx',
  'libs/widget/pro-gpt/src/components/DrawerMenu.tsx',
  'libs/widget/pro-gpt/src/components/GptChatArea.tsx',
  'libs/widget/pro-gpt/src/components/GptMessageArea.tsx',
  'libs/widget/pro-gpt/src/components/MessageTickers.tsx',
  'libs/widget/pro-graph/src/widget.tsx',
  'libs/widget/pro-graph/src/components/graphConfigSelector.tsx',
  'libs/widget/pro-graph/src/components/SearchBar.tsx',
  'libs/widget/pro-graph/src/components/list/Collapse.tsx',
  'libs/widget/pro-graph/src/components/list/listItem.tsx',
  'libs/widget/pro-graph/src/components/list/listTrace.tsx',
  'libs/widget/pro-insiders/src/Insiders.tsx',
  'libs/widget/pro-insiders/src/toolbar.tsx',
  'libs/widget/pro-movers/src/Movers.tsx',
  'libs/widget/pro-movers/src/widget.tsx',
  'libs/widget/pro-newsfeed/src/component.tsx',
  'libs/widget/pro-newsfeed/src/widget.tsx',
  'libs/widget/pro-newsfeed/src/components/FilterField.tsx',
  'libs/widget/pro-newsfeed/src/components/globalSettings.tsx',
  'libs/widget/pro-newsfeed/src/components/moreStoriesBanner.tsx',
  'libs/widget/pro-newsfeed/src/components/screenerFilters.tsx',
  'libs/widget/pro-newsfeed/src/components/story.tsx',
  'libs/widget/pro-newsfeed/src/components/storyContainer.tsx',
  'libs/widget/pro-newsfeed/src/components/storyHeadlines.tsx',
  'libs/widget/pro-newsfeed/src/components/storyTools.tsx',
  'libs/widget/pro-newsfeed/src/components/table.tsx',
  'libs/widget/pro-newsfeed/src/components/tableContainer.tsx',
  'libs/widget/pro-newsfeed/src/components/theme/theme.tsx',
  'libs/widget/pro-newsfeed/src/components/theme/themePicker.tsx',
  'libs/widget/pro-newsfeed/src/components/theme/themeSlider.tsx',
  'libs/widget/pro-newsfeed/src/components/toolbar/DatePicker.tsx',
  'libs/widget/pro-newsfeed/src/components/toolbar/newsConfigSelector.tsx',
  'libs/widget/pro-newsfeed/src/components/toolbar/toolbar.tsx',
  'libs/widget/pro-newsfeed/src/context/feedContext.tsx',
  'libs/widget/pro-newsfeed/src/context/internalSettingContext.tsx',
  'libs/widget/pro-newsfeed/src/marketMoversNews/marketMoversNews.tsx',
  'libs/widget/pro-newsfeed/src/marketMoversNews/modal.tsx',
  'libs/widget/pro-newsfeed/src/utils/useGlobalSettings.tsx',
  'libs/widget/pro-notification/src/widget.tsx',
  'libs/widget/pro-notification/src/notifications/Container.tsx',
  'libs/widget/pro-notification/src/notifications/PanelBody.tsx',
  'libs/widget/pro-notification/src/notifications/PanelHeader.tsx',
  'libs/widget/pro-notification/src/notifications/useFilters.ts',
  'libs/widget/pro-research/src/widget.tsx',
  'libs/widget/pro-scanner/src/ColumnsPanel.tsx',
  'libs/widget/pro-scanner/src/globalSettings.tsx',
  'libs/widget/pro-scanner/src/Scanner.tsx',
  'libs/widget/pro-scanner/src/ScannerContext.tsx',
  'libs/widget/pro-scanner/src/ScannerGrid.tsx',
  'libs/widget/pro-scanner/src/filters/DateSelector.tsx',
  'libs/widget/pro-scanner/src/filters/Filters.tsx',
  'libs/widget/pro-scanner/src/filters/RangeInput.tsx',
  'libs/widget/pro-scanner/src/filters/WatchlistFilter.tsx',
  'libs/widget/pro-scanner/src/notifications/NotificationsPanel.tsx',
  'libs/widget/pro-scanner/src/Toolbar/ConfigSelect.tsx',
  'libs/widget/pro-scanner/src/Toolbar/createConfig.tsx',
  'libs/widget/pro-scanner/src/Toolbar/FiltersBar.tsx',
  'libs/widget/pro-scanner/src/Toolbar/Toolbar.tsx',
  'libs/widget/pro-signals/src/notifications.tsx',
  'libs/widget/pro-signals/src/screener.tsx',
  'libs/widget/pro-signals/src/signals.tsx',
  'libs/widget/pro-signals/src/signalsSearchbar.tsx',
  'libs/widget/pro-signals/src/toolbar.tsx',
  'libs/widget/pro-watchlist/src/alerts.tsx',
  'libs/widget/pro-watchlist/src/create.tsx',
  'libs/widget/pro-watchlist/src/delete.tsx',
  'libs/widget/pro-watchlist/src/edit.tsx',
  'libs/widget/pro-watchlist/src/globalSettings.tsx',
  'libs/widget/pro-watchlist/src/grid.tsx',
  'libs/widget/pro-watchlist/src/link.tsx',
  'libs/widget/pro-watchlist/src/toolbar.tsx',
  'libs/widget/pro-watchlist/src/widget.tsx',
  'libs/widget/pro-widget-utils/src/iframe-widget-helper.tsx',
  'libs/widget/pro-widget-utils/src/iframe-widget-with-optional-single-search.tsx',
  'libs/widget/pro-widget-utils/src/iframe-widget-with-single-search.tsx',




];

module.exports = {
  filePaths,
};
