'use client';
import styled, { hueToColorAlpha } from '@benzinga/themetron';
import { SessionContext } from '@benzinga/session-context';
import { Signal, SignalConfig, SignalGroup, SignalType, signalTypeToFeature } from '@benzinga/signals-manager';
import { PermissionedComponent } from '@benzinga/user-context';
import { hexToRGBA } from '@benzinga/utils';

import { Checkbox, Dropdown, Menu, Tabs } from 'antd';
import { FilterOutlined, LineChartOutlined, NotificationOutlined, QuestionOutlined } from '@ant-design/icons';
import React from 'react';

import { signalGroupConfigsById, signalTypes } from './data';
import { SignalsHelp } from './help';
import { NotificationType, SignalsNotifications } from './notifications';
import { Scanner } from './screener';
import { SignalsSearchBar } from './signalsSearchbar';
import { TrackingManager } from '@benzinga/tracking-manager';
import { Lock } from '@benzinga/themed-icons';
import { SignalsScreenerFilter, SignalsModuleItem } from '@benzinga/signals-user-settings';
import { SignalsConfigSelector } from './signalsConfigSelector';
import { PermissionsManager } from '@benzinga/permission-manager';

interface Props {
  addSignal: (signal: Signal) => void;
  enabledNotifications: NotificationType[];
  filters: SignalsModuleItem[];
  screenerFilters: SignalsScreenerFilter[];
  setEnabledNotifications: (enabledNotifications: NotificationType[]) => void;
  selectedSignalTypes: SignalType[];
  setFilters: (filters: SignalsModuleItem[]) => void;
  setScannerFilters: (screenerFilters: SignalsScreenerFilter[]) => void;
  setSelectedSignalTypes: (selectedSignalTypes: SignalType[]) => void;
  setShowUpsellModal: (showUpsellModal: boolean) => void;
}

const tabLabel = (props: { icon?: React.ReactElement | null; label: string; size?: number }) => {
  const { icon, label, size } = props;
  const badge = size && size > 0 ? <TabBadge>{size}</TabBadge> : null;
  return (
    <Row style={{ flexWrap: 'nowrap' }}>
      {icon}
      {label}
      {badge}
    </Row>
  );
};

export const SignalsToolbar: React.FC<Props> = props => {
  const session = React.useContext(SessionContext);
  const tags = React.useMemo(() => props.filters, [props.filters]);
  const onTagsChange = React.useCallback(
    (filters: SignalsModuleItem[]) => {
      const setFilters = props.setFilters;
      setFilters(filters);
    },
    [props.setFilters],
  );

  const changeSignalType = (signalType: SignalType) => () => {
    const { selectedSignalTypes, setSelectedSignalTypes } = props;

    if (selectedSignalTypes?.includes(signalType)) {
      session.getManager(TrackingManager).trackWidgetEvent('remove_filter', 'signals_widget', {
        widget_preset: signalType,
      });

      setSelectedSignalTypes(selectedSignalTypes?.filter(i => i !== signalType));
    } else {
      session.getManager(TrackingManager).trackWidgetEvent('add_filter', 'signals_widget', {
        widget_preset: signalType,
      });

      setSelectedSignalTypes([...selectedSignalTypes, signalType]);
    }
  };

  const renderSignalCheckbox = ({ hue, label, type }: SignalConfig, inMenu = false) => {
    const { selectedSignalTypes } = props;

    const CheckboxType = inMenu ? StyledMenuCheckbox : StyledCheckbox;

    const classDict = {
      0: 'red',
      120: 'green',
      180: 'cyan',
      210: 'blue',
      270: 'purple',
      35: 'orange',
      65: 'yellow',
    };

    const checkbox = (
      <CheckboxWrapper key={type}>
        <CheckboxType
          checked={selectedSignalTypes?.includes(type)}
          className={classDict[hue]}
          key={type}
          onChange={changeSignalType(type)}
        >
          {label}
        </CheckboxType>
      </CheckboxWrapper>
    );

    const hasAccessToUOA = !!session.getManager(PermissionsManager).hasAccess(`bzpro/feature/use`, 'signals-options')
      .ok;

    return (
      <div>
        {type === SignalType.option && !hasAccessToUOA ? (
          <LockedDiv
            $hue={hue}
            onClick={() => {
              if (type === SignalType.option) {
                props.setShowUpsellModal(true);
                return;
              }
            }}
          >
            <LockIconWrapper>
              <Lock />
            </LockIconWrapper>
            <LockedSpan>{label}</LockedSpan>
          </LockedDiv>
        ) : (
          <PermissionedComponent
            isFeatureUpdate
            key={type}
            onPurchase={changeSignalType(type)}
            permissionsAnd={[
              signalTypeToFeature[type] ? signalTypeToFeature[type] : true,
              { action: 'bzpro/widget/use', resource: 'signals' },
            ]}
            slug={signalTypeToFeature[type]?.resource}
            trackingInfoMessage="Signals Options Activity"
          >
            {access =>
              access ? (
                checkbox
              ) : (
                <LockedDiv $hue={hue} onClick={() => changeSignalType(type)}>
                  <LockIconWrapper>
                    <Lock />
                  </LockIconWrapper>
                  <LockedSpan>{label}</LockedSpan>
                </LockedDiv>
              )
            }
          </PermissionedComponent>
        )}
      </div>
    );
  };

  const renderSignalCheckboxes = () => {
    const { selectedSignalTypes } = props;

    const groups = signalTypes.reduce<Partial<Record<SignalGroup | 'no-group', SignalConfig[]>>>((acc, type) => {
      const group = type.group ?? 'no-group';
      if (acc[group] === undefined) {
        acc[group] = [];
      }
      (acc[group] as SignalConfig[]).push(type);
      return acc;
    }, {});

    return Object.keys(groups).reduce<JSX.Element[]>((accumulator, groupKey) => {
      const group = groups[groupKey] as SignalConfig[];
      if (groupKey !== 'no-group') {
        const groupConfig = signalGroupConfigsById[groupKey];
        const activeInGroup = group.filter(({ type }) => selectedSignalTypes?.includes(type))?.length;

        const overlay = (
          <StyledMenu
            items={group.map(signal => {
              return {
                key: signal.type,
                label: renderSignalCheckbox(signal, true),
                style: { padding: 0, width: '100%' },
              };
            })}
          />
        );

        const className = groupConfig.label.includes('Low')
          ? 'red'
          : groupConfig.label.includes('High')
            ? 'green'
            : 'blue';

        const groupDropdown = (
          <CheckboxWrapper>
            <SignalsGroupDropdown
              className={className}
              dropdownRender={() => overlay}
              key={groupConfig.type}
              trigger={['click']}
            >
              <SignalsGroupButton>
                <SignalsGroupCounter $isEmpty={activeInGroup === 0}>
                  <PermissionedComponent
                    isFeatureUpdate
                    permissionsAnd={[{ action: 'bzpro/widget/use', resource: 'signals' }]}
                  >
                    {access =>
                      access ? (
                        activeInGroup
                      ) : (
                        <LockIconWrapper>
                          <Lock />
                        </LockIconWrapper>
                      )
                    }
                  </PermissionedComponent>
                </SignalsGroupCounter>
                <SignalsGroupLabel>{groupConfig.label}</SignalsGroupLabel>
              </SignalsGroupButton>
            </SignalsGroupDropdown>
          </CheckboxWrapper>
        );

        return [...accumulator, groupDropdown];
      } else {
        return [...accumulator, ...group.map(s => renderSignalCheckbox(s))];
      }
    }, []);
  };

  const setEnabledNotifications = (enabledNotifications: NotificationType[]) => {
    props.setEnabledNotifications(enabledNotifications);
  };

  const updateFilters = (screenerFilters: SignalsScreenerFilter[]) => {
    props.setScannerFilters(screenerFilters);
  };

  const tabChanged = (tabName: string) => {
    session.getManager(TrackingManager).trackWidgetEvent('view', 'signals_widget', {
      widget_sub_type: tabName,
    });
  };

  const { addSignal, enabledNotifications, selectedSignalTypes = [] } = props;
  const screenerFilters = props?.screenerFilters?.filter(filter => filter.fieldId !== 'link') ?? [];

  return (
    <ToolBar>
      <TopBar>
        <SignalsSearchBar changeTags={onTagsChange} newsConfigSelector={SignalsConfigSelector} tags={tags} />
      </TopBar>
      <PermissionedComponent isFeatureUpdate permissionsAnd={[{ action: 'bzpro/widget/use', resource: 'signals' }]}>
        {access => (
          <StyledTabs
            defaultActiveKey="Signals"
            items={[
              {
                children: <TabContentsWrapper>{renderSignalCheckboxes()}</TabContentsWrapper>,
                key: 'Signals',
                label: tabLabel({
                  icon: <LineChartOutlined />,
                  label: 'Signals',
                  size: access ? selectedSignalTypes?.length : 0,
                }),
              },
              {
                children: (
                  <TabContentsWrapper>
                    <Scanner screenerFilters={screenerFilters} updateFilters={updateFilters} />
                  </TabContentsWrapper>
                ),
                key: 'Filters',
                label: tabLabel({
                  icon: <FilterOutlined />,
                  label: 'Filters',
                  size: screenerFilters.length,
                }),
              },
              {
                children: (
                  <TabContentsWrapper>
                    <SignalsNotifications
                      addSignal={addSignal}
                      enabledNotifications={enabledNotifications}
                      setEnabledNotifications={setEnabledNotifications}
                    />
                  </TabContentsWrapper>
                ),
                key: 'Notifications',
                label: tabLabel({
                  icon: <NotificationOutlined />,
                  label: 'Notifications',
                  size: 0, // TODO
                }),
              },
              {
                children: (
                  <TabContentsWrapper>
                    <SignalsHelp />
                  </TabContentsWrapper>
                ),
                key: 'Help',
                label: tabLabel({
                  icon: <QuestionOutlined />,
                  label: 'Help',
                }),
              },
            ]}
            onTabClick={tabChanged}
            size="small"
            tabPosition="top"
          />
        )}
      </PermissionedComponent>
    </ToolBar>
  );
};

const ToolBar = styled.div`
  display: flex;
  flex-direction: column;
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;

const StyledTabs = styled(Tabs)`
  overflow: visible;
  background-color: ${props => props.theme.colors.backgroundActive}80;
  .ant-tabs-nav-list {
    margin-left: 10px !important;
  }
  .Search {
    .Search-wrapper {
      height: 25px;
      width: 100%;
      padding-top: 0px;
      padding-bottom: 0px;
      overflow-x: auto;
      overflow-y: hidden;
      .Search-tag {
        font-size: 12px;
        height: 16px;
        line-height: 10px;
        margin-bottom: 3px;
      }
    }
    .Search-wrapper::-webkit-scrollbar {
      height: 8px;
    }
    .Signal-search-wrapper {
      display: flex;
      flex-wrap: wrap;
      height: 100%;
      min-height: 25px;
    }

    &-dropdown {
      right: auto;
      position: fixed;
      top: 161px;
      left: 240px;
    }

    &-input {
      height: 16px;
      padding-left: 0px;
    }
  }
`;

const StyledCheckbox = styled(Checkbox)`
  margin: 3px 3px 2px;
  padding: 3px;
  white-space: nowrap;
  align-items: center;
  .ant-checkbox {
    margin: 0px !important;
  }
`;

const StyledMenuCheckbox = styled(Checkbox)`
  margin: 0 0 3px 0;
  padding: 3px;
  white-space: nowrap;
  align-items: center;
  width: 100%;
  .ant-checkbox {
    margin: 0px !important;
  }
`;

const CheckboxWrapper = styled.div`
  .blue {
    background-color: rgba(99, 166, 233, 0.4);
  }
  .purple {
    background-color: rgba(166, 99, 233, 0.4);
  }
  .orange {
    background-color: rgba(233, 177, 99, 0.4);
  }
  .yellow {
    background-color: rgba(222, 233, 99, 0.4);
  }
  .cyan {
    background-color: rgba(99, 233, 233, 0.4);
  }
  .green {
    background-color: ${props => hexToRGBA(props.theme.colors.statistic.positive, 0.4)};
  }
  .red {
    background-color: ${props => hexToRGBA(props.theme.colors.statistic.negative, 0.4)};
  }
`;

const LockedDiv = styled.div<{ $hue: number }>`
  background-color: ${props => hueToColorAlpha(props.$hue)};
  margin: 3px 3px 2px !important;
  padding: 3px 3px 1px !important;
  white-space: nowrap;
  display: inline-flex;
`;

const LockedSpan = styled.span`
  padding-right: 8px;
  padding-left: 8px;
  margin-top: 2px;
`;

const LockIconWrapper = styled.span`
  display: flex;
  justify-content: center;
  margin: 3px 3px 2px !important;
  padding: 2px;
  background-color: ${props => props.theme.colors.background};
  border-radius: 2px;
  align-items: center;
  width: 13px;
  height: 13px;

  svg {
    fill: ${props => props.theme.colors.accent};
  }
`;

const Row = styled.div`
  display: flex;
  flex-direction: row;
  min-width: 0%;
  gap: 6px;
`;

const TabContentsWrapper = styled(Row)`
  background-color: ${props => props.theme.colors.background};
  flex-wrap: wrap;
  max-height: 30vh;
  overflow-y: auto;
  padding: 0.25em 0.5em;
`;

const TabBadge = styled(Row)`
  align-items: center;
  color: ${props => props.theme.colors.accent};
  font-size: 0.875em;
  font-weight: 700;
  padding-left: 0.5em;
`;

const SignalsGroupDropdown = styled(Dropdown)`
  // @ts-ignore as of antd@3.9.3 this is a bug due to the defaultProps type in antd's Dropdown. bugfix PR here: https://github.com/ant-design/ant-design/pull/12421
`;

const SignalsGroupButton = styled('div')`
  align-items: center;
  cursor: pointer;
  display: flex;
  justify-content: center;
  margin: 3px 3px 2px 3px;
  padding: 3px;
`;

const StyledMenu = styled(Menu)`
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  padding: 0 !important;
`;

const SignalsGroupCounter = styled(Row)<{ $isEmpty: boolean }>`
  align-items: center;
  background: ${props => (props.$isEmpty ? props.theme.colors.background : props.theme.colors.brand)};
  border: 1px solid ${props => (props.$isEmpty ? props.theme.colors.border : props.theme.colors.brand)};
  color: ${props => (props.$isEmpty ? props.theme.colors.background : props.theme.colors.brandForeground)};
  // hide the number if it's empty
  font-size: 14px;
  font-weight: 600;
  height: 16px;
  justify-content: center;
  padding-top: 1px;
  width: 15px;
`;

const SignalsGroupLabel = styled(Row)`
  padding: 0 8px;
`;

const TopBar = styled.div`
  display: flex;
  width: 100%;
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;
