import React from 'react';
import { connect, useDispatch } from 'react-redux';
import { bindActionCreators } from 'redux';
import { ThunkDispatch } from 'redux-thunk';
import '@ant-design/v5-patch-for-react-19';
import { useNavigate } from 'react-router-dom';
import { Badge, Button, Dropdown, Popover, Tooltip } from 'antd';
import { NotificationManager, NotificationsManagerEvent } from '@benzinga/notification-manager';
import { SessionContext } from '@benzinga/session-context';
import { TrackingManager } from '@benzinga/tracking-manager';
import styled, { createGlobalStyle } from '@benzinga/themetron';
import { openWindow } from '@benzinga/fission';
import { getNotificationWidgetId, setStreamDisplayPosition } from '../../actions/settingsActions';
import { RootAction, RootState } from '../../redux/types';
import {
  betaChatAccessPermission,
  deskChatAccessSelector,
  widgetAccessSelector,
} from '../../selectors/accessSelectors';
import {
  selectSettings,
  selectStreamDisplayPosition,
  selectStreamDrawerVisible,
} from '../../selectors/settingsSelectors';
import WorkspaceNav from '../dashboard/workspace/WorkspaceNav';
import {
  useIsUserLoggedIn,
  LoggedInComponent,
  PermissionedComponent,
  useIsUserTrialing,
  LoginPrompt,
} from '@benzinga/user-context';
import WidgetMenuBarComponent from './WidgetMenuItems';
import { Menu } from 'antd';
import { setStreamVisibility } from '../../actions/settingsActions';
import { Widget, WidgetType } from '../../entities/widgetEntity';
import SquawkChannelsContainer from '../ui/SquawkChannelsContainer';
import { isWindowWidthLargerThenSmall } from '../../selectors/uiSelectors';
import { selectWidgetsByType } from '../../selectors/widgetSelectors';
import { head } from 'ramda';
import { MenuItem } from './MenuItem';
import Hooks from '@benzinga/hooks';
import { objectShallowEqualWithExclude } from '@benzinga/utils';
import { PlatformBanner } from './PlatformBanner';
import { FeedbackFormCaller } from '../FeedbackFormCaller';

import { openMorningReportPage, openWelcomePage } from './workspaceActions';
import { WatchlistLink } from '@benzinga/pro-watchlist-widget';
import { Watchlist } from '@benzinga/watchlist-manager';
import { ProContext } from '@benzinga/pro-tools';
import { SendLinkContextProvider } from '@benzinga/pro-ui';
import { HotkeyReferenceIcon } from './platformBarItems/hotkeyReference';
import { faBell, faUser } from '@fortawesome/pro-light-svg-icons';
import {
  Alert,
  Chat,
  Help,
  Report,
  Settings,
  Link,
  Account,
  Layout,
  Billing,
  History,
  Logout,
  Handwave,
  Sun,
  Keyboard,
} from '@benzinga/valentyn-icons';
import { SafeFontAwesomeIcon } from '../common/SafeFontAwesomeIcon';
import { ProUserSettingsManager } from '@benzinga/pro-user-settings';
import { Layout as LayoutThemedIcon, TrendingUp, BZ, Popout, Lock } from '@benzinga/themed-icons';
import { AdBanners } from './banners';

interface ReduxState {
  canAccessDeskChat: boolean;
  hasWidgetAccess: (widgetType: WidgetType) => boolean;
  isWindowWidthLargerThenSmall: boolean;
  menuHoverable: boolean;
  selectWidgetByType: (widgetType: WidgetType) => () => Widget | undefined;
  streamVisible?: boolean;
  streamDisplayPosition: 'left' | 'right';
  notificationVisible?: boolean;
}
interface DispatchableActions {
  openMorningReportPage: typeof openMorningReportPage;
  openWelcomePage: typeof openWelcomePage;
  setStreamVisibility: typeof setStreamVisibility;
  setStreamDisplayPosition: typeof setStreamDisplayPosition;
}

interface OwnProps {
  notificationsOpen: boolean | undefined;
  handleNotificationClick: () => void;
}

interface DropDownItem {
  displayLock?: boolean;
  icon: JSX.Element;
  id: PlatformBarTab;
  isPoppable: boolean;
  name: PlatformBarTabName;
  onClick: () => void;
}

type Props = ReduxState & DispatchableActions & OwnProps;

export enum PlatformBarTabName {
  About = 'About',
  Account = 'Account',
  Alerts = 'Alerts',
  Billing = 'Billing',
  Changelog = 'Recent Changes',
  Feedback = 'Feedback',
  Help = 'Help',
  Hotkeys = 'Hotkeys',
  Layouts = 'Layouts',
  Login = 'Login',
  Logout = 'Logout',
  PremiumTradeIdeas = 'Premium Trade Ideas',
  Research = 'Research',
  Settings = 'Settings',
  WelcomePage = 'Welcome Page',
  MorningReport = 'Morning Update',
  PortfolioLink = 'Link Portfolio',
}

enum PlatformBarTab {
  about = 'about',
  account = 'account',
  alerts = 'alerts/categories',
  billing = 'billing',
  changelog = 'changelog',
  feedback = 'feedback',
  help = 'help',
  hotkeys = 'hotkeys',
  layouts = 'layouts',
  login = 'login',
  logout = 'logout',
  premiumTradeIdeas = 'premiumTradeIdeas',
  research = 'research',
  settings = 'settings',
  WelcomePage = 'welcomePage',
  MorningReport = 'morningReport',
  PortfolioLink = 'portfolioLink',
}

const PlatformBar: React.FC<Props> = React.memo(
  props => {
    const proContext = React.useContext(ProContext);
    const session = React.useContext(SessionContext);
    const proUserSettingsManager = session.getManager(ProUserSettingsManager);
    const notificationManager = session.getManager(NotificationManager);
    const [unreadNotificationsCount, setUnreadNotificationsCount] = React.useState<number | undefined>();
    const [platformBar, setPlatformBar] = React.useState<HTMLDivElement | null>(null);
    const onRefChanged = React.useCallback((node: HTMLDivElement | null) => {
      setPlatformBar(node);
    }, []);

    const [displayLoginPrompt, setDisplayLoginPrompt] = React.useState(false);
    const [profileVisible, setProfileVisible] = React.useState(false);

    const [isFeedbackShown, setIsFeedbackShown] = React.useState(false);

    const isLoggedIn = useIsUserLoggedIn();
    const isUserTrialing = useIsUserTrialing('benzinga-pro');

    const [protfolioLinkOpen, setPortfolioLinkOpen] = React.useState(false);

    const onProtfolioLinkClick = React.useCallback(() => {
      setPortfolioLinkOpen(true);
    }, []);

    const closeProtfolioLinkModal = React.useCallback(() => {
      setPortfolioLinkOpen(false);
    }, []);

    const dispatch = useDispatch<any>();
    const handleNotificationOpenClose = React.useCallback(async () => {
      dispatch(getNotificationWidgetId());

      const handleNotificationClick = props.handleNotificationClick;
      handleNotificationClick();
    }, [dispatch, props.handleNotificationClick]);
    const navigate = useNavigate();

    const dropdownItems: DropDownItem[] = React.useMemo(() => {
      const openTab = (tab: PlatformBarTab) => {
        navigate(`/dashboard/preferences/${tab}/`);
      };

      const logout = () => {
        navigate('/logout');
      };
      return [
        {
          displayLock: !isLoggedIn,
          icon: (
            <IconWrapper>
              <Account />
            </IconWrapper>
          ),
          id: PlatformBarTab.account,
          isPoppable: true,
          name: PlatformBarTabName.Account,
          onClick: () => {
            openWindow('http://www.benzinga.com/account', undefined, undefined, undefined, {
              free: false,
            });
          },
        },
        {
          displayLock: !isLoggedIn,
          icon: (
            <IconWrapper>
              <LayoutThemedIcon />
            </IconWrapper>
          ),
          id: PlatformBarTab.layouts,
          isPoppable: false,
          name: PlatformBarTabName.Layouts,
          onClick: () => {
            isLoggedIn ? openTab(PlatformBarTab.layouts) : setDisplayLoginPrompt(true);
          },
        },
        {
          displayLock: !isLoggedIn,
          icon: (
            <IconWrapper>
              <Alert />
            </IconWrapper>
          ),
          id: PlatformBarTab.alerts,
          isPoppable: false,
          name: PlatformBarTabName.Alerts,
          onClick: () => {
            isLoggedIn ? openTab(PlatformBarTab.alerts) : setDisplayLoginPrompt(true);
          },
        },
        {
          icon: (
            <IconWrapper>
              <Settings />
            </IconWrapper>
          ),
          id: PlatformBarTab.settings,
          isPoppable: false,
          name: PlatformBarTabName.Settings,
          onClick: () => {
            openTab(PlatformBarTab.settings);
          },
        },
        {
          icon: (
            <IconWrapper>
              <Handwave />
            </IconWrapper>
          ),
          id: PlatformBarTab.WelcomePage,
          isPoppable: false,
          name: PlatformBarTabName.WelcomePage,
          onClick: () => {
            const openWelcomePage = props.openWelcomePage;
            openWelcomePage();
          },
        },
        {
          icon: (
            <IconWrapper>
              <Sun />
            </IconWrapper>
          ),
          id: PlatformBarTab.MorningReport,
          isPoppable: false,
          name: PlatformBarTabName.MorningReport,
          onClick: () => {
            const openMorningReportPage = props.openMorningReportPage;
            openMorningReportPage();
          },
        },
        {
          displayLock: !isLoggedIn,
          icon: (
            <IconWrapper>
              <Link />
            </IconWrapper>
          ),
          id: PlatformBarTab.PortfolioLink,
          isPoppable: false,
          name: PlatformBarTabName.PortfolioLink,
          onClick: onProtfolioLinkClick,
        },
        ...(isUserTrialing
          ? [
              {
                displayLock: !isLoggedIn,
                icon: (
                  <IconWrapper>
                    <Billing />
                  </IconWrapper>
                ),
                id: PlatformBarTab.billing,
                isPoppable: true,
                name: PlatformBarTabName.Billing,
                onClick: () => {
                  openWindow('https://www.benzinga.com/account/billing', undefined, undefined, undefined, {
                    free: false,
                  });
                },
              },
            ]
          : []),
        {
          icon: (
            <IconWrapper>
              <History />
            </IconWrapper>
          ),
          id: PlatformBarTab.changelog,
          isPoppable: false,
          name: PlatformBarTabName.Changelog,
          onClick: () => {
            openWindow('https://headwayapp.co/benzinga-pro-changes', undefined, undefined, undefined, { free: true });
          },
        },
        {
          icon: (
            <IconWrapper>
              <TrendingUp />
            </IconWrapper>
          ),
          id: PlatformBarTab.research,
          isPoppable: false,
          name: PlatformBarTabName.Research,
          onClick: () => {
            openWindow('https://www.benzinga.com/research', undefined, undefined, undefined, { free: true });
          },
        },
        {
          icon: (
            <IconWrapper>
              <Help />
            </IconWrapper>
          ),
          id: PlatformBarTab.help,
          isPoppable: false,
          name: PlatformBarTabName.Help,
          onClick: () => {
            openTab(PlatformBarTab.help);
          },
        },
        {
          icon: (
            <IconWrapper>
              <Keyboard />
            </IconWrapper>
          ),
          id: PlatformBarTab.hotkeys,
          isPoppable: false,
          name: PlatformBarTabName.Hotkeys,
          onClick: () => {
            openTab(PlatformBarTab.hotkeys);
          },
        },
        {
          icon: (
            <IconWrapper>
              <Report />
            </IconWrapper>
          ),
          id: PlatformBarTab.feedback,
          isPoppable: false,
          name: PlatformBarTabName.Feedback,
          onClick: () => {
            setIsFeedbackShown(old => !old);
          },
        },
        {
          icon: <BZ />,
          id: PlatformBarTab.about,
          isPoppable: false,
          name: PlatformBarTabName.About,
          onClick: () => {
            openTab(PlatformBarTab.about);
          },
        },
        isLoggedIn
          ? {
              icon: (
                <IconWrapper>
                  <Logout />
                </IconWrapper>
              ),
              id: PlatformBarTab.logout,
              isPoppable: false,
              name: PlatformBarTabName.Logout,
              onClick: logout,
            }
          : {
              icon: (
                <IconWrapper>
                  <Logout />
                </IconWrapper>
              ),
              id: PlatformBarTab.login,
              isPoppable: false,
              name: PlatformBarTabName.Login,
              onClick: () => setDisplayLoginPrompt(true),
            },
      ];
    }, [isLoggedIn, onProtfolioLinkClick, isUserTrialing, navigate, props]);

    React.useEffect(() => {
      if (window.Headway && window.Headway.init && !window.HeadwayInitialized) {
        window.HeadwayInitialized = true;
        const HW_config = {
          account: 'xGnjOy',
          callbacks: {
            onShowWidget() {
              session.getManager(TrackingManager).trackModalEvent('view', {
                modal_id: 'changelog',
                modal_type: 'changelog',
              });
            },
          },
          selector: '.changelogBadge',
        };
        window.Headway.init(HW_config);
      }
    }, [session]);

    const toggleSidebarVisibility = React.useCallback(() => {
      proUserSettingsManager.setSetting('sidebarVisible', value => !value);
    }, [proUserSettingsManager]);

    const handleChatPopoutClick = () => async () => {
      session.getManager(TrackingManager).trackLinkEvent('click', {
        link_action: 'open_widget',
        link_id: 'chat-stream',
        link_type: 'popout',
        value: 'chat-stream',
      });
      proContext.addPopoutForWidget({ widgetType: 'stream' });
    };

    const openWatchlist = React.useCallback(
      (watchlist: Watchlist) => {
        const addWidget = proContext.addWidget;
        addWidget('watchlist', { parameters: { watchlistId: watchlist.watchlistId }, widgetVersion: 3 });
      },
      [proContext.addWidget],
    );

    const toggleStreamVisibility = () => {
      const { isWindowWidthLargerThenSmall } = props;
      if (isWindowWidthLargerThenSmall) {
        const { setStreamVisibility, streamVisible } = props;
        setStreamVisibility(!streamVisible);
      }
    };

    const toggleStreamPosition = () => {
      const { setStreamDisplayPosition, streamDisplayPosition } = props;
      setStreamDisplayPosition?.(streamDisplayPosition === 'left' ? 'right' : 'left');
      const { setStreamVisibility } = props;
      setStreamVisibility(true);
    };
    Hooks.useSubscriber(notificationManager, async (event: NotificationsManagerEvent) => {
      switch (event.type) {
        case 'notifications:update': {
          setUnreadNotificationsCount(notificationManager.getUnreadCount());
          break;
        }
      }
    });

    const streamChatContent = () => {
      const { hasWidgetAccess, streamDisplayPosition, streamVisible } = props;
      if (hasWidgetAccess('stream')) {
        return [
          {
            key: 'toggleStream',
            label: (
              <ChatMainDiv>
                <ChatSpan>
                  <IconWrapper>
                    <Chat height="1.5rem" width="1.25rem" />
                  </IconWrapper>
                  <span style={{ marginLeft: `8px` }}>{streamVisible ? 'Close Side Chat' : 'Open Side Chat'}</span>
                </ChatSpan>
                <div>
                  <IconWrapper>
                    <Layout height="1.75rem" width="1.5rem" />
                  </IconWrapper>
                </div>
              </ChatMainDiv>
            ),
            onClick: toggleStreamVisibility,
          },
          ...(props.isWindowWidthLargerThenSmall
            ? [
                {
                  key: 'toggleStreamPosition',
                  label: (
                    <ChatMainDiv>
                      <ChatSpan>
                        <IconWrapper>
                          <Chat height="1.5rem" width="1.25rem" />
                        </IconWrapper>
                        <span style={{ marginLeft: `8px` }}>
                          {streamDisplayPosition === 'left' ? 'Set Chat Position Right' : 'Set Chat Position Left'}
                        </span>
                      </ChatSpan>
                      <div>
                        <IconWrapper>
                          <Layout height="1.75rem" width="1.5rem" />
                        </IconWrapper>
                      </div>
                    </ChatMainDiv>
                  ),
                  onClick: toggleStreamPosition,
                },
              ]
            : []),
          {
            key: 'popoutChat',
            label: (
              <ChatMainDiv>
                <ChatSpan>
                  <IconWrapper>
                    <Chat height="1.5rem" width="1.25rem" />
                  </IconWrapper>
                  <span style={{ marginLeft: `8px` }}>New Popped out Chat</span>
                </ChatSpan>
                <div>
                  <Popout height="1.5rem" width="1.25rem" />
                </div>
              </ChatMainDiv>
            ),
            onClick: handleChatPopoutClick(),
          },
        ];
      }
      return undefined;
    };

    const personalContent = () => {
      const items = dropdownItems;
      return items.map(item => ({
        key: item.id,
        label: (
          <ChatMainDiv>
            <ChatSpan>
              {React.cloneElement(item.icon, { height: '1.5rem', width: '1.25rem' })}
              <DropdownNameDiv style={{ marginLeft: `8px` }}>{item.name}</DropdownNameDiv>
              {item.isPoppable && <Popout height="1.5em" title="redirects to a newly created tab" width="1.5em" />}
              {item.displayLock && (
                <LockMenu>
                  <Lock height="1rem" width="1rem" />
                </LockMenu>
              )}
            </ChatSpan>
          </ChatMainDiv>
        ),
        onClick: item.onClick,
      }));
    };

    const update = Hooks.useForceUpdate();

    Hooks.useSubscriber(proUserSettingsManager, e => {
      switch (e.type) {
        case 'pro_user_settings:update':
          update();
          break;
      }
    });

    return (
      <SendLinkContextProvider>
        <MenuBarStyle top={platformBar?.getBoundingClientRect().bottom ?? 44} />
        {displayLoginPrompt && <LoginPrompt onClose={() => setDisplayLoginPrompt(false)} />}
        <FeedbackFormCaller isShown={isFeedbackShown} />
        <PlatformBanner onBannerClosed={update} />
        <AdBanners />
        <PlatformBarDiv ref={onRefChanged}>
          <div className="menu-bz-logo">
            <div
              className="tooltip--left"
              {...{
                'data-button-hint': `${proUserSettingsManager.getSettings().sidebarVisible ? 'Hide' : 'Show'} Sidebar`,
                onClick: toggleSidebarVisibility,
              }}
            >
              <BZ />
            </div>
          </div>

          {props.isWindowWidthLargerThenSmall && <WidgetMenuBarComponent menuHoverable={props.menuHoverable} />}
          <SquawkChannelsContainer menuHoverable={props.menuHoverable} />

          {props.isWindowWidthLargerThenSmall ? (
            <WorkspaceNav className="middle" platformBar={platformBar} />
          ) : (
            <div className={`middle WorkspaceNav u-flexHorizontal`} />
          )}

          <HotkeyReferenceIcon menuHoverable={props.menuHoverable} />

          <LoggedInComponent>
            {loggedIn =>
              loggedIn ? (
                <MenuItem data-button-hint="Notifications" menuHoverable={props.menuHoverable}>
                  <SubMenuItem onClick={handleNotificationOpenClose}>
                    <NotificationsBadge count={unreadNotificationsCount ?? 0} dot offset={[3, 1]}>
                      {!props.notificationsOpen ? (
                        <Tooltip title="Notification Center">
                          <PlatformIconWrapper>
                            <SafeFontAwesomeIcon icon={faBell} />
                          </PlatformIconWrapper>
                        </Tooltip>
                      ) : (
                        <PlatformIconWrapper>
                          <SafeFontAwesomeIcon icon={faBell} />
                        </PlatformIconWrapper>
                      )}
                    </NotificationsBadge>
                  </SubMenuItem>
                </MenuItem>
              ) : (
                <MenuItem>
                  <SubMenuItem>
                    <LockIcon />
                    <PlatformIconWrapper>
                      <SafeFontAwesomeIcon icon={faBell} />
                    </PlatformIconWrapper>
                  </SubMenuItem>
                </MenuItem>
              )
            }
          </LoggedInComponent>
          {props.isWindowWidthLargerThenSmall && (
            <PermissionedComponent permission={betaChatAccessPermission}>
              {access => (
                <MenuItem menu={streamChatContent()} menuHoverable={props.menuHoverable} placement="right">
                  <SubMenuItem>
                    {!access && <LockIcon />}
                    <Chat
                      onClick={() => {
                        if (access && props.menuHoverable) {
                          toggleStreamVisibility();
                        }
                      }}
                      title="Chat"
                      transform="scale(1.3)"
                    />
                  </SubMenuItem>
                </MenuItem>
              )}
            </PermissionedComponent>
          )}
          <MenuItem
            menu={personalContent()}
            menuHoverable={props.menuHoverable}
            placement="right"
            setIsVisible={setProfileVisible}
          >
            {!profileVisible && !props.menuHoverable ? (
              <Tooltip className="tooltip" placement="bottomLeft" title="Profile">
                <PlatformIconWrapper>
                  <SafeFontAwesomeIcon icon={faUser} />
                </PlatformIconWrapper>
              </Tooltip>
            ) : (
              <PlatformIconWrapper>
                <SafeFontAwesomeIcon icon={faUser} />
              </PlatformIconWrapper>
            )}
          </MenuItem>
        </PlatformBarDiv>
        {protfolioLinkOpen && <WatchlistLink closeModal={closeProtfolioLinkModal} linkedWatchlist={openWatchlist} />}
      </SendLinkContextProvider>
    );
  },
  (p: Props, n: Props) =>
    objectShallowEqualWithExclude(p, n, [
      'hasWidgetAccess',
      'selectWidgetByType',
      'openMorningReportPage',
      'openWelcomePage',
      'setStreamVisibility',
    ]),
);
const SubMenuItem = styled.div`
  display: flex;
  align-items: center;
  position: relative;
`;
const PlatformIconWrapper = styled.div`
  svg path {
    fill: ${props => props.theme.colors.foreground};
  }

  svg {
    height: 16px;
    width: 14px;
  }

  #exclamation {
    fill: ${props => props.theme.colors.background};
  }

  display: flex;
  justify-content: center;
  align-items: center;
`;

const IconWrapper = styled(PlatformIconWrapper)`
  svg {
    height: 20px;
    width: 20px;
  }
`;

const LockMenu = styled.span`
  display: flex;
  justify-content: space-between;
  right: 0;
  margin-left: auto;
  svg {
    cursor: pointer;
    fill: ${props => props.theme.colors.accent};
  }
`;

const ChatMainDiv = styled.div`
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  // padding: 4px;
  // div {
  //   opacity: 0.75;
  //   transition: opacity 0.5s ease-in-out;
  // }
  div svg {
    opacity: 0.35;
  }
  // &:hover {
  //   div {
  //     opacity: 1;
  //     transition: opacity 0.7s ease-in-out;
  //   }
  //   svg {
  //     opacity: 1;
  //   }
  //   background-color: ${props => props.theme.colors.backgroundInactive};
  //   border-radius: 5px;
  //   -moz-transition: all 0.51s;
  //   -o-transition: all 0.51s;
  //   -webkit-transition: all 0.51s;
  //   transition: opacity 0.7s ease-in-out;
  // }
`;

const LockIcon = styled(Lock)`
  cursor: pointer;
  left: 15px;
  position: absolute;
  top: -3px;
  font-size: 10px;
  fill: ${props => props.theme.colors.accent} !important;
`;

const NotificationsBadge = styled(Badge)`
  .ant-badge-dot {
    background: ${props => props.theme.colors.accent} !important;
  }
`;

const ChatSpan = styled.span`
  display: flex;
  align-items: center;
  position: relative;
  padding: 4px;
  width: 100%;
`;

const DropdownNameDiv = styled.span`
  display: flex;
  flex-grow: 1;
`;

const PlatformBarDiv = styled.div`
  display: flex;
  background: ${props => props.theme.colors.background};
  font-size: 16px;

  svg {
    fill: ${props => props.theme.colors.foregroundInactive};
    &:hover {
      fill: ${props => props.theme.colors.foregroundActive};
    }
  }

  .z-20 {
    z-index: 20;
  }

  & .menu-bz-logo {
    display: flex;
    flex-grow: 0;
    flex-shrink: 0;
    justify-content: center;
    align-items: center;
    margin: 0 4px;
    svg {
      width: 3em;
      fill: ${props => props.theme.colors.foregroundActive};
      background: ${props => props.theme.colors.background};
    }
  }
`;

function mapStateToProps(state: RootState): ReduxState {
  const { menuHoverable } = selectSettings(state);

  return {
    canAccessDeskChat: deskChatAccessSelector(state),
    hasWidgetAccess: (widgetType: WidgetType) => {
      return widgetAccessSelector(state, widgetType);
    },
    isWindowWidthLargerThenSmall: isWindowWidthLargerThenSmall(state),
    menuHoverable,
    selectWidgetByType:
      <T extends WidgetType>(widgetType: T) =>
      () => {
        const widgets = selectWidgetsByType(state, { type: widgetType }) as unknown as Extract<Widget, { type: T }>[];
        return head(widgets);
      },
    streamDisplayPosition: selectStreamDisplayPosition(state),
    streamVisible: selectStreamDrawerVisible(state),
  };
}

const mapDispatchToProps = (dispatch: ThunkDispatch<RootState, void, RootAction>): DispatchableActions =>
  bindActionCreators(
    {
      openMorningReportPage,
      openWelcomePage,
      setStreamDisplayPosition,
      setStreamVisibility,
    },
    dispatch,
  );

export default connect<ReduxState, DispatchableActions, OwnProps, RootState>(
  mapStateToProps,
  mapDispatchToProps,
)(PlatformBar as any) as React.FC<OwnProps>;

const MenuBarStyle = createGlobalStyle<{ top: number }>`
  .menubar-dropdown-left {
    .ant-dropdown-menu {
      min-width: 14rem;
      width: fit-content;
    }

    .ant-dropdown-menu-item,
    .ant-dropdown-menu-submenu-title {
      padding: 1px !important;
      overflow: hidden !important;
      display: flex !important;
    }
    .ant-dropdown-menu-title-content {
      font-weight: bold !important;
    }
    .ant-dropdown-menu-item:hover,
    .ant-dropdown-menu-submenu-title:hover {
      background-color: transparent !important;
    }

    .ant-dropdown-menu-submenu-vertical {
      svg {
        opacity: 0.35 !important;
      }
    }

    .ant-dropdown-menu-submenu-vertical:hover {
      svg {
        opacity: 1 !important;
      }
      background-color: ${props => props.theme.colors.backgroundInactive} !important;
      border-radius: 5px !important;
      -moz-transition: all 0.51s !important;
      -o-transition: all 0.51s !important;
      -webkit-transition: all 0.51s !important;
      transition: opacity 0.7s ease-in-out !important;
    }

    .ant-dropdown-menu {
      background-color: ${props => props.theme.colors.backgroundActive} !important;
      padding: 0px !important;
    }
    .ant-dropdown-menu-item-group {
      background-color: ${props => props.theme.colors.backgroundActive} !important;
    }

    .ant-dropdown-menu-root {
    }

    left: 0px !important;
    background-color: rgba(0, 0, 0, 0.4) !important;
    position: fixed !important;
    width: 100% !important;
    height: 100% !important;
    min-height: fill-available !important;
    top: ${props => props.top}px !important;
  }

  .menubar-dropdown-right {
    .ant-dropdown-menu {
      min-width: 14rem;
      width: fit-content;
    }

    .ant-dropdown-menu-item,
    .ant-dropdown-menu-submenu-title {
      // padding: 1px !important;
      overflow: hidden !important;
    }
    .ant-dropdown-menu-title-content {
      font-weight: bold !important;
    }
    // .ant-dropdown-menu-item:hover,
    // .ant-dropdown-menu-submenu-title:hover {
    //   background-color: transparent !important;
    // }

    .ant-dropdown-menu-item-group {
      background-color: ${props => props.theme.colors.backgroundActive} !important;
    }

    .ant-dropdown-menu-root {
      margin-left: auto !important;
      padding: 0 !important;
    }

    background-color: rgba(0, 0, 0, 0.4) !important;
    position: fixed !important;
    width: 100% !important;
    height: 100% !important;
    min-height: fill-available !important;
    top: ${props => props.top}px !important;
  }

  .menubar-dropdown-center {
    .ant-dropdown-menu-item,
    .ant-dropdown-menu-submenu-title {
      padding: 1px !important;
      overflow: hidden !important;
    }
    .ant-dropdown-menu-title-content {
      font-weight: bold !important;
    }
    .ant-dropdown-menu-item:hover,
    .ant-dropdown-menu-submenu-title:hover {
      background-color: transparent !important;
    }

    .ant-dropdown-menu-item-group {
      background-color: ${props => props.theme.colors.backgroundActive} !important;
    }

    .ant-dropdown-menu-root {
      margin-left: auto !important;
      padding: 0 !important;
    }

    background-color: rgba(0, 0, 0, 0.4) !important;
    /* position: fixed !important;
    width: 100% !important;
    height: 100% !important; */
    min-height: fill-available !important;
    top: ${props => props.top}px !important;
  }

  .menubar-submenu-popup {
    .ant-dropdown-menu-item,
    .ant-dropdown-menu-submenu-title {
      padding: 1px 6px 1px 6px !important;
    }

    .ant-dropdown-menu-item:hover,
    .ant-dropdown-menu-submenu-title:hover {
      background-color: transparent !important;
    }

    .ant-dropdown-menu {
      background-color: ${props => props.theme.colors.backgroundActive} !important;
    }
  }
`;
