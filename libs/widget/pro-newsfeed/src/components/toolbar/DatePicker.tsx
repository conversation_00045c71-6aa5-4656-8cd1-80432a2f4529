'use client';
import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import 'antd/es/date-picker/style/index';
import { DateTime } from 'luxon';
import styled from '@benzinga/themetron';
import { useTime } from '@benzinga/time-manager-hooks';
import Hooks from '@benzinga/hooks';
import { DatePicker as AntdDatePicker } from 'antd';

interface Props {
  button: React.ReactNode;
  date: DateTime | null;
  timezone?: string;
  onChangeDate: (date: DateTime | null, dateString: string | null) => void;
  onOpenChange?: (open: boolean) => void;
  open?: boolean;
}

export const DatePicker: React.FC<Props> = props => {
  const { button, date, onChangeDate } = props;
  const timezone = React.useMemo(() => props.timezone ?? 'utc', [props.timezone]);
  const time = useTime();
  const pickerRef = React.useRef<any>(null);

  //antd expects the date in PC's local timezone. This may be different from the timezone set in settings.
  //Return the datetime with the same 'values' but in PC timezone for antd to display as-is
  const value = React.useMemo<Dayjs | null>(
    () => (date ? dayjs(date.setZone(DateTime.now().zoneName, { keepLocalTime: true }).toISO()) : null),
    [date],
  );

  const onChange = React.useCallback(
    (date: Dayjs | null) => {
      //antd gives us the date in PC's local timezone. This may be different from the timezone set in settings
      //Return the datetime with the same 'values' but in user defined timezone
      const adjustedDate: DateTime | null = date
        ? DateTime.fromJSDate(date.toDate())
            .set({ millisecond: 0, second: 0 })
            .setZone(timezone, { keepLocalTime: true })
        : null;

      const adjustedDateString: string | null = adjustedDate?.toISO() ?? null;
      onChangeDate(adjustedDate, adjustedDateString);
    },
    [timezone, onChangeDate],
  );

  const prevOpen = Hooks.usePrevious(props.open);
  if (prevOpen !== props.open && props.open) {
    setTimeout(() => {
      const elem = document.body.querySelector('.ant-picker-ok')?.querySelector('span');
      if (elem) {
        elem.innerHTML = 'Jump To';
      }
    }, 0);
  }
  return (
    <div className="bz-range-picker">
      <AntdDatePicker
        disabledDate={selectedDate => selectedDate?.isAfter(dayjs()) ?? true}
        onChange={onChange}
        onOpenChange={props.onOpenChange}
        open={props.open}
        ref={pickerRef}
        renderExtraFooter={() => (
          <button
            onClick={() => {
              onChange(null);
              props.onOpenChange?.(false);
            }}
            style={{ float: 'left', padding: '2px' }}
          >
            Clear
          </button>
        )}
        showNow={false}
        showTime={{
          defaultValue: dayjs('12:00', 'HH:mm'),
          format: time.timeFormat === 'AM/PM' ? 'hh:mm a' : 'HH:mm',
        }}
        size="small"
        style={{
          minWidth: '0px',
          padding: '0px',
          width: '1px',
          zIndex: -1000,
        }}
        use12Hours={time.timeFormat === 'AM/PM'}
        value={value}
      />
      <ButtonWrapper>{button}</ButtonWrapper>
    </div>
  );
};

const ButtonWrapper = styled.span`
  left: -2px;
  position: relative;
`;
