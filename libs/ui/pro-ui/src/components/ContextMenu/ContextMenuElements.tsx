import styled from '@benzinga/themetron';
import { Menu as MenuAntd, MenuProps as AntdMenuProps } from 'antd';
import React from 'react';
import type { MenuItemType } from 'antd/es/menu/interface';

export type MenuNodeProps =
  | {
      type: 'Item';
      icon?: React.ReactNode;
      name: string | React.ReactElement;
      key: string;
    }
  | {
      type: 'SubMenu';
      icon?: React.ReactElement;
      name: string | React.ReactElement;
      key: string;
      nodes: MenuNodeProps[];
    }
  | {
      type: 'Divider';
      key: string;
    };

interface MenuNodeInternalProps {
  siblingsHaveIcon: boolean;
}

interface MenuProps {
  nodes: MenuNodeProps[];
  onClick: AntdMenuProps['onClick'];
}

const createTitle = (
  text: string | React.ReactElement | undefined,
  icon: React.ReactNode,
  siblingsHaveIcon: boolean,
): JSX.Element => {
  if (!siblingsHaveIcon) {
    return <TextWrapper>{text}</TextWrapper>;
  } else {
    return (
      <>
        <IconWrapper>{icon}</IconWrapper>
        <TextWrapper>{text}</TextWrapper>
      </>
    );
  }
};

const convertToMenuItem = (node: MenuNodeProps, _siblingsHaveIcon: boolean): MenuItemType => {
  if (node.type === 'Divider') {
    return { key: node.key, type: 'divider' } as unknown as MenuItemType;
  }

  const baseItem = {
    icon: node.icon ?? null,
    key: node.key,
    label: createTitle(node.name, node.icon, false),
    style: { paddingBottom: 0, paddingTop: 0 },
  };

  if (node.type === 'SubMenu') {
    // const subSiblingsHaveIcon = node.nodes?.some(n => (n.type === 'Item' || n.type === 'SubMenu') && !!n.icon) ?? false;
    return {
      ...baseItem,
      children: node.nodes?.map(n => convertToMenuItem(n, false)),
      style: { paddingBottom: 0, paddingTop: 0 },
    } as MenuItemType;
  }

  return baseItem as MenuItemType;
};

export const Menu: React.FC<MenuProps> = props => {
  const siblingsHaveIcon =
    props.nodes?.some(node => (node.type === 'Item' || node.type === 'SubMenu') && !!node.icon) ?? false;

  const menuItems = React.useMemo(
    () => props.nodes?.map(node => convertToMenuItem(node, siblingsHaveIcon)),
    [props.nodes, siblingsHaveIcon],
  );

  return <StyledMenu items={menuItems} onClick={props.onClick} selectable={false} />;
};

export const MenuNode: React.FC<MenuNodeProps & MenuNodeInternalProps> = props => {
  switch (props.type) {
    case 'Item':
      return <StyledItem key={props.key}>123{createTitle(props.name, props.icon, props.siblingsHaveIcon)}</StyledItem>;
    case 'SubMenu': {
      const siblingsHaveIcon =
        props.nodes?.some(node => (node.type === 'Item' || node.type === 'SubMenu') && !!node.icon) ?? false;
      return (
        <StyledSubMenu key={props.key} title={createTitle(props.name, props.icon, props.siblingsHaveIcon)}>
          {props.nodes?.map(node => <MenuNode siblingsHaveIcon={siblingsHaveIcon} {...node} />)}
        </StyledSubMenu>
      );
    }
    case 'Divider':
      return <StyledDivider key={props.key} />;
    default:
      return <></>;
  }
};

const StyledMenu = styled(MenuAntd)`
  margin: 0px !important;
  padding: 4px 0px !important;
  box-shadow: 5px 5px 10px rgb(0 0 0 / 30%) !important;

  .ant-dropdown-menu-submenu-expand-icon {
    top: 45% !important;
    color: ${props => props.theme.colors.foreground} !important;
    svg {
      fill: ${props => props.theme.colors.foreground} !important;
    }
    position: absolute !important;
    width: 10px !important;
    transform: translateY(-50%) !important;
    transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) !important;
  }

  .ant-dropdown-menu-submenu-title {
    padding-top: 0px !important;
    padding-bottom: 0px !important;
  }

  .ant-dropdown-menu-item-icon {
    align-self: center;
  }
`;

const StyledItem = styled(MenuAntd.Item)`
  height: 24px !important;
  line-height: 12px !important;
  margin: 0px !important;
  padding: 0px !important;
  color: ${props => props.theme.colors.foregroundActive} !important;
  .ant-dropdown-menu-title-content {
    display: flex !important;
  }
`;

const StyledSubMenu = styled(MenuAntd.SubMenu)`
  height: 24px !important;
  line-height: 12px !important;
  margin: 0px !important;
  padding: 0px !important;
  .ant-dropdown-menu-submenu-title {
    line-height: 12px !important;
    height: 24px !important;
    margin: 0px !important;
    padding-top: 0px !important;
    padding-bottom: 0px !important;
    padding-left: 0px !important;
    padding-right: 12px !important;
    color: ${props => props.theme.colors.foregroundActive} !important;
  }
  .ant-dropdown-menu-submenu-arrow {
    right: 0px;
    top: 45%;
    color: ${props => props.theme.colors.foregroundActive} !important;
  }
  .ant-dropdown-menu-title-content {
    display: flex !important;
  }
`;

const StyledDivider = styled(MenuAntd.Divider)``;

const IconWrapper = styled.div`
  line-height: 12px;
  height: 24px;
  padding: 6px 0px;
  padding-left: 8px;
  width: 20px;
  overflow: hidden;
`;

const TextWrapper = styled.div`
  line-height: 12px;
  height: 24px;
  padding: 6px 8px;
  overflow: hidden;
`;
