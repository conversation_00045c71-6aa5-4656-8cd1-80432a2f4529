import { getGlobalSession } from '../../pages/api/session';
import { ContentManager, WordpressPost } from '@benzinga/content-manager';

export const getPageProps = async (): Promise<{
  post: WordpressPost | null;
  pageTargeting?: Record<string, string | string[]>;
}> => {
  const session = getGlobalSession();
  const contentManager = session.getManager(ContentManager);
  const postData = await contentManager.getPost(23833);
  return {
    post: postData?.ok ? postData.ok : null,
    pageTargeting: { BZ_CHANNEL: ['Forex'], BZ_PTYPE: 'tool' },
  };
};
