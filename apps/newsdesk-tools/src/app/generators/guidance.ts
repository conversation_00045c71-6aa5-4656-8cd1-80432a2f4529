import { equals, isEmpty, replace } from 'ramda';

import { GeneratedPost } from '../components/collections/collectionEntities';
import { Categories } from './entities';
import { contract, DEFAULT_EPS_DECIMAL_PLACES, dynamicallyCalculateRevDecimalPlaces, expand } from './utils';

enum EPSType {
  Adj = 'Adj',
  FFO = 'FFO',
  GAAP = 'GAAP',
}

interface PostKeys {
  calEst_checked: boolean;
  date: string;
  eps_guidance_est: string;
  eps_guidance_max: string;
  eps_guidance_min: string;
  eps_guidance_prior_max: string;
  eps_guidance_prior_min: number;
  eps_type: EPSType;
  exchange: string;
  importance: number;
  name: string;
  period_year: string;
  period: string;
  revenue_guidance_est: string;
  revenue_guidance_max: string;
  revenue_guidance_min: string;
  revenue_guidance_prior_max: string;
  revenue_guidance_prior_min: string;
  ticker: string;
  time: string;
  refinitiv_estimate: any;
}

const parseOrDefault = (value, fallback) => (!isNaN(expand(value)) && value !== '' ? expand(value) : fallback);

export default (keys: PostKeys): GeneratedPost => {
  const {
    calEst_checked,
    date,
    eps_guidance_est,
    eps_guidance_max,
    eps_guidance_min,
    eps_guidance_prior_max,
    eps_guidance_prior_min,
    eps_type,
    exchange,
    importance,
    name,
    period,
    period_year,
    refinitiv_estimate,
    revenue_guidance_est,
    revenue_guidance_max,
    revenue_guidance_min,
    revenue_guidance_prior_max,
    revenue_guidance_prior_min,
    ticker,
    time,
  } = keys;

  if (isEmpty(date) || isEmpty(time) || isEmpty(ticker) || isEmpty(name) || isEmpty(exchange) || isEmpty(period)) {
    return null;
  }

  //const epsEst = expand(eps_guidance_est);
  const epsMax = expand(eps_guidance_max);
  const epsMin = expand(eps_guidance_min);
  const epsPriorMax = expand(eps_guidance_prior_max);
  const epsPriorMin = expand(eps_guidance_prior_min);
  //const revEst = expand(revenue_guidance_est);
  const revMax = expand(revenue_guidance_max);
  const revMin = expand(revenue_guidance_min);
  const revPriorMax = expand(revenue_guidance_prior_max);
  const revPriorMin = expand(revenue_guidance_prior_min);

  const gap_est = parseOrDefault(calEst_checked ? eps_guidance_est : refinitiv_estimate?.gps, expand(eps_guidance_est));
  const ffo_est = parseOrDefault(
    calEst_checked ? expand(eps_guidance_est) : refinitiv_estimate?.ffo_est,
    expand(eps_guidance_est),
  );
  const eps_est_ref = parseOrDefault(
    calEst_checked ? expand(eps_guidance_est) : refinitiv_estimate?.eps_est,
    expand(eps_guidance_est),
  );

  const epsEst = eps_type === 'GAAP' ? gap_est : eps_type === 'FFO' ? ffo_est : eps_est_ref;
  const revEst = parseOrDefault(
    calEst_checked ? expand(revenue_guidance_est) : refinitiv_estimate?.revenue_est,
    expand(revenue_guidance_est),
  );
  const revDecimalPlaces = dynamicallyCalculateRevDecimalPlaces([
    revenue_guidance_est,
    revenue_guidance_max,
    revenue_guidance_min,
    revenue_guidance_prior_max,
    revenue_guidance_prior_min,
  ]);

  const bodyIntro = `${name} (${exchange}: ${ticker}) `;
  const titleIntro = `${name} `;

  const changePeriod = () => {
    if (equals(period, 'FY')) {
      return `${period_year} `;
    }
    return ' ';
  };

  const getEpsType = epsType => {
    if (isEmpty(epsType)) {
      return '';
    }
    return equals(epsType, EPSType.FFO) ? epsType : `${epsType} EPS`;
  };

  const epsType = getEpsType(eps_type);

  const changeEpsAction = () => {
    if (equals(parseFloat(epsMin), parseFloat(epsPriorMin)) && equals(parseFloat(epsMax), parseFloat(epsPriorMax))) {
      return 'affirms ';
    }
    if (parseFloat(epsMin) >= parseFloat(epsPriorMin) && parseFloat(epsMax) >= parseFloat(epsPriorMax)) {
      return 'raises ';
    }
    if (parseFloat(epsMin) >= parseFloat(epsPriorMin) && parseFloat(epsMax) <= parseFloat(epsPriorMax)) {
      return 'narrows ';
    }
    if (parseFloat(epsMin) <= parseFloat(epsPriorMin) && parseFloat(epsMax) >= parseFloat(epsPriorMax)) {
      return 'widens ';
    }
    if (parseFloat(epsMin) <= parseFloat(epsPriorMin) && parseFloat(epsMax) <= parseFloat(epsPriorMax)) {
      return 'lowers ';
    }
  };

  const changeEpsPriorRange = (decimal = DEFAULT_EPS_DECIMAL_PLACES, short = true, accounting = true) => {
    if (equals(parseFloat(epsPriorMin), parseFloat(epsPriorMax))) {
      return contract(epsPriorMin, '$', decimal, short, accounting);
    } else {
      return `${contract(epsPriorMin, '$', decimal, short, accounting)}-${contract(
        epsPriorMax,
        '$',
        decimal,
        short,
        accounting,
      )}`;
    }
  };

  const changeEpsCurrentRange = (decimal = DEFAULT_EPS_DECIMAL_PLACES, short = true, accounting = true) => {
    if (equals(parseFloat(epsMin), parseFloat(epsMax))) {
      return contract(epsMin, '$', decimal, short, accounting);
    } else {
      return `${contract(epsMin, '$', decimal, short, accounting)}-${contract(epsMax, '$', decimal, short, accounting)}`;
    }
  };

  let bodyEpsGuidance = '';
  let bodyEpsGuidanceAction = '';
  let bodyEpsGuidanceEst = '';
  let bodyEpsGuidancePrior = '';
  let titleEpsGuidance = '';
  let titleEpsGuidanceAction = '';
  let titleEpsGuidanceEst = '';
  let titleEpsGuidancePrior = '';

  if (epsMin && epsMax && epsPriorMin && epsPriorMax) {
    titleEpsGuidanceAction = `@1${period}@2`;
    titleEpsGuidanceAction = replace(/@1/g, changeEpsAction(), titleEpsGuidanceAction);
    titleEpsGuidanceAction = replace(/@2/g, changePeriod(), titleEpsGuidanceAction);
    if (changeEpsPriorRange() === changeEpsCurrentRange()) {
      titleEpsGuidancePrior = `${epsType} Guidance of @1`;
      titleEpsGuidancePrior = replace(/@1/g, changeEpsPriorRange(), titleEpsGuidancePrior);
    } else {
      titleEpsGuidancePrior = `${epsType} Guidance from @1 to @2`;
      titleEpsGuidancePrior = replace(/@1/g, changeEpsPriorRange(), titleEpsGuidancePrior);
      titleEpsGuidancePrior = replace(/@2/g, changeEpsCurrentRange(), titleEpsGuidancePrior);
    }
    if (epsEst) {
      titleEpsGuidanceEst = ` vs ${contract(epsEst, '$', DEFAULT_EPS_DECIMAL_PLACES)} Est`;
    }
    bodyEpsGuidanceAction = `@1${period}@2`;
    bodyEpsGuidanceAction = replace(/@1/g, changeEpsAction(), bodyEpsGuidanceAction);
    bodyEpsGuidanceAction = replace(/@2/g, changePeriod(), bodyEpsGuidanceAction);
    bodyEpsGuidancePrior = `${epsType} guidance from @1 to @2`;
    bodyEpsGuidancePrior = replace(/@1/g, changeEpsPriorRange(DEFAULT_EPS_DECIMAL_PLACES, false), bodyEpsGuidancePrior);
    bodyEpsGuidancePrior = replace(
      /@2/g,
      changeEpsCurrentRange(DEFAULT_EPS_DECIMAL_PLACES, false),
      bodyEpsGuidancePrior,
    );
    if (epsEst) {
      bodyEpsGuidanceEst = ` vs ${contract(epsEst, '$', DEFAULT_EPS_DECIMAL_PLACES, false)} analyst estimate.`;
    }
  } else if (epsMin && epsMax) {
    titleEpsGuidanceAction = `Sees ${period}@2`;
    titleEpsGuidanceAction = replace(/@1/g, changeEpsAction(), titleEpsGuidanceAction);
    titleEpsGuidanceAction = replace(/@2/g, changePeriod(), titleEpsGuidanceAction);
    titleEpsGuidance = `${epsType} @1`;
    titleEpsGuidance = replace(/@1/g, changeEpsCurrentRange(DEFAULT_EPS_DECIMAL_PLACES, false), titleEpsGuidance);
    if (epsEst) {
      titleEpsGuidanceEst = ` vs ${contract(epsEst, '$', DEFAULT_EPS_DECIMAL_PLACES)} Est`;
    }
    bodyEpsGuidanceAction = `is looking for ${period}@2`;
    bodyEpsGuidanceAction = replace(/@1/g, changeEpsAction(), bodyEpsGuidanceAction);
    bodyEpsGuidanceAction = replace(/@2/g, changePeriod(), bodyEpsGuidanceAction);
    bodyEpsGuidance = `${epsType} of @1`;
    bodyEpsGuidance = replace(/@1/g, changeEpsCurrentRange(DEFAULT_EPS_DECIMAL_PLACES, false), bodyEpsGuidance);
    if (epsEst) {
      bodyEpsGuidanceEst = ` vs ${contract(epsEst, '$', DEFAULT_EPS_DECIMAL_PLACES, false, true)} analyst estimate.`;
    }
  } else {
    // case 1
    if (epsMin && !epsMax) {
      titleEpsGuidanceAction = `${period}@2 expected to be more than `;
      titleEpsGuidanceAction = replace(/@2/g, changePeriod(), titleEpsGuidanceAction);
      if (epsEst) {
        titleEpsGuidanceEst = `${contract(epsMin, '$', 2)} vs ${contract(epsEst, '$', 2)} Est`;
        bodyEpsGuidanceEst = `${contract(epsMin, '$', 2)} vs ${contract(epsEst, '$', 2)} Est.`;
      }
      bodyEpsGuidanceAction = `${period}@2 expected to be more than `;
      bodyEpsGuidanceAction = replace(/@2/g, changePeriod(), bodyEpsGuidanceAction);
    }
    // case 2
    if (!epsMin && epsMax) {
      titleEpsGuidanceAction = `${period}@2 expected to be below `;
      titleEpsGuidanceAction = replace(/@2/g, changePeriod(), titleEpsGuidanceAction);
      if (epsEst) {
        titleEpsGuidanceEst = `${contract(epsMax, '$', 2)} vs ${contract(epsEst, '$', 2)} Est`;
        bodyEpsGuidanceEst = `${contract(epsMax, '$', 2)} vs ${contract(epsEst, '$', 2)} Est.`;
      }
      bodyEpsGuidanceAction = `${period}@2 expected to be below `;
      bodyEpsGuidanceAction = replace(/@2/g, changePeriod(), bodyEpsGuidanceAction);
    }
  }

  const changeRevenueAction = () => {
    if (equals(parseFloat(revMin), parseFloat(revPriorMin)) && equals(parseFloat(revMax), parseFloat(revPriorMax))) {
      return 'Affirms ';
    }
    if (parseFloat(revMin) >= parseFloat(revPriorMin) && parseFloat(revMax) >= parseFloat(revPriorMax)) {
      return 'Raises ';
    }
    if (parseFloat(revMin) >= parseFloat(revPriorMin) && parseFloat(revMax) <= parseFloat(revPriorMax)) {
      return 'Narrows ';
    }
    if (parseFloat(revMin) <= parseFloat(revPriorMin) && parseFloat(revMax) >= parseFloat(revPriorMax)) {
      return 'Widens ';
    }
    if (parseFloat(revMin) <= parseFloat(revPriorMin) && parseFloat(revMax) <= parseFloat(revPriorMax)) {
      return 'Lowers ';
    }
  };

  const changeRevenuePriorRange = (decimal = revDecimalPlaces, short = true, accounting = true) => {
    if (equals(parseFloat(revPriorMin), parseFloat(revPriorMax))) {
      return contract(revPriorMin, '$', decimal, short, accounting);
    } else {
      return `${contract(revPriorMin, '$', decimal, short, accounting)}-${contract(
        revPriorMax,
        '$',
        decimal,
        short,
        accounting,
      )}`;
    }
  };

  const changeRevenueCurrentRange = (decimal = 3, short = true, accounting = true) => {
    if (equals(parseFloat(revMin), parseFloat(revMax))) {
      return contract(revMin, '$', decimal, short, accounting);
    } else {
      return `${contract(revMin, '$', decimal, short, accounting)}-${contract(revMax, '$', decimal, short, accounting)}`;
    }
  };

  let bodyRevGuidance = '';
  let bodyRevGuidanceAction = '';
  let bodyRevGuidanceEst = '';
  let bodyRevGuidancePrior = '';
  let titleRevGuidance = '';
  let titleRevGuidanceAction = '';
  let titleRevGuidanceEst = '';
  let titleRevGuidancePrior = '';

  if (revMin && revMax && revPriorMin && revPriorMax) {
    titleRevGuidanceAction = `@1${period}@2`;
    titleRevGuidanceAction = replace(/@1/g, changeRevenueAction(), titleRevGuidanceAction);
    titleRevGuidanceAction = replace(/@2/g, changePeriod(), titleRevGuidanceAction);
    if (changeRevenuePriorRange(3) === changeRevenueCurrentRange(3)) {
      titleRevGuidancePrior = 'Sales Guidance of @1';
      titleRevGuidancePrior = replace(/@1/g, changeRevenuePriorRange(3), titleRevGuidancePrior);
    } else {
      titleRevGuidancePrior = 'Sales Guidance from @1 to @2';
      titleRevGuidancePrior = replace(/@1/g, changeRevenuePriorRange(3), titleRevGuidancePrior);
      titleRevGuidancePrior = replace(/@2/g, changeRevenueCurrentRange(3), titleRevGuidancePrior);
    }
    if (epsEst) {
      titleRevGuidanceEst = ` vs ${contract(revEst, '$', 3)} Est`;
    }

    bodyRevGuidanceAction = `@1${period}@2`;
    bodyRevGuidanceAction = replace(/@1/g, changeRevenueAction(), bodyRevGuidanceAction);
    bodyRevGuidanceAction = replace(/@2/g, changePeriod(), bodyRevGuidanceAction);
    bodyRevGuidancePrior = 'sales outlook from @1 to @2';
    bodyRevGuidancePrior = replace(/@1/g, changeRevenuePriorRange(3, false), bodyRevGuidancePrior);
    bodyRevGuidancePrior = replace(/@2/g, changeRevenueCurrentRange(3, false), bodyRevGuidancePrior);
    if (epsEst) {
      bodyRevGuidanceEst = ` vs ${contract(revEst, '$', 3, false)} estimate.`;
    }
  } else if (revMin && revMax) {
    titleRevGuidanceAction = `Sees ${period}@2`;
    titleRevGuidanceAction = replace(/@2/g, changePeriod(), titleRevGuidanceAction);
    if (epsMin && epsMax) {
      titleRevGuidanceAction = 'Sees ';
    }
    titleRevGuidance = 'Sales @1';
    titleRevGuidance = replace(/@1/g, changeRevenueCurrentRange(), titleRevGuidance);
    if (revEst) {
      titleRevGuidanceEst = ` vs ${contract(revEst, '$', 3)} Est`;
    }

    bodyRevGuidanceAction = epsMin && epsMax ? `Sees ${period}@2` : `sees ${period}@2`;
    bodyRevGuidanceAction = replace(/@2/g, changePeriod(), bodyRevGuidanceAction);
    if (epsMin && epsMax) {
      bodyRevGuidanceAction = 'sees ';
    }
    bodyRevGuidance = 'sales of @1';
    bodyRevGuidance = replace(/@1/g, changeRevenueCurrentRange(3, false), bodyRevGuidance);
    if (revEst) {
      bodyRevGuidanceEst = ` vs ${contract(revEst, '$', 3, false)} analyst estimate.`;
    }
  } else {
    const emptyOrSemiColon = epsMin || epsMax ? '; ' : '';

    // case 1
    if (revMin && !revMax) {
      titleRevGuidanceAction = `${emptyOrSemiColon}${period}@2 Revenue expected to be more than `;
      titleRevGuidanceAction = replace(/@2/g, changePeriod(), titleRevGuidanceAction);
      if (revEst) {
        titleRevGuidanceEst = `${contract(revMin, '$', 3)} vs ${contract(revEst, '$', 3)} Est`;
        bodyRevGuidanceEst = titleRevGuidanceEst;
      }
      bodyRevGuidance = ` ${period}@2 Revenue expected to be more than `;
      bodyRevGuidance = replace(/@2/g, changePeriod(), bodyRevGuidance);
    }
    // case 2
    if (!revMin && revMax) {
      titleRevGuidanceAction = `${emptyOrSemiColon}${period}@2 Revenue expected to be below `;
      titleRevGuidanceAction = replace(/@2/g, changePeriod(), titleRevGuidanceAction);
      if (revEst) {
        titleRevGuidanceEst = `${contract(revMax, '$', 3)} vs ${contract(revEst, '$', 3)} Est`;
        bodyRevGuidanceEst = titleRevGuidanceEst;
      }
      bodyRevGuidance = ` ${period}@2 Revenue expected to be below `;
      bodyRevGuidance = replace(/@2/g, changePeriod(), bodyRevGuidance);
    }
  }

  const emptyOrSemiColon = revMin && revMax && (epsMin || epsMax) ? '; ' : '';
  const epsEstEmptyOrFullStop = (epsMin || epsMax) && isEmpty(bodyEpsGuidanceEst) ? '.' : '';
  const revEstEmptyOrFullStop = isEmpty(bodyRevGuidanceEst) ? '.' : '';
  const spaceAfterEps = revMin && revMax && (epsMin || epsMax) ? ' ' : '';

  let title = `${titleIntro}${titleEpsGuidanceAction}${titleEpsGuidancePrior}${titleEpsGuidance}${titleEpsGuidanceEst}${emptyOrSemiColon}${titleRevGuidanceAction}${titleRevGuidancePrior}${titleRevGuidance}${titleRevGuidanceEst}`;
  let body = `${bodyIntro}${bodyEpsGuidanceAction}${bodyEpsGuidancePrior}${bodyEpsGuidance}${bodyEpsGuidanceEst}${epsEstEmptyOrFullStop}${spaceAfterEps}${bodyRevGuidanceAction}${bodyRevGuidancePrior}${bodyRevGuidance}${bodyRevGuidanceEst}${revEstEmptyOrFullStop}`;

  title = replace(/raises/g, 'Raises', title);
  title = replace(/lowers/g, 'Lowers', title);
  title = replace(/widens/g, 'Widens', title);
  title = replace(/narrows/g, 'Narrows', title);
  title = replace(/affirms/g, 'Affirms', title);

  if (!(epsMin && epsMax)) {
    body = replace(/Raises/g, 'raises', body);
    body = replace(/Lowers/g, 'lowers', body);
    body = replace(/Widens/g, 'widens', body);
    body = replace(/Narrows/g, 'narrows', body);
    body = replace(/Sees/g, 'sees', body);
    body = replace(/Affirms/g, 'affirms', body);
  }

  const categories = [Categories.NEWS, Categories.GUIDANCE];
  if (importance === 5) {
    categories.push(Categories.HOT);
  }

  return {
    body,
    categories,
    title,
  };
};
