import React from 'react';
import { StyleSheet, Text, Image, View, StyleProp, ViewStyle } from 'react-native';
import { formatDate } from '../../services/helpers';
import ArticleChannels from './ArticleChannels';
import QuotesChipView from '../../components/QuotesChipView';
import Colors from '../../constants/Colors';
import BenzingaHTML from '../../components/BenzingaHTML';
import { useTheme } from '../../theme/themecontext';
import { decode } from 'html-entities';
import { News, NewsImage } from '@benzinga/advanced-news-manager';
import CustomPressable from '../CustomPressable';

interface ArticleViewProps {
  article: News;
  max_lines?: number;
  onPressArticle?: (wiim: News) => void;
  onPressTicker?: (ticker: string) => void;
  onPressChannel?: (channel: string) => void;
  showAuthor?: boolean;
  showChannels?: boolean;
  showQuotes?: boolean;
  showTeaser?: boolean;
  style?: StyleProp<ViewStyle>;
}

const ArticleView = ({
  article,
  max_lines,
  onPressArticle,
  onPressChannel,
  onPressTicker,
  showAuthor = false,
  showChannels = false,
  showQuotes = false,
  showTeaser = false,
  style,
}: ArticleViewProps) => {
  const { colors, isDark } = useTheme();
  max_lines = max_lines ?? 4;

  const handleShowChannel = (channel: string) => {
    onPressChannel && onPressChannel(channel);
  };

  return (
    <View style={[style, { paddingVertical: 10 }]}>
      <CustomPressable
        onPress={() => {
          onPressArticle && onPressArticle(article as News);
        }}
        style={{
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%',
        }}
      >
        <View style={styles.info}>
          <View style={styles.textInfo}>
            <Text
              ellipsizeMode="tail"
              numberOfLines={max_lines}
              style={[
                styles.title,
                {
                  color: isDark ? colors.text : '#395173',
                  fontSize: 20,
                },
              ]}
            >
              {decode(article?.title)}
            </Text>
            {showAuthor && article?.author ? (
              <Text style={{ color: colors.text, marginBottom: 6 }}>by {article.author}</Text>
            ) : null}
            <View>
              <Text style={[styles.date, { color: colors.text }]}>{formatDate(article?.created)}</Text>
            </View>
          </View>
          {article?.thumbnail ||
          (!Array.isArray(article?.image) && (article as { image: NewsImage[] })?.image?.length) ? (
            <View style={styles.imageContainer}>
              <Image source={{ uri: article?.thumbnail || (article?.image as string) }} style={styles.imageStyle} />
            </View>
          ) : null}
        </View>
        {showTeaser && article?.teaser ? (
          <>
            <BenzingaHTML html={article?.teaser} />
            <View
              style={{
                alignItems: 'center',
                backgroundColor: colors.backgroundSecondary,
                borderColor: colors.border,
                borderRadius: 4,
                borderWidth: 1,
                height: 40,
                justifyContent: 'center',
                marginTop: 10,
                width: '100%',
              }}
            >
              <Text
                style={{
                  color: '#1A79FF',
                  fontWeight: '700',
                  textAlign: 'center',
                }}
              >
                Read more
              </Text>
            </View>
          </>
        ) : null}
      </CustomPressable>
      {showQuotes && article?.quotes?.length ? (
        <QuotesChipView
          pressedTicker={(ticker: string) => {
            onPressTicker && onPressTicker(ticker);
          }}
          quotes={article?.quotes}
        />
      ) : (
        <View />
      )}
      {showChannels ? <ArticleChannels article={article as News} onPressChannel={handleShowChannel} /> : <View />}
    </View>
  );
};

export default ArticleView;

const styles = StyleSheet.create({
  date: {
    color: Colors.lightText,
    fontSize: 12,
    marginBottom: 4,
  },
  imageContainer: {
    alignItems: 'center',
    backgroundColor: '#eeeeee',
    borderColor: '#dddddd',
    borderRadius: 4,
    borderWidth: 1,
    height: 96,
    justifyContent: 'center',
    marginBottom: 8,
    marginLeft: 8,
    overflow: 'hidden',
    padding: 0,
    width: 96,
  },
  imageStyle: {
    height: '100%',
    width: '100%',
  },
  info: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },

  separator: {
    color: '#ddd',
  },
  textInfo: {
    flex: 1,
    flexGrow: 1,
  },
  title: {
    color: Colors.darkText,
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 4,
  },
});
