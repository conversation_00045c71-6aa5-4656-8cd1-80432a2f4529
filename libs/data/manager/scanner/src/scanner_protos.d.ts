import * as $protobuf from "protobufjs";
/** Namespace ScannerProtos. */
export namespace ScannerProtos {

    /** Represents a ScannerService */
    class ScannerService extends $protobuf.rpc.Service {

        /**
         * Constructs a new ScannerService service.
         * @param rpcImpl RPC implementation
         * @param [requestDelimited=false] Whether requests are length-delimited
         * @param [responseDelimited=false] Whether responses are length-delimited
         */
        constructor(rpcImpl: $protobuf.RPCImpl, requestDelimited?: boolean, responseDelimited?: boolean);

        /**
         * Creates new ScannerService service using the specified rpc implementation.
         * @param rpcImpl RPC implementation
         * @param [requestDelimited=false] Whether requests are length-delimited
         * @param [responseDelimited=false] Whether responses are length-delimited
         * @returns RPC service. Useful where requests and/or responses are streamed.
         */
        public static create(rpcImpl: $protobuf.RPCImpl, requestDelimited?: boolean, responseDelimited?: boolean): ScannerService;

        /**
         * Calls executeQuery.
         * @param request Query message or plain object
         * @param callback Node-style callback called with the error, if any, and QueryResponse
         */
        public executeQuery(request: ScannerProtos.IQuery, callback: ScannerProtos.ScannerService.executeQueryCallback): void;

        /**
         * Calls executeQuery.
         * @param request Query message or plain object
         * @returns Promise
         */
        public executeQuery(request: ScannerProtos.IQuery): Promise<ScannerProtos.QueryResponse>;

        /**
         * * Query using simplified, a human readable format.
         * @param request QueryString message or plain object
         * @param callback Node-style callback called with the error, if any, and QueryResponse
         */
        public executeQueryString(request: ScannerProtos.IQueryString, callback: ScannerProtos.ScannerService.executeQueryStringCallback): void;

        /**
         * * Query using simplified, a human readable format.
         * @param request QueryString message or plain object
         * @returns Promise
         */
        public executeQueryString(request: ScannerProtos.IQueryString): Promise<ScannerProtos.QueryResponse>;
    }

    namespace ScannerService {

        /**
         * Callback as used by {@link ScannerProtos.ScannerService#executeQuery}.
         * @param error Error, if any
         * @param [response] QueryResponse
         */
        type executeQueryCallback = (error: (Error|null), response?: ScannerProtos.QueryResponse) => void;

        /**
         * Callback as used by {@link ScannerProtos.ScannerService#executeQueryString}.
         * @param error Error, if any
         * @param [response] QueryResponse
         */
        type executeQueryStringCallback = (error: (Error|null), response?: ScannerProtos.QueryResponse) => void;
    }

    /** Properties of a QueryString. */
    interface IQueryString {

        /** QueryString query */
        query?: (string|null);

        /** QueryString sortField */
        sortField?: (string|null);

        /** QueryString fields */
        fields?: (string|null);

        /** QueryString limit */
        limit?: (number|null);

        /** QueryString sortDir */
        sortDir?: (ScannerProtos.SortDir|null);
    }

    /**
     * An alternative to using Query.
     * This is a simplified way to building a query and using the gRPC method executeQueryString()
     * See scanner_fields.json for a list of field names.
     */
    class QueryString implements IQueryString {

        /**
         * Constructs a new QueryString.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IQueryString);

        /** QueryString query. */
        public query: string;

        /** QueryString sortField. */
        public sortField: string;

        /** QueryString fields. */
        public fields: string;

        /** QueryString limit. */
        public limit: number;

        /** QueryString sortDir. */
        public sortDir: ScannerProtos.SortDir;

        /**
         * Creates a new QueryString instance using the specified properties.
         * @param [properties] Properties to set
         * @returns QueryString instance
         */
        public static create(properties?: ScannerProtos.IQueryString): ScannerProtos.QueryString;

        /**
         * Encodes the specified QueryString message. Does not implicitly {@link ScannerProtos.QueryString.verify|verify} messages.
         * @param message QueryString message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IQueryString, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified QueryString message, length delimited. Does not implicitly {@link ScannerProtos.QueryString.verify|verify} messages.
         * @param message QueryString message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IQueryString, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a QueryString message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns QueryString
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.QueryString;

        /**
         * Decodes a QueryString message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns QueryString
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.QueryString;

        /**
         * Verifies a QueryString message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a QueryString message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns QueryString
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.QueryString;

        /**
         * Creates a plain object from a QueryString message. Also converts values to other types if specified.
         * @param message QueryString
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.QueryString, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this QueryString to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /**
     * By default the first (none) sort is used.
     * DESC_ABS, is a special sort for showing Gainers & Losers.
     */
    enum SortDir {
        ASC = 0,
        DESC = 1,
        DESC_ABS = 2,
        NONE = 3
    }

    /** Properties of a Query. */
    interface IQuery {

        /** Query filters */
        filters?: (ScannerProtos.IFilter[]|null);

        /** Query fields */
        fields?: (string[]|null);

        /** Query sortField */
        sortField?: (string|null);

        /** Query sortDir */
        sortDir?: (ScannerProtos.SortDir|null);

        /**
         * Filters as a string, i.e., price_gt_1,5;exchanges_in_NYSE.
         * If this is set, then filters is ignored.
         */
        filtersAsString?: (string|null);

        /** Query limit */
        limit?: (number|null);

        /** Query offset */
        offset?: (number|null);
    }

    /** Represents a Query. */
    class Query implements IQuery {

        /**
         * Constructs a new Query.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IQuery);

        /** Query filters. */
        public filters: ScannerProtos.IFilter[];

        /** Query fields. */
        public fields: string[];

        /** Query sortField. */
        public sortField: string;

        /** Query sortDir. */
        public sortDir: ScannerProtos.SortDir;

        /**
         * * Filters as a string, i.e., price_gt_1,5;exchanges_in_NYSE.
         * * If this is set, then filters is ignored.
         */
        public filtersAsString: string;

        /** Query limit. */
        public limit: number;

        /** Query offset. */
        public offset: number;

        /**
         * Creates a new Query instance using the specified properties.
         * @param [properties] Properties to set
         * @returns Query instance
         */
        public static create(properties?: ScannerProtos.IQuery): ScannerProtos.Query;

        /**
         * Encodes the specified Query message. Does not implicitly {@link ScannerProtos.Query.verify|verify} messages.
         * @param message Query message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IQuery, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified Query message, length delimited. Does not implicitly {@link ScannerProtos.Query.verify|verify} messages.
         * @param message Query message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IQuery, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a Query message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Query
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.Query;

        /**
         * Decodes a Query message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns Query
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.Query;

        /**
         * Verifies a Query message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a Query message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Query
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.Query;

        /**
         * Creates a plain object from a Query message. Also converts values to other types if specified.
         * @param message Query
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.Query, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Query to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a QueryResponse. */
    interface IQueryResponse {

        /** QueryResponse error */
        error?: (CommonProtos.IErr|null);

        /** QueryResponse totalInstruments */
        totalInstruments?: (number|null);

        /** QueryResponse instruments */
        instruments?: (QuoteProtos.IQuote[]|null);
    }

    /** Represents a QueryResponse. */
    class QueryResponse implements IQueryResponse {

        /**
         * Constructs a new QueryResponse.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IQueryResponse);

        /** QueryResponse error. */
        public error?: (CommonProtos.IErr|null);

        /** QueryResponse totalInstruments. */
        public totalInstruments: number;

        /** QueryResponse instruments. */
        public instruments: QuoteProtos.IQuote[];

        /**
         * Creates a new QueryResponse instance using the specified properties.
         * @param [properties] Properties to set
         * @returns QueryResponse instance
         */
        public static create(properties?: ScannerProtos.IQueryResponse): ScannerProtos.QueryResponse;

        /**
         * Encodes the specified QueryResponse message. Does not implicitly {@link ScannerProtos.QueryResponse.verify|verify} messages.
         * @param message QueryResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IQueryResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified QueryResponse message, length delimited. Does not implicitly {@link ScannerProtos.QueryResponse.verify|verify} messages.
         * @param message QueryResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IQueryResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a QueryResponse message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns QueryResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.QueryResponse;

        /**
         * Decodes a QueryResponse message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns QueryResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.QueryResponse;

        /**
         * Verifies a QueryResponse message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a QueryResponse message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns QueryResponse
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.QueryResponse;

        /**
         * Creates a plain object from a QueryResponse message. Also converts values to other types if specified.
         * @param message QueryResponse
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.QueryResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this QueryResponse to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a SnapshotRequest. */
    interface ISnapshotRequest {
    }

    /** Represents a SnapshotRequest. */
    class SnapshotRequest implements ISnapshotRequest {

        /**
         * Constructs a new SnapshotRequest.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.ISnapshotRequest);

        /**
         * Creates a new SnapshotRequest instance using the specified properties.
         * @param [properties] Properties to set
         * @returns SnapshotRequest instance
         */
        public static create(properties?: ScannerProtos.ISnapshotRequest): ScannerProtos.SnapshotRequest;

        /**
         * Encodes the specified SnapshotRequest message. Does not implicitly {@link ScannerProtos.SnapshotRequest.verify|verify} messages.
         * @param message SnapshotRequest message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.ISnapshotRequest, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified SnapshotRequest message, length delimited. Does not implicitly {@link ScannerProtos.SnapshotRequest.verify|verify} messages.
         * @param message SnapshotRequest message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.ISnapshotRequest, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a SnapshotRequest message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns SnapshotRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.SnapshotRequest;

        /**
         * Decodes a SnapshotRequest message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns SnapshotRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.SnapshotRequest;

        /**
         * Verifies a SnapshotRequest message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a SnapshotRequest message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns SnapshotRequest
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.SnapshotRequest;

        /**
         * Creates a plain object from a SnapshotRequest message. Also converts values to other types if specified.
         * @param message SnapshotRequest
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.SnapshotRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this SnapshotRequest to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a SnapshotResponse. */
    interface ISnapshotResponse {

        /** SnapshotResponse snapshot */
        snapshot?: (ScannerProtos.ISnapshot|null);
    }

    /** Represents a SnapshotResponse. */
    class SnapshotResponse implements ISnapshotResponse {

        /**
         * Constructs a new SnapshotResponse.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.ISnapshotResponse);

        /** SnapshotResponse snapshot. */
        public snapshot?: (ScannerProtos.ISnapshot|null);

        /**
         * Creates a new SnapshotResponse instance using the specified properties.
         * @param [properties] Properties to set
         * @returns SnapshotResponse instance
         */
        public static create(properties?: ScannerProtos.ISnapshotResponse): ScannerProtos.SnapshotResponse;

        /**
         * Encodes the specified SnapshotResponse message. Does not implicitly {@link ScannerProtos.SnapshotResponse.verify|verify} messages.
         * @param message SnapshotResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.ISnapshotResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified SnapshotResponse message, length delimited. Does not implicitly {@link ScannerProtos.SnapshotResponse.verify|verify} messages.
         * @param message SnapshotResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.ISnapshotResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a SnapshotResponse message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns SnapshotResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.SnapshotResponse;

        /**
         * Decodes a SnapshotResponse message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns SnapshotResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.SnapshotResponse;

        /**
         * Verifies a SnapshotResponse message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a SnapshotResponse message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns SnapshotResponse
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.SnapshotResponse;

        /**
         * Creates a plain object from a SnapshotResponse message. Also converts values to other types if specified.
         * @param message SnapshotResponse
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.SnapshotResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this SnapshotResponse to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Operator enum. */
    enum Operator {
        UNKNOWN_OPERATOR = 0,
        LT = 1,
        GT = 2,
        GTE = 3,
        LTE = 4,
        EQ = 5,
        NE = 6,
        IN = 7,
        BETWEEN = 8,
        REGEEX = 9,
        NOTIN = 10,
        BEGINSWITH = 11,
        ENDSWITH = 12,
        CONTAINS = 13,
        NOTEMPTY = 14,
        ISEMPTY = 15
    }

    /** Properties of a Filter. */
    interface IFilter {

        /** Filter dataFieldName */
        dataFieldName?: (string|null);

        /** Filter doubleFilter */
        doubleFilter?: (ScannerProtos.IDoubleFilter|null);

        /** Filter longFilter */
        longFilter?: (ScannerProtos.ILongFilter|null);

        /** Filter containsFilter */
        containsFilter?: (ScannerProtos.IContainsFilter|null);

        /** Filter equalsFilter */
        equalsFilter?: (ScannerProtos.IEqualsFilter|null);

        /** Filter intFilter */
        intFilter?: (ScannerProtos.IIntFilter|null);

        /** Filter dateFilter */
        dateFilter?: (ScannerProtos.IDateFilter|null);

        /** Filter greaterThanDoubleFilter */
        greaterThanDoubleFilter?: (ScannerProtos.IGreaterThanDoubleFilter|null);

        /** Filter lessThanDoubleFilter */
        lessThanDoubleFilter?: (ScannerProtos.ILessThanDoubleFilter|null);

        /** Filter greaterThanLongFilter */
        greaterThanLongFilter?: (ScannerProtos.IGreaterThanLongFilter|null);

        /** Filter lessThanLongFilter */
        lessThanLongFilter?: (ScannerProtos.ILessThanLongFilter|null);

        /** Filter operator */
        operator?: (ScannerProtos.Operator|null);

        /** Filter arguments */
        "arguments"?: (string[]|null);
    }

    /** Represents a Filter. */
    class Filter implements IFilter {

        /**
         * Constructs a new Filter.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IFilter);

        /** Filter dataFieldName. */
        public dataFieldName: string;

        /** Filter doubleFilter. */
        public doubleFilter?: (ScannerProtos.IDoubleFilter|null);

        /** Filter longFilter. */
        public longFilter?: (ScannerProtos.ILongFilter|null);

        /** Filter containsFilter. */
        public containsFilter?: (ScannerProtos.IContainsFilter|null);

        /** Filter equalsFilter. */
        public equalsFilter?: (ScannerProtos.IEqualsFilter|null);

        /** Filter intFilter. */
        public intFilter?: (ScannerProtos.IIntFilter|null);

        /** Filter dateFilter. */
        public dateFilter?: (ScannerProtos.IDateFilter|null);

        /** Filter greaterThanDoubleFilter. */
        public greaterThanDoubleFilter?: (ScannerProtos.IGreaterThanDoubleFilter|null);

        /** Filter lessThanDoubleFilter. */
        public lessThanDoubleFilter?: (ScannerProtos.ILessThanDoubleFilter|null);

        /** Filter greaterThanLongFilter. */
        public greaterThanLongFilter?: (ScannerProtos.IGreaterThanLongFilter|null);

        /** Filter lessThanLongFilter. */
        public lessThanLongFilter?: (ScannerProtos.ILessThanLongFilter|null);

        /** Filter operator. */
        public operator: ScannerProtos.Operator;

        /** Filter arguments. */
        public arguments: string[];

        /** Filter type. */
        public type?: ("doubleFilter"|"longFilter"|"containsFilter"|"equalsFilter"|"intFilter"|"dateFilter"|"greaterThanDoubleFilter"|"lessThanDoubleFilter"|"greaterThanLongFilter"|"lessThanLongFilter");

        /**
         * Creates a new Filter instance using the specified properties.
         * @param [properties] Properties to set
         * @returns Filter instance
         */
        public static create(properties?: ScannerProtos.IFilter): ScannerProtos.Filter;

        /**
         * Encodes the specified Filter message. Does not implicitly {@link ScannerProtos.Filter.verify|verify} messages.
         * @param message Filter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IFilter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified Filter message, length delimited. Does not implicitly {@link ScannerProtos.Filter.verify|verify} messages.
         * @param message Filter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IFilter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a Filter message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Filter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.Filter;

        /**
         * Decodes a Filter message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns Filter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.Filter;

        /**
         * Verifies a Filter message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a Filter message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Filter
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.Filter;

        /**
         * Creates a plain object from a Filter message. Also converts values to other types if specified.
         * @param message Filter
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.Filter, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Filter to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of an EqualsFilter. */
    interface IEqualsFilter {

        /** EqualsFilter stringValue */
        stringValue?: (string|null);
    }

    /** Represents an EqualsFilter. */
    class EqualsFilter implements IEqualsFilter {

        /**
         * Constructs a new EqualsFilter.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IEqualsFilter);

        /** EqualsFilter stringValue. */
        public stringValue: string;

        /** EqualsFilter type. */
        public type?: "stringValue";

        /**
         * Creates a new EqualsFilter instance using the specified properties.
         * @param [properties] Properties to set
         * @returns EqualsFilter instance
         */
        public static create(properties?: ScannerProtos.IEqualsFilter): ScannerProtos.EqualsFilter;

        /**
         * Encodes the specified EqualsFilter message. Does not implicitly {@link ScannerProtos.EqualsFilter.verify|verify} messages.
         * @param message EqualsFilter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IEqualsFilter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified EqualsFilter message, length delimited. Does not implicitly {@link ScannerProtos.EqualsFilter.verify|verify} messages.
         * @param message EqualsFilter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IEqualsFilter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an EqualsFilter message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns EqualsFilter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.EqualsFilter;

        /**
         * Decodes an EqualsFilter message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns EqualsFilter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.EqualsFilter;

        /**
         * Verifies an EqualsFilter message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an EqualsFilter message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns EqualsFilter
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.EqualsFilter;

        /**
         * Creates a plain object from an EqualsFilter message. Also converts values to other types if specified.
         * @param message EqualsFilter
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.EqualsFilter, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this EqualsFilter to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a GreaterThanDoubleFilter. */
    interface IGreaterThanDoubleFilter {

        /** GreaterThanDoubleFilter value */
        value?: (number|null);
    }

    /** Represents a GreaterThanDoubleFilter. */
    class GreaterThanDoubleFilter implements IGreaterThanDoubleFilter {

        /**
         * Constructs a new GreaterThanDoubleFilter.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IGreaterThanDoubleFilter);

        /** GreaterThanDoubleFilter value. */
        public value: number;

        /**
         * Creates a new GreaterThanDoubleFilter instance using the specified properties.
         * @param [properties] Properties to set
         * @returns GreaterThanDoubleFilter instance
         */
        public static create(properties?: ScannerProtos.IGreaterThanDoubleFilter): ScannerProtos.GreaterThanDoubleFilter;

        /**
         * Encodes the specified GreaterThanDoubleFilter message. Does not implicitly {@link ScannerProtos.GreaterThanDoubleFilter.verify|verify} messages.
         * @param message GreaterThanDoubleFilter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IGreaterThanDoubleFilter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified GreaterThanDoubleFilter message, length delimited. Does not implicitly {@link ScannerProtos.GreaterThanDoubleFilter.verify|verify} messages.
         * @param message GreaterThanDoubleFilter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IGreaterThanDoubleFilter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a GreaterThanDoubleFilter message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns GreaterThanDoubleFilter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.GreaterThanDoubleFilter;

        /**
         * Decodes a GreaterThanDoubleFilter message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns GreaterThanDoubleFilter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.GreaterThanDoubleFilter;

        /**
         * Verifies a GreaterThanDoubleFilter message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a GreaterThanDoubleFilter message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns GreaterThanDoubleFilter
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.GreaterThanDoubleFilter;

        /**
         * Creates a plain object from a GreaterThanDoubleFilter message. Also converts values to other types if specified.
         * @param message GreaterThanDoubleFilter
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.GreaterThanDoubleFilter, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this GreaterThanDoubleFilter to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a LessThanDoubleFilter. */
    interface ILessThanDoubleFilter {

        /** LessThanDoubleFilter value */
        value?: (number|null);
    }

    /** Represents a LessThanDoubleFilter. */
    class LessThanDoubleFilter implements ILessThanDoubleFilter {

        /**
         * Constructs a new LessThanDoubleFilter.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.ILessThanDoubleFilter);

        /** LessThanDoubleFilter value. */
        public value: number;

        /**
         * Creates a new LessThanDoubleFilter instance using the specified properties.
         * @param [properties] Properties to set
         * @returns LessThanDoubleFilter instance
         */
        public static create(properties?: ScannerProtos.ILessThanDoubleFilter): ScannerProtos.LessThanDoubleFilter;

        /**
         * Encodes the specified LessThanDoubleFilter message. Does not implicitly {@link ScannerProtos.LessThanDoubleFilter.verify|verify} messages.
         * @param message LessThanDoubleFilter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.ILessThanDoubleFilter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified LessThanDoubleFilter message, length delimited. Does not implicitly {@link ScannerProtos.LessThanDoubleFilter.verify|verify} messages.
         * @param message LessThanDoubleFilter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.ILessThanDoubleFilter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a LessThanDoubleFilter message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns LessThanDoubleFilter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.LessThanDoubleFilter;

        /**
         * Decodes a LessThanDoubleFilter message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns LessThanDoubleFilter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.LessThanDoubleFilter;

        /**
         * Verifies a LessThanDoubleFilter message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a LessThanDoubleFilter message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns LessThanDoubleFilter
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.LessThanDoubleFilter;

        /**
         * Creates a plain object from a LessThanDoubleFilter message. Also converts values to other types if specified.
         * @param message LessThanDoubleFilter
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.LessThanDoubleFilter, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this LessThanDoubleFilter to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a GreaterThanLongFilter. */
    interface IGreaterThanLongFilter {

        /** GreaterThanLongFilter value */
        value?: (number|Long|null);
    }

    /** Represents a GreaterThanLongFilter. */
    class GreaterThanLongFilter implements IGreaterThanLongFilter {

        /**
         * Constructs a new GreaterThanLongFilter.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IGreaterThanLongFilter);

        /** GreaterThanLongFilter value. */
        public value: (number|Long);

        /**
         * Creates a new GreaterThanLongFilter instance using the specified properties.
         * @param [properties] Properties to set
         * @returns GreaterThanLongFilter instance
         */
        public static create(properties?: ScannerProtos.IGreaterThanLongFilter): ScannerProtos.GreaterThanLongFilter;

        /**
         * Encodes the specified GreaterThanLongFilter message. Does not implicitly {@link ScannerProtos.GreaterThanLongFilter.verify|verify} messages.
         * @param message GreaterThanLongFilter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IGreaterThanLongFilter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified GreaterThanLongFilter message, length delimited. Does not implicitly {@link ScannerProtos.GreaterThanLongFilter.verify|verify} messages.
         * @param message GreaterThanLongFilter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IGreaterThanLongFilter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a GreaterThanLongFilter message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns GreaterThanLongFilter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.GreaterThanLongFilter;

        /**
         * Decodes a GreaterThanLongFilter message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns GreaterThanLongFilter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.GreaterThanLongFilter;

        /**
         * Verifies a GreaterThanLongFilter message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a GreaterThanLongFilter message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns GreaterThanLongFilter
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.GreaterThanLongFilter;

        /**
         * Creates a plain object from a GreaterThanLongFilter message. Also converts values to other types if specified.
         * @param message GreaterThanLongFilter
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.GreaterThanLongFilter, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this GreaterThanLongFilter to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a LessThanLongFilter. */
    interface ILessThanLongFilter {

        /** LessThanLongFilter value */
        value?: (number|Long|null);
    }

    /** Represents a LessThanLongFilter. */
    class LessThanLongFilter implements ILessThanLongFilter {

        /**
         * Constructs a new LessThanLongFilter.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.ILessThanLongFilter);

        /** LessThanLongFilter value. */
        public value: (number|Long);

        /**
         * Creates a new LessThanLongFilter instance using the specified properties.
         * @param [properties] Properties to set
         * @returns LessThanLongFilter instance
         */
        public static create(properties?: ScannerProtos.ILessThanLongFilter): ScannerProtos.LessThanLongFilter;

        /**
         * Encodes the specified LessThanLongFilter message. Does not implicitly {@link ScannerProtos.LessThanLongFilter.verify|verify} messages.
         * @param message LessThanLongFilter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.ILessThanLongFilter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified LessThanLongFilter message, length delimited. Does not implicitly {@link ScannerProtos.LessThanLongFilter.verify|verify} messages.
         * @param message LessThanLongFilter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.ILessThanLongFilter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a LessThanLongFilter message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns LessThanLongFilter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.LessThanLongFilter;

        /**
         * Decodes a LessThanLongFilter message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns LessThanLongFilter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.LessThanLongFilter;

        /**
         * Verifies a LessThanLongFilter message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a LessThanLongFilter message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns LessThanLongFilter
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.LessThanLongFilter;

        /**
         * Creates a plain object from a LessThanLongFilter message. Also converts values to other types if specified.
         * @param message LessThanLongFilter
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.LessThanLongFilter, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this LessThanLongFilter to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a DateFilter. */
    interface IDateFilter {

        /** DateFilter from */
        from?: (number|null);

        /** DateFilter to */
        to?: (number|null);
    }

    /** Represents a DateFilter. */
    class DateFilter implements IDateFilter {

        /**
         * Constructs a new DateFilter.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IDateFilter);

        /** DateFilter from. */
        public from: number;

        /** DateFilter to. */
        public to: number;

        /**
         * Creates a new DateFilter instance using the specified properties.
         * @param [properties] Properties to set
         * @returns DateFilter instance
         */
        public static create(properties?: ScannerProtos.IDateFilter): ScannerProtos.DateFilter;

        /**
         * Encodes the specified DateFilter message. Does not implicitly {@link ScannerProtos.DateFilter.verify|verify} messages.
         * @param message DateFilter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IDateFilter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified DateFilter message, length delimited. Does not implicitly {@link ScannerProtos.DateFilter.verify|verify} messages.
         * @param message DateFilter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IDateFilter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a DateFilter message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns DateFilter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.DateFilter;

        /**
         * Decodes a DateFilter message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns DateFilter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.DateFilter;

        /**
         * Verifies a DateFilter message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a DateFilter message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns DateFilter
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.DateFilter;

        /**
         * Creates a plain object from a DateFilter message. Also converts values to other types if specified.
         * @param message DateFilter
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.DateFilter, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this DateFilter to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a DoubleFilter. */
    interface IDoubleFilter {

        /** DoubleFilter minimum */
        minimum?: (number|null);

        /** DoubleFilter maximum */
        maximum?: (number|null);
    }

    /** Represents a DoubleFilter. */
    class DoubleFilter implements IDoubleFilter {

        /**
         * Constructs a new DoubleFilter.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IDoubleFilter);

        /** DoubleFilter minimum. */
        public minimum: number;

        /** DoubleFilter maximum. */
        public maximum: number;

        /**
         * Creates a new DoubleFilter instance using the specified properties.
         * @param [properties] Properties to set
         * @returns DoubleFilter instance
         */
        public static create(properties?: ScannerProtos.IDoubleFilter): ScannerProtos.DoubleFilter;

        /**
         * Encodes the specified DoubleFilter message. Does not implicitly {@link ScannerProtos.DoubleFilter.verify|verify} messages.
         * @param message DoubleFilter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IDoubleFilter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified DoubleFilter message, length delimited. Does not implicitly {@link ScannerProtos.DoubleFilter.verify|verify} messages.
         * @param message DoubleFilter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IDoubleFilter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a DoubleFilter message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns DoubleFilter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.DoubleFilter;

        /**
         * Decodes a DoubleFilter message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns DoubleFilter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.DoubleFilter;

        /**
         * Verifies a DoubleFilter message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a DoubleFilter message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns DoubleFilter
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.DoubleFilter;

        /**
         * Creates a plain object from a DoubleFilter message. Also converts values to other types if specified.
         * @param message DoubleFilter
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.DoubleFilter, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this DoubleFilter to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a LongFilter. */
    interface ILongFilter {

        /** LongFilter minimum */
        minimum?: (number|Long|null);

        /** LongFilter maximum */
        maximum?: (number|Long|null);
    }

    /** Represents a LongFilter. */
    class LongFilter implements ILongFilter {

        /**
         * Constructs a new LongFilter.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.ILongFilter);

        /** LongFilter minimum. */
        public minimum: (number|Long);

        /** LongFilter maximum. */
        public maximum: (number|Long);

        /**
         * Creates a new LongFilter instance using the specified properties.
         * @param [properties] Properties to set
         * @returns LongFilter instance
         */
        public static create(properties?: ScannerProtos.ILongFilter): ScannerProtos.LongFilter;

        /**
         * Encodes the specified LongFilter message. Does not implicitly {@link ScannerProtos.LongFilter.verify|verify} messages.
         * @param message LongFilter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.ILongFilter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified LongFilter message, length delimited. Does not implicitly {@link ScannerProtos.LongFilter.verify|verify} messages.
         * @param message LongFilter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.ILongFilter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a LongFilter message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns LongFilter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.LongFilter;

        /**
         * Decodes a LongFilter message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns LongFilter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.LongFilter;

        /**
         * Verifies a LongFilter message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a LongFilter message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns LongFilter
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.LongFilter;

        /**
         * Creates a plain object from a LongFilter message. Also converts values to other types if specified.
         * @param message LongFilter
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.LongFilter, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this LongFilter to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of an IntFilter. */
    interface IIntFilter {

        /** IntFilter minimum */
        minimum?: (number|null);

        /** IntFilter maximum */
        maximum?: (number|null);
    }

    /** Represents an IntFilter. */
    class IntFilter implements IIntFilter {

        /**
         * Constructs a new IntFilter.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IIntFilter);

        /** IntFilter minimum. */
        public minimum: number;

        /** IntFilter maximum. */
        public maximum: number;

        /**
         * Creates a new IntFilter instance using the specified properties.
         * @param [properties] Properties to set
         * @returns IntFilter instance
         */
        public static create(properties?: ScannerProtos.IIntFilter): ScannerProtos.IntFilter;

        /**
         * Encodes the specified IntFilter message. Does not implicitly {@link ScannerProtos.IntFilter.verify|verify} messages.
         * @param message IntFilter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IIntFilter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified IntFilter message, length delimited. Does not implicitly {@link ScannerProtos.IntFilter.verify|verify} messages.
         * @param message IntFilter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IIntFilter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an IntFilter message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns IntFilter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.IntFilter;

        /**
         * Decodes an IntFilter message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns IntFilter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.IntFilter;

        /**
         * Verifies an IntFilter message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an IntFilter message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns IntFilter
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.IntFilter;

        /**
         * Creates a plain object from an IntFilter message. Also converts values to other types if specified.
         * @param message IntFilter
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.IntFilter, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this IntFilter to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a ContainsFilter. */
    interface IContainsFilter {

        /** ContainsFilter value */
        value?: (string[]|null);
    }

    /** Represents a ContainsFilter. */
    class ContainsFilter implements IContainsFilter {

        /**
         * Constructs a new ContainsFilter.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IContainsFilter);

        /** ContainsFilter value. */
        public value: string[];

        /**
         * Creates a new ContainsFilter instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ContainsFilter instance
         */
        public static create(properties?: ScannerProtos.IContainsFilter): ScannerProtos.ContainsFilter;

        /**
         * Encodes the specified ContainsFilter message. Does not implicitly {@link ScannerProtos.ContainsFilter.verify|verify} messages.
         * @param message ContainsFilter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IContainsFilter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ContainsFilter message, length delimited. Does not implicitly {@link ScannerProtos.ContainsFilter.verify|verify} messages.
         * @param message ContainsFilter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IContainsFilter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ContainsFilter message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ContainsFilter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.ContainsFilter;

        /**
         * Decodes a ContainsFilter message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ContainsFilter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.ContainsFilter;

        /**
         * Verifies a ContainsFilter message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ContainsFilter message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ContainsFilter
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.ContainsFilter;

        /**
         * Creates a plain object from a ContainsFilter message. Also converts values to other types if specified.
         * @param message ContainsFilter
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.ContainsFilter, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ContainsFilter to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a ScannerResult. */
    interface IScannerResult {

        /** ScannerResult processingTimeMillis */
        processingTimeMillis?: (number|null);

        /** ScannerResult totalInstruments */
        totalInstruments?: (number|null);

        /** ScannerResult instruments */
        instruments?: (QuoteProtos.IQuote[]|null);

        /** ScannerResult fields */
        fields?: (string[]|null);

        /** ScannerResult maxAge */
        maxAge?: (number|null);
    }

    /** Represents a ScannerResult. */
    class ScannerResult implements IScannerResult {

        /**
         * Constructs a new ScannerResult.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IScannerResult);

        /** ScannerResult processingTimeMillis. */
        public processingTimeMillis: number;

        /** ScannerResult totalInstruments. */
        public totalInstruments: number;

        /** ScannerResult instruments. */
        public instruments: QuoteProtos.IQuote[];

        /** ScannerResult fields. */
        public fields: string[];

        /** ScannerResult maxAge. */
        public maxAge: number;

        /**
         * Creates a new ScannerResult instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ScannerResult instance
         */
        public static create(properties?: ScannerProtos.IScannerResult): ScannerProtos.ScannerResult;

        /**
         * Encodes the specified ScannerResult message. Does not implicitly {@link ScannerProtos.ScannerResult.verify|verify} messages.
         * @param message ScannerResult message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IScannerResult, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ScannerResult message, length delimited. Does not implicitly {@link ScannerProtos.ScannerResult.verify|verify} messages.
         * @param message ScannerResult message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IScannerResult, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ScannerResult message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ScannerResult
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.ScannerResult;

        /**
         * Decodes a ScannerResult message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ScannerResult
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.ScannerResult;

        /**
         * Verifies a ScannerResult message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ScannerResult message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ScannerResult
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.ScannerResult;

        /**
         * Creates a plain object from a ScannerResult message. Also converts values to other types if specified.
         * @param message ScannerResult
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.ScannerResult, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ScannerResult to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a DataField. */
    interface IDataField {

        /** DataField name */
        name?: (string|null);

        /** DataField label */
        label?: (string|null);

        /** DataField category */
        category?: (string|null);

        /** DataField subCategory */
        subCategory?: (string|null);

        /** DataField uiFilterHint */
        uiFilterHint?: (ScannerProtos.DataField.IUIFilterHint|null);

        /** DataField options */
        options?: (ScannerProtos.DataField.IOption[]|null);

        /** DataField filterable */
        filterable?: (boolean|null);

        /** DataField columnable */
        columnable?: (boolean|null);

        /** DataField uiColumnHint */
        uiColumnHint?: (ScannerProtos.DataField.IUIColumnHint|null);

        /** DataField updateFrequency */
        updateFrequency?: (ScannerProtos.DataField.UpdateFrequency|null);

        /** DataField order */
        order?: (number|null);

        /** DataField labelSubText */
        labelSubText?: (string|null);

        /** DataField percentage */
        percentage?: (boolean|null);

        /** DataField uiHints */
        uiHints?: (ScannerProtos.DataField.IUIHints|null);

        /** DataField filterOptionsName */
        filterOptionsName?: (string|null);

        /** DataField source */
        source?: (QuoteProtos.QuoteSource|null);

        /** DataField dependentFilter */
        dependentFilter?: (ScannerProtos.IDataField|null);
    }

    /**
     * For each field in the Instrument message there will be a DataField record.
     * scanner_datafields.json contains the fixed list of data fields and meta information.
     */
    class DataField implements IDataField {

        /**
         * Constructs a new DataField.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IDataField);

        /** DataField name. */
        public name: string;

        /** DataField label. */
        public label: string;

        /** DataField category. */
        public category: string;

        /** DataField subCategory. */
        public subCategory: string;

        /** DataField uiFilterHint. */
        public uiFilterHint?: (ScannerProtos.DataField.IUIFilterHint|null);

        /** DataField options. */
        public options: ScannerProtos.DataField.IOption[];

        /** DataField filterable. */
        public filterable: boolean;

        /** DataField columnable. */
        public columnable: boolean;

        /** DataField uiColumnHint. */
        public uiColumnHint?: (ScannerProtos.DataField.IUIColumnHint|null);

        /** DataField updateFrequency. */
        public updateFrequency: ScannerProtos.DataField.UpdateFrequency;

        /** DataField order. */
        public order: number;

        /** DataField labelSubText. */
        public labelSubText: string;

        /** DataField percentage. */
        public percentage: boolean;

        /** DataField uiHints. */
        public uiHints?: (ScannerProtos.DataField.IUIHints|null);

        /** DataField filterOptionsName. */
        public filterOptionsName: string;

        /** DataField source. */
        public source: QuoteProtos.QuoteSource;

        /** DataField dependentFilter. */
        public dependentFilter?: (ScannerProtos.IDataField|null);

        /**
         * Creates a new DataField instance using the specified properties.
         * @param [properties] Properties to set
         * @returns DataField instance
         */
        public static create(properties?: ScannerProtos.IDataField): ScannerProtos.DataField;

        /**
         * Encodes the specified DataField message. Does not implicitly {@link ScannerProtos.DataField.verify|verify} messages.
         * @param message DataField message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IDataField, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified DataField message, length delimited. Does not implicitly {@link ScannerProtos.DataField.verify|verify} messages.
         * @param message DataField message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IDataField, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a DataField message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns DataField
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.DataField;

        /**
         * Decodes a DataField message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns DataField
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.DataField;

        /**
         * Verifies a DataField message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a DataField message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns DataField
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.DataField;

        /**
         * Creates a plain object from a DataField message. Also converts values to other types if specified.
         * @param message DataField
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.DataField, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this DataField to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    namespace DataField {

        /** Properties of a UIFilterHint. */
        interface IUIFilterHint {

            /** UIFilterHint type */
            type?: (string|null);

            /** UIFilterHint defaultValue */
            defaultValue?: (string|null);

            /** UIFilterHint defaultOperator */
            defaultOperator?: (ScannerProtos.Operator|null);

            /** UIFilterHint multiplier */
            multiplier?: (number|Long|null);

            /** UIFilterHint emptyText */
            emptyText?: (string|null);

            /** UIFilterHint slider */
            slider?: (boolean|null);

            /** UIFilterHint hidden */
            hidden?: (boolean|null);

            /** UIFilterHint minFrom */
            minFrom?: (string|null);

            /** UIFilterHint maxTo */
            maxTo?: (string|null);
        }

        /** Represents a UIFilterHint. */
        class UIFilterHint implements IUIFilterHint {

            /**
             * Constructs a new UIFilterHint.
             * @param [properties] Properties to set
             */
            constructor(properties?: ScannerProtos.DataField.IUIFilterHint);

            /** UIFilterHint type. */
            public type: string;

            /** UIFilterHint defaultValue. */
            public defaultValue: string;

            /** UIFilterHint defaultOperator. */
            public defaultOperator: ScannerProtos.Operator;

            /** UIFilterHint multiplier. */
            public multiplier: (number|Long);

            /** UIFilterHint emptyText. */
            public emptyText: string;

            /** UIFilterHint slider. */
            public slider: boolean;

            /** UIFilterHint hidden. */
            public hidden: boolean;

            /** UIFilterHint minFrom. */
            public minFrom: string;

            /** UIFilterHint maxTo. */
            public maxTo: string;

            /**
             * Creates a new UIFilterHint instance using the specified properties.
             * @param [properties] Properties to set
             * @returns UIFilterHint instance
             */
            public static create(properties?: ScannerProtos.DataField.IUIFilterHint): ScannerProtos.DataField.UIFilterHint;

            /**
             * Encodes the specified UIFilterHint message. Does not implicitly {@link ScannerProtos.DataField.UIFilterHint.verify|verify} messages.
             * @param message UIFilterHint message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: ScannerProtos.DataField.IUIFilterHint, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified UIFilterHint message, length delimited. Does not implicitly {@link ScannerProtos.DataField.UIFilterHint.verify|verify} messages.
             * @param message UIFilterHint message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: ScannerProtos.DataField.IUIFilterHint, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a UIFilterHint message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns UIFilterHint
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.DataField.UIFilterHint;

            /**
             * Decodes a UIFilterHint message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns UIFilterHint
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.DataField.UIFilterHint;

            /**
             * Verifies a UIFilterHint message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a UIFilterHint message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns UIFilterHint
             */
            public static fromObject(object: { [k: string]: any }): ScannerProtos.DataField.UIFilterHint;

            /**
             * Creates a plain object from a UIFilterHint message. Also converts values to other types if specified.
             * @param message UIFilterHint
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: ScannerProtos.DataField.UIFilterHint, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this UIFilterHint to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        /** Properties of a UIColumnHint. */
        interface IUIColumnHint {

            /** UIColumnHint format */
            format?: (ScannerProtos.DataField.UIColumnHint.Format|null);

            /** UIColumnHint decimalPlaces */
            decimalPlaces?: (number|null);

            /** UIColumnHint percentage */
            percentage?: (boolean|null);
        }

        /** * */
        class UIColumnHint implements IUIColumnHint {

            /**
             * Constructs a new UIColumnHint.
             * @param [properties] Properties to set
             */
            constructor(properties?: ScannerProtos.DataField.IUIColumnHint);

            /** UIColumnHint format. */
            public format: ScannerProtos.DataField.UIColumnHint.Format;

            /** UIColumnHint decimalPlaces. */
            public decimalPlaces: number;

            /** UIColumnHint percentage. */
            public percentage: boolean;

            /**
             * Creates a new UIColumnHint instance using the specified properties.
             * @param [properties] Properties to set
             * @returns UIColumnHint instance
             */
            public static create(properties?: ScannerProtos.DataField.IUIColumnHint): ScannerProtos.DataField.UIColumnHint;

            /**
             * Encodes the specified UIColumnHint message. Does not implicitly {@link ScannerProtos.DataField.UIColumnHint.verify|verify} messages.
             * @param message UIColumnHint message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: ScannerProtos.DataField.IUIColumnHint, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified UIColumnHint message, length delimited. Does not implicitly {@link ScannerProtos.DataField.UIColumnHint.verify|verify} messages.
             * @param message UIColumnHint message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: ScannerProtos.DataField.IUIColumnHint, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a UIColumnHint message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns UIColumnHint
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.DataField.UIColumnHint;

            /**
             * Decodes a UIColumnHint message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns UIColumnHint
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.DataField.UIColumnHint;

            /**
             * Verifies a UIColumnHint message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a UIColumnHint message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns UIColumnHint
             */
            public static fromObject(object: { [k: string]: any }): ScannerProtos.DataField.UIColumnHint;

            /**
             * Creates a plain object from a UIColumnHint message. Also converts values to other types if specified.
             * @param message UIColumnHint
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: ScannerProtos.DataField.UIColumnHint, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this UIColumnHint to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        namespace UIColumnHint {

            /** Format enum. */
            enum Format {
                PRICE = 0,
                PRICE_PERCENT = 1,
                LONG_HUMAN_READABLE = 2,
                PRICE_CHANGE = 3,
                PERCENT = 4,
                DATE = 5,
                DATETIME = 6
            }
        }

        /** Properties of a UIHints. */
        interface IUIHints {

            /** UIHints noRowsWarning */
            noRowsWarning?: (string|null);
        }

        /**
         * * Hints for a ui implementing the scanner.
         * * THIS IS NOT SPECIFIC TO A UI, BUT SHOULD BE GENERIC FOR ALL
         */
        class UIHints implements IUIHints {

            /**
             * Constructs a new UIHints.
             * @param [properties] Properties to set
             */
            constructor(properties?: ScannerProtos.DataField.IUIHints);

            /** UIHints noRowsWarning. */
            public noRowsWarning: string;

            /**
             * Creates a new UIHints instance using the specified properties.
             * @param [properties] Properties to set
             * @returns UIHints instance
             */
            public static create(properties?: ScannerProtos.DataField.IUIHints): ScannerProtos.DataField.UIHints;

            /**
             * Encodes the specified UIHints message. Does not implicitly {@link ScannerProtos.DataField.UIHints.verify|verify} messages.
             * @param message UIHints message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: ScannerProtos.DataField.IUIHints, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified UIHints message, length delimited. Does not implicitly {@link ScannerProtos.DataField.UIHints.verify|verify} messages.
             * @param message UIHints message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: ScannerProtos.DataField.IUIHints, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a UIHints message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns UIHints
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.DataField.UIHints;

            /**
             * Decodes a UIHints message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns UIHints
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.DataField.UIHints;

            /**
             * Verifies a UIHints message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a UIHints message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns UIHints
             */
            public static fromObject(object: { [k: string]: any }): ScannerProtos.DataField.UIHints;

            /**
             * Creates a plain object from a UIHints message. Also converts values to other types if specified.
             * @param message UIHints
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: ScannerProtos.DataField.UIHints, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this UIHints to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        /** Properties of an Option. */
        interface IOption {

            /** Option label */
            label?: (string|null);

            /** Option value */
            value?: (string|null);
        }

        /** Represents an Option. */
        class Option implements IOption {

            /**
             * Constructs a new Option.
             * @param [properties] Properties to set
             */
            constructor(properties?: ScannerProtos.DataField.IOption);

            /** Option label. */
            public label: string;

            /** Option value. */
            public value: string;

            /**
             * Creates a new Option instance using the specified properties.
             * @param [properties] Properties to set
             * @returns Option instance
             */
            public static create(properties?: ScannerProtos.DataField.IOption): ScannerProtos.DataField.Option;

            /**
             * Encodes the specified Option message. Does not implicitly {@link ScannerProtos.DataField.Option.verify|verify} messages.
             * @param message Option message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: ScannerProtos.DataField.IOption, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified Option message, length delimited. Does not implicitly {@link ScannerProtos.DataField.Option.verify|verify} messages.
             * @param message Option message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: ScannerProtos.DataField.IOption, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an Option message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns Option
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.DataField.Option;

            /**
             * Decodes an Option message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns Option
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.DataField.Option;

            /**
             * Verifies an Option message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an Option message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns Option
             */
            public static fromObject(object: { [k: string]: any }): ScannerProtos.DataField.Option;

            /**
             * Creates a plain object from an Option message. Also converts values to other types if specified.
             * @param message Option
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: ScannerProtos.DataField.Option, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this Option to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        /** UpdateFrequency enum. */
        enum UpdateFrequency {
            UNKNOWN = 0,
            INTRADAY = 1,
            EOD = 2
        }
    }

    /** Properties of a Datafields. */
    interface IDatafields {

        /** Datafields dataField */
        dataField?: (ScannerProtos.IDataField[]|null);

        /** Datafields filterOptions */
        filterOptions?: (ScannerProtos.IFilterOption[]|null);
    }

    /** Used to provide meta information about datafields to UI's. */
    class Datafields implements IDatafields {

        /**
         * Constructs a new Datafields.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IDatafields);

        /** Datafields dataField. */
        public dataField: ScannerProtos.IDataField[];

        /** Datafields filterOptions. */
        public filterOptions: ScannerProtos.IFilterOption[];

        /**
         * Creates a new Datafields instance using the specified properties.
         * @param [properties] Properties to set
         * @returns Datafields instance
         */
        public static create(properties?: ScannerProtos.IDatafields): ScannerProtos.Datafields;

        /**
         * Encodes the specified Datafields message. Does not implicitly {@link ScannerProtos.Datafields.verify|verify} messages.
         * @param message Datafields message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IDatafields, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified Datafields message, length delimited. Does not implicitly {@link ScannerProtos.Datafields.verify|verify} messages.
         * @param message Datafields message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IDatafields, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a Datafields message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Datafields
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.Datafields;

        /**
         * Decodes a Datafields message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns Datafields
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.Datafields;

        /**
         * Verifies a Datafields message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a Datafields message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Datafields
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.Datafields;

        /**
         * Creates a plain object from a Datafields message. Also converts values to other types if specified.
         * @param message Datafields
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.Datafields, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Datafields to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a FilterOption. */
    interface IFilterOption {

        /** FilterOption name */
        name?: (string|null);

        /** FilterOption options */
        options?: (ScannerProtos.IOption[]|null);
    }

    /** Represents a FilterOption. */
    class FilterOption implements IFilterOption {

        /**
         * Constructs a new FilterOption.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IFilterOption);

        /** FilterOption name. */
        public name: string;

        /** FilterOption options. */
        public options: ScannerProtos.IOption[];

        /**
         * Creates a new FilterOption instance using the specified properties.
         * @param [properties] Properties to set
         * @returns FilterOption instance
         */
        public static create(properties?: ScannerProtos.IFilterOption): ScannerProtos.FilterOption;

        /**
         * Encodes the specified FilterOption message. Does not implicitly {@link ScannerProtos.FilterOption.verify|verify} messages.
         * @param message FilterOption message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IFilterOption, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified FilterOption message, length delimited. Does not implicitly {@link ScannerProtos.FilterOption.verify|verify} messages.
         * @param message FilterOption message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IFilterOption, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a FilterOption message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns FilterOption
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.FilterOption;

        /**
         * Decodes a FilterOption message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns FilterOption
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.FilterOption;

        /**
         * Verifies a FilterOption message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a FilterOption message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns FilterOption
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.FilterOption;

        /**
         * Creates a plain object from a FilterOption message. Also converts values to other types if specified.
         * @param message FilterOption
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.FilterOption, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this FilterOption to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of an Option. */
    interface IOption {

        /** Option label */
        label?: (string|null);

        /** Option params */
        params?: (string[]|null);
    }

    /** Represents an Option. */
    class Option implements IOption {

        /**
         * Constructs a new Option.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IOption);

        /** Option label. */
        public label: string;

        /** Option params. */
        public params: string[];

        /**
         * Creates a new Option instance using the specified properties.
         * @param [properties] Properties to set
         * @returns Option instance
         */
        public static create(properties?: ScannerProtos.IOption): ScannerProtos.Option;

        /**
         * Encodes the specified Option message. Does not implicitly {@link ScannerProtos.Option.verify|verify} messages.
         * @param message Option message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IOption, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified Option message, length delimited. Does not implicitly {@link ScannerProtos.Option.verify|verify} messages.
         * @param message Option message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IOption, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an Option message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Option
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.Option;

        /**
         * Decodes an Option message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns Option
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.Option;

        /**
         * Verifies an Option message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an Option message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Option
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.Option;

        /**
         * Creates a plain object from an Option message. Also converts values to other types if specified.
         * @param message Option
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.Option, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Option to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a Snapshot. */
    interface ISnapshot {

        /** Snapshot instruments */
        instruments?: (QuoteProtos.IQuote[]|null);
    }

    /** Represents a Snapshot. */
    class Snapshot implements ISnapshot {

        /**
         * Constructs a new Snapshot.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.ISnapshot);

        /** Snapshot instruments. */
        public instruments: QuoteProtos.IQuote[];

        /**
         * Creates a new Snapshot instance using the specified properties.
         * @param [properties] Properties to set
         * @returns Snapshot instance
         */
        public static create(properties?: ScannerProtos.ISnapshot): ScannerProtos.Snapshot;

        /**
         * Encodes the specified Snapshot message. Does not implicitly {@link ScannerProtos.Snapshot.verify|verify} messages.
         * @param message Snapshot message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.ISnapshot, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified Snapshot message, length delimited. Does not implicitly {@link ScannerProtos.Snapshot.verify|verify} messages.
         * @param message Snapshot message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.ISnapshot, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a Snapshot message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Snapshot
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.Snapshot;

        /**
         * Decodes a Snapshot message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns Snapshot
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.Snapshot;

        /**
         * Verifies a Snapshot message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a Snapshot message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Snapshot
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.Snapshot;

        /**
         * Creates a plain object from a Snapshot message. Also converts values to other types if specified.
         * @param message Snapshot
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.Snapshot, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Snapshot to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a Response. */
    interface IResponse {

        /** Response id */
        id?: (number|null);

        /** Response error */
        error?: (CommonProtos.IErr|null);

        /** Response snapshot */
        snapshot?: (ScannerProtos.ISnapshot|null);

        /** Response heartbeat */
        heartbeat?: (number|Long|null);

        /** Response rowUpdates */
        rowUpdates?: (ScannerProtos.IRowUpdates|null);

        /** Response signal */
        signal?: (QuoteProtos.IQuote|null);

        /** Response rowUpdatesV2 */
        rowUpdatesV2?: (ScannerProtos.IRowUpdatesV2|null);
    }

    /** Part of the messages. This is the only message ever sent by the websocket server. */
    class Response implements IResponse {

        /**
         * Constructs a new Response.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IResponse);

        /** Response id. */
        public id: number;

        /** Response error. */
        public error?: (CommonProtos.IErr|null);

        /** Response snapshot. */
        public snapshot?: (ScannerProtos.ISnapshot|null);

        /** Response heartbeat. */
        public heartbeat: (number|Long);

        /** Response rowUpdates. */
        public rowUpdates?: (ScannerProtos.IRowUpdates|null);

        /** Response signal. */
        public signal?: (QuoteProtos.IQuote|null);

        /** Response rowUpdatesV2. */
        public rowUpdatesV2?: (ScannerProtos.IRowUpdatesV2|null);

        /** Response type. */
        public type?: ("snapshot"|"heartbeat"|"rowUpdates"|"signal"|"rowUpdatesV2");

        /**
         * Creates a new Response instance using the specified properties.
         * @param [properties] Properties to set
         * @returns Response instance
         */
        public static create(properties?: ScannerProtos.IResponse): ScannerProtos.Response;

        /**
         * Encodes the specified Response message. Does not implicitly {@link ScannerProtos.Response.verify|verify} messages.
         * @param message Response message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified Response message, length delimited. Does not implicitly {@link ScannerProtos.Response.verify|verify} messages.
         * @param message Response message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a Response message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Response
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.Response;

        /**
         * Decodes a Response message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns Response
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.Response;

        /**
         * Verifies a Response message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a Response message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Response
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.Response;

        /**
         * Creates a plain object from a Response message. Also converts values to other types if specified.
         * @param message Response
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.Response, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Response to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a RowUpdates. */
    interface IRowUpdates {

        /** RowUpdates updates */
        updates?: (ScannerProtos.IRowUpdate[]|null);

        /** RowUpdates sequence */
        sequence?: (number|Long|null);

        /** RowUpdates total */
        total?: (number|null);
    }

    /** Represents a RowUpdates. */
    class RowUpdates implements IRowUpdates {

        /**
         * Constructs a new RowUpdates.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IRowUpdates);

        /** RowUpdates updates. */
        public updates: ScannerProtos.IRowUpdate[];

        /** RowUpdates sequence. */
        public sequence: (number|Long);

        /** RowUpdates total. */
        public total: number;

        /**
         * Creates a new RowUpdates instance using the specified properties.
         * @param [properties] Properties to set
         * @returns RowUpdates instance
         */
        public static create(properties?: ScannerProtos.IRowUpdates): ScannerProtos.RowUpdates;

        /**
         * Encodes the specified RowUpdates message. Does not implicitly {@link ScannerProtos.RowUpdates.verify|verify} messages.
         * @param message RowUpdates message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IRowUpdates, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified RowUpdates message, length delimited. Does not implicitly {@link ScannerProtos.RowUpdates.verify|verify} messages.
         * @param message RowUpdates message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IRowUpdates, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a RowUpdates message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns RowUpdates
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.RowUpdates;

        /**
         * Decodes a RowUpdates message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns RowUpdates
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.RowUpdates;

        /**
         * Verifies a RowUpdates message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a RowUpdates message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns RowUpdates
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.RowUpdates;

        /**
         * Creates a plain object from a RowUpdates message. Also converts values to other types if specified.
         * @param message RowUpdates
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.RowUpdates, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this RowUpdates to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a RowUpdate. */
    interface IRowUpdate {

        /** RowUpdate type */
        type?: (ScannerProtos.RowUpdate.Type|null);

        /** RowUpdate index */
        index?: (number|null);

        /** RowUpdate rowId */
        rowId?: (string|null);

        /** RowUpdate updated */
        updated?: (QuoteProtos.IQuote|null);

        /** RowUpdate removed */
        removed?: (QuoteProtos.IQuote|null);
    }

    /** Represents a RowUpdate. */
    class RowUpdate implements IRowUpdate {

        /**
         * Constructs a new RowUpdate.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IRowUpdate);

        /** RowUpdate type. */
        public type: ScannerProtos.RowUpdate.Type;

        /** RowUpdate index. */
        public index: number;

        /** RowUpdate rowId. */
        public rowId: string;

        /** RowUpdate updated. */
        public updated?: (QuoteProtos.IQuote|null);

        /** RowUpdate removed. */
        public removed?: (QuoteProtos.IQuote|null);

        /**
         * Creates a new RowUpdate instance using the specified properties.
         * @param [properties] Properties to set
         * @returns RowUpdate instance
         */
        public static create(properties?: ScannerProtos.IRowUpdate): ScannerProtos.RowUpdate;

        /**
         * Encodes the specified RowUpdate message. Does not implicitly {@link ScannerProtos.RowUpdate.verify|verify} messages.
         * @param message RowUpdate message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IRowUpdate, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified RowUpdate message, length delimited. Does not implicitly {@link ScannerProtos.RowUpdate.verify|verify} messages.
         * @param message RowUpdate message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IRowUpdate, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a RowUpdate message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns RowUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.RowUpdate;

        /**
         * Decodes a RowUpdate message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns RowUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.RowUpdate;

        /**
         * Verifies a RowUpdate message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a RowUpdate message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns RowUpdate
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.RowUpdate;

        /**
         * Creates a plain object from a RowUpdate message. Also converts values to other types if specified.
         * @param message RowUpdate
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.RowUpdate, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this RowUpdate to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    namespace RowUpdate {

        /** Type enum. */
        enum Type {
            ADD = 0,
            UPDATE = 1,
            REMOVE = 2,
            FLUSH = 3
        }
    }

    /** Properties of a RowUpdatesV2. */
    interface IRowUpdatesV2 {

        /** RowUpdatesV2 sequence */
        sequence?: (number|Long|null);

        /** RowUpdatesV2 total */
        total?: (number|null);

        /** RowUpdatesV2 updates */
        updates?: (ScannerProtos.IUpdateRowUpdate[]|null);

        /** RowUpdatesV2 adds */
        adds?: (ScannerProtos.IAddRowUpdate[]|null);

        /** RowUpdatesV2 removes */
        removes?: (ScannerProtos.IRemoveRowUpdate[]|null);

        /** RowUpdatesV2 moves */
        moves?: (ScannerProtos.IMoveRowUpdate[]|null);
    }

    /** Represents a RowUpdatesV2. */
    class RowUpdatesV2 implements IRowUpdatesV2 {

        /**
         * Constructs a new RowUpdatesV2.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IRowUpdatesV2);

        /** RowUpdatesV2 sequence. */
        public sequence: (number|Long);

        /** RowUpdatesV2 total. */
        public total: number;

        /** RowUpdatesV2 updates. */
        public updates: ScannerProtos.IUpdateRowUpdate[];

        /** RowUpdatesV2 adds. */
        public adds: ScannerProtos.IAddRowUpdate[];

        /** RowUpdatesV2 removes. */
        public removes: ScannerProtos.IRemoveRowUpdate[];

        /** RowUpdatesV2 moves. */
        public moves: ScannerProtos.IMoveRowUpdate[];

        /**
         * Creates a new RowUpdatesV2 instance using the specified properties.
         * @param [properties] Properties to set
         * @returns RowUpdatesV2 instance
         */
        public static create(properties?: ScannerProtos.IRowUpdatesV2): ScannerProtos.RowUpdatesV2;

        /**
         * Encodes the specified RowUpdatesV2 message. Does not implicitly {@link ScannerProtos.RowUpdatesV2.verify|verify} messages.
         * @param message RowUpdatesV2 message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IRowUpdatesV2, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified RowUpdatesV2 message, length delimited. Does not implicitly {@link ScannerProtos.RowUpdatesV2.verify|verify} messages.
         * @param message RowUpdatesV2 message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IRowUpdatesV2, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a RowUpdatesV2 message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns RowUpdatesV2
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.RowUpdatesV2;

        /**
         * Decodes a RowUpdatesV2 message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns RowUpdatesV2
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.RowUpdatesV2;

        /**
         * Verifies a RowUpdatesV2 message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a RowUpdatesV2 message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns RowUpdatesV2
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.RowUpdatesV2;

        /**
         * Creates a plain object from a RowUpdatesV2 message. Also converts values to other types if specified.
         * @param message RowUpdatesV2
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.RowUpdatesV2, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this RowUpdatesV2 to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of an UpdateRowUpdate. */
    interface IUpdateRowUpdate {

        /** UpdateRowUpdate index */
        index?: (number|null);

        /** UpdateRowUpdate rowId */
        rowId?: (string|null);

        /** UpdateRowUpdate updated */
        updated?: (QuoteProtos.IQuote|null);

        /** UpdateRowUpdate removed */
        removed?: (QuoteProtos.IQuote|null);
    }

    /** Represents an UpdateRowUpdate. */
    class UpdateRowUpdate implements IUpdateRowUpdate {

        /**
         * Constructs a new UpdateRowUpdate.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IUpdateRowUpdate);

        /** UpdateRowUpdate index. */
        public index: number;

        /** UpdateRowUpdate rowId. */
        public rowId: string;

        /** UpdateRowUpdate updated. */
        public updated?: (QuoteProtos.IQuote|null);

        /** UpdateRowUpdate removed. */
        public removed?: (QuoteProtos.IQuote|null);

        /**
         * Creates a new UpdateRowUpdate instance using the specified properties.
         * @param [properties] Properties to set
         * @returns UpdateRowUpdate instance
         */
        public static create(properties?: ScannerProtos.IUpdateRowUpdate): ScannerProtos.UpdateRowUpdate;

        /**
         * Encodes the specified UpdateRowUpdate message. Does not implicitly {@link ScannerProtos.UpdateRowUpdate.verify|verify} messages.
         * @param message UpdateRowUpdate message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IUpdateRowUpdate, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified UpdateRowUpdate message, length delimited. Does not implicitly {@link ScannerProtos.UpdateRowUpdate.verify|verify} messages.
         * @param message UpdateRowUpdate message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IUpdateRowUpdate, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an UpdateRowUpdate message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns UpdateRowUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.UpdateRowUpdate;

        /**
         * Decodes an UpdateRowUpdate message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns UpdateRowUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.UpdateRowUpdate;

        /**
         * Verifies an UpdateRowUpdate message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an UpdateRowUpdate message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns UpdateRowUpdate
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.UpdateRowUpdate;

        /**
         * Creates a plain object from an UpdateRowUpdate message. Also converts values to other types if specified.
         * @param message UpdateRowUpdate
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.UpdateRowUpdate, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this UpdateRowUpdate to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of an AddRowUpdate. */
    interface IAddRowUpdate {

        /** AddRowUpdate index */
        index?: (number|null);

        /** AddRowUpdate rowId */
        rowId?: (string|null);

        /** AddRowUpdate updated */
        updated?: (QuoteProtos.IQuote|null);
    }

    /** Represents an AddRowUpdate. */
    class AddRowUpdate implements IAddRowUpdate {

        /**
         * Constructs a new AddRowUpdate.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IAddRowUpdate);

        /** AddRowUpdate index. */
        public index: number;

        /** AddRowUpdate rowId. */
        public rowId: string;

        /** AddRowUpdate updated. */
        public updated?: (QuoteProtos.IQuote|null);

        /**
         * Creates a new AddRowUpdate instance using the specified properties.
         * @param [properties] Properties to set
         * @returns AddRowUpdate instance
         */
        public static create(properties?: ScannerProtos.IAddRowUpdate): ScannerProtos.AddRowUpdate;

        /**
         * Encodes the specified AddRowUpdate message. Does not implicitly {@link ScannerProtos.AddRowUpdate.verify|verify} messages.
         * @param message AddRowUpdate message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IAddRowUpdate, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified AddRowUpdate message, length delimited. Does not implicitly {@link ScannerProtos.AddRowUpdate.verify|verify} messages.
         * @param message AddRowUpdate message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IAddRowUpdate, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an AddRowUpdate message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns AddRowUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.AddRowUpdate;

        /**
         * Decodes an AddRowUpdate message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns AddRowUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.AddRowUpdate;

        /**
         * Verifies an AddRowUpdate message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an AddRowUpdate message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns AddRowUpdate
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.AddRowUpdate;

        /**
         * Creates a plain object from an AddRowUpdate message. Also converts values to other types if specified.
         * @param message AddRowUpdate
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.AddRowUpdate, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this AddRowUpdate to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a RemoveRowUpdate. */
    interface IRemoveRowUpdate {

        /** RemoveRowUpdate previousIndex */
        previousIndex?: (number|null);

        /** RemoveRowUpdate rowId */
        rowId?: (string|null);
    }

    /** Represents a RemoveRowUpdate. */
    class RemoveRowUpdate implements IRemoveRowUpdate {

        /**
         * Constructs a new RemoveRowUpdate.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IRemoveRowUpdate);

        /** RemoveRowUpdate previousIndex. */
        public previousIndex: number;

        /** RemoveRowUpdate rowId. */
        public rowId: string;

        /**
         * Creates a new RemoveRowUpdate instance using the specified properties.
         * @param [properties] Properties to set
         * @returns RemoveRowUpdate instance
         */
        public static create(properties?: ScannerProtos.IRemoveRowUpdate): ScannerProtos.RemoveRowUpdate;

        /**
         * Encodes the specified RemoveRowUpdate message. Does not implicitly {@link ScannerProtos.RemoveRowUpdate.verify|verify} messages.
         * @param message RemoveRowUpdate message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IRemoveRowUpdate, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified RemoveRowUpdate message, length delimited. Does not implicitly {@link ScannerProtos.RemoveRowUpdate.verify|verify} messages.
         * @param message RemoveRowUpdate message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IRemoveRowUpdate, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a RemoveRowUpdate message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns RemoveRowUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.RemoveRowUpdate;

        /**
         * Decodes a RemoveRowUpdate message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns RemoveRowUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.RemoveRowUpdate;

        /**
         * Verifies a RemoveRowUpdate message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a RemoveRowUpdate message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns RemoveRowUpdate
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.RemoveRowUpdate;

        /**
         * Creates a plain object from a RemoveRowUpdate message. Also converts values to other types if specified.
         * @param message RemoveRowUpdate
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.RemoveRowUpdate, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this RemoveRowUpdate to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a MoveRowUpdate. */
    interface IMoveRowUpdate {

        /** MoveRowUpdate index */
        index?: (number|null);

        /** MoveRowUpdate rowId */
        rowId?: (string|null);

        /** MoveRowUpdate previousIndex */
        previousIndex?: (number|null);

        /** MoveRowUpdate updated */
        updated?: (QuoteProtos.IQuote|null);

        /** MoveRowUpdate removed */
        removed?: (QuoteProtos.IQuote|null);
    }

    /** Represents a MoveRowUpdate. */
    class MoveRowUpdate implements IMoveRowUpdate {

        /**
         * Constructs a new MoveRowUpdate.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IMoveRowUpdate);

        /** MoveRowUpdate index. */
        public index: number;

        /** MoveRowUpdate rowId. */
        public rowId: string;

        /** MoveRowUpdate previousIndex. */
        public previousIndex: number;

        /** MoveRowUpdate updated. */
        public updated?: (QuoteProtos.IQuote|null);

        /** MoveRowUpdate removed. */
        public removed?: (QuoteProtos.IQuote|null);

        /**
         * Creates a new MoveRowUpdate instance using the specified properties.
         * @param [properties] Properties to set
         * @returns MoveRowUpdate instance
         */
        public static create(properties?: ScannerProtos.IMoveRowUpdate): ScannerProtos.MoveRowUpdate;

        /**
         * Encodes the specified MoveRowUpdate message. Does not implicitly {@link ScannerProtos.MoveRowUpdate.verify|verify} messages.
         * @param message MoveRowUpdate message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IMoveRowUpdate, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified MoveRowUpdate message, length delimited. Does not implicitly {@link ScannerProtos.MoveRowUpdate.verify|verify} messages.
         * @param message MoveRowUpdate message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IMoveRowUpdate, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a MoveRowUpdate message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns MoveRowUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.MoveRowUpdate;

        /**
         * Decodes a MoveRowUpdate message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns MoveRowUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.MoveRowUpdate;

        /**
         * Verifies a MoveRowUpdate message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a MoveRowUpdate message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns MoveRowUpdate
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.MoveRowUpdate;

        /**
         * Creates a plain object from a MoveRowUpdate message. Also converts values to other types if specified.
         * @param message MoveRowUpdate
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.MoveRowUpdate, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this MoveRowUpdate to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a Column. */
    interface IColumn {

        /** Column dataFieldName */
        dataFieldName?: (string|null);
    }

    /** Represents a Column. */
    class Column implements IColumn {

        /**
         * Constructs a new Column.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IColumn);

        /** Column dataFieldName. */
        public dataFieldName: string;

        /**
         * Creates a new Column instance using the specified properties.
         * @param [properties] Properties to set
         * @returns Column instance
         */
        public static create(properties?: ScannerProtos.IColumn): ScannerProtos.Column;

        /**
         * Encodes the specified Column message. Does not implicitly {@link ScannerProtos.Column.verify|verify} messages.
         * @param message Column message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IColumn, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified Column message, length delimited. Does not implicitly {@link ScannerProtos.Column.verify|verify} messages.
         * @param message Column message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IColumn, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a Column message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Column
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.Column;

        /**
         * Decodes a Column message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns Column
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.Column;

        /**
         * Verifies a Column message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a Column message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Column
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.Column;

        /**
         * Creates a plain object from a Column message. Also converts values to other types if specified.
         * @param message Column
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.Column, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Column to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a WebSocketRequest. */
    interface IWebSocketRequest {

        /** WebSocketRequest id */
        id?: (number|null);

        /** WebSocketRequest scannerRequest */
        scannerRequest?: (ScannerProtos.IScannerRequest|null);
    }

    /** Represents a WebSocketRequest. */
    class WebSocketRequest implements IWebSocketRequest {

        /**
         * Constructs a new WebSocketRequest.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IWebSocketRequest);

        /** WebSocketRequest id. */
        public id: number;

        /** WebSocketRequest scannerRequest. */
        public scannerRequest?: (ScannerProtos.IScannerRequest|null);

        /** WebSocketRequest type. */
        public type?: "scannerRequest";

        /**
         * Creates a new WebSocketRequest instance using the specified properties.
         * @param [properties] Properties to set
         * @returns WebSocketRequest instance
         */
        public static create(properties?: ScannerProtos.IWebSocketRequest): ScannerProtos.WebSocketRequest;

        /**
         * Encodes the specified WebSocketRequest message. Does not implicitly {@link ScannerProtos.WebSocketRequest.verify|verify} messages.
         * @param message WebSocketRequest message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IWebSocketRequest, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified WebSocketRequest message, length delimited. Does not implicitly {@link ScannerProtos.WebSocketRequest.verify|verify} messages.
         * @param message WebSocketRequest message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IWebSocketRequest, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a WebSocketRequest message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns WebSocketRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.WebSocketRequest;

        /**
         * Decodes a WebSocketRequest message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns WebSocketRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.WebSocketRequest;

        /**
         * Verifies a WebSocketRequest message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a WebSocketRequest message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns WebSocketRequest
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.WebSocketRequest;

        /**
         * Creates a plain object from a WebSocketRequest message. Also converts values to other types if specified.
         * @param message WebSocketRequest
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.WebSocketRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this WebSocketRequest to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a ScannerConfig. */
    interface IScannerConfig {

        /** ScannerConfig name */
        name?: (string|null);

        /** ScannerConfig updateType */
        updateType?: (ScannerProtos.ScannerConfig.UpdateType|null);

        /** ScannerConfig query */
        query?: (ScannerProtos.IQuery|null);

        /** List of signal types, if this is set */
        signalTypes?: (ScannerProtos.SignalType[]|null);
    }

    /** Represents a ScannerConfig. */
    class ScannerConfig implements IScannerConfig {

        /**
         * Constructs a new ScannerConfig.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IScannerConfig);

        /** ScannerConfig name. */
        public name: string;

        /** ScannerConfig updateType. */
        public updateType: ScannerProtos.ScannerConfig.UpdateType;

        /** ScannerConfig query. */
        public query?: (ScannerProtos.IQuery|null);

        /** * List of signal types, if this is set */
        public signalTypes: ScannerProtos.SignalType[];

        /**
         * Creates a new ScannerConfig instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ScannerConfig instance
         */
        public static create(properties?: ScannerProtos.IScannerConfig): ScannerProtos.ScannerConfig;

        /**
         * Encodes the specified ScannerConfig message. Does not implicitly {@link ScannerProtos.ScannerConfig.verify|verify} messages.
         * @param message ScannerConfig message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IScannerConfig, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ScannerConfig message, length delimited. Does not implicitly {@link ScannerProtos.ScannerConfig.verify|verify} messages.
         * @param message ScannerConfig message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IScannerConfig, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ScannerConfig message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ScannerConfig
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.ScannerConfig;

        /**
         * Decodes a ScannerConfig message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ScannerConfig
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.ScannerConfig;

        /**
         * Verifies a ScannerConfig message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ScannerConfig message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ScannerConfig
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.ScannerConfig;

        /**
         * Creates a plain object from a ScannerConfig message. Also converts values to other types if specified.
         * @param message ScannerConfig
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.ScannerConfig, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ScannerConfig to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    namespace ScannerConfig {

        /** UpdateType enum. */
        enum UpdateType {
            STREAM = 0,
            SNAPSHOTS = 1,
            SNAPSHOT_DELTAS = 2
        }
    }

    /** Properties of a ScannerRequest. */
    interface IScannerRequest {

        /** ScannerRequest scanner */
        scanner?: (ScannerProtos.IScannerConfig|null);
    }

    /** Represents a ScannerRequest. */
    class ScannerRequest implements IScannerRequest {

        /**
         * Constructs a new ScannerRequest.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IScannerRequest);

        /** ScannerRequest scanner. */
        public scanner?: (ScannerProtos.IScannerConfig|null);

        /**
         * Creates a new ScannerRequest instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ScannerRequest instance
         */
        public static create(properties?: ScannerProtos.IScannerRequest): ScannerProtos.ScannerRequest;

        /**
         * Encodes the specified ScannerRequest message. Does not implicitly {@link ScannerProtos.ScannerRequest.verify|verify} messages.
         * @param message ScannerRequest message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IScannerRequest, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ScannerRequest message, length delimited. Does not implicitly {@link ScannerProtos.ScannerRequest.verify|verify} messages.
         * @param message ScannerRequest message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IScannerRequest, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ScannerRequest message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ScannerRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.ScannerRequest;

        /**
         * Decodes a ScannerRequest message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ScannerRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.ScannerRequest;

        /**
         * Verifies a ScannerRequest message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ScannerRequest message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ScannerRequest
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.ScannerRequest;

        /**
         * Creates a plain object from a ScannerRequest message. Also converts values to other types if specified.
         * @param message ScannerRequest
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.ScannerRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ScannerRequest to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** SignalType enum. */
    enum SignalType {
        UNKNOWN_SIGNAL_TYPE = 0,
        NEW_HIGH = 1,
        NEW_LOW = 2,
        NEW_PREMARKET_HIGH = 3,
        NEW_PREMARKET_LOW = 4,
        NEW_AFTERHOURS_HIGH = 5,
        NEW_AFTERHOURS_LOW = 6
    }

    /** Properties of a Signal. */
    interface ISignal {

        /** Signal symbol */
        symbol?: (string|null);

        /** Signal time */
        time?: (number|Long|null);

        /** Signal instrument */
        instrument?: (QuoteProtos.IQuote|null);

        /** Signal heartbeat */
        heartbeat?: (number|Long|null);

        /** Signal newHigh */
        newHigh?: (ScannerProtos.INewHigh|null);

        /** Signal newLow */
        newLow?: (ScannerProtos.INewLow|null);

        /** Signal newPreMarketHigh */
        newPreMarketHigh?: (ScannerProtos.INewPreMarketHigh|null);

        /** Signal newPreMarketLow */
        newPreMarketLow?: (ScannerProtos.INewPreMarketLow|null);

        /** Signal newAfterHoursHigh */
        newAfterHoursHigh?: (ScannerProtos.INewAfterHoursHigh|null);

        /** Signal newAfterHoursLow */
        newAfterHoursLow?: (ScannerProtos.INewAfterHoursLow|null);
    }

    /** Represents a Signal. */
    class Signal implements ISignal {

        /**
         * Constructs a new Signal.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.ISignal);

        /** Signal symbol. */
        public symbol: string;

        /** Signal time. */
        public time: (number|Long);

        /** Signal instrument. */
        public instrument?: (QuoteProtos.IQuote|null);

        /** Signal heartbeat. */
        public heartbeat: (number|Long);

        /** Signal newHigh. */
        public newHigh?: (ScannerProtos.INewHigh|null);

        /** Signal newLow. */
        public newLow?: (ScannerProtos.INewLow|null);

        /** Signal newPreMarketHigh. */
        public newPreMarketHigh?: (ScannerProtos.INewPreMarketHigh|null);

        /** Signal newPreMarketLow. */
        public newPreMarketLow?: (ScannerProtos.INewPreMarketLow|null);

        /** Signal newAfterHoursHigh. */
        public newAfterHoursHigh?: (ScannerProtos.INewAfterHoursHigh|null);

        /** Signal newAfterHoursLow. */
        public newAfterHoursLow?: (ScannerProtos.INewAfterHoursLow|null);

        /** Signal type. */
        public type?: ("heartbeat"|"newHigh"|"newLow"|"newPreMarketHigh"|"newPreMarketLow"|"newAfterHoursHigh"|"newAfterHoursLow");

        /**
         * Creates a new Signal instance using the specified properties.
         * @param [properties] Properties to set
         * @returns Signal instance
         */
        public static create(properties?: ScannerProtos.ISignal): ScannerProtos.Signal;

        /**
         * Encodes the specified Signal message. Does not implicitly {@link ScannerProtos.Signal.verify|verify} messages.
         * @param message Signal message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.ISignal, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified Signal message, length delimited. Does not implicitly {@link ScannerProtos.Signal.verify|verify} messages.
         * @param message Signal message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.ISignal, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a Signal message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Signal
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.Signal;

        /**
         * Decodes a Signal message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns Signal
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.Signal;

        /**
         * Verifies a Signal message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a Signal message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Signal
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.Signal;

        /**
         * Creates a plain object from a Signal message. Also converts values to other types if specified.
         * @param message Signal
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.Signal, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Signal to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a NewHigh. */
    interface INewHigh {

        /** NewHigh value */
        value?: (number|null);
    }

    /** Represents a NewHigh. */
    class NewHigh implements INewHigh {

        /**
         * Constructs a new NewHigh.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.INewHigh);

        /** NewHigh value. */
        public value: number;

        /**
         * Creates a new NewHigh instance using the specified properties.
         * @param [properties] Properties to set
         * @returns NewHigh instance
         */
        public static create(properties?: ScannerProtos.INewHigh): ScannerProtos.NewHigh;

        /**
         * Encodes the specified NewHigh message. Does not implicitly {@link ScannerProtos.NewHigh.verify|verify} messages.
         * @param message NewHigh message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.INewHigh, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified NewHigh message, length delimited. Does not implicitly {@link ScannerProtos.NewHigh.verify|verify} messages.
         * @param message NewHigh message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.INewHigh, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a NewHigh message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns NewHigh
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.NewHigh;

        /**
         * Decodes a NewHigh message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns NewHigh
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.NewHigh;

        /**
         * Verifies a NewHigh message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a NewHigh message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns NewHigh
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.NewHigh;

        /**
         * Creates a plain object from a NewHigh message. Also converts values to other types if specified.
         * @param message NewHigh
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.NewHigh, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this NewHigh to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a NewLow. */
    interface INewLow {

        /** NewLow value */
        value?: (number|null);
    }

    /** Represents a NewLow. */
    class NewLow implements INewLow {

        /**
         * Constructs a new NewLow.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.INewLow);

        /** NewLow value. */
        public value: number;

        /**
         * Creates a new NewLow instance using the specified properties.
         * @param [properties] Properties to set
         * @returns NewLow instance
         */
        public static create(properties?: ScannerProtos.INewLow): ScannerProtos.NewLow;

        /**
         * Encodes the specified NewLow message. Does not implicitly {@link ScannerProtos.NewLow.verify|verify} messages.
         * @param message NewLow message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.INewLow, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified NewLow message, length delimited. Does not implicitly {@link ScannerProtos.NewLow.verify|verify} messages.
         * @param message NewLow message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.INewLow, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a NewLow message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns NewLow
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.NewLow;

        /**
         * Decodes a NewLow message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns NewLow
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.NewLow;

        /**
         * Verifies a NewLow message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a NewLow message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns NewLow
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.NewLow;

        /**
         * Creates a plain object from a NewLow message. Also converts values to other types if specified.
         * @param message NewLow
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.NewLow, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this NewLow to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a NewPreMarketHigh. */
    interface INewPreMarketHigh {

        /** NewPreMarketHigh value */
        value?: (number|null);
    }

    /** Represents a NewPreMarketHigh. */
    class NewPreMarketHigh implements INewPreMarketHigh {

        /**
         * Constructs a new NewPreMarketHigh.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.INewPreMarketHigh);

        /** NewPreMarketHigh value. */
        public value: number;

        /**
         * Creates a new NewPreMarketHigh instance using the specified properties.
         * @param [properties] Properties to set
         * @returns NewPreMarketHigh instance
         */
        public static create(properties?: ScannerProtos.INewPreMarketHigh): ScannerProtos.NewPreMarketHigh;

        /**
         * Encodes the specified NewPreMarketHigh message. Does not implicitly {@link ScannerProtos.NewPreMarketHigh.verify|verify} messages.
         * @param message NewPreMarketHigh message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.INewPreMarketHigh, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified NewPreMarketHigh message, length delimited. Does not implicitly {@link ScannerProtos.NewPreMarketHigh.verify|verify} messages.
         * @param message NewPreMarketHigh message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.INewPreMarketHigh, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a NewPreMarketHigh message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns NewPreMarketHigh
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.NewPreMarketHigh;

        /**
         * Decodes a NewPreMarketHigh message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns NewPreMarketHigh
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.NewPreMarketHigh;

        /**
         * Verifies a NewPreMarketHigh message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a NewPreMarketHigh message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns NewPreMarketHigh
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.NewPreMarketHigh;

        /**
         * Creates a plain object from a NewPreMarketHigh message. Also converts values to other types if specified.
         * @param message NewPreMarketHigh
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.NewPreMarketHigh, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this NewPreMarketHigh to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a NewPreMarketLow. */
    interface INewPreMarketLow {

        /** NewPreMarketLow value */
        value?: (number|null);
    }

    /** Represents a NewPreMarketLow. */
    class NewPreMarketLow implements INewPreMarketLow {

        /**
         * Constructs a new NewPreMarketLow.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.INewPreMarketLow);

        /** NewPreMarketLow value. */
        public value: number;

        /**
         * Creates a new NewPreMarketLow instance using the specified properties.
         * @param [properties] Properties to set
         * @returns NewPreMarketLow instance
         */
        public static create(properties?: ScannerProtos.INewPreMarketLow): ScannerProtos.NewPreMarketLow;

        /**
         * Encodes the specified NewPreMarketLow message. Does not implicitly {@link ScannerProtos.NewPreMarketLow.verify|verify} messages.
         * @param message NewPreMarketLow message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.INewPreMarketLow, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified NewPreMarketLow message, length delimited. Does not implicitly {@link ScannerProtos.NewPreMarketLow.verify|verify} messages.
         * @param message NewPreMarketLow message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.INewPreMarketLow, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a NewPreMarketLow message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns NewPreMarketLow
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.NewPreMarketLow;

        /**
         * Decodes a NewPreMarketLow message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns NewPreMarketLow
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.NewPreMarketLow;

        /**
         * Verifies a NewPreMarketLow message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a NewPreMarketLow message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns NewPreMarketLow
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.NewPreMarketLow;

        /**
         * Creates a plain object from a NewPreMarketLow message. Also converts values to other types if specified.
         * @param message NewPreMarketLow
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.NewPreMarketLow, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this NewPreMarketLow to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a NewAfterHoursHigh. */
    interface INewAfterHoursHigh {

        /** NewAfterHoursHigh value */
        value?: (number|null);
    }

    /** Represents a NewAfterHoursHigh. */
    class NewAfterHoursHigh implements INewAfterHoursHigh {

        /**
         * Constructs a new NewAfterHoursHigh.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.INewAfterHoursHigh);

        /** NewAfterHoursHigh value. */
        public value: number;

        /**
         * Creates a new NewAfterHoursHigh instance using the specified properties.
         * @param [properties] Properties to set
         * @returns NewAfterHoursHigh instance
         */
        public static create(properties?: ScannerProtos.INewAfterHoursHigh): ScannerProtos.NewAfterHoursHigh;

        /**
         * Encodes the specified NewAfterHoursHigh message. Does not implicitly {@link ScannerProtos.NewAfterHoursHigh.verify|verify} messages.
         * @param message NewAfterHoursHigh message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.INewAfterHoursHigh, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified NewAfterHoursHigh message, length delimited. Does not implicitly {@link ScannerProtos.NewAfterHoursHigh.verify|verify} messages.
         * @param message NewAfterHoursHigh message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.INewAfterHoursHigh, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a NewAfterHoursHigh message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns NewAfterHoursHigh
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.NewAfterHoursHigh;

        /**
         * Decodes a NewAfterHoursHigh message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns NewAfterHoursHigh
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.NewAfterHoursHigh;

        /**
         * Verifies a NewAfterHoursHigh message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a NewAfterHoursHigh message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns NewAfterHoursHigh
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.NewAfterHoursHigh;

        /**
         * Creates a plain object from a NewAfterHoursHigh message. Also converts values to other types if specified.
         * @param message NewAfterHoursHigh
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.NewAfterHoursHigh, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this NewAfterHoursHigh to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a NewAfterHoursLow. */
    interface INewAfterHoursLow {

        /** NewAfterHoursLow value */
        value?: (number|null);
    }

    /** Represents a NewAfterHoursLow. */
    class NewAfterHoursLow implements INewAfterHoursLow {

        /**
         * Constructs a new NewAfterHoursLow.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.INewAfterHoursLow);

        /** NewAfterHoursLow value. */
        public value: number;

        /**
         * Creates a new NewAfterHoursLow instance using the specified properties.
         * @param [properties] Properties to set
         * @returns NewAfterHoursLow instance
         */
        public static create(properties?: ScannerProtos.INewAfterHoursLow): ScannerProtos.NewAfterHoursLow;

        /**
         * Encodes the specified NewAfterHoursLow message. Does not implicitly {@link ScannerProtos.NewAfterHoursLow.verify|verify} messages.
         * @param message NewAfterHoursLow message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.INewAfterHoursLow, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified NewAfterHoursLow message, length delimited. Does not implicitly {@link ScannerProtos.NewAfterHoursLow.verify|verify} messages.
         * @param message NewAfterHoursLow message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.INewAfterHoursLow, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a NewAfterHoursLow message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns NewAfterHoursLow
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.NewAfterHoursLow;

        /**
         * Decodes a NewAfterHoursLow message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns NewAfterHoursLow
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.NewAfterHoursLow;

        /**
         * Verifies a NewAfterHoursLow message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a NewAfterHoursLow message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns NewAfterHoursLow
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.NewAfterHoursLow;

        /**
         * Creates a plain object from a NewAfterHoursLow message. Also converts values to other types if specified.
         * @param message NewAfterHoursLow
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.NewAfterHoursLow, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this NewAfterHoursLow to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a Poller. */
    interface IPoller {

        /** Poller name */
        name?: (string|null);

        /** Poller schedule */
        schedule?: (string|null);
    }

    /** Represents a Poller. */
    class Poller implements IPoller {

        /**
         * Constructs a new Poller.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IPoller);

        /** Poller name. */
        public name: string;

        /** Poller schedule. */
        public schedule: string;

        /**
         * Creates a new Poller instance using the specified properties.
         * @param [properties] Properties to set
         * @returns Poller instance
         */
        public static create(properties?: ScannerProtos.IPoller): ScannerProtos.Poller;

        /**
         * Encodes the specified Poller message. Does not implicitly {@link ScannerProtos.Poller.verify|verify} messages.
         * @param message Poller message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IPoller, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified Poller message, length delimited. Does not implicitly {@link ScannerProtos.Poller.verify|verify} messages.
         * @param message Poller message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IPoller, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a Poller message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Poller
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.Poller;

        /**
         * Decodes a Poller message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns Poller
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.Poller;

        /**
         * Verifies a Poller message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a Poller message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Poller
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.Poller;

        /**
         * Creates a plain object from a Poller message. Also converts values to other types if specified.
         * @param message Poller
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.Poller, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Poller to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a PollersConfig. */
    interface IPollersConfig {

        /** PollersConfig poller */
        poller?: (ScannerProtos.IPoller[]|null);
    }

    /** Represents a PollersConfig. */
    class PollersConfig implements IPollersConfig {

        /**
         * Constructs a new PollersConfig.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IPollersConfig);

        /** PollersConfig poller. */
        public poller: ScannerProtos.IPoller[];

        /**
         * Creates a new PollersConfig instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PollersConfig instance
         */
        public static create(properties?: ScannerProtos.IPollersConfig): ScannerProtos.PollersConfig;

        /**
         * Encodes the specified PollersConfig message. Does not implicitly {@link ScannerProtos.PollersConfig.verify|verify} messages.
         * @param message PollersConfig message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IPollersConfig, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PollersConfig message, length delimited. Does not implicitly {@link ScannerProtos.PollersConfig.verify|verify} messages.
         * @param message PollersConfig message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IPollersConfig, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PollersConfig message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PollersConfig
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.PollersConfig;

        /**
         * Decodes a PollersConfig message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PollersConfig
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.PollersConfig;

        /**
         * Verifies a PollersConfig message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PollersConfig message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PollersConfig
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.PollersConfig;

        /**
         * Creates a plain object from a PollersConfig message. Also converts values to other types if specified.
         * @param message PollersConfig
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.PollersConfig, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PollersConfig to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a DataResponse. */
    interface IDataResponse {

        /** DataResponse quotes */
        quotes?: (QuoteProtos.IQuote[]|null);
    }

    /** Represents a DataResponse. */
    class DataResponse implements IDataResponse {

        /**
         * Constructs a new DataResponse.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IDataResponse);

        /** DataResponse quotes. */
        public quotes: QuoteProtos.IQuote[];

        /**
         * Creates a new DataResponse instance using the specified properties.
         * @param [properties] Properties to set
         * @returns DataResponse instance
         */
        public static create(properties?: ScannerProtos.IDataResponse): ScannerProtos.DataResponse;

        /**
         * Encodes the specified DataResponse message. Does not implicitly {@link ScannerProtos.DataResponse.verify|verify} messages.
         * @param message DataResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IDataResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified DataResponse message, length delimited. Does not implicitly {@link ScannerProtos.DataResponse.verify|verify} messages.
         * @param message DataResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IDataResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a DataResponse message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns DataResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.DataResponse;

        /**
         * Decodes a DataResponse message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns DataResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.DataResponse;

        /**
         * Verifies a DataResponse message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a DataResponse message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns DataResponse
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.DataResponse;

        /**
         * Creates a plain object from a DataResponse message. Also converts values to other types if specified.
         * @param message DataResponse
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.DataResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this DataResponse to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a Wiim. */
    interface IWiim {

        /** Wiim id */
        id?: (string|null);

        /** Wiim title */
        title?: (string|null);

        /** Wiim asOf */
        asOf?: (string|null);

        /** Wiim tickers */
        tickers?: (string[]|null);
    }

    /** Represents a Wiim. */
    class Wiim implements IWiim {

        /**
         * Constructs a new Wiim.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IWiim);

        /** Wiim id. */
        public id: string;

        /** Wiim title. */
        public title: string;

        /** Wiim asOf. */
        public asOf: string;

        /** Wiim tickers. */
        public tickers: string[];

        /**
         * Creates a new Wiim instance using the specified properties.
         * @param [properties] Properties to set
         * @returns Wiim instance
         */
        public static create(properties?: ScannerProtos.IWiim): ScannerProtos.Wiim;

        /**
         * Encodes the specified Wiim message. Does not implicitly {@link ScannerProtos.Wiim.verify|verify} messages.
         * @param message Wiim message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IWiim, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified Wiim message, length delimited. Does not implicitly {@link ScannerProtos.Wiim.verify|verify} messages.
         * @param message Wiim message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IWiim, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a Wiim message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Wiim
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.Wiim;

        /**
         * Decodes a Wiim message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns Wiim
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.Wiim;

        /**
         * Verifies a Wiim message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a Wiim message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Wiim
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.Wiim;

        /**
         * Creates a plain object from a Wiim message. Also converts values to other types if specified.
         * @param message Wiim
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.Wiim, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Wiim to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a WiimStore. */
    interface IWiimStore {

        /** WiimStore data */
        data?: ({ [k: string]: ScannerProtos.IWiim }|null);
    }

    /** Represents a WiimStore. */
    class WiimStore implements IWiimStore {

        /**
         * Constructs a new WiimStore.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IWiimStore);

        /** WiimStore data. */
        public data: { [k: string]: ScannerProtos.IWiim };

        /**
         * Creates a new WiimStore instance using the specified properties.
         * @param [properties] Properties to set
         * @returns WiimStore instance
         */
        public static create(properties?: ScannerProtos.IWiimStore): ScannerProtos.WiimStore;

        /**
         * Encodes the specified WiimStore message. Does not implicitly {@link ScannerProtos.WiimStore.verify|verify} messages.
         * @param message WiimStore message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IWiimStore, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified WiimStore message, length delimited. Does not implicitly {@link ScannerProtos.WiimStore.verify|verify} messages.
         * @param message WiimStore message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IWiimStore, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a WiimStore message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns WiimStore
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.WiimStore;

        /**
         * Decodes a WiimStore message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns WiimStore
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.WiimStore;

        /**
         * Verifies a WiimStore message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a WiimStore message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns WiimStore
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.WiimStore;

        /**
         * Creates a plain object from a WiimStore message. Also converts values to other types if specified.
         * @param message WiimStore
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.WiimStore, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this WiimStore to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a QuoteNotifications. */
    interface IQuoteNotifications {

        /** QuoteNotifications quoteFieldNumbers */
        quoteFieldNumbers?: (number[]|null);

        /** QuoteNotifications asOf */
        asOf?: (string|null);
    }

    /** Represents a QuoteNotifications. */
    class QuoteNotifications implements IQuoteNotifications {

        /**
         * Constructs a new QuoteNotifications.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IQuoteNotifications);

        /** QuoteNotifications quoteFieldNumbers. */
        public quoteFieldNumbers: number[];

        /** QuoteNotifications asOf. */
        public asOf: string;

        /**
         * Creates a new QuoteNotifications instance using the specified properties.
         * @param [properties] Properties to set
         * @returns QuoteNotifications instance
         */
        public static create(properties?: ScannerProtos.IQuoteNotifications): ScannerProtos.QuoteNotifications;

        /**
         * Encodes the specified QuoteNotifications message. Does not implicitly {@link ScannerProtos.QuoteNotifications.verify|verify} messages.
         * @param message QuoteNotifications message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IQuoteNotifications, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified QuoteNotifications message, length delimited. Does not implicitly {@link ScannerProtos.QuoteNotifications.verify|verify} messages.
         * @param message QuoteNotifications message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IQuoteNotifications, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a QuoteNotifications message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns QuoteNotifications
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.QuoteNotifications;

        /**
         * Decodes a QuoteNotifications message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns QuoteNotifications
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.QuoteNotifications;

        /**
         * Verifies a QuoteNotifications message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a QuoteNotifications message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns QuoteNotifications
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.QuoteNotifications;

        /**
         * Creates a plain object from a QuoteNotifications message. Also converts values to other types if specified.
         * @param message QuoteNotifications
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.QuoteNotifications, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this QuoteNotifications to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a RVolAverageData. */
    interface IRVolAverageData {

        /** RVolAverageData data */
        data?: ({ [k: string]: (number|Long) }|null);
    }

    /** Represents a RVolAverageData. */
    class RVolAverageData implements IRVolAverageData {

        /**
         * Constructs a new RVolAverageData.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IRVolAverageData);

        /** RVolAverageData data. */
        public data: { [k: string]: (number|Long) };

        /**
         * Creates a new RVolAverageData instance using the specified properties.
         * @param [properties] Properties to set
         * @returns RVolAverageData instance
         */
        public static create(properties?: ScannerProtos.IRVolAverageData): ScannerProtos.RVolAverageData;

        /**
         * Encodes the specified RVolAverageData message. Does not implicitly {@link ScannerProtos.RVolAverageData.verify|verify} messages.
         * @param message RVolAverageData message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IRVolAverageData, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified RVolAverageData message, length delimited. Does not implicitly {@link ScannerProtos.RVolAverageData.verify|verify} messages.
         * @param message RVolAverageData message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IRVolAverageData, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a RVolAverageData message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns RVolAverageData
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.RVolAverageData;

        /**
         * Decodes a RVolAverageData message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns RVolAverageData
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.RVolAverageData;

        /**
         * Verifies a RVolAverageData message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a RVolAverageData message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns RVolAverageData
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.RVolAverageData;

        /**
         * Creates a plain object from a RVolAverageData message. Also converts values to other types if specified.
         * @param message RVolAverageData
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.RVolAverageData, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this RVolAverageData to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a DataStore. */
    interface IDataStore {

        /** DataStore data */
        data?: ({ [k: string]: QuoteProtos.IQuote }|null);
    }

    /** Represents a DataStore. */
    class DataStore implements IDataStore {

        /**
         * Constructs a new DataStore.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IDataStore);

        /** DataStore data. */
        public data: { [k: string]: QuoteProtos.IQuote };

        /**
         * Creates a new DataStore instance using the specified properties.
         * @param [properties] Properties to set
         * @returns DataStore instance
         */
        public static create(properties?: ScannerProtos.IDataStore): ScannerProtos.DataStore;

        /**
         * Encodes the specified DataStore message. Does not implicitly {@link ScannerProtos.DataStore.verify|verify} messages.
         * @param message DataStore message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IDataStore, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified DataStore message, length delimited. Does not implicitly {@link ScannerProtos.DataStore.verify|verify} messages.
         * @param message DataStore message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IDataStore, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a DataStore message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns DataStore
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.DataStore;

        /**
         * Decodes a DataStore message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns DataStore
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.DataStore;

        /**
         * Verifies a DataStore message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a DataStore message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns DataStore
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.DataStore;

        /**
         * Creates a plain object from a DataStore message. Also converts values to other types if specified.
         * @param message DataStore
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.DataStore, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this DataStore to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a ContentStore. */
    interface IContentStore {

        /** ContentStore data */
        data?: ({ [k: string]: ScannerProtos.IContent }|null);
    }

    /** Represents a ContentStore. */
    class ContentStore implements IContentStore {

        /**
         * Constructs a new ContentStore.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IContentStore);

        /** ContentStore data. */
        public data: { [k: string]: ScannerProtos.IContent };

        /**
         * Creates a new ContentStore instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ContentStore instance
         */
        public static create(properties?: ScannerProtos.IContentStore): ScannerProtos.ContentStore;

        /**
         * Encodes the specified ContentStore message. Does not implicitly {@link ScannerProtos.ContentStore.verify|verify} messages.
         * @param message ContentStore message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IContentStore, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ContentStore message, length delimited. Does not implicitly {@link ScannerProtos.ContentStore.verify|verify} messages.
         * @param message ContentStore message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IContentStore, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ContentStore message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ContentStore
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.ContentStore;

        /**
         * Decodes a ContentStore message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ContentStore
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.ContentStore;

        /**
         * Verifies a ContentStore message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ContentStore message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ContentStore
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.ContentStore;

        /**
         * Creates a plain object from a ContentStore message. Also converts values to other types if specified.
         * @param message ContentStore
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.ContentStore, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ContentStore to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a Content. */
    interface IContent {

        /** Content id */
        id?: (string|null);

        /** Content nodeId */
        nodeId?: (string|null);

        /** Content title */
        title?: (string|null);

        /** Content asOf */
        asOf?: (string|null);

        /** Content tickers */
        tickers?: (string[]|null);
    }

    /** Represents a Content. */
    class Content implements IContent {

        /**
         * Constructs a new Content.
         * @param [properties] Properties to set
         */
        constructor(properties?: ScannerProtos.IContent);

        /** Content id. */
        public id: string;

        /** Content nodeId. */
        public nodeId: string;

        /** Content title. */
        public title: string;

        /** Content asOf. */
        public asOf: string;

        /** Content tickers. */
        public tickers: string[];

        /**
         * Creates a new Content instance using the specified properties.
         * @param [properties] Properties to set
         * @returns Content instance
         */
        public static create(properties?: ScannerProtos.IContent): ScannerProtos.Content;

        /**
         * Encodes the specified Content message. Does not implicitly {@link ScannerProtos.Content.verify|verify} messages.
         * @param message Content message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: ScannerProtos.IContent, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified Content message, length delimited. Does not implicitly {@link ScannerProtos.Content.verify|verify} messages.
         * @param message Content message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: ScannerProtos.IContent, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a Content message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Content
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ScannerProtos.Content;

        /**
         * Decodes a Content message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns Content
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ScannerProtos.Content;

        /**
         * Verifies a Content message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a Content message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Content
         */
        public static fromObject(object: { [k: string]: any }): ScannerProtos.Content;

        /**
         * Creates a plain object from a Content message. Also converts values to other types if specified.
         * @param message Content
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: ScannerProtos.Content, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Content to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }
}

/** Namespace QuoteProtos. */
export namespace QuoteProtos {

    /** SessionType enum. */
    enum SessionType {
        NA = 0,
        PRE_MARKET = 1,
        REGULAR = 2,
        AFTER_MARKET = 3
    }

    /** TickType enum. */
    enum TickType {
        UNKNOWN = 0,
        VALID = 1,
        IN_VALID = 2
    }

    /** QuoteSource enum. */
    enum QuoteSource {
        UNKNOWN_SOURCE = 0,
        QUOTE_STORE = 1,
        QUOTE_CALCULATOR = 2,
        POLLED_DATA = 3,
        ROLLING_FIELDS = 4,
        RSI_FIELDS = 5,
        CONTENT_EVENT_PUBLISHER = 6,
        SWAP = 7
    }

    /** Properties of a QuoteSeedRequest. */
    interface IQuoteSeedRequest {

        /** QuoteSeedRequest reqId */
        reqId?: (string|null);

        /** QuoteSeedRequest symbols */
        symbols?: (string[]|null);

        /** QuoteSeedRequest seedAll */
        seedAll?: (boolean|null);
    }

    /** Represents a QuoteSeedRequest. */
    class QuoteSeedRequest implements IQuoteSeedRequest {

        /**
         * Constructs a new QuoteSeedRequest.
         * @param [properties] Properties to set
         */
        constructor(properties?: QuoteProtos.IQuoteSeedRequest);

        /** QuoteSeedRequest reqId. */
        public reqId: string;

        /** QuoteSeedRequest symbols. */
        public symbols: string[];

        /** QuoteSeedRequest seedAll. */
        public seedAll: boolean;

        /**
         * Creates a new QuoteSeedRequest instance using the specified properties.
         * @param [properties] Properties to set
         * @returns QuoteSeedRequest instance
         */
        public static create(properties?: QuoteProtos.IQuoteSeedRequest): QuoteProtos.QuoteSeedRequest;

        /**
         * Encodes the specified QuoteSeedRequest message. Does not implicitly {@link QuoteProtos.QuoteSeedRequest.verify|verify} messages.
         * @param message QuoteSeedRequest message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: QuoteProtos.IQuoteSeedRequest, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified QuoteSeedRequest message, length delimited. Does not implicitly {@link QuoteProtos.QuoteSeedRequest.verify|verify} messages.
         * @param message QuoteSeedRequest message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: QuoteProtos.IQuoteSeedRequest, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a QuoteSeedRequest message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns QuoteSeedRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): QuoteProtos.QuoteSeedRequest;

        /**
         * Decodes a QuoteSeedRequest message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns QuoteSeedRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): QuoteProtos.QuoteSeedRequest;

        /**
         * Verifies a QuoteSeedRequest message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a QuoteSeedRequest message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns QuoteSeedRequest
         */
        public static fromObject(object: { [k: string]: any }): QuoteProtos.QuoteSeedRequest;

        /**
         * Creates a plain object from a QuoteSeedRequest message. Also converts values to other types if specified.
         * @param message QuoteSeedRequest
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: QuoteProtos.QuoteSeedRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this QuoteSeedRequest to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a QuoteSeedResponse. */
    interface IQuoteSeedResponse {

        /** QuoteSeedResponse reqId */
        reqId?: (string|null);

        /** QuoteSeedResponse quote */
        quote?: (QuoteProtos.IQuote|null);
    }

    /** Represents a QuoteSeedResponse. */
    class QuoteSeedResponse implements IQuoteSeedResponse {

        /**
         * Constructs a new QuoteSeedResponse.
         * @param [properties] Properties to set
         */
        constructor(properties?: QuoteProtos.IQuoteSeedResponse);

        /** QuoteSeedResponse reqId. */
        public reqId: string;

        /** QuoteSeedResponse quote. */
        public quote?: (QuoteProtos.IQuote|null);

        /**
         * Creates a new QuoteSeedResponse instance using the specified properties.
         * @param [properties] Properties to set
         * @returns QuoteSeedResponse instance
         */
        public static create(properties?: QuoteProtos.IQuoteSeedResponse): QuoteProtos.QuoteSeedResponse;

        /**
         * Encodes the specified QuoteSeedResponse message. Does not implicitly {@link QuoteProtos.QuoteSeedResponse.verify|verify} messages.
         * @param message QuoteSeedResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: QuoteProtos.IQuoteSeedResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified QuoteSeedResponse message, length delimited. Does not implicitly {@link QuoteProtos.QuoteSeedResponse.verify|verify} messages.
         * @param message QuoteSeedResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: QuoteProtos.IQuoteSeedResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a QuoteSeedResponse message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns QuoteSeedResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): QuoteProtos.QuoteSeedResponse;

        /**
         * Decodes a QuoteSeedResponse message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns QuoteSeedResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): QuoteProtos.QuoteSeedResponse;

        /**
         * Verifies a QuoteSeedResponse message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a QuoteSeedResponse message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns QuoteSeedResponse
         */
        public static fromObject(object: { [k: string]: any }): QuoteProtos.QuoteSeedResponse;

        /**
         * Creates a plain object from a QuoteSeedResponse message. Also converts values to other types if specified.
         * @param message QuoteSeedResponse
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: QuoteProtos.QuoteSeedResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this QuoteSeedResponse to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a Quote. */
    interface IQuote {

        /** Quote symbol */
        symbol?: (string|null);

        /** Quote opol */
        opol?: (string|null);

        /** Quote previousClose */
        previousClose?: (number|null);

        /** Quote close */
        close?: (number|null);

        /** Quote change */
        change?: (number|null);

        /** Quote changePercent */
        changePercent?: (number|null);

        /** Quote name */
        name?: (string|null);

        /** Quote ipoDate */
        ipoDate?: (string|null);

        /** Quote exchange */
        exchange?: (string|null);

        /** Quote tradeExchange */
        tradeExchange?: (string|null);

        /** Quote preMarketOpen */
        preMarketOpen?: (number|null);

        /** Quote preMarketOpenTime */
        preMarketOpenTime?: (number|Long|null);

        /** Quote preMarketVolume */
        preMarketVolume?: (number|Long|null);

        /** Quote afterMarketVolume */
        afterMarketVolume?: (number|Long|null);

        /** Quote preMarketOpenChange */
        preMarketOpenChange?: (number|null);

        /** Quote preMarketOpenChangePercent */
        preMarketOpenChangePercent?: (number|null);

        /** Quote source */
        source?: (QuoteProtos.QuoteSource|null);

        /** Quote clearFieldNumbers */
        clearFieldNumbers?: (number[]|null);

        /** Quote marketCap */
        marketCap?: (number|Long|null);

        /** Quote rsi */
        rsi?: (number|null);

        /** Quote atr */
        atr?: (number|null);

        /** Quote priceToEarnings */
        priceToEarnings?: (number|null);

        /** Quote averageVolume */
        averageVolume?: (number|Long|null);

        /** Quote sharesOutstanding */
        sharesOutstanding?: (number|Long|null);

        /** Quote sector */
        sector?: (string|null);

        /** Quote sectorCode */
        sectorCode?: (number|null);

        /** Quote industry */
        industry?: (string|null);

        /** Quote industryCode */
        industryCode?: (number|null);

        /** Quote forwardPERatio */
        forwardPERatio?: (number|null);

        /** Quote pricetoEBITDA */
        pricetoEBITDA?: (number|null);

        /** Quote trailingDividendYield */
        trailingDividendYield?: (number|null);

        /** Quote PBRatio */
        PBRatio?: (number|null);

        /** Quote priceToSales */
        priceToSales?: (number|null);

        /** Quote FCFPerShare */
        FCFPerShare?: (number|null);

        /** Quote buyBackYield */
        buyBackYield?: (number|null);

        /** Quote open */
        open?: (number|null);

        /** Quote price */
        price?: (number|null);

        /** Quote high */
        high?: (number|null);

        /** Quote low */
        low?: (number|null);

        /** Quote time */
        time?: (number|Long|null);

        /** Quote size */
        size?: (number|null);

        /** Quote type */
        type?: (string|null);

        /** Quote dayVolume */
        dayVolume?: (number|Long|null);

        /** Quote askPrice */
        askPrice?: (number|null);

        /** Quote bidPrice */
        bidPrice?: (number|null);

        /** Quote shareFloatDate */
        shareFloatDate?: (string|null);

        /** Quote sharesOutstandingDate */
        sharesOutstandingDate?: (string|null);

        /** Quote shareFloat */
        shareFloat?: (number|Long|null);

        /** Quote updateType */
        updateType?: (QuoteProtos.TickType|null);

        /** Quote subtype */
        subtype?: (string|null);

        /** Quote sessionType */
        sessionType?: (QuoteProtos.SessionType|null);

        /** Quote volume */
        volume?: (number|Long|null);

        /** Quote changePercent1Minute */
        changePercent1Minute?: (number|null);

        /** Quote changePercentClose */
        changePercentClose?: (number|null);

        /** Quote changePercent5Minute */
        changePercent5Minute?: (number|null);

        /** Quote volume1Minute */
        volume1Minute?: (number|Long|null);

        /** Quote relativeVolume1Minute */
        relativeVolume1Minute?: (number|null);

        /** Quote change15Minute */
        change15Minute?: (number|null);

        /** Quote changePercent15Minute */
        changePercent15Minute?: (number|null);

        /** Quote change30Minute */
        change30Minute?: (number|null);

        /** Quote changePercent30Minute */
        changePercent30Minute?: (number|null);

        /** Quote change60Minute */
        change60Minute?: (number|null);

        /** Quote changePercent60Minute */
        changePercent60Minute?: (number|null);

        /** Quote changePercentOpen */
        changePercentOpen?: (number|null);

        /** Quote changeOpen */
        changeOpen?: (number|null);

        /** Quote gics */
        gics?: (number|null);

        /** Quote gicsSector */
        gicsSector?: (number|null);

        /** Quote gicsSectorName */
        gicsSectorName?: (string|null);

        /** Quote gicsIndustryGroup */
        gicsIndustryGroup?: (number|null);

        /** Quote gicsIndustryGroupName */
        gicsIndustryGroupName?: (string|null);

        /** Quote gicsIndustry */
        gicsIndustry?: (number|null);

        /** Quote gicsIndustryName */
        gicsIndustryName?: (string|null);

        /** Quote gicsSubIndustry */
        gicsSubIndustry?: (number|null);

        /** Quote gicsSubIndustryName */
        gicsSubIndustryName?: (string|null);

        /** Quote optionable */
        optionable?: (number|null);

        /** Quote cik */
        cik?: (number|null);

        /** Quote signalName */
        signalName?: (string|null);

        /** Quote lastTradeTime */
        lastTradeTime?: (number|Long|null);

        /** Quote country */
        country?: (string|null);

        /** Quote sharesShort */
        sharesShort?: (number|Long|null);

        /** Quote sharesShortPercentOfFloat */
        sharesShortPercentOfFloat?: (number|null);

        /** Quote sharesShortChangePercent */
        sharesShortChangePercent?: (number|null);

        /** Quote sharesShortReceiptDate */
        sharesShortReceiptDate?: (number|Long|null);

        /** Quote sharesShortSettlementDate */
        sharesShortSettlementDate?: (number|Long|null);

        /** Quote shortInterestRatio */
        shortInterestRatio?: (number|null);

        /** Quote sharesShortValue */
        sharesShortValue?: (number|null);

        /** Quote sharesShortPrevious */
        sharesShortPrevious?: (number|Long|null);

        /** Quote sharesShortPreviousValue */
        sharesShortPreviousValue?: (number|null);

        /** Quote sharesShortPreviousPercentOfFloat */
        sharesShortPreviousPercentOfFloat?: (number|null);

        /** Quote sharesShortChange */
        sharesShortChange?: (number|Long|null);

        /** Quote sentTime */
        sentTime?: (number|Long|null);

        /** Quote volume5Minute */
        volume5Minute?: (number|Long|null);

        /** Quote volume15Minute */
        volume15Minute?: (number|Long|null);

        /** Quote volume30Minute */
        volume30Minute?: (number|Long|null);

        /** Quote volume60Minute */
        volume60Minute?: (number|Long|null);

        /** Quote relativeVolume5Minute */
        relativeVolume5Minute?: (number|null);

        /** Quote relativeVolume90Day */
        relativeVolume90Day?: (number|null);

        /** Quote rvol1m10d */
        rvol1m10d?: (number|null);

        /** Quote rvol5m10d */
        rvol5m10d?: (number|null);

        /** Quote rvol5m30d */
        rvol5m30d?: (number|null);

        /** Quote rvol5m90d */
        rvol5m90d?: (number|null);

        /** Quote averageVolume10Day */
        averageVolume10Day?: (number|Long|null);

        /** Quote averageVolumeChangePercent10Day */
        averageVolumeChangePercent10Day?: (number|null);

        /** Quote high10Day */
        high10Day?: (number|null);

        /** Quote highChangePercent10Day */
        highChangePercent10Day?: (number|null);

        /** Quote low10Day */
        low10Day?: (number|null);

        /** Quote lowChangePercent10Day */
        lowChangePercent10Day?: (number|null);

        /** Quote movingAverage10Day */
        movingAverage10Day?: (number|null);

        /** Quote movingAverageChangePercent10Day */
        movingAverageChangePercent10Day?: (number|null);

        /** Quote averageVolume20Day */
        averageVolume20Day?: (number|Long|null);

        /** Quote averageVolumeChangePercent20Day */
        averageVolumeChangePercent20Day?: (number|null);

        /** Quote high20Day */
        high20Day?: (number|null);

        /** Quote highChangePercent20Day */
        highChangePercent20Day?: (number|null);

        /** Quote low20Day */
        low20Day?: (number|null);

        /** Quote lowChangePercent20Day */
        lowChangePercent20Day?: (number|null);

        /** Quote movingAverage20Day */
        movingAverage20Day?: (number|null);

        /** Quote movingAverageChangePercent20Day */
        movingAverageChangePercent20Day?: (number|null);

        /** Quote averageVolume50Day */
        averageVolume50Day?: (number|Long|null);

        /** Quote averageVolumeChangePercent50Day */
        averageVolumeChangePercent50Day?: (number|null);

        /** Quote high50Day */
        high50Day?: (number|null);

        /** Quote highChangePercent50Day */
        highChangePercent50Day?: (number|null);

        /** Quote low50Day */
        low50Day?: (number|null);

        /** Quote lowChangePercent50Day */
        lowChangePercent50Day?: (number|null);

        /** Quote movingAverage50Day */
        movingAverage50Day?: (number|null);

        /** Quote movingAverageChangePercent50Day */
        movingAverageChangePercent50Day?: (number|null);

        /** Quote averageVolume100Day */
        averageVolume100Day?: (number|Long|null);

        /** Quote averageVolumeChangePercent100Day */
        averageVolumeChangePercent100Day?: (number|null);

        /** Quote high100Day */
        high100Day?: (number|null);

        /** Quote highChangePercent100Day */
        highChangePercent100Day?: (number|null);

        /** Quote low100Day */
        low100Day?: (number|null);

        /** Quote lowChangePercent100Day */
        lowChangePercent100Day?: (number|null);

        /** Quote movingAverage100Day */
        movingAverage100Day?: (number|null);

        /** Quote movingAverageChangePercent100Day */
        movingAverageChangePercent100Day?: (number|null);

        /** Quote averageVolume200Day */
        averageVolume200Day?: (number|Long|null);

        /** Quote averageVolumeChangePercent200Day */
        averageVolumeChangePercent200Day?: (number|null);

        /** Quote high200Day */
        high200Day?: (number|null);

        /** Quote highChangePercent200Day */
        highChangePercent200Day?: (number|null);

        /** Quote low200Day */
        low200Day?: (number|null);

        /** Quote lowChangePercent200Day */
        lowChangePercent200Day?: (number|null);

        /** Quote movingAverage200Day */
        movingAverage200Day?: (number|null);

        /** Quote movingAverageChangePercent200Day */
        movingAverageChangePercent200Day?: (number|null);

        /** Quote movingAverage250Day */
        movingAverage250Day?: (number|null);

        /** Quote movingAverageChangePercent250Day */
        movingAverageChangePercent250Day?: (number|null);

        /** Quote averageVolume90Day */
        averageVolume90Day?: (number|Long|null);

        /** Quote nextEarningsDate */
        nextEarningsDate?: (number|null);

        /** Quote dividend */
        dividend?: (number|null);

        /** Quote dividendYield */
        dividendYield?: (number|null);

        /** Quote dividendExDate */
        dividendExDate?: (number|null);

        /** Quote dividendFrequency */
        dividendFrequency?: (number|Long|null);

        /** Quote forwardDividendYield */
        forwardDividendYield?: (number|null);

        /** Quote roe */
        roe?: (number|null);

        /** Quote roa */
        roa?: (number|null);

        /** Quote roic */
        roic?: (number|null);

        /** Quote currentRatio */
        currentRatio?: (number|null);

        /** Quote grossMargin */
        grossMargin?: (number|null);

        /** Quote netMargin */
        netMargin?: (number|null);

        /** Quote operationMargin */
        operationMargin?: (number|null);

        /** Quote epsMrq */
        epsMrq?: (number|null);

        /** Quote epsTtm */
        epsTtm?: (number|null);

        /** Quote dilutedEpsMrq */
        dilutedEpsMrq?: (number|null);

        /** Quote dilutedEpsTtm */
        dilutedEpsTtm?: (number|null);

        /** Quote low52Week */
        low52Week?: (number|null);

        /** Quote high52Week */
        high52Week?: (number|null);

        /** Quote revenueTtm */
        revenueTtm?: (number|null);

        /** Quote earningsAsOf */
        earningsAsOf?: (number|null);

        /** Quote netProfitMargin */
        netProfitMargin?: (number|null);

        /** Quote price1Week */
        price1Week?: (number|null);

        /** Quote priceChangePercent1Week */
        priceChangePercent1Week?: (number|null);

        /** Quote price1Month */
        price1Month?: (number|null);

        /** Quote priceChangePercent1Month */
        priceChangePercent1Month?: (number|null);

        /** Quote priceHalfYear */
        priceHalfYear?: (number|null);

        /** Quote priceChangePercentHalfYear */
        priceChangePercentHalfYear?: (number|null);

        /** Quote priceQuarter */
        priceQuarter?: (number|null);

        /** Quote priceChangePercentQuarter */
        priceChangePercentQuarter?: (number|null);

        /** Quote price1Year */
        price1Year?: (number|null);

        /** Quote priceChangePercent1Year */
        priceChangePercent1Year?: (number|null);

        /** Quote priceYearToDate */
        priceYearToDate?: (number|null);

        /** Quote priceChangePercentYearToDate */
        priceChangePercentYearToDate?: (number|null);

        /** Quote roeMrq */
        roeMrq?: (number|null);

        /** Quote roaMrq */
        roaMrq?: (number|null);

        /** Quote roicMrq */
        roicMrq?: (number|null);

        /** Quote currentRatioMrq */
        currentRatioMrq?: (number|null);

        /** Quote grossMarginMrq */
        grossMarginMrq?: (number|null);

        /** Quote netMarginMrq */
        netMarginMrq?: (number|null);

        /** Quote operationMarginMrq */
        operationMarginMrq?: (number|null);

        /** Quote previousDayVolume */
        previousDayVolume?: (number|Long|null);

        /** Quote previousDayAtr */
        previousDayAtr?: (number|null);

        /** Quote previousDayAdx */
        previousDayAdx?: (number|null);

        /** Quote previousDayPlusDMSmoothed */
        previousDayPlusDMSmoothed?: (number|null);

        /** Quote previousDayMinusDMSmoothed */
        previousDayMinusDMSmoothed?: (number|null);

        /** Quote previousDayHigh */
        previousDayHigh?: (number|null);

        /** Quote previousDayLow */
        previousDayLow?: (number|null);

        /** Quote adx */
        adx?: (number|null);

        /** Quote percentInstitutionalOwnership */
        percentInstitutionalOwnership?: (number|null);

        /** Quote percentInstitutionalTransactions */
        percentInstitutionalTransactions?: (number|null);

        /** Quote wiim */
        wiim?: (string|null);

        /** Quote wiimAsOf */
        wiimAsOf?: (number|Long|null);

        /** Quote vwap */
        vwap?: (number|null);

        /** Quote vwapChangePercent */
        vwapChangePercent?: (number|null);

        /** Quote vwapChange */
        vwapChange?: (number|null);

        /** Quote rsiAvgGain */
        rsiAvgGain?: (number|null);

        /** Quote rsiAvgLoss */
        rsiAvgLoss?: (number|null);

        /** Quote halted */
        halted?: (string|null);

        /** Quote tradeCount */
        tradeCount?: (number|Long|null);

        /** Quote regularHighCount */
        regularHighCount?: (number|Long|null);

        /** Quote regularLowCount */
        regularLowCount?: (number|Long|null);

        /** Quote preMarketHighCount */
        preMarketHighCount?: (number|Long|null);

        /** Quote preMarketLowCount */
        preMarketLowCount?: (number|Long|null);

        /** Quote afterMarketHighCount */
        afterMarketHighCount?: (number|Long|null);

        /** Quote afterMarketLowCount */
        afterMarketLowCount?: (number|Long|null);

        /** Quote dayHighCount */
        dayHighCount?: (number|Long|null);

        /** Quote dayLowCount */
        dayLowCount?: (number|Long|null);

        /** Quote dayHigh */
        dayHigh?: (number|null);

        /** Quote dayLow */
        dayLow?: (number|null);

        /** Quote preMarketHigh */
        preMarketHigh?: (number|null);

        /** Quote preMarketLow */
        preMarketLow?: (number|null);

        /** Quote afterMarketHigh */
        afterMarketHigh?: (number|null);

        /** Quote afterMarketLow */
        afterMarketLow?: (number|null);

        /** Quote regularHigh */
        regularHigh?: (number|null);

        /** Quote regularLow */
        regularLow?: (number|null);

        /** Quote priceSpikeUpCount */
        priceSpikeUpCount?: (number|Long|null);

        /** Quote priceSpikeDownCount */
        priceSpikeDownCount?: (number|Long|null);

        /** Quote haltedCount */
        haltedCount?: (number|Long|null);

        /** Quote usCongressTotal */
        usCongressTotal?: (number|null);

        /** Quote usCongressRepublicansTotal */
        usCongressRepublicansTotal?: (number|null);

        /** Quote usCongressDemocratsTotal */
        usCongressDemocratsTotal?: (number|null);

        /** Quote usCongressOtherTotal */
        usCongressOtherTotal?: (number|null);

        /** Quote volumeFloatRatio */
        volumeFloatRatio?: (number|null);

        /** Quote atrp */
        atrp?: (number|null);

        /** Quote ytdGainLoss */
        ytdGainLoss?: (number|null);

        /** Quote mtdGainLoss */
        mtdGainLoss?: (number|null);

        /** Quote ytdChange */
        ytdChange?: (number|null);

        /** Quote mtdChange */
        mtdChange?: (number|null);

        /** Quote yearOpen */
        yearOpen?: (number|null);

        /** Quote monthOpen */
        monthOpen?: (number|null);

        /** Quote volumeFloatPercent */
        volumeFloatPercent?: (number|null);

        /** Quote rsi1m */
        rsi1m?: (number|null);

        /** Quote rsi2m */
        rsi2m?: (number|null);

        /** Quote rsi5m */
        rsi5m?: (number|null);

        /** Quote rsi15m */
        rsi15m?: (number|null);

        /** Quote rsi30m */
        rsi30m?: (number|null);

        /** Quote rsi60m */
        rsi60m?: (number|null);

        /** Quote rsiEth1m */
        rsiEth1m?: (number|null);

        /** Quote rsiEth2m */
        rsiEth2m?: (number|null);

        /** Quote rsiEth5m */
        rsiEth5m?: (number|null);

        /** Quote rsiEth15m */
        rsiEth15m?: (number|null);

        /** Quote rsiEth30m */
        rsiEth30m?: (number|null);

        /** Quote rsiEth60m */
        rsiEth60m?: (number|null);

        /** Quote averageDollarVolume10Day */
        averageDollarVolume10Day?: (number|null);

        /** Quote averageVolume60Day */
        averageVolume60Day?: (number|Long|null);

        /** Quote relativeVolumePercent60Day */
        relativeVolumePercent60Day?: (number|null);

        /** Quote rsi1mLowest */
        rsi1mLowest?: (number|null);

        /** Quote rsi2mLowest */
        rsi2mLowest?: (number|null);

        /** Quote rsi5mLowest */
        rsi5mLowest?: (number|null);

        /** Quote rsi15mLowest */
        rsi15mLowest?: (number|null);

        /** Quote rsi30mLowest */
        rsi30mLowest?: (number|null);

        /** Quote rsi60mLowest */
        rsi60mLowest?: (number|null);

        /** Quote rsiEth1mLowest */
        rsiEth1mLowest?: (number|null);

        /** Quote rsiEth2mLowest */
        rsiEth2mLowest?: (number|null);

        /** Quote rsiEth5mLowest */
        rsiEth5mLowest?: (number|null);

        /** Quote rsiEth15mLowest */
        rsiEth15mLowest?: (number|null);

        /** Quote rsiEth30mLowest */
        rsiEth30mLowest?: (number|null);

        /** Quote rsiEth60mLowest */
        rsiEth60mLowest?: (number|null);

        /** Quote rsi1mGreatest */
        rsi1mGreatest?: (number|null);

        /** Quote rsi2mGreatest */
        rsi2mGreatest?: (number|null);

        /** Quote rsi5mGreatest */
        rsi5mGreatest?: (number|null);

        /** Quote rsi15mGreatest */
        rsi15mGreatest?: (number|null);

        /** Quote rsi30mGreatest */
        rsi30mGreatest?: (number|null);

        /** Quote rsi60mGreatest */
        rsi60mGreatest?: (number|null);

        /** Quote rsiEth1mGreatest */
        rsiEth1mGreatest?: (number|null);

        /** Quote rsiEth2mGreatest */
        rsiEth2mGreatest?: (number|null);

        /** Quote rsiEth5mGreatest */
        rsiEth5mGreatest?: (number|null);

        /** Quote rsiEth15mGreatest */
        rsiEth15mGreatest?: (number|null);

        /** Quote rsiEth30mGreatest */
        rsiEth30mGreatest?: (number|null);

        /** Quote rsiEth60mGreatest */
        rsiEth60mGreatest?: (number|null);

        /** Quote rsiCrossOver */
        rsiCrossOver?: (number|null);

        /** Quote ema12d */
        ema12d?: (number|null);

        /** Quote ema26d */
        ema26d?: (number|null);

        /** Quote macd */
        macd?: (number|null);

        /** Quote macdSignalLine */
        macdSignalLine?: (number|null);

        /** Quote macdHistogram */
        macdHistogram?: (number|null);

        /** Quote macdSignalLineBase */
        macdSignalLineBase?: (number|null);

        /** Quote dividendGrowthRate1Year */
        dividendGrowthRate1Year?: (number|null);

        /** Quote dividendGrowthRate3Year */
        dividendGrowthRate3Year?: (number|null);

        /** Quote dividendGrowthRate5Year */
        dividendGrowthRate5Year?: (number|null);

        /** Quote totalDividendTtm */
        totalDividendTtm?: (number|null);

        /** Quote dividendIncreasing */
        dividendIncreasing?: (number|null);

        /** Quote newsBzWireTitle */
        newsBzWireTitle?: (string|null);

        /** Quote newsBzWireNodeId */
        newsBzWireNodeId?: (string|null);

        /** Quote newsBzWireTimestamp */
        newsBzWireTimestamp?: (number|Long|null);

        /** Quote newsPRTitle */
        newsPRTitle?: (string|null);

        /** Quote newsPRNodeId */
        newsPRNodeId?: (string|null);

        /** Quote newsPRTimestamp */
        newsPRTimestamp?: (number|Long|null);

        /** Quote newsSecTitle */
        newsSecTitle?: (string|null);

        /** Quote newsSecNodeId */
        newsSecNodeId?: (string|null);

        /** Quote newsSecTimestamp */
        newsSecTimestamp?: (number|Long|null);

        /** Quote newsBzWireTitleWithNodeId */
        newsBzWireTitleWithNodeId?: (string|null);

        /** Quote newsPRTitleWithNodeId */
        newsPRTitleWithNodeId?: (string|null);

        /** Quote newsSecTitleWithNodeId */
        newsSecTitleWithNodeId?: (string|null);

        /** Quote movingAverage500Day */
        movingAverage500Day?: (number|null);

        /** Quote gapChange */
        gapChange?: (number|null);

        /** Quote gapChangePercent */
        gapChangePercent?: (number|null);

        /** Quote adr14d */
        adr14d?: (number|null);

        /** Quote dilutedEpsMrqSurprisePercent */
        dilutedEpsMrqSurprisePercent?: (number|null);

        /** Quote dilutedEpsTtmSurprisePercent */
        dilutedEpsTtmSurprisePercent?: (number|null);

        /** Quote dilutedRevenueMrqSurprisePercent */
        dilutedRevenueMrqSurprisePercent?: (number|null);

        /** Quote dilutedRevenueTtmSurprisePercent */
        dilutedRevenueTtmSurprisePercent?: (number|null);

        /** Quote epsGrowthTtm */
        epsGrowthTtm?: (number|null);

        /** Quote epsGrowthY2Y */
        epsGrowthY2Y?: (number|null);

        /** Quote epsGrowthQ2Q */
        epsGrowthQ2Q?: (number|null);

        /** Quote epsGrowth5Year */
        epsGrowth5Year?: (number|null);

        /** Quote epsGrowthYearAgoQ */
        epsGrowthYearAgoQ?: (number|null);

        /** Quote revenueGrowthTtm */
        revenueGrowthTtm?: (number|null);

        /** Quote revenueGrowthY2Y */
        revenueGrowthY2Y?: (number|null);

        /** Quote revenueGrowthQ2Q */
        revenueGrowthQ2Q?: (number|null);

        /** Quote revenueGrowth5Year */
        revenueGrowth5Year?: (number|null);

        /** Quote revenueGrowthYearAgoQ */
        revenueGrowthYearAgoQ?: (number|null);

        /** Quote ltDebtEquity */
        ltDebtEquity?: (number|null);

        /** Quote bullsSay */
        bullsSay?: (string|null);

        /** Quote bearsSay */
        bearsSay?: (string|null);

        /** Quote ipoDateNumber */
        ipoDateNumber?: (number|Long|null);

        /** Quote ffoPerShare */
        ffoPerShare?: (number|null);

        /** Quote quickRatioMrq */
        quickRatioMrq?: (number|null);

        /** Quote highAllTime */
        highAllTime?: (number|null);

        /** Quote lowAllTime */
        lowAllTime?: (number|null);

        /** Quote evToEbitda */
        evToEbitda?: (number|null);

        /** Quote totalDebtEquityRatio */
        totalDebtEquityRatio?: (number|null);

        /** Quote dividendPayoutRatio */
        dividendPayoutRatio?: (number|null);

        /** Quote operatingCashFlow */
        operatingCashFlow?: (number|null);

        /** Quote insiderPercentage */
        insiderPercentage?: (number|null);

        /** Quote priceToCashFlowRatio */
        priceToCashFlowRatio?: (number|null);

        /** Quote regularHoursChange */
        regularHoursChange?: (number|null);

        /** Quote regularHoursPercentChange */
        regularHoursPercentChange?: (number|null);

        /** Quote postToPreHoursChange */
        postToPreHoursChange?: (number|null);

        /** Quote postToPreHoursPercentChange */
        postToPreHoursPercentChange?: (number|null);

        /** Quote scannerClose */
        scannerClose?: (number|null);

        /** Quote scannerChange */
        scannerChange?: (number|null);

        /** Quote scannerChangePercent */
        scannerChangePercent?: (number|null);

        /** Quote scannerChangePercentClose */
        scannerChangePercentClose?: (number|null);

        /** Quote scannerPreviousClose */
        scannerPreviousClose?: (number|null);

        /** Quote freeCashFlow */
        freeCashFlow?: (number|null);

        /** Quote minPriceTarget */
        minPriceTarget?: (number|null);

        /** Quote maxPriceTarget */
        maxPriceTarget?: (number|null);

        /** Quote meanPriceTarget */
        meanPriceTarget?: (number|null);

        /** Quote totalNumberOfAnalyst */
        totalNumberOfAnalyst?: (number|null);

        /** Quote averageAnalystRecommendation */
        averageAnalystRecommendation?: (string|null);

        /** Quote pegRatio */
        pegRatio?: (number|null);

        /** Quote priceToFreeCashFlowRatio */
        priceToFreeCashFlowRatio?: (number|null);

        /** Quote distanceToMaxPriceTarget */
        distanceToMaxPriceTarget?: (number|null);

        /** Quote distanceToMinPriceTarget */
        distanceToMinPriceTarget?: (number|null);

        /** Quote distanceToMeanPriceTarget */
        distanceToMeanPriceTarget?: (number|null);

        /** Quote shortTermTrend */
        shortTermTrend?: (string|null);

        /** Quote mediumTermTrend */
        mediumTermTrend?: (string|null);

        /** Quote longTermTrend */
        longTermTrend?: (string|null);

        /** Quote twentySixWeekChange */
        twentySixWeekChange?: (number|null);

        /** Quote fiftyTwoWeekChange */
        fiftyTwoWeekChange?: (number|null);

        /** Quote annualizedStandardDeviation */
        annualizedStandardDeviation?: (number|null);

        /** Quote momentum */
        momentum?: (number|null);

        /** Quote value */
        value?: (number|null);

        /** Quote piotroskiFScore */
        piotroskiFScore?: (number|Long|null);

        /** Quote netIncomeTtm */
        netIncomeTtm?: (number|null);

        /** Quote currentRatioYearAgo */
        currentRatioYearAgo?: (number|null);

        /** Quote roaYearAgo */
        roaYearAgo?: (number|null);

        /** Quote grossMarginYearAgo */
        grossMarginYearAgo?: (number|null);

        /** Quote longTermDebt */
        longTermDebt?: (number|null);

        /** Quote longTermDebtYearAgo */
        longTermDebtYearAgo?: (number|null);

        /** Quote shareIssued */
        shareIssued?: (number|null);

        /** Quote shareIssuedYearAgo */
        shareIssuedYearAgo?: (number|null);

        /** Quote totalAssets */
        totalAssets?: (number|null);

        /** Quote totalAssetsYearAgo */
        totalAssetsYearAgo?: (number|null);

        /** Quote totalAssets2YearAgo */
        totalAssets2YearAgo?: (number|null);

        /** Quote revenueTtmYearAgo */
        revenueTtmYearAgo?: (number|null);

        /** Quote longTermDebtAndCapitalLeaseObligation */
        longTermDebtAndCapitalLeaseObligation?: (number|null);

        /** Quote averageTotalAssets */
        averageTotalAssets?: (number|null);

        /** Quote averageTotalAssetsYearAgo */
        averageTotalAssetsYearAgo?: (number|null);

        /** Quote growth */
        growth?: (number|null);

        /** Quote quality */
        quality?: (number|null);

        /** Quote momentumPercentile */
        momentumPercentile?: (number|null);

        /** Quote valuePercentile */
        valuePercentile?: (number|null);

        /** Quote growthPercentile */
        growthPercentile?: (number|null);

        /** Quote qualityPercentile */
        qualityPercentile?: (number|null);

        /** Quote rateOfReturnDay */
        rateOfReturnDay?: (number|null);

        /** Quote rateOfReturnWeek */
        rateOfReturnWeek?: (number|null);

        /** Quote rateOfReturnMonth */
        rateOfReturnMonth?: (number|null);

        /** Quote dividendOneDay */
        dividendOneDay?: (number|null);

        /** Quote dividendOneWeek */
        dividendOneWeek?: (number|null);

        /** Quote dividendOneMonth */
        dividendOneMonth?: (number|null);

        /** Quote totalLiabilities */
        totalLiabilities?: (number|null);

        /** Quote retainedEarnings */
        retainedEarnings?: (number|null);

        /** Quote ebit */
        ebit?: (number|null);

        /** Quote altmanZScore */
        altmanZScore?: (number|null);

        /** Quote powerEarningGap */
        powerEarningGap?: (boolean|null);

        /** Quote monsterPowerEarningGap */
        monsterPowerEarningGap?: (boolean|null);

        /** Quote monsterGap */
        monsterGap?: (boolean|null);

        /** Quote oelGap */
        oelGap?: (boolean|null);
    }

    /** Represents a Quote. */
    class Quote implements IQuote {

        /**
         * Constructs a new Quote.
         * @param [properties] Properties to set
         */
        constructor(properties?: QuoteProtos.IQuote);

        /** Quote symbol. */
        public symbol: string;

        /** Quote opol. */
        public opol: string;

        /** Quote previousClose. */
        public previousClose: number;

        /** Quote close. */
        public close: number;

        /** Quote change. */
        public change: number;

        /** Quote changePercent. */
        public changePercent: number;

        /** Quote name. */
        public name: string;

        /** Quote ipoDate. */
        public ipoDate: string;

        /** Quote exchange. */
        public exchange: string;

        /** Quote tradeExchange. */
        public tradeExchange: string;

        /** Quote preMarketOpen. */
        public preMarketOpen: number;

        /** Quote preMarketOpenTime. */
        public preMarketOpenTime: (number|Long);

        /** Quote preMarketVolume. */
        public preMarketVolume: (number|Long);

        /** Quote afterMarketVolume. */
        public afterMarketVolume: (number|Long);

        /** Quote preMarketOpenChange. */
        public preMarketOpenChange: number;

        /** Quote preMarketOpenChangePercent. */
        public preMarketOpenChangePercent: number;

        /** Quote source. */
        public source: QuoteProtos.QuoteSource;

        /** Quote clearFieldNumbers. */
        public clearFieldNumbers: number[];

        /** Quote marketCap. */
        public marketCap: (number|Long);

        /** Quote rsi. */
        public rsi: number;

        /** Quote atr. */
        public atr: number;

        /** Quote priceToEarnings. */
        public priceToEarnings: number;

        /** Quote averageVolume. */
        public averageVolume: (number|Long);

        /** Quote sharesOutstanding. */
        public sharesOutstanding: (number|Long);

        /** Quote sector. */
        public sector: string;

        /** Quote sectorCode. */
        public sectorCode: number;

        /** Quote industry. */
        public industry: string;

        /** Quote industryCode. */
        public industryCode: number;

        /** Quote forwardPERatio. */
        public forwardPERatio: number;

        /** Quote pricetoEBITDA. */
        public pricetoEBITDA: number;

        /** Quote trailingDividendYield. */
        public trailingDividendYield: number;

        /** Quote PBRatio. */
        public PBRatio: number;

        /** Quote priceToSales. */
        public priceToSales: number;

        /** Quote FCFPerShare. */
        public FCFPerShare: number;

        /** Quote buyBackYield. */
        public buyBackYield: number;

        /** Quote open. */
        public open: number;

        /** Quote price. */
        public price: number;

        /** Quote high. */
        public high: number;

        /** Quote low. */
        public low: number;

        /** Quote time. */
        public time: (number|Long);

        /** Quote size. */
        public size: number;

        /** Quote type. */
        public type: string;

        /** Quote dayVolume. */
        public dayVolume: (number|Long);

        /** Quote askPrice. */
        public askPrice: number;

        /** Quote bidPrice. */
        public bidPrice: number;

        /** Quote shareFloatDate. */
        public shareFloatDate: string;

        /** Quote sharesOutstandingDate. */
        public sharesOutstandingDate: string;

        /** Quote shareFloat. */
        public shareFloat: (number|Long);

        /** Quote updateType. */
        public updateType: QuoteProtos.TickType;

        /** Quote subtype. */
        public subtype: string;

        /** Quote sessionType. */
        public sessionType: QuoteProtos.SessionType;

        /** Quote volume. */
        public volume: (number|Long);

        /** Quote changePercent1Minute. */
        public changePercent1Minute: number;

        /** Quote changePercentClose. */
        public changePercentClose: number;

        /** Quote changePercent5Minute. */
        public changePercent5Minute: number;

        /** Quote volume1Minute. */
        public volume1Minute: (number|Long);

        /** Quote relativeVolume1Minute. */
        public relativeVolume1Minute: number;

        /** Quote change15Minute. */
        public change15Minute: number;

        /** Quote changePercent15Minute. */
        public changePercent15Minute: number;

        /** Quote change30Minute. */
        public change30Minute: number;

        /** Quote changePercent30Minute. */
        public changePercent30Minute: number;

        /** Quote change60Minute. */
        public change60Minute: number;

        /** Quote changePercent60Minute. */
        public changePercent60Minute: number;

        /** Quote changePercentOpen. */
        public changePercentOpen: number;

        /** Quote changeOpen. */
        public changeOpen: number;

        /** Quote gics. */
        public gics: number;

        /** Quote gicsSector. */
        public gicsSector: number;

        /** Quote gicsSectorName. */
        public gicsSectorName: string;

        /** Quote gicsIndustryGroup. */
        public gicsIndustryGroup: number;

        /** Quote gicsIndustryGroupName. */
        public gicsIndustryGroupName: string;

        /** Quote gicsIndustry. */
        public gicsIndustry: number;

        /** Quote gicsIndustryName. */
        public gicsIndustryName: string;

        /** Quote gicsSubIndustry. */
        public gicsSubIndustry: number;

        /** Quote gicsSubIndustryName. */
        public gicsSubIndustryName: string;

        /** Quote optionable. */
        public optionable: number;

        /** Quote cik. */
        public cik: number;

        /** Quote signalName. */
        public signalName: string;

        /** Quote lastTradeTime. */
        public lastTradeTime: (number|Long);

        /** Quote country. */
        public country: string;

        /** Quote sharesShort. */
        public sharesShort: (number|Long);

        /** Quote sharesShortPercentOfFloat. */
        public sharesShortPercentOfFloat: number;

        /** Quote sharesShortChangePercent. */
        public sharesShortChangePercent: number;

        /** Quote sharesShortReceiptDate. */
        public sharesShortReceiptDate: (number|Long);

        /** Quote sharesShortSettlementDate. */
        public sharesShortSettlementDate: (number|Long);

        /** Quote shortInterestRatio. */
        public shortInterestRatio: number;

        /** Quote sharesShortValue. */
        public sharesShortValue: number;

        /** Quote sharesShortPrevious. */
        public sharesShortPrevious: (number|Long);

        /** Quote sharesShortPreviousValue. */
        public sharesShortPreviousValue: number;

        /** Quote sharesShortPreviousPercentOfFloat. */
        public sharesShortPreviousPercentOfFloat: number;

        /** Quote sharesShortChange. */
        public sharesShortChange: (number|Long);

        /** Quote sentTime. */
        public sentTime: (number|Long);

        /** Quote volume5Minute. */
        public volume5Minute: (number|Long);

        /** Quote volume15Minute. */
        public volume15Minute: (number|Long);

        /** Quote volume30Minute. */
        public volume30Minute: (number|Long);

        /** Quote volume60Minute. */
        public volume60Minute: (number|Long);

        /** Quote relativeVolume5Minute. */
        public relativeVolume5Minute: number;

        /** Quote relativeVolume90Day. */
        public relativeVolume90Day: number;

        /** Quote rvol1m10d. */
        public rvol1m10d: number;

        /** Quote rvol5m10d. */
        public rvol5m10d: number;

        /** Quote rvol5m30d. */
        public rvol5m30d: number;

        /** Quote rvol5m90d. */
        public rvol5m90d: number;

        /** Quote averageVolume10Day. */
        public averageVolume10Day: (number|Long);

        /** Quote averageVolumeChangePercent10Day. */
        public averageVolumeChangePercent10Day: number;

        /** Quote high10Day. */
        public high10Day: number;

        /** Quote highChangePercent10Day. */
        public highChangePercent10Day: number;

        /** Quote low10Day. */
        public low10Day: number;

        /** Quote lowChangePercent10Day. */
        public lowChangePercent10Day: number;

        /** Quote movingAverage10Day. */
        public movingAverage10Day: number;

        /** Quote movingAverageChangePercent10Day. */
        public movingAverageChangePercent10Day: number;

        /** Quote averageVolume20Day. */
        public averageVolume20Day: (number|Long);

        /** Quote averageVolumeChangePercent20Day. */
        public averageVolumeChangePercent20Day: number;

        /** Quote high20Day. */
        public high20Day: number;

        /** Quote highChangePercent20Day. */
        public highChangePercent20Day: number;

        /** Quote low20Day. */
        public low20Day: number;

        /** Quote lowChangePercent20Day. */
        public lowChangePercent20Day: number;

        /** Quote movingAverage20Day. */
        public movingAverage20Day: number;

        /** Quote movingAverageChangePercent20Day. */
        public movingAverageChangePercent20Day: number;

        /** Quote averageVolume50Day. */
        public averageVolume50Day: (number|Long);

        /** Quote averageVolumeChangePercent50Day. */
        public averageVolumeChangePercent50Day: number;

        /** Quote high50Day. */
        public high50Day: number;

        /** Quote highChangePercent50Day. */
        public highChangePercent50Day: number;

        /** Quote low50Day. */
        public low50Day: number;

        /** Quote lowChangePercent50Day. */
        public lowChangePercent50Day: number;

        /** Quote movingAverage50Day. */
        public movingAverage50Day: number;

        /** Quote movingAverageChangePercent50Day. */
        public movingAverageChangePercent50Day: number;

        /** Quote averageVolume100Day. */
        public averageVolume100Day: (number|Long);

        /** Quote averageVolumeChangePercent100Day. */
        public averageVolumeChangePercent100Day: number;

        /** Quote high100Day. */
        public high100Day: number;

        /** Quote highChangePercent100Day. */
        public highChangePercent100Day: number;

        /** Quote low100Day. */
        public low100Day: number;

        /** Quote lowChangePercent100Day. */
        public lowChangePercent100Day: number;

        /** Quote movingAverage100Day. */
        public movingAverage100Day: number;

        /** Quote movingAverageChangePercent100Day. */
        public movingAverageChangePercent100Day: number;

        /** Quote averageVolume200Day. */
        public averageVolume200Day: (number|Long);

        /** Quote averageVolumeChangePercent200Day. */
        public averageVolumeChangePercent200Day: number;

        /** Quote high200Day. */
        public high200Day: number;

        /** Quote highChangePercent200Day. */
        public highChangePercent200Day: number;

        /** Quote low200Day. */
        public low200Day: number;

        /** Quote lowChangePercent200Day. */
        public lowChangePercent200Day: number;

        /** Quote movingAverage200Day. */
        public movingAverage200Day: number;

        /** Quote movingAverageChangePercent200Day. */
        public movingAverageChangePercent200Day: number;

        /** Quote movingAverage250Day. */
        public movingAverage250Day: number;

        /** Quote movingAverageChangePercent250Day. */
        public movingAverageChangePercent250Day: number;

        /** Quote averageVolume90Day. */
        public averageVolume90Day: (number|Long);

        /** Quote nextEarningsDate. */
        public nextEarningsDate: number;

        /** Quote dividend. */
        public dividend: number;

        /** Quote dividendYield. */
        public dividendYield: number;

        /** Quote dividendExDate. */
        public dividendExDate: number;

        /** Quote dividendFrequency. */
        public dividendFrequency: (number|Long);

        /** Quote forwardDividendYield. */
        public forwardDividendYield: number;

        /** Quote roe. */
        public roe: number;

        /** Quote roa. */
        public roa: number;

        /** Quote roic. */
        public roic: number;

        /** Quote currentRatio. */
        public currentRatio: number;

        /** Quote grossMargin. */
        public grossMargin: number;

        /** Quote netMargin. */
        public netMargin: number;

        /** Quote operationMargin. */
        public operationMargin: number;

        /** Quote epsMrq. */
        public epsMrq: number;

        /** Quote epsTtm. */
        public epsTtm: number;

        /** Quote dilutedEpsMrq. */
        public dilutedEpsMrq: number;

        /** Quote dilutedEpsTtm. */
        public dilutedEpsTtm: number;

        /** Quote low52Week. */
        public low52Week: number;

        /** Quote high52Week. */
        public high52Week: number;

        /** Quote revenueTtm. */
        public revenueTtm: number;

        /** Quote earningsAsOf. */
        public earningsAsOf: number;

        /** Quote netProfitMargin. */
        public netProfitMargin: number;

        /** Quote price1Week. */
        public price1Week: number;

        /** Quote priceChangePercent1Week. */
        public priceChangePercent1Week: number;

        /** Quote price1Month. */
        public price1Month: number;

        /** Quote priceChangePercent1Month. */
        public priceChangePercent1Month: number;

        /** Quote priceHalfYear. */
        public priceHalfYear: number;

        /** Quote priceChangePercentHalfYear. */
        public priceChangePercentHalfYear: number;

        /** Quote priceQuarter. */
        public priceQuarter: number;

        /** Quote priceChangePercentQuarter. */
        public priceChangePercentQuarter: number;

        /** Quote price1Year. */
        public price1Year: number;

        /** Quote priceChangePercent1Year. */
        public priceChangePercent1Year: number;

        /** Quote priceYearToDate. */
        public priceYearToDate: number;

        /** Quote priceChangePercentYearToDate. */
        public priceChangePercentYearToDate: number;

        /** Quote roeMrq. */
        public roeMrq: number;

        /** Quote roaMrq. */
        public roaMrq: number;

        /** Quote roicMrq. */
        public roicMrq: number;

        /** Quote currentRatioMrq. */
        public currentRatioMrq: number;

        /** Quote grossMarginMrq. */
        public grossMarginMrq: number;

        /** Quote netMarginMrq. */
        public netMarginMrq: number;

        /** Quote operationMarginMrq. */
        public operationMarginMrq: number;

        /** Quote previousDayVolume. */
        public previousDayVolume: (number|Long);

        /** Quote previousDayAtr. */
        public previousDayAtr: number;

        /** Quote previousDayAdx. */
        public previousDayAdx: number;

        /** Quote previousDayPlusDMSmoothed. */
        public previousDayPlusDMSmoothed: number;

        /** Quote previousDayMinusDMSmoothed. */
        public previousDayMinusDMSmoothed: number;

        /** Quote previousDayHigh. */
        public previousDayHigh: number;

        /** Quote previousDayLow. */
        public previousDayLow: number;

        /** Quote adx. */
        public adx: number;

        /** Quote percentInstitutionalOwnership. */
        public percentInstitutionalOwnership: number;

        /** Quote percentInstitutionalTransactions. */
        public percentInstitutionalTransactions: number;

        /** Quote wiim. */
        public wiim: string;

        /** Quote wiimAsOf. */
        public wiimAsOf: (number|Long);

        /** Quote vwap. */
        public vwap: number;

        /** Quote vwapChangePercent. */
        public vwapChangePercent: number;

        /** Quote vwapChange. */
        public vwapChange: number;

        /** Quote rsiAvgGain. */
        public rsiAvgGain: number;

        /** Quote rsiAvgLoss. */
        public rsiAvgLoss: number;

        /** Quote halted. */
        public halted: string;

        /** Quote tradeCount. */
        public tradeCount: (number|Long);

        /** Quote regularHighCount. */
        public regularHighCount: (number|Long);

        /** Quote regularLowCount. */
        public regularLowCount: (number|Long);

        /** Quote preMarketHighCount. */
        public preMarketHighCount: (number|Long);

        /** Quote preMarketLowCount. */
        public preMarketLowCount: (number|Long);

        /** Quote afterMarketHighCount. */
        public afterMarketHighCount: (number|Long);

        /** Quote afterMarketLowCount. */
        public afterMarketLowCount: (number|Long);

        /** Quote dayHighCount. */
        public dayHighCount: (number|Long);

        /** Quote dayLowCount. */
        public dayLowCount: (number|Long);

        /** Quote dayHigh. */
        public dayHigh: number;

        /** Quote dayLow. */
        public dayLow: number;

        /** Quote preMarketHigh. */
        public preMarketHigh: number;

        /** Quote preMarketLow. */
        public preMarketLow: number;

        /** Quote afterMarketHigh. */
        public afterMarketHigh: number;

        /** Quote afterMarketLow. */
        public afterMarketLow: number;

        /** Quote regularHigh. */
        public regularHigh: number;

        /** Quote regularLow. */
        public regularLow: number;

        /** Quote priceSpikeUpCount. */
        public priceSpikeUpCount: (number|Long);

        /** Quote priceSpikeDownCount. */
        public priceSpikeDownCount: (number|Long);

        /** Quote haltedCount. */
        public haltedCount: (number|Long);

        /** Quote usCongressTotal. */
        public usCongressTotal: number;

        /** Quote usCongressRepublicansTotal. */
        public usCongressRepublicansTotal: number;

        /** Quote usCongressDemocratsTotal. */
        public usCongressDemocratsTotal: number;

        /** Quote usCongressOtherTotal. */
        public usCongressOtherTotal: number;

        /** Quote volumeFloatRatio. */
        public volumeFloatRatio: number;

        /** Quote atrp. */
        public atrp: number;

        /** Quote ytdGainLoss. */
        public ytdGainLoss: number;

        /** Quote mtdGainLoss. */
        public mtdGainLoss: number;

        /** Quote ytdChange. */
        public ytdChange: number;

        /** Quote mtdChange. */
        public mtdChange: number;

        /** Quote yearOpen. */
        public yearOpen: number;

        /** Quote monthOpen. */
        public monthOpen: number;

        /** Quote volumeFloatPercent. */
        public volumeFloatPercent: number;

        /** Quote rsi1m. */
        public rsi1m: number;

        /** Quote rsi2m. */
        public rsi2m: number;

        /** Quote rsi5m. */
        public rsi5m: number;

        /** Quote rsi15m. */
        public rsi15m: number;

        /** Quote rsi30m. */
        public rsi30m: number;

        /** Quote rsi60m. */
        public rsi60m: number;

        /** Quote rsiEth1m. */
        public rsiEth1m: number;

        /** Quote rsiEth2m. */
        public rsiEth2m: number;

        /** Quote rsiEth5m. */
        public rsiEth5m: number;

        /** Quote rsiEth15m. */
        public rsiEth15m: number;

        /** Quote rsiEth30m. */
        public rsiEth30m: number;

        /** Quote rsiEth60m. */
        public rsiEth60m: number;

        /** Quote averageDollarVolume10Day. */
        public averageDollarVolume10Day: number;

        /** Quote averageVolume60Day. */
        public averageVolume60Day: (number|Long);

        /** Quote relativeVolumePercent60Day. */
        public relativeVolumePercent60Day: number;

        /** Quote rsi1mLowest. */
        public rsi1mLowest: number;

        /** Quote rsi2mLowest. */
        public rsi2mLowest: number;

        /** Quote rsi5mLowest. */
        public rsi5mLowest: number;

        /** Quote rsi15mLowest. */
        public rsi15mLowest: number;

        /** Quote rsi30mLowest. */
        public rsi30mLowest: number;

        /** Quote rsi60mLowest. */
        public rsi60mLowest: number;

        /** Quote rsiEth1mLowest. */
        public rsiEth1mLowest: number;

        /** Quote rsiEth2mLowest. */
        public rsiEth2mLowest: number;

        /** Quote rsiEth5mLowest. */
        public rsiEth5mLowest: number;

        /** Quote rsiEth15mLowest. */
        public rsiEth15mLowest: number;

        /** Quote rsiEth30mLowest. */
        public rsiEth30mLowest: number;

        /** Quote rsiEth60mLowest. */
        public rsiEth60mLowest: number;

        /** Quote rsi1mGreatest. */
        public rsi1mGreatest: number;

        /** Quote rsi2mGreatest. */
        public rsi2mGreatest: number;

        /** Quote rsi5mGreatest. */
        public rsi5mGreatest: number;

        /** Quote rsi15mGreatest. */
        public rsi15mGreatest: number;

        /** Quote rsi30mGreatest. */
        public rsi30mGreatest: number;

        /** Quote rsi60mGreatest. */
        public rsi60mGreatest: number;

        /** Quote rsiEth1mGreatest. */
        public rsiEth1mGreatest: number;

        /** Quote rsiEth2mGreatest. */
        public rsiEth2mGreatest: number;

        /** Quote rsiEth5mGreatest. */
        public rsiEth5mGreatest: number;

        /** Quote rsiEth15mGreatest. */
        public rsiEth15mGreatest: number;

        /** Quote rsiEth30mGreatest. */
        public rsiEth30mGreatest: number;

        /** Quote rsiEth60mGreatest. */
        public rsiEth60mGreatest: number;

        /** Quote rsiCrossOver. */
        public rsiCrossOver: number;

        /** Quote ema12d. */
        public ema12d: number;

        /** Quote ema26d. */
        public ema26d: number;

        /** Quote macd. */
        public macd: number;

        /** Quote macdSignalLine. */
        public macdSignalLine: number;

        /** Quote macdHistogram. */
        public macdHistogram: number;

        /** Quote macdSignalLineBase. */
        public macdSignalLineBase: number;

        /** Quote dividendGrowthRate1Year. */
        public dividendGrowthRate1Year: number;

        /** Quote dividendGrowthRate3Year. */
        public dividendGrowthRate3Year: number;

        /** Quote dividendGrowthRate5Year. */
        public dividendGrowthRate5Year: number;

        /** Quote totalDividendTtm. */
        public totalDividendTtm: number;

        /** Quote dividendIncreasing. */
        public dividendIncreasing: number;

        /** Quote newsBzWireTitle. */
        public newsBzWireTitle: string;

        /** Quote newsBzWireNodeId. */
        public newsBzWireNodeId: string;

        /** Quote newsBzWireTimestamp. */
        public newsBzWireTimestamp: (number|Long);

        /** Quote newsPRTitle. */
        public newsPRTitle: string;

        /** Quote newsPRNodeId. */
        public newsPRNodeId: string;

        /** Quote newsPRTimestamp. */
        public newsPRTimestamp: (number|Long);

        /** Quote newsSecTitle. */
        public newsSecTitle: string;

        /** Quote newsSecNodeId. */
        public newsSecNodeId: string;

        /** Quote newsSecTimestamp. */
        public newsSecTimestamp: (number|Long);

        /** Quote newsBzWireTitleWithNodeId. */
        public newsBzWireTitleWithNodeId: string;

        /** Quote newsPRTitleWithNodeId. */
        public newsPRTitleWithNodeId: string;

        /** Quote newsSecTitleWithNodeId. */
        public newsSecTitleWithNodeId: string;

        /** Quote movingAverage500Day. */
        public movingAverage500Day: number;

        /** Quote gapChange. */
        public gapChange: number;

        /** Quote gapChangePercent. */
        public gapChangePercent: number;

        /** Quote adr14d. */
        public adr14d: number;

        /** Quote dilutedEpsMrqSurprisePercent. */
        public dilutedEpsMrqSurprisePercent: number;

        /** Quote dilutedEpsTtmSurprisePercent. */
        public dilutedEpsTtmSurprisePercent: number;

        /** Quote dilutedRevenueMrqSurprisePercent. */
        public dilutedRevenueMrqSurprisePercent: number;

        /** Quote dilutedRevenueTtmSurprisePercent. */
        public dilutedRevenueTtmSurprisePercent: number;

        /** Quote epsGrowthTtm. */
        public epsGrowthTtm: number;

        /** Quote epsGrowthY2Y. */
        public epsGrowthY2Y: number;

        /** Quote epsGrowthQ2Q. */
        public epsGrowthQ2Q: number;

        /** Quote epsGrowth5Year. */
        public epsGrowth5Year: number;

        /** Quote epsGrowthYearAgoQ. */
        public epsGrowthYearAgoQ: number;

        /** Quote revenueGrowthTtm. */
        public revenueGrowthTtm: number;

        /** Quote revenueGrowthY2Y. */
        public revenueGrowthY2Y: number;

        /** Quote revenueGrowthQ2Q. */
        public revenueGrowthQ2Q: number;

        /** Quote revenueGrowth5Year. */
        public revenueGrowth5Year: number;

        /** Quote revenueGrowthYearAgoQ. */
        public revenueGrowthYearAgoQ: number;

        /** Quote ltDebtEquity. */
        public ltDebtEquity: number;

        /** Quote bullsSay. */
        public bullsSay: string;

        /** Quote bearsSay. */
        public bearsSay: string;

        /** Quote ipoDateNumber. */
        public ipoDateNumber: (number|Long);

        /** Quote ffoPerShare. */
        public ffoPerShare: number;

        /** Quote quickRatioMrq. */
        public quickRatioMrq: number;

        /** Quote highAllTime. */
        public highAllTime: number;

        /** Quote lowAllTime. */
        public lowAllTime: number;

        /** Quote evToEbitda. */
        public evToEbitda: number;

        /** Quote totalDebtEquityRatio. */
        public totalDebtEquityRatio: number;

        /** Quote dividendPayoutRatio. */
        public dividendPayoutRatio: number;

        /** Quote operatingCashFlow. */
        public operatingCashFlow: number;

        /** Quote insiderPercentage. */
        public insiderPercentage: number;

        /** Quote priceToCashFlowRatio. */
        public priceToCashFlowRatio: number;

        /** Quote regularHoursChange. */
        public regularHoursChange: number;

        /** Quote regularHoursPercentChange. */
        public regularHoursPercentChange: number;

        /** Quote postToPreHoursChange. */
        public postToPreHoursChange: number;

        /** Quote postToPreHoursPercentChange. */
        public postToPreHoursPercentChange: number;

        /** Quote scannerClose. */
        public scannerClose: number;

        /** Quote scannerChange. */
        public scannerChange: number;

        /** Quote scannerChangePercent. */
        public scannerChangePercent: number;

        /** Quote scannerChangePercentClose. */
        public scannerChangePercentClose: number;

        /** Quote scannerPreviousClose. */
        public scannerPreviousClose: number;

        /** Quote freeCashFlow. */
        public freeCashFlow: number;

        /** Quote minPriceTarget. */
        public minPriceTarget: number;

        /** Quote maxPriceTarget. */
        public maxPriceTarget: number;

        /** Quote meanPriceTarget. */
        public meanPriceTarget: number;

        /** Quote totalNumberOfAnalyst. */
        public totalNumberOfAnalyst: number;

        /** Quote averageAnalystRecommendation. */
        public averageAnalystRecommendation: string;

        /** Quote pegRatio. */
        public pegRatio: number;

        /** Quote priceToFreeCashFlowRatio. */
        public priceToFreeCashFlowRatio: number;

        /** Quote distanceToMaxPriceTarget. */
        public distanceToMaxPriceTarget: number;

        /** Quote distanceToMinPriceTarget. */
        public distanceToMinPriceTarget: number;

        /** Quote distanceToMeanPriceTarget. */
        public distanceToMeanPriceTarget: number;

        /** Quote shortTermTrend. */
        public shortTermTrend: string;

        /** Quote mediumTermTrend. */
        public mediumTermTrend: string;

        /** Quote longTermTrend. */
        public longTermTrend: string;

        /** Quote twentySixWeekChange. */
        public twentySixWeekChange: number;

        /** Quote fiftyTwoWeekChange. */
        public fiftyTwoWeekChange: number;

        /** Quote annualizedStandardDeviation. */
        public annualizedStandardDeviation: number;

        /** Quote momentum. */
        public momentum: number;

        /** Quote value. */
        public value: number;

        /** Quote piotroskiFScore. */
        public piotroskiFScore: (number|Long);

        /** Quote netIncomeTtm. */
        public netIncomeTtm: number;

        /** Quote currentRatioYearAgo. */
        public currentRatioYearAgo: number;

        /** Quote roaYearAgo. */
        public roaYearAgo: number;

        /** Quote grossMarginYearAgo. */
        public grossMarginYearAgo: number;

        /** Quote longTermDebt. */
        public longTermDebt: number;

        /** Quote longTermDebtYearAgo. */
        public longTermDebtYearAgo: number;

        /** Quote shareIssued. */
        public shareIssued: number;

        /** Quote shareIssuedYearAgo. */
        public shareIssuedYearAgo: number;

        /** Quote totalAssets. */
        public totalAssets: number;

        /** Quote totalAssetsYearAgo. */
        public totalAssetsYearAgo: number;

        /** Quote totalAssets2YearAgo. */
        public totalAssets2YearAgo: number;

        /** Quote revenueTtmYearAgo. */
        public revenueTtmYearAgo: number;

        /** Quote longTermDebtAndCapitalLeaseObligation. */
        public longTermDebtAndCapitalLeaseObligation: number;

        /** Quote averageTotalAssets. */
        public averageTotalAssets: number;

        /** Quote averageTotalAssetsYearAgo. */
        public averageTotalAssetsYearAgo: number;

        /** Quote growth. */
        public growth: number;

        /** Quote quality. */
        public quality: number;

        /** Quote momentumPercentile. */
        public momentumPercentile: number;

        /** Quote valuePercentile. */
        public valuePercentile: number;

        /** Quote growthPercentile. */
        public growthPercentile: number;

        /** Quote qualityPercentile. */
        public qualityPercentile: number;

        /** Quote rateOfReturnDay. */
        public rateOfReturnDay: number;

        /** Quote rateOfReturnWeek. */
        public rateOfReturnWeek: number;

        /** Quote rateOfReturnMonth. */
        public rateOfReturnMonth: number;

        /** Quote dividendOneDay. */
        public dividendOneDay: number;

        /** Quote dividendOneWeek. */
        public dividendOneWeek: number;

        /** Quote dividendOneMonth. */
        public dividendOneMonth: number;

        /** Quote totalLiabilities. */
        public totalLiabilities: number;

        /** Quote retainedEarnings. */
        public retainedEarnings: number;

        /** Quote ebit. */
        public ebit: number;

        /** Quote altmanZScore. */
        public altmanZScore: number;

        /** Quote powerEarningGap. */
        public powerEarningGap: boolean;

        /** Quote monsterPowerEarningGap. */
        public monsterPowerEarningGap: boolean;

        /** Quote monsterGap. */
        public monsterGap: boolean;

        /** Quote oelGap. */
        public oelGap: boolean;

        /**
         * Creates a new Quote instance using the specified properties.
         * @param [properties] Properties to set
         * @returns Quote instance
         */
        public static create(properties?: QuoteProtos.IQuote): QuoteProtos.Quote;

        /**
         * Encodes the specified Quote message. Does not implicitly {@link QuoteProtos.Quote.verify|verify} messages.
         * @param message Quote message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: QuoteProtos.IQuote, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified Quote message, length delimited. Does not implicitly {@link QuoteProtos.Quote.verify|verify} messages.
         * @param message Quote message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: QuoteProtos.IQuote, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a Quote message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Quote
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): QuoteProtos.Quote;

        /**
         * Decodes a Quote message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns Quote
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): QuoteProtos.Quote;

        /**
         * Verifies a Quote message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a Quote message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Quote
         */
        public static fromObject(object: { [k: string]: any }): QuoteProtos.Quote;

        /**
         * Creates a plain object from a Quote message. Also converts values to other types if specified.
         * @param message Quote
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: QuoteProtos.Quote, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Quote to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }
}

/** Namespace LastProtos. */
export namespace LastProtos {

    /** Properties of a Heartbeat. */
    interface IHeartbeat {

        /** Heartbeat time */
        time?: (number|null);
    }

    /** Represents a Heartbeat. */
    class Heartbeat implements IHeartbeat {

        /**
         * Constructs a new Heartbeat.
         * @param [properties] Properties to set
         */
        constructor(properties?: LastProtos.IHeartbeat);

        /** Heartbeat time. */
        public time: number;

        /**
         * Creates a new Heartbeat instance using the specified properties.
         * @param [properties] Properties to set
         * @returns Heartbeat instance
         */
        public static create(properties?: LastProtos.IHeartbeat): LastProtos.Heartbeat;

        /**
         * Encodes the specified Heartbeat message. Does not implicitly {@link LastProtos.Heartbeat.verify|verify} messages.
         * @param message Heartbeat message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: LastProtos.IHeartbeat, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified Heartbeat message, length delimited. Does not implicitly {@link LastProtos.Heartbeat.verify|verify} messages.
         * @param message Heartbeat message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: LastProtos.IHeartbeat, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a Heartbeat message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Heartbeat
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): LastProtos.Heartbeat;

        /**
         * Decodes a Heartbeat message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns Heartbeat
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): LastProtos.Heartbeat;

        /**
         * Verifies a Heartbeat message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a Heartbeat message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Heartbeat
         */
        public static fromObject(object: { [k: string]: any }): LastProtos.Heartbeat;

        /**
         * Creates a plain object from a Heartbeat message. Also converts values to other types if specified.
         * @param message Heartbeat
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: LastProtos.Heartbeat, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Heartbeat to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a QuotesResponse. */
    interface IQuotesResponse {

        /** QuotesResponse quotes */
        quotes?: (LastProtos.ILastQuote[]|null);

        /** QuotesResponse processTimeMillis */
        processTimeMillis?: (number|null);
    }

    /** Represents a QuotesResponse. */
    class QuotesResponse implements IQuotesResponse {

        /**
         * Constructs a new QuotesResponse.
         * @param [properties] Properties to set
         */
        constructor(properties?: LastProtos.IQuotesResponse);

        /** QuotesResponse quotes. */
        public quotes: LastProtos.ILastQuote[];

        /** QuotesResponse processTimeMillis. */
        public processTimeMillis: number;

        /**
         * Creates a new QuotesResponse instance using the specified properties.
         * @param [properties] Properties to set
         * @returns QuotesResponse instance
         */
        public static create(properties?: LastProtos.IQuotesResponse): LastProtos.QuotesResponse;

        /**
         * Encodes the specified QuotesResponse message. Does not implicitly {@link LastProtos.QuotesResponse.verify|verify} messages.
         * @param message QuotesResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: LastProtos.IQuotesResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified QuotesResponse message, length delimited. Does not implicitly {@link LastProtos.QuotesResponse.verify|verify} messages.
         * @param message QuotesResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: LastProtos.IQuotesResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a QuotesResponse message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns QuotesResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): LastProtos.QuotesResponse;

        /**
         * Decodes a QuotesResponse message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns QuotesResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): LastProtos.QuotesResponse;

        /**
         * Verifies a QuotesResponse message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a QuotesResponse message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns QuotesResponse
         */
        public static fromObject(object: { [k: string]: any }): LastProtos.QuotesResponse;

        /**
         * Creates a plain object from a QuotesResponse message. Also converts values to other types if specified.
         * @param message QuotesResponse
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: LastProtos.QuotesResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this QuotesResponse to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a LastQuote. */
    interface ILastQuote {

        /** LastQuote symbol */
        symbol?: (string|null);

        /** LastQuote currency */
        currency?: (string|null);

        /** LastQuote delayedMinutes */
        delayedMinutes?: (number|null);

        /** LastQuote sessionType */
        sessionType?: (QuoteProtos.SessionType|null);

        /** LastQuote error */
        error?: (LastProtos.IError|null);

        /** LastQuote name */
        name?: (string|null);

        /** LastQuote type */
        type?: (string|null);

        /** LastQuote exchange */
        exchange?: (string|null);

        /** LastQuote open */
        open?: (number|null);

        /** LastQuote dayHigh */
        dayHigh?: (number|null);

        /** LastQuote dayLow */
        dayLow?: (number|null);

        /** LastQuote close */
        close?: (number|null);

        /** LastQuote volume */
        volume?: (number|null);

        /** LastQuote vwap */
        vwap?: (number|null);

        /** LastQuote closeDate */
        closeDate?: (string|null);

        /** LastQuote bidPrice */
        bidPrice?: (number|null);

        /** LastQuote askPrice */
        askPrice?: (number|null);

        /** LastQuote lastTradePrice */
        lastTradePrice?: (number|null);

        /** LastQuote lastTradeTime */
        lastTradeTime?: (number|Long|null);

        /** LastQuote previousClose */
        previousClose?: (number|null);

        /** LastQuote change */
        change?: (number|null);

        /** LastQuote percentChange */
        percentChange?: (number|null);

        /** LastQuote fiftyTwoWeekHigh */
        fiftyTwoWeekHigh?: (number|null);

        /** LastQuote fiftyTwoWeekLow */
        fiftyTwoWeekLow?: (number|null);

        /** LastQuote preMarketPrice */
        preMarketPrice?: (number|null);

        /** LastQuote preMarketVolume */
        preMarketVolume?: (number|Long|null);

        /** LastQuote afterMarketPrice */
        afterMarketPrice?: (number|null);

        /** LastQuote afterMarketVolume */
        afterMarketVolume?: (number|Long|null);

        /** LastQuote preMarketOpen */
        preMarketOpen?: (number|null);

        /** LastQuote preMarketOpenTime */
        preMarketOpenTime?: (number|Long|null);

        /** LastQuote version */
        version?: (number|null);

        /** LastQuote tradeCount */
        tradeCount?: (number|Long|null);

        /** LastQuote regularHighCount */
        regularHighCount?: (number|Long|null);

        /** LastQuote regularLowCount */
        regularLowCount?: (number|Long|null);

        /** LastQuote preMarketHighCount */
        preMarketHighCount?: (number|Long|null);

        /** LastQuote preMarketLowCount */
        preMarketLowCount?: (number|Long|null);

        /** LastQuote afterMarketHighCount */
        afterMarketHighCount?: (number|Long|null);

        /** LastQuote afterMarketLowCount */
        afterMarketLowCount?: (number|Long|null);

        /** LastQuote dayHighCount */
        dayHighCount?: (number|Long|null);

        /** LastQuote dayLowCount */
        dayLowCount?: (number|Long|null);

        /** LastQuote high */
        high?: (number|null);

        /** LastQuote low */
        low?: (number|null);

        /** LastQuote preMarketHigh */
        preMarketHigh?: (number|null);

        /** LastQuote preMarketLow */
        preMarketLow?: (number|null);

        /** LastQuote afterMarketHigh */
        afterMarketHigh?: (number|null);

        /** LastQuote afterMarketLow */
        afterMarketLow?: (number|null);

        /** LastQuote regularHigh */
        regularHigh?: (number|null);

        /** LastQuote regularLow */
        regularLow?: (number|null);
    }

    /** Represents a LastQuote. */
    class LastQuote implements ILastQuote {

        /**
         * Constructs a new LastQuote.
         * @param [properties] Properties to set
         */
        constructor(properties?: LastProtos.ILastQuote);

        /** LastQuote symbol. */
        public symbol: string;

        /** LastQuote currency. */
        public currency: string;

        /** LastQuote delayedMinutes. */
        public delayedMinutes: number;

        /** LastQuote sessionType. */
        public sessionType: QuoteProtos.SessionType;

        /** LastQuote error. */
        public error?: (LastProtos.IError|null);

        /** LastQuote name. */
        public name: string;

        /** LastQuote type. */
        public type: string;

        /** LastQuote exchange. */
        public exchange: string;

        /** LastQuote open. */
        public open: number;

        /** LastQuote dayHigh. */
        public dayHigh: number;

        /** LastQuote dayLow. */
        public dayLow: number;

        /** LastQuote close. */
        public close: number;

        /** LastQuote volume. */
        public volume: number;

        /** LastQuote vwap. */
        public vwap: number;

        /** LastQuote closeDate. */
        public closeDate: string;

        /** LastQuote bidPrice. */
        public bidPrice: number;

        /** LastQuote askPrice. */
        public askPrice: number;

        /** LastQuote lastTradePrice. */
        public lastTradePrice: number;

        /** LastQuote lastTradeTime. */
        public lastTradeTime: (number|Long);

        /** LastQuote previousClose. */
        public previousClose: number;

        /** LastQuote change. */
        public change: number;

        /** LastQuote percentChange. */
        public percentChange: number;

        /** LastQuote fiftyTwoWeekHigh. */
        public fiftyTwoWeekHigh: number;

        /** LastQuote fiftyTwoWeekLow. */
        public fiftyTwoWeekLow: number;

        /** LastQuote preMarketPrice. */
        public preMarketPrice: number;

        /** LastQuote preMarketVolume. */
        public preMarketVolume: (number|Long);

        /** LastQuote afterMarketPrice. */
        public afterMarketPrice: number;

        /** LastQuote afterMarketVolume. */
        public afterMarketVolume: (number|Long);

        /** LastQuote preMarketOpen. */
        public preMarketOpen: number;

        /** LastQuote preMarketOpenTime. */
        public preMarketOpenTime: (number|Long);

        /** LastQuote version. */
        public version: number;

        /** LastQuote tradeCount. */
        public tradeCount: (number|Long);

        /** LastQuote regularHighCount. */
        public regularHighCount: (number|Long);

        /** LastQuote regularLowCount. */
        public regularLowCount: (number|Long);

        /** LastQuote preMarketHighCount. */
        public preMarketHighCount: (number|Long);

        /** LastQuote preMarketLowCount. */
        public preMarketLowCount: (number|Long);

        /** LastQuote afterMarketHighCount. */
        public afterMarketHighCount: (number|Long);

        /** LastQuote afterMarketLowCount. */
        public afterMarketLowCount: (number|Long);

        /** LastQuote dayHighCount. */
        public dayHighCount: (number|Long);

        /** LastQuote dayLowCount. */
        public dayLowCount: (number|Long);

        /** LastQuote high. */
        public high: number;

        /** LastQuote low. */
        public low: number;

        /** LastQuote preMarketHigh. */
        public preMarketHigh: number;

        /** LastQuote preMarketLow. */
        public preMarketLow: number;

        /** LastQuote afterMarketHigh. */
        public afterMarketHigh: number;

        /** LastQuote afterMarketLow. */
        public afterMarketLow: number;

        /** LastQuote regularHigh. */
        public regularHigh: number;

        /** LastQuote regularLow. */
        public regularLow: number;

        /**
         * Creates a new LastQuote instance using the specified properties.
         * @param [properties] Properties to set
         * @returns LastQuote instance
         */
        public static create(properties?: LastProtos.ILastQuote): LastProtos.LastQuote;

        /**
         * Encodes the specified LastQuote message. Does not implicitly {@link LastProtos.LastQuote.verify|verify} messages.
         * @param message LastQuote message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: LastProtos.ILastQuote, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified LastQuote message, length delimited. Does not implicitly {@link LastProtos.LastQuote.verify|verify} messages.
         * @param message LastQuote message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: LastProtos.ILastQuote, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a LastQuote message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns LastQuote
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): LastProtos.LastQuote;

        /**
         * Decodes a LastQuote message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns LastQuote
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): LastProtos.LastQuote;

        /**
         * Verifies a LastQuote message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a LastQuote message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns LastQuote
         */
        public static fromObject(object: { [k: string]: any }): LastProtos.LastQuote;

        /**
         * Creates a plain object from a LastQuote message. Also converts values to other types if specified.
         * @param message LastQuote
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: LastProtos.LastQuote, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this LastQuote to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of an Error. */
    interface IError {

        /** Error code */
        code?: (LastProtos.Error.Code|null);

        /** Error message */
        message?: (string|null);
    }

    /** Represents an Error. */
    class Error implements IError {

        /**
         * Constructs a new Error.
         * @param [properties] Properties to set
         */
        constructor(properties?: LastProtos.IError);

        /** Error code. */
        public code: LastProtos.Error.Code;

        /** Error message. */
        public message: string;

        /**
         * Creates a new Error instance using the specified properties.
         * @param [properties] Properties to set
         * @returns Error instance
         */
        public static create(properties?: LastProtos.IError): LastProtos.Error;

        /**
         * Encodes the specified Error message. Does not implicitly {@link LastProtos.Error.verify|verify} messages.
         * @param message Error message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: LastProtos.IError, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified Error message, length delimited. Does not implicitly {@link LastProtos.Error.verify|verify} messages.
         * @param message Error message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: LastProtos.IError, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an Error message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Error
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): LastProtos.Error;

        /**
         * Decodes an Error message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns Error
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): LastProtos.Error;

        /**
         * Verifies an Error message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an Error message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Error
         */
        public static fromObject(object: { [k: string]: any }): LastProtos.Error;

        /**
         * Creates a plain object from an Error message. Also converts values to other types if specified.
         * @param message Error
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: LastProtos.Error, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Error to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    namespace Error {

        /** Code enum. */
        enum Code {
            ERROR = 0,
            UNKNOWN_SYMBOL = 1
        }
    }

    /** Properties of a MoversRequest. */
    interface IMoversRequest {

        /** MoversRequest from */
        from?: (string|null);

        /** MoversRequest to */
        to?: (string|null);

        /** MoversRequest asOf */
        asOf?: (string|null);

        /** MoversRequest session */
        session?: (string|null);

        /** MoversRequest indexFilter */
        indexFilter?: (string|null);

        /** MoversRequest screenerQuery */
        screenerQuery?: (string|null);

        /** MoversRequest maxResults */
        maxResults?: (number|null);

        /** MoversRequest sortField */
        sortField?: (LastProtos.MoversRequest.SortField|null);

        /** MoversRequest moversQuery */
        moversQuery?: (string|null);

        /** MoversRequest gainers */
        gainers?: (boolean|null);

        /** MoversRequest losers */
        losers?: (boolean|null);
    }

    /** Represents a MoversRequest. */
    class MoversRequest implements IMoversRequest {

        /**
         * Constructs a new MoversRequest.
         * @param [properties] Properties to set
         */
        constructor(properties?: LastProtos.IMoversRequest);

        /** MoversRequest from. */
        public from: string;

        /** MoversRequest to. */
        public to: string;

        /** MoversRequest asOf. */
        public asOf: string;

        /** MoversRequest session. */
        public session: string;

        /** MoversRequest indexFilter. */
        public indexFilter: string;

        /** MoversRequest screenerQuery. */
        public screenerQuery: string;

        /** MoversRequest maxResults. */
        public maxResults: number;

        /** MoversRequest sortField. */
        public sortField: LastProtos.MoversRequest.SortField;

        /** MoversRequest moversQuery. */
        public moversQuery: string;

        /** MoversRequest gainers. */
        public gainers: boolean;

        /** MoversRequest losers. */
        public losers: boolean;

        /**
         * Creates a new MoversRequest instance using the specified properties.
         * @param [properties] Properties to set
         * @returns MoversRequest instance
         */
        public static create(properties?: LastProtos.IMoversRequest): LastProtos.MoversRequest;

        /**
         * Encodes the specified MoversRequest message. Does not implicitly {@link LastProtos.MoversRequest.verify|verify} messages.
         * @param message MoversRequest message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: LastProtos.IMoversRequest, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified MoversRequest message, length delimited. Does not implicitly {@link LastProtos.MoversRequest.verify|verify} messages.
         * @param message MoversRequest message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: LastProtos.IMoversRequest, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a MoversRequest message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns MoversRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): LastProtos.MoversRequest;

        /**
         * Decodes a MoversRequest message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns MoversRequest
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): LastProtos.MoversRequest;

        /**
         * Verifies a MoversRequest message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a MoversRequest message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns MoversRequest
         */
        public static fromObject(object: { [k: string]: any }): LastProtos.MoversRequest;

        /**
         * Creates a plain object from a MoversRequest message. Also converts values to other types if specified.
         * @param message MoversRequest
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: LastProtos.MoversRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this MoversRequest to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    namespace MoversRequest {

        /** SortField enum. */
        enum SortField {
            CHANGE_PERCENT = 0,
            ATR_RATIO = 1
        }
    }

    /** Properties of a MoversResponse. */
    interface IMoversResponse {

        /** MoversResponse processingTimeMillis */
        processingTimeMillis?: (number|null);

        /** MoversResponse error */
        error?: (LastProtos.IError|null);

        /** MoversResponse result */
        result?: (LastProtos.IMoversResult|null);
    }

    /** Represents a MoversResponse. */
    class MoversResponse implements IMoversResponse {

        /**
         * Constructs a new MoversResponse.
         * @param [properties] Properties to set
         */
        constructor(properties?: LastProtos.IMoversResponse);

        /** MoversResponse processingTimeMillis. */
        public processingTimeMillis: number;

        /** MoversResponse error. */
        public error?: (LastProtos.IError|null);

        /** MoversResponse result. */
        public result?: (LastProtos.IMoversResult|null);

        /**
         * Creates a new MoversResponse instance using the specified properties.
         * @param [properties] Properties to set
         * @returns MoversResponse instance
         */
        public static create(properties?: LastProtos.IMoversResponse): LastProtos.MoversResponse;

        /**
         * Encodes the specified MoversResponse message. Does not implicitly {@link LastProtos.MoversResponse.verify|verify} messages.
         * @param message MoversResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: LastProtos.IMoversResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified MoversResponse message, length delimited. Does not implicitly {@link LastProtos.MoversResponse.verify|verify} messages.
         * @param message MoversResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: LastProtos.IMoversResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a MoversResponse message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns MoversResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): LastProtos.MoversResponse;

        /**
         * Decodes a MoversResponse message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns MoversResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): LastProtos.MoversResponse;

        /**
         * Verifies a MoversResponse message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a MoversResponse message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns MoversResponse
         */
        public static fromObject(object: { [k: string]: any }): LastProtos.MoversResponse;

        /**
         * Creates a plain object from a MoversResponse message. Also converts values to other types if specified.
         * @param message MoversResponse
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: LastProtos.MoversResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this MoversResponse to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a MoversResult. */
    interface IMoversResult {

        /** MoversResult fromDate */
        fromDate?: (string|null);

        /** MoversResult toDate */
        toDate?: (string|null);

        /** MoversResult snapFrom */
        snapFrom?: (string|null);

        /** MoversResult snapTo */
        snapTo?: (string|null);

        /** MoversResult usePreviousClose */
        usePreviousClose?: (boolean|null);

        /** MoversResult inReset */
        inReset?: (boolean|null);

        /** MoversResult gainers */
        gainers?: (LastProtos.MoversResult.IMover[]|null);

        /** MoversResult losers */
        losers?: (LastProtos.MoversResult.IMover[]|null);
    }

    /** Represents a MoversResult. */
    class MoversResult implements IMoversResult {

        /**
         * Constructs a new MoversResult.
         * @param [properties] Properties to set
         */
        constructor(properties?: LastProtos.IMoversResult);

        /** MoversResult fromDate. */
        public fromDate: string;

        /** MoversResult toDate. */
        public toDate: string;

        /** MoversResult snapFrom. */
        public snapFrom: string;

        /** MoversResult snapTo. */
        public snapTo: string;

        /** MoversResult usePreviousClose. */
        public usePreviousClose: boolean;

        /** MoversResult inReset. */
        public inReset: boolean;

        /** MoversResult gainers. */
        public gainers: LastProtos.MoversResult.IMover[];

        /** MoversResult losers. */
        public losers: LastProtos.MoversResult.IMover[];

        /**
         * Creates a new MoversResult instance using the specified properties.
         * @param [properties] Properties to set
         * @returns MoversResult instance
         */
        public static create(properties?: LastProtos.IMoversResult): LastProtos.MoversResult;

        /**
         * Encodes the specified MoversResult message. Does not implicitly {@link LastProtos.MoversResult.verify|verify} messages.
         * @param message MoversResult message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: LastProtos.IMoversResult, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified MoversResult message, length delimited. Does not implicitly {@link LastProtos.MoversResult.verify|verify} messages.
         * @param message MoversResult message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: LastProtos.IMoversResult, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a MoversResult message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns MoversResult
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): LastProtos.MoversResult;

        /**
         * Decodes a MoversResult message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns MoversResult
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): LastProtos.MoversResult;

        /**
         * Verifies a MoversResult message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a MoversResult message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns MoversResult
         */
        public static fromObject(object: { [k: string]: any }): LastProtos.MoversResult;

        /**
         * Creates a plain object from a MoversResult message. Also converts values to other types if specified.
         * @param message MoversResult
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: LastProtos.MoversResult, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this MoversResult to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    namespace MoversResult {

        /** Properties of a Mover. */
        interface IMover {

            /** Mover symbol */
            symbol?: (string|null);

            /** Mover price */
            price?: (number|null);

            /** Mover change */
            change?: (number|null);

            /** Mover changePercent */
            changePercent?: (number|null);

            /** Mover volume */
            volume?: (number|Long|null);

            /** Mover close */
            close?: (number|null);

            /** Mover atr */
            atr?: (number|null);

            /** Mover atrRatio */
            atrRatio?: (number|null);

            /** Mover companyName */
            companyName?: (string|null);

            /** Mover averageVolume */
            averageVolume?: (number|null);

            /** Mover previousClose */
            previousClose?: (number|null);

            /** Mover sector */
            sector?: (string|null);

            /** Mover marketCap */
            marketCap?: (number|Long|null);

            /** Mover gicsSectorName */
            gicsSectorName?: (string|null);

            /** Mover shareFloat */
            shareFloat?: (number|Long|null);

            /** Mover isin */
            isin?: (string|null);
        }

        /** Represents a Mover. */
        class Mover implements IMover {

            /**
             * Constructs a new Mover.
             * @param [properties] Properties to set
             */
            constructor(properties?: LastProtos.MoversResult.IMover);

            /** Mover symbol. */
            public symbol: string;

            /** Mover price. */
            public price: number;

            /** Mover change. */
            public change: number;

            /** Mover changePercent. */
            public changePercent: number;

            /** Mover volume. */
            public volume: (number|Long);

            /** Mover close. */
            public close: number;

            /** Mover atr. */
            public atr: number;

            /** Mover atrRatio. */
            public atrRatio: number;

            /** Mover companyName. */
            public companyName: string;

            /** Mover averageVolume. */
            public averageVolume: number;

            /** Mover previousClose. */
            public previousClose: number;

            /** Mover sector. */
            public sector: string;

            /** Mover marketCap. */
            public marketCap: (number|Long);

            /** Mover gicsSectorName. */
            public gicsSectorName: string;

            /** Mover shareFloat. */
            public shareFloat: (number|Long);

            /** Mover isin. */
            public isin: string;

            /**
             * Creates a new Mover instance using the specified properties.
             * @param [properties] Properties to set
             * @returns Mover instance
             */
            public static create(properties?: LastProtos.MoversResult.IMover): LastProtos.MoversResult.Mover;

            /**
             * Encodes the specified Mover message. Does not implicitly {@link LastProtos.MoversResult.Mover.verify|verify} messages.
             * @param message Mover message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: LastProtos.MoversResult.IMover, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified Mover message, length delimited. Does not implicitly {@link LastProtos.MoversResult.Mover.verify|verify} messages.
             * @param message Mover message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: LastProtos.MoversResult.IMover, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a Mover message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns Mover
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): LastProtos.MoversResult.Mover;

            /**
             * Decodes a Mover message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns Mover
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): LastProtos.MoversResult.Mover;

            /**
             * Verifies a Mover message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a Mover message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns Mover
             */
            public static fromObject(object: { [k: string]: any }): LastProtos.MoversResult.Mover;

            /**
             * Creates a plain object from a Mover message. Also converts values to other types if specified.
             * @param message Mover
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: LastProtos.MoversResult.Mover, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this Mover to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }
    }

    /** Properties of a QuoteSnapshot. */
    interface IQuoteSnapshot {

        /** QuoteSnapshot date */
        date?: (string|null);

        /** QuoteSnapshot quotes */
        quotes?: (QuoteProtos.IQuote[]|null);
    }

    /** Represents a QuoteSnapshot. */
    class QuoteSnapshot implements IQuoteSnapshot {

        /**
         * Constructs a new QuoteSnapshot.
         * @param [properties] Properties to set
         */
        constructor(properties?: LastProtos.IQuoteSnapshot);

        /** QuoteSnapshot date. */
        public date: string;

        /** QuoteSnapshot quotes. */
        public quotes: QuoteProtos.IQuote[];

        /**
         * Creates a new QuoteSnapshot instance using the specified properties.
         * @param [properties] Properties to set
         * @returns QuoteSnapshot instance
         */
        public static create(properties?: LastProtos.IQuoteSnapshot): LastProtos.QuoteSnapshot;

        /**
         * Encodes the specified QuoteSnapshot message. Does not implicitly {@link LastProtos.QuoteSnapshot.verify|verify} messages.
         * @param message QuoteSnapshot message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: LastProtos.IQuoteSnapshot, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified QuoteSnapshot message, length delimited. Does not implicitly {@link LastProtos.QuoteSnapshot.verify|verify} messages.
         * @param message QuoteSnapshot message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: LastProtos.IQuoteSnapshot, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a QuoteSnapshot message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns QuoteSnapshot
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): LastProtos.QuoteSnapshot;

        /**
         * Decodes a QuoteSnapshot message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns QuoteSnapshot
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): LastProtos.QuoteSnapshot;

        /**
         * Verifies a QuoteSnapshot message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a QuoteSnapshot message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns QuoteSnapshot
         */
        public static fromObject(object: { [k: string]: any }): LastProtos.QuoteSnapshot;

        /**
         * Creates a plain object from a QuoteSnapshot message. Also converts values to other types if specified.
         * @param message QuoteSnapshot
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: LastProtos.QuoteSnapshot, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this QuoteSnapshot to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of a QuoteSnapshotResponse. */
    interface IQuoteSnapshotResponse {

        /** QuoteSnapshotResponse snapshotData */
        snapshotData?: ({ [k: string]: LastProtos.IQuoteSnapshot }|null);
    }

    /** Represents a QuoteSnapshotResponse. */
    class QuoteSnapshotResponse implements IQuoteSnapshotResponse {

        /**
         * Constructs a new QuoteSnapshotResponse.
         * @param [properties] Properties to set
         */
        constructor(properties?: LastProtos.IQuoteSnapshotResponse);

        /** QuoteSnapshotResponse snapshotData. */
        public snapshotData: { [k: string]: LastProtos.IQuoteSnapshot };

        /**
         * Creates a new QuoteSnapshotResponse instance using the specified properties.
         * @param [properties] Properties to set
         * @returns QuoteSnapshotResponse instance
         */
        public static create(properties?: LastProtos.IQuoteSnapshotResponse): LastProtos.QuoteSnapshotResponse;

        /**
         * Encodes the specified QuoteSnapshotResponse message. Does not implicitly {@link LastProtos.QuoteSnapshotResponse.verify|verify} messages.
         * @param message QuoteSnapshotResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: LastProtos.IQuoteSnapshotResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified QuoteSnapshotResponse message, length delimited. Does not implicitly {@link LastProtos.QuoteSnapshotResponse.verify|verify} messages.
         * @param message QuoteSnapshotResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: LastProtos.IQuoteSnapshotResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a QuoteSnapshotResponse message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns QuoteSnapshotResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): LastProtos.QuoteSnapshotResponse;

        /**
         * Decodes a QuoteSnapshotResponse message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns QuoteSnapshotResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): LastProtos.QuoteSnapshotResponse;

        /**
         * Verifies a QuoteSnapshotResponse message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a QuoteSnapshotResponse message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns QuoteSnapshotResponse
         */
        public static fromObject(object: { [k: string]: any }): LastProtos.QuoteSnapshotResponse;

        /**
         * Creates a plain object from a QuoteSnapshotResponse message. Also converts values to other types if specified.
         * @param message QuoteSnapshotResponse
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: LastProtos.QuoteSnapshotResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this QuoteSnapshotResponse to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }
}

/** Namespace CommonProtos. */
export namespace CommonProtos {

    /** Properties of an Err. */
    interface IErr {

        /** Err code */
        code?: (number|null);

        /** Err message */
        message?: (string|null);

        /** Err detail */
        detail?: (string|null);
    }

    /** Represents an Err. */
    class Err implements IErr {

        /**
         * Constructs a new Err.
         * @param [properties] Properties to set
         */
        constructor(properties?: CommonProtos.IErr);

        /** Err code. */
        public code: number;

        /** Err message. */
        public message: string;

        /** Err detail. */
        public detail: string;

        /**
         * Creates a new Err instance using the specified properties.
         * @param [properties] Properties to set
         * @returns Err instance
         */
        public static create(properties?: CommonProtos.IErr): CommonProtos.Err;

        /**
         * Encodes the specified Err message. Does not implicitly {@link CommonProtos.Err.verify|verify} messages.
         * @param message Err message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: CommonProtos.IErr, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified Err message, length delimited. Does not implicitly {@link CommonProtos.Err.verify|verify} messages.
         * @param message Err message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: CommonProtos.IErr, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an Err message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Err
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): CommonProtos.Err;

        /**
         * Decodes an Err message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns Err
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): CommonProtos.Err;

        /**
         * Verifies an Err message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an Err message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Err
         */
        public static fromObject(object: { [k: string]: any }): CommonProtos.Err;

        /**
         * Creates a plain object from an Err message. Also converts values to other types if specified.
         * @param message Err
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: CommonProtos.Err, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Err to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of an ApiError. */
    interface IApiError {

        /** ApiError code */
        code?: (number|null);

        /** ApiError description */
        description?: (string|null);

        /** ApiError detail */
        detail?: (string|null);

        /** ApiError httpStatus */
        httpStatus?: (number|null);
    }

    /** Represents an ApiError. */
    class ApiError implements IApiError {

        /**
         * Constructs a new ApiError.
         * @param [properties] Properties to set
         */
        constructor(properties?: CommonProtos.IApiError);

        /** ApiError code. */
        public code: number;

        /** ApiError description. */
        public description: string;

        /** ApiError detail. */
        public detail: string;

        /** ApiError httpStatus. */
        public httpStatus: number;

        /**
         * Creates a new ApiError instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ApiError instance
         */
        public static create(properties?: CommonProtos.IApiError): CommonProtos.ApiError;

        /**
         * Encodes the specified ApiError message. Does not implicitly {@link CommonProtos.ApiError.verify|verify} messages.
         * @param message ApiError message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: CommonProtos.IApiError, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ApiError message, length delimited. Does not implicitly {@link CommonProtos.ApiError.verify|verify} messages.
         * @param message ApiError message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: CommonProtos.IApiError, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an ApiError message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ApiError
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): CommonProtos.ApiError;

        /**
         * Decodes an ApiError message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ApiError
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): CommonProtos.ApiError;

        /**
         * Verifies an ApiError message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an ApiError message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ApiError
         */
        public static fromObject(object: { [k: string]: any }): CommonProtos.ApiError;

        /**
         * Creates a plain object from an ApiError message. Also converts values to other types if specified.
         * @param message ApiError
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: CommonProtos.ApiError, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ApiError to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }

    /** Properties of an ApiErrorResponse. */
    interface IApiErrorResponse {

        /** ApiErrorResponse error */
        error?: (CommonProtos.IApiError|null);
    }

    /** Represents an ApiErrorResponse. */
    class ApiErrorResponse implements IApiErrorResponse {

        /**
         * Constructs a new ApiErrorResponse.
         * @param [properties] Properties to set
         */
        constructor(properties?: CommonProtos.IApiErrorResponse);

        /** ApiErrorResponse error. */
        public error?: (CommonProtos.IApiError|null);

        /**
         * Creates a new ApiErrorResponse instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ApiErrorResponse instance
         */
        public static create(properties?: CommonProtos.IApiErrorResponse): CommonProtos.ApiErrorResponse;

        /**
         * Encodes the specified ApiErrorResponse message. Does not implicitly {@link CommonProtos.ApiErrorResponse.verify|verify} messages.
         * @param message ApiErrorResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: CommonProtos.IApiErrorResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ApiErrorResponse message, length delimited. Does not implicitly {@link CommonProtos.ApiErrorResponse.verify|verify} messages.
         * @param message ApiErrorResponse message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: CommonProtos.IApiErrorResponse, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an ApiErrorResponse message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ApiErrorResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): CommonProtos.ApiErrorResponse;

        /**
         * Decodes an ApiErrorResponse message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ApiErrorResponse
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): CommonProtos.ApiErrorResponse;

        /**
         * Verifies an ApiErrorResponse message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an ApiErrorResponse message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ApiErrorResponse
         */
        public static fromObject(object: { [k: string]: any }): CommonProtos.ApiErrorResponse;

        /**
         * Creates a plain object from an ApiErrorResponse message. Also converts values to other types if specified.
         * @param message ApiErrorResponse
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: CommonProtos.ApiErrorResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ApiErrorResponse to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };
    }
}
