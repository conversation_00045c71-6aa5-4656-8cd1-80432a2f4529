import React, { Suspense } from 'react';
import { useTranslation } from 'react-i18next';
import subYears from 'date-fns/subYears';

import { formatPercentage, numberShorthand } from '@benzinga/utils';
import { InsiderTradesManager, InsidersFilingResult } from '@benzinga/insider-trades-manager';
import { News, StoryObject } from '@benzinga/basic-news-manager';
import { SectionTitle, StatBoxesContainer } from '@benzinga/core-ui';
import { formatPrice } from '@benzinga/calendars';

import { getQuoteData, getQuoteNews } from '../../api/quote';
import { getGlobalSession } from '../../api/session';
import getQuoteV2PageData, {
  QuoteV2PageProps,
  getQuoteFeedTypeByLocale,
  getQuoteNotFoundData,
} from '../../../src/quoteV2Utils';

import QuoteCalendar from '../../../src/components/Quote/QuoteCalendar';
import { QuoteLayout } from '../../../src/components/Quote/V2/QuoteLayout';
import { InsiderTradesFAQ } from '../../../src/components/Quote/FAQ/InsiderTradesFAQ';
import DrilldownFooter from '../../../src/components/Quote/V2/DrilldownFooter';
import { QuoteNewsFeed } from '../../../src/components/Quote/V2/QuoteNewsFeed';
import { StatBox } from '../../../src/components/Quote/V2/StatBox';
import i18n, { translate, setLanguageByHost, LocaleType } from '@benzinga/translate';
import { QuoteAltSuggestions } from '../../../src/components/Quote/QuoteAltSuggestions';
import { getInsiderTradesSummary, InsiderTradesSummary } from '../../api/quote/[ticker]/insider-trades';
import { DateTime } from 'luxon';
import { sanitizeHTML } from '@benzinga/frontend-utils';

const RaptiveAdPlaceholder = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.RaptiveAdPlaceholder };
  }),
);

interface InsiderTradesQuoteV2PageProps extends QuoteV2PageProps {
  insiderOwnershipSummary: InsiderOwnership;
  insiderTradesSummary: InsiderTradesSummary | null;
  insiderNews: (StoryObject | News)[];
}

interface InsiderOwnership {
  insiderPercentOwned?: number;
  insiderSharesOwned?: number;
  institutionPercentHeld?: number;
  institutionSharesHeld?: number;
  shareFloat?: number;
  sharesOutstanding?: number;
}

interface DateRange {
  date_to: Date;
  date_from: Date;
}

const QuoteInsiderTradesPage: React.FC<InsiderTradesQuoteV2PageProps> = ({
  activeTab,
  advertiserProfile,
  altSuggestions,
  insiderNews,
  insiderOwnershipSummary,
  insiderTradesSummary,
  isAdvertiser,
  isETF,
  metaProps,
  peersChartData,
  profile,
  showGovLink,
  symbol,
}) => {
  const { t } = useTranslation(['quote', 'common'], { i18n });

  if (altSuggestions) {
    return <QuoteAltSuggestions {...altSuggestions} symbol={symbol} />;
  }

  const formatDate = (date: string) =>
    DateTime.fromFormat(date, 'yyyy-MM-dd').setLocale(i18n.language).toFormat('MMMM d, yyyy');

  const companyName = profile.richQuoteData?.companyStandardName || symbol;

  const generateCompanyInsiderActivity = () => {
    if (!insiderTradesSummary?.lastThirtyDaysFilingsSummary) {
      return t('Quote.InsiderTrades.what-are-company-insiders-doing-no-data');
    }
    const totalSharesPurchased = insiderTradesSummary.lastThirtyDaysFilingsSummary.totalSharesPurchased
      ? numberShorthand(insiderTradesSummary.lastThirtyDaysFilingsSummary.totalSharesPurchased, 2)
      : '-';
    const totalSharesPurchasedValue = insiderTradesSummary.lastThirtyDaysFilingsSummary.totalSharesPurchasedValue
      ? `$${numberShorthand(insiderTradesSummary.lastThirtyDaysFilingsSummary.totalSharesPurchasedValue, 2)}`
      : '-';
    const totalSharesSoldValue = insiderTradesSummary.lastThirtyDaysFilingsSummary.totalSharesSoldValue
      ? `$${numberShorthand(insiderTradesSummary.lastThirtyDaysFilingsSummary.totalSharesSoldValue, 2)}`
      : '-';
    const totalSharesSold = insiderTradesSummary.lastThirtyDaysFilingsSummary.totalSharesSold
      ? numberShorthand(insiderTradesSummary.lastThirtyDaysFilingsSummary.totalSharesSold, 2)
      : '-';

    if (totalSharesPurchased === '-' && totalSharesSold) {
      return t('Quote.InsiderTrades.what-are-company-insiders-doing-description-no-purchases', {
        companyName,
        totalSharesPurchased,
        totalSharesPurchasedValue,
        totalSharesSold,
        totalSharesSoldValue,
      });
    } else if (totalSharesSold === '-' && totalSharesPurchased) {
      return t('Quote.InsiderTrades.what-are-company-insiders-doing-description-no-sold', {
        companyName,
        totalSharesPurchased,
        totalSharesPurchasedValue,
        totalSharesSold,
        totalSharesSoldValue,
      });
    }
    return t('Quote.InsiderTrades.what-are-company-insiders-doing-description', {
      companyName,
      totalSharesPurchased,
      totalSharesPurchasedValue,
      totalSharesSold,
      totalSharesSoldValue,
    });
  };

  const generateNotableRecentTrades = (): string[] | null => {
    if (!insiderTradesSummary?.largestThreeTransactionsFromLastNinetyDays?.length) {
      return null;
    }

    const notableTrades: string[] = insiderTradesSummary.largestThreeTransactionsFromLastNinetyDays
      .map(trade => {
        const tradeType = trade.trade_status === 'SELL' ? 'sold' : 'purchased';
        const tradeDate = trade.signature.date ? formatDate(trade.signature.date) : '-';
        const shareValue = trade.traded_share_price ? formatPrice(trade.traded_share_price) : '-';
        const shareCount = trade.traded_shares ? numberShorthand(Math.abs(trade.traded_shares), 2, true) : null;
        if (!shareCount) return null;
        return t('Quote.InsiderTrades.notable-recent-insider-trades-item', {
          companyName,
          date: tradeDate,
          name: trade.insider_names,
          shareCount,
          shareValue,
          title: trade.insider_titles_unique || '-',
          tradeDate,
          tradeType,
        });
      })
      .filter((item): item is string => Boolean(item));
    return notableTrades;
  };

  const companyInsiderActivity = generateCompanyInsiderActivity();
  const notableRecentTrades = generateNotableRecentTrades();

  return (
    <QuoteLayout
      advertiserProfile={advertiserProfile}
      defaultPath={activeTab}
      isAdvertiser={isAdvertiser}
      profile={profile}
      showGovLink={showGovLink}
      symbol={symbol}
    >
      {/* Beef this up. */}
      <div className="main-div pt-0 p-4">
        <div className="flex flex-col gap-2 mb-8">
          <SectionTitle level={1} uppercase={false}>
            {metaProps.title}
          </SectionTitle>
          <p className="section-description">
            {t('Quote.Paragraphs.insider-trades', { stockName: profile.richQuoteData?.name })}
          </p>
          <SectionTitle capitalize={false} level={2} uppercase={false}>
            {t('Quote.InsiderTrades.what-is-insider-activity')}
          </SectionTitle>
          <p className="section-description">{t('Quote.InsiderTrades.what-is-insider-activity-description')}</p>
          <SectionTitle capitalize={false} level={2} uppercase={false}>
            {t('Quote.InsiderTrades.what-are-company-insiders-doing', {
              companyName,
            })}
          </SectionTitle>
          <p className="section-description" dangerouslySetInnerHTML={{ __html: companyInsiderActivity }} />
          <SectionTitle capitalize={false} level={2} uppercase={false}>
            <span className="mb-2 inline-block">{t('Quote.InsiderTrades.notable-recent-insider-trades-title')}</span>
          </SectionTitle>
          <div>
            {Array.isArray(notableRecentTrades) ? (
              <ul className="md:ml-8 list-disc">
                {notableRecentTrades.map((text, index) => (
                  <li className="section-description" dangerouslySetInnerHTML={{ __html: text }} key={index} />
                ))}
              </ul>
            ) : (
              <p className="section-description">{t('Quote.InsiderTrades.notable-recent-insider-trades-no-data')}</p>
            )}
          </div>
        </div>
        <SectionTitle level={2} uppercase={false}>
          <span className="mb-2 inline-block">
            {t('Quote.InsiderTrades.insider-summary-box-title', {
              companyName,
            })}
          </span>
        </SectionTitle>
        <InsiderSummaryBox insiderOwnershipSummary={insiderOwnershipSummary} />
      </div>
      <div className="main-div flex flex-col gap-y-8">
        <QuoteCalendar
          calendar="insider-trades"
          hideFilters={false}
          initialData={insiderTradesSummary?.filings}
          symbol={symbol}
        />
        <Suspense fallback={<div />}>
          <RaptiveAdPlaceholder className="w-[300px] min-w-[300px]" onlyMobile={true} type="content-small" />
        </Suspense>
        <DrilldownFooter
          advertiserProfile={advertiserProfile}
          isAdvertiser={isAdvertiser}
          isETF={isETF}
          leftSection={
            <>
              <SectionTitle level={3} size="2xl">
                {t('Quote.FAQ.faq')}
              </SectionTitle>
              <InsiderTradesFAQ />
              <p>
                {/* eslint-disable-next-line @next/next/no-html-link-for-pages */}
                {t('Buttons.browse', { ns: 'common' })}{' '}
                <a href="/sec/insider-trades/">{t('Quote.InsiderTrades.insider-trades')}</a>{' '}
                {t('Quote.Helpers.on-all-stocks')}
              </p>
              <InsiderNews news={insiderNews} symbol={symbol} />
            </>
          }
          peers={profile.quotes}
          peersChartData={peersChartData}
          symbol={symbol}
          type={profile.schedule.type}
        />
      </div>
    </QuoteLayout>
  );
};

const InsiderSummaryBox: React.FC<{ insiderOwnershipSummary: InsiderOwnership }> = ({ insiderOwnershipSummary }) => {
  const { t } = useTranslation('quote', { i18n });

  const {
    insiderPercentOwned,
    insiderSharesOwned,
    institutionPercentHeld,
    institutionSharesHeld,
    shareFloat,
    sharesOutstanding,
  } = insiderOwnershipSummary;
  return (
    <>
      <StatBoxesContainer>
        {sharesOutstanding && (
          <StatBox
            title={t('Quote.InsiderTrades.shares-outstanding')}
            value={`${numberShorthand(sharesOutstanding)}`}
          />
        )}
        {shareFloat && (
          <StatBox title={t('Quote.InsiderTrades.shares-float')} value={`${formatPercentage(shareFloat)}%`} />
        )}
        {insiderSharesOwned && (
          <StatBox title={t('Quote.InsiderTrades.insider-shares-owned')} value={numberShorthand(insiderSharesOwned)} />
        )}
        {insiderPercentOwned && (
          <StatBox
            title={t('Quote.InsiderTrades.insider-percentage-owned')}
            value={`${formatPercentage(insiderPercentOwned * 100)}%`}
          />
        )}
        {institutionSharesHeld && (
          <StatBox
            title={t('Quote.InsiderTrades.institutional-shared-held')}
            value={numberShorthand(institutionSharesHeld)}
          />
        )}
        {institutionPercentHeld && (
          <StatBox
            title={t('Quote.InsiderTrades.institutional-percentage-held')}
            value={`${formatPercentage(institutionPercentHeld * 100)}%`}
          />
        )}
      </StatBoxesContainer>
    </>
  );
};

const InsiderNews = ({ news, symbol }) => {
  return (
    <div>
      <div className="flex items-center whitespace-nowrap mr-4 my-8">
        <SectionTitle className="leading-none" level={3}>
          {translate('Quote.InsiderTrades.insider-trading-news', { ns: 'quote', symbol })}
        </SectionTitle>
      </div>
      <QuoteNewsFeed nodes={news} query={{ channels: ['62'], pageSize: 5, tickers: [symbol] }} seeMoreEnabled={true} />
    </div>
  );
};

export default QuoteInsiderTradesPage;

export const getServerSideProps = async ({ query: { ticker }, req }) => {
  const translations = await setLanguageByHost(req.headers.host ?? '', ['quote', 'common']);
  const symbol: string = sanitizeHTML(ticker) || '';
  const activeTab = 'insider-trades';

  const profile = await getQuoteData(symbol);

  const dateRange = {
    date_from: subYears(new Date(), 5),
    date_to: new Date(),
  };

  const session = getGlobalSession();

  const insiderTradesResponse = await getInsiderTradesSummary(symbol, dateRange);

  const insiderNews = await getQuoteNews(session, {
    channels: ['62'],
    pageSize: 5,
    symbols: [symbol],
  });

  const insiderOwnershipSummary = {
    insiderPercentOwned: profile.fundamentals?.ownership?.[0]?.insiderPercentOwned || null,
    insiderSharesOwned: profile.fundamentals?.ownership?.[0]?.insiderSharesOwned || null,
    institutionPercentHeld: profile.fundamentals?.ownership?.[0]?.institutionPercentHeld || null,
    institutionSharesHeld: profile.fundamentals?.ownership?.[0]?.institutionSharesHeld || null,
    sharesFloat: profile.fundamentals?.ownership?.[0]?.shareFloat || null,
    sharesOutstanding: profile.fundamentals?.ownership?.[0]?.sharesOutstanding || null,
  } as InsiderOwnership;

  const pageData = (await getQuoteV2PageData(
    symbol,
    activeTab,
    getQuoteFeedTypeByLocale(i18n.language as LocaleType),
    req?.headers?.host,
  )) as { props: InsiderTradesQuoteV2PageProps };
  pageData.props.insiderOwnershipSummary = insiderOwnershipSummary;
  pageData.props.insiderTradesSummary = insiderTradesResponse ?? null;
  pageData.props.insiderNews = insiderNews ?? [];

  if (!insiderTradesResponse?.filings || insiderTradesResponse?.filings.length < 5) {
    pageData.props.metaProps.robots = 'noindex, nofollow';
  }

  if (translations) {
    pageData.props.metaProps.translations = translations;
  }

  return pageData;
};
