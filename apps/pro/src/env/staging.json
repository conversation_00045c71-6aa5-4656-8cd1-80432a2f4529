{"ADV_NEWSFEED_URL": "wss://api.zingbot.bz/api/v3/news/advanced/ws", "API_ROOT": "https://api.zingbot.bz", "CONTENT_ADDR": "wss://pro-backend.zingbot.bz/content/", "DATAAPI_ROOT": "https://data-api-pro.zingbot.bz/rest/", "DATAAPI_KEY": "jmJCeySbOuL2TwLQCeJttQyqYjzPrbLS", "IAM_ROOT": "https://accounts.zingbot.bz", "NCHAN_ADDR": "https://pro-live.zingbot.bz/sub", "PRO_API": "https://accounts.zingbot.bz/api", "QUOTE_ADDR": "wss://pro-quote-v2.zingbot.bz/quote/", "SERVICES_ROOT": "https://bz.zingbot.bz", "CALENDAR_KEY": "135d6bbcf5cb4a51861be074bb64f13a", "LOG_ROCKET_API_KEY": "b1ewvx/benzinga-pro", "INCLUDE_LOGROCKET": "false", "MIXPANEL_KEY": "8244f264fe778d0a97d1910e5c6dc27", "SEGMENT_KEY": "ahec8GwIYfImGvAXWsHoP6eVEBu1AxV2", "SENTRY_DSN": "https://<EMAIL>/60963", "SQUAWKV3_ADDR": "wss://squawk-lb.zingbot.bz/squawk", "SQUAWK_ADDR": "wss://squawk-lb.zingbot.bz/ws/v4/squawk", "STRIPE_PUBLISHABLE_KEY": "pk_test_cKYZbepiuYLumURkRervARLQ", "QUOTE_STORE_API_ROOT": "https://data.zingbot.bz/quote-store/api", "SIGNALS_RESTFUL_ADDR": "https://data-api-pro.zingbot.bz/signals/api", "SIGNALS_SOCKET_ADDR": "wss://data-api-pro.zingbot.bz/signals/ws", "TRADE_IT_API_ENDPOINT": "https://ems.qa.tradingticket.com/api/v2", "TRADE_IT_API_KEY": "df76fd9083b9431cb508dea9509e811c", "TRADE_IT_ORIGIN_URL": "https://www.trade.it", "CHART_SOCK": "wss://pro-charts.zingbot.bz/quotes/socket", "CHART_API": "https://pro-charts.zingbot.bz/tradingview", "CHART_TRADINGVIEW": "https://pro-charts.zingbot.bz/tradingview", "STREAM_CHAT_KEY": "dydh4rhwc8kr", "SCANNER_ADDR": "https://data-api-pro.zingbot.bz/scanner", "SCANNER_SOCK": "wss://data-api-pro.zingbot.bz/scanner/socket", "SCANNER_KEY": "jmJCeySbOuL2TwLQCeJttQyqYjzPrbLS", "INSIDERS_ADDR": "https://bz.zingbot.bz/sec/insider-trades/api", "FASTLINK_API_URL": "https://fl4.preprod.yodlee.com/authenticate/USDevexPreProd3-33/fastlink?channelAppName=usdevexpreprod3", "FASTLINK_CONFIG_NAME": "Aggregation", "LOGIN": {"IFRAME_URL": "https://zingbot.bz/login/?phone=required", "NEXT_URL": "/dashboard", "REGISTER_NEXT_URL": "/dashboard/upsell"}, "VAPID_PUBLIC_KEY": "BKSR96YS3EqtjJbv-DC5dr376oZE5VmCzSzaIGwMDrTLSpGuZmL36vEEFjLM9WUYcIO1mO8PXlxwsp8KxNEqZU4", "WEB_PUSH_ROOT": "https://bz.zingbot.bz/web-push", "WIDGET_GLOBAL_PARAMETERS": {"stream": {"defaultChannel": "35a74bc1-d308-4c2f-8ffa-13a4c3d82091"}}, "SESSION_ENV": {"benzinga-authentication": {"googleClientId": "*************-g29rukffvi2pdjrg9800am4a3591jfi5.apps.googleusercontent.com", "url": "https://accounts.zingbot.bz/"}, "benzinga-autocomplete": {"symbolsKey": "jmJCeySbOuL2TwLQCeJttQyqYjzPrbLS", "fundsUrl": "https://bz.zingbot.bz", "symbolsUrl": "https://data-api-pro.zingbot.bz/rest/"}, "benzinga-calendar": {"url": "https://bz.zingbot.bz/"}, "benzinga-calendar-guidance": {"url": "https://api.zingbot.bz/api/v2.1/calendar/guidance"}, "benzinga-calendar-conference-calls": {"url": "https://api.zingbot.bz/api/v2.1/calendar/conference-calls"}, "benzinga-calendar-ipo": {"url": "https://api.zingbot.bz/api/v2.1/calendar/ipos"}, "benzinga-calendar-ma": {"url": "https://api.zingbot.bz/api/v2.1/calendar/ma"}, "benzinga-calendar-offerings": {"url": "https://api.zingbot.bz/api/v2.1/calendar/offerings"}, "benzinga-calendar-option-activity": {"url": "https://api.zingbot.bz/api/v1/signal/option_activity"}, "benzinga-calendar-ratings": {"url": "https://api.zingbot.bz/api/v2.1/calendar/ratings"}, "benzinga-calendar-retail": {"url": "https://api.zingbot.bz/api/v2.1/calendar/retail"}, "benzinga-calendar-sec": {"url": "https://api.zingbot.bz/api/v2/calendar/sec"}, "benzinga-calendar-splits": {"url": "https://api.zingbot.bz/api/v2.1/calendar/splits"}, "benzinga-calendar-squawk": {"url": "https://api.zingbot.bz/api/v1/calendar/squawk"}, "benzinga-calendar-fda": {"url": "https://api.zingbot.bz/api/v2.1/calendar/fda"}, "benzinga-calendar-economics": {"url": "https://api.zingbot.bz/api/v2.1/calendar/economics"}, "benzinga-calendar-earnings": {"url": "https://api.zingbot.bz/api/v2.1/calendar/earnings"}, "benzinga-calendar-dividends": {"url": "https://api.zingbot.bz/api/v2.1/calendar/dividends"}, "benzinga-calendar-short-interest": {"token": "2RiuR92vjytxS8r93w3c8WTpGSd3y9Gk"}, "benzinga-chart": {"key": "jmJCeySbOuL2TwLQCeJttQyqYjzPrbLS", "url": "https://data-api-pro.zingbot.bz/rest/"}, "benzinga-chart-config": {"url": "https://pro-charts.zingbot.bz/tradingview/"}, "benzinga-chat": {"realmUuid": "33fefa02-7c42-4686-8222-cf3cde23569f", "streamKey": "dydh4rhwc8kr", "url": "https://accounts.zingbot.bz/"}, "benzinga-notification": {"url": "https://api.zingbot.bz/", "socketUrl": "wss://api.zingbot.bz/api/v1.1/notification/ws", "rootUrl": "https://pro.zingbot.bz/", "vapidPublicKey": "", "webPushRoot": "https://pro.zingbot.bz/web-push"}, "benzinga-layout": {"url": "https://accounts.zingbot.bz/"}, "benzinga-advanced-news": {"contentKey": "TkWiPBzZ5YdSlzmEblWB8eTljd5kXvmb6zNy", "contentUrl": "https://content-internal.benzinga.com/content/", "socketUrl": "wss://api.zingbot.bz/api/v3/news/advanced/ws", "url": "https://bz.zingbot.bz/"}, "benzinga-news-alerts": {"url": "https://api.zingbot.bz/"}, "benzinga-movers": {"key": "jmJCeySbOuL2TwLQCeJttQyqYjzPrbLS", "url": "https://data-api-pro.zingbot.bz/rest/"}, "benzinga-notes": {"url": "https://accounts.zingbot.bz/"}, "benzinga-permissions": {"url": "https://accounts.zingbot.bz/"}, "benzinga-price-alerts": {"url": "https://data-api-pro.zingbot.bz/rest/v1/"}, "benzinga-quotes": {"socketUrl": "wss://pro-quote-v2.zingbot.bz/quote/", "symbolsKey": "jmJCeySbOuL2TwLQCeJttQyqYjzPrbLS", "symbolsUrl": "https://data-api-pro.zingbot.bz/rest/", "tickersKey": "jmJCeySbOuL2TwLQCeJttQyqYjzPrbLS", "tickersUrl": "https://data-api-pro.zingbot.bz/rest/"}, "benzinga-securities": {"key": "anBvLgmzgKHJhQdQQzBe24yKFpHwcBJN", "url": "https://data-api-pro.zingbot.bz/"}, "benzinga-shop": {"url": "https://accounts.zingbot.bz/api/v1/"}, "benzinga-scanner": {"key": "jmJCeySbOuL2TwLQCeJttQyqYjzPrbLS", "url": "https://data-api-pro.zingbot.bz/rest/v2/scanner/us/equity/scan", "socketUrl": "wss://data-api-pro.zingbot.bz/ws/v2/scanner/us/equity"}, "benzinga-signals": {"key": "jmJCeySbOuL2TwLQCeJttQyqYjzPrbLS", "restfulUrl": "https://data-api-pro.zingbot.bz/signals/api/signals", "socketUrl": "wss://data-api-pro.zingbot.bz/signals/ws"}, "benzinga-squawk": {"url": "wss://squawk-lb.zingbot.bz/ws/v4/squawk"}, "benzinga-subscriptions": {"url": "https://accounts.zingbot.bz/"}, "benzinga-text-to-speech": {"key": "jmJCeySbOuL2TwLQCeJttQyqYjzPrbLS", "url": "https://data-api-pro.zingbot.bz/rest/"}, "benzinga-watchlist": {"legacyUrl": "https://bz.zingbot.bz/", "url": "https://api.zingbot.bz/"}, "benzinga-tracking": {"segmentKey": "ahec8GwIYfImGvAXWsHoP6eVEBu1AxV2"}, "benzinga-product-notifications": {"apiKey": "O4fEmFjaFEkfstJ4UczDumMDTTzaFC021-HcwUqRcxU"}}}