'use client';

import styled from '@benzinga/themetron';

export const Container = styled.div`
  background-color: #f2f8ff;

  &.services-page {
    .main-content-container {
      max-width: 1000px;
    }
    .layout-header {
      overflow: visible;
      margin-bottom: 440px;
    }
    .layout-content-container {
      padding-left: 0.5rem;
      padding-right: 0.5rem;
      margin-bottom: 0;
      padding-bottom: 2rem;
    }
    .services-hero-section {
      background-color: #192940;
      display: flex;
      flex-direction: column;
      position: relative;
      text-align: center;
      color: white;
      min-height: 740px;
      justify-content: space-around;

      .services-hero-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 1;
      }

      .services-hero-content {
        z-index: 2;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0 2rem;
        margin: 3rem 0;
        gap: 2rem;

        h1 {
          font-size: 34px;
          line-height: 48px;
          color: white;
          max-width: 620px;
        }

        div {
          margin-top: 0.5rem;
          color: #f2f8ff;
          text-transform: uppercase;
          font-weight: bold;
          font-size: 18px;
          line-height: 32px;
          border: 1px solid #ceddf23d;
          padding: 4px 16px;
          border-radius: 12px;
        }

        p {
          color: #99aecc;
          max-width: 510px;
          font-size: 16px;
          line-height: 26px;
        }
      }
    }
    .ads-container {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }
    .service-cards-container {
      height: 80px;
      max-width: 1000px;
      margin: 0 auto;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 1rem;
    }
    @media (max-width: 1000px) {
      .service-cards-container {
        margin: 0 0.75rem;
      }
    }
    @media (max-width: 800px) {
      .layout-header {
        margin-bottom: 1470px;
        .services-hero-content {
          h1 {
            font-size: 24px;
            line-height: 32px;
            min-height: 128px;
          }
          p {
            min-height: 78px;
          }
        }
      }
      .service-cards-container {
        grid-template-columns: 1fr;
      }
    }
  }
`;
