import React from 'react';
import { useTranslation } from 'react-i18next';
import i18n, { LocaleType, setLanguageByHost } from '@benzinga/translate';

import { getQuoteData } from '../../api/quote';
import { ShortInterestSummary, getShortInterestSummary } from '../../api/quote/[ticker]/short-interest';

import { SectionTitle } from '@benzinga/core-ui';

import QuoteCalendar from '../../../src/components/Quote/QuoteCalendar';
import { QuoteLayout } from '../../../src/components/Quote/V2/QuoteLayout';
import DrilldownFooter from '../../../src/components/Quote/V2/DrilldownFooter';
import ShortInterestSummaryBox from '../../../src/components/Quote/V2/ShortInterestSummaryBox';
import getQuoteV2PageData, {
  QuoteV2PageProps,
  getQuoteFeedTypeByLocale,
  getQuoteNotFoundData,
} from '../../../src/quoteV2Utils';
import { QuoteAltSuggestions } from '../../../src/components/Quote/QuoteAltSuggestions';
import { numberShorthand } from '@benzinga/utils';
import { sanitizeHTML } from '@benzinga/frontend-utils';

interface ShortInterestQuoteV2PageProps extends QuoteV2PageProps {
  shortInterestSummary: ShortInterestSummary;
}

const QuoteShortInterestPage: React.FC<ShortInterestQuoteV2PageProps> = ({
  activeTab,
  advertiserProfile,
  altSuggestions,
  isAdvertiser,
  isETF,
  metaProps,
  peersChartData,
  profile,
  shortInterestSummary,
  showGovLink,
  symbol,
}) => {
  const { t } = useTranslation(['quote', 'common'], { i18n });

  if (altSuggestions) {
    return <QuoteAltSuggestions {...altSuggestions} symbol={symbol} />;
  }

  const mostRecentShortInterest = shortInterestSummary?.shortInterest?.[0];

  const generateShortInterestPerformanceDescription = (): string | null => {
    const name = profile.richQuoteData?.companyStandardName || symbol;
    const shortPercentOfFloat = mostRecentShortInterest?.shortPercentOfFloat || '-';
    const shortInterestPriorMonthAsNumber = parseInt(mostRecentShortInterest?.shortPriorMo);
    const shortInterestPriorMonth = shortInterestPriorMonthAsNumber
      ? numberShorthand(shortInterestPriorMonthAsNumber, 2)
      : '-';
    const shortInterestAsNumber = parseInt(mostRecentShortInterest?.totalShortInterest);
    const shortInterest = shortInterestAsNumber ? numberShorthand(shortInterestAsNumber, 2) : '-';

    if (!shortInterestPriorMonthAsNumber || !shortInterestAsNumber) {
      return null;
    }

    const shortInterestTrend =
      shortInterestPriorMonthAsNumber && shortInterestAsNumber
        ? shortInterestAsNumber > shortInterestPriorMonthAsNumber
          ? t('Quote.ShortInterest.increased')
          : t('Quote.ShortInterest.decreased')
        : '-';
    const shortInterestRise =
      shortInterestPriorMonthAsNumber && shortInterestAsNumber
        ? shortInterestAsNumber > shortInterestPriorMonthAsNumber
          ? t('Quote.ShortInterest.rising')
          : t('Quote.ShortInterest.falling')
        : '-';

    const ticker = profile.richQuoteData?.bzExchange ? `${profile.richQuoteData?.bzExchange}:${symbol}` : symbol;
    return t('Quote.ShortInterest.short-interest-performance', {
      companyName: name,
      rise: shortInterestRise,
      shortInterest,
      shortInterestPriorMonth,
      shortPercentOfFloat,
      ticker,
      trend: shortInterestTrend,
    });
  };

  const generateShortInterestVolumeDescription = (): string | null => {
    const volume = mostRecentShortInterest?.averageDailyVolume
      ? numberShorthand(mostRecentShortInterest?.averageDailyVolume, 2)
      : null;
    const daysToCover = mostRecentShortInterest?.daysToCover || null;

    if (!volume && !daysToCover) {
      return null;
    }

    return t('Quote.ShortInterest.short-interest-volume', {
      daysToCover: daysToCover || '-',
      volume: volume || '-',
    });
  };

  const shortInterestPerformanceDescription = generateShortInterestPerformanceDescription();
  const shortInterestVolumeDescription = generateShortInterestVolumeDescription();

  return (
    <QuoteLayout
      advertiserProfile={advertiserProfile}
      defaultPath={activeTab}
      isAdvertiser={isAdvertiser}
      profile={profile}
      showGovLink={showGovLink}
      symbol={symbol}
    >
      {/* Beef this up. */}
      <div className="main-div pt-0 flex flex-col gap-y-8">
        <div className="flex flex-col gap-2">
          <SectionTitle level={1} uppercase={false}>
            {metaProps.title}
          </SectionTitle>
          {shortInterestPerformanceDescription && (
            <p
              className="section-description"
              dangerouslySetInnerHTML={{ __html: shortInterestPerformanceDescription }}
            />
          )}
          <p className="section-description">
            {t('Quote.Paragraphs.short-interest', {
              stockName: profile.richQuoteData?.name,
              symbolWithExchange: `${profile.richQuoteData?.exchange}:${symbol}`,
            })}
          </p>
          {shortInterestVolumeDescription && (
            <p className="section-description" dangerouslySetInnerHTML={{ __html: shortInterestVolumeDescription }} />
          )}
        </div>
        <ShortInterestSummaryBox shortInterestSummary={shortInterestSummary} />
        <QuoteCalendar
          calendar="short-interest"
          initialData={shortInterestSummary?.shortInterest ?? []}
          symbol={symbol}
        />
        <DrilldownFooter
          advertiserProfile={advertiserProfile}
          isAdvertiser={isAdvertiser}
          isETF={isETF}
          leftSection={
            <p className="text-sm ml-6">
              {t('Quote.ShortInterest.looking-for-the')}{' '}
              {/* eslint-disable-next-line @next/next/no-html-link-for-pages */}
              <a href="/short-interest/most-shorted">{t('Quote.ShortInterest.most-shorted-stocks')}</a>?
            </p>
          }
          peers={profile.quotes}
          peersChartData={peersChartData}
          symbol={symbol}
          type={profile.schedule.type}
        />
      </div>
    </QuoteLayout>
  );
};

export default QuoteShortInterestPage;

export const getServerSideProps = async ({ query: { ticker }, req, res }) => {
  if (req?.headers?.host) {
    setLanguageByHost(req?.headers?.host); // set language for server side translations
  }

  const symbol: string = sanitizeHTML(ticker) || '';
  const activeTab = 'short-interest';

  const pageData = (await getQuoteV2PageData(
    symbol,
    activeTab,
    getQuoteFeedTypeByLocale(i18n.language as LocaleType),
    req?.headers?.host,
  )) as {
    props: ShortInterestQuoteV2PageProps;
  };

  if (pageData?.props?.errorCode) {
    res.statusCode = pageData.props.errorCode;
  }

  const shortInterestSummary = await getShortInterestSummary(symbol);

  if (shortInterestSummary === null) {
    const props = await getQuoteNotFoundData(symbol);
    return props;
  }

  pageData.props.shortInterestSummary = shortInterestSummary;

  return pageData;
};
