import React from 'react';
import dayjs from 'dayjs';
import styled from '@benzinga/themetron';
import { BzImage } from '@benzinga/image';
import { CalendarIcon } from '@benzinga/themed-icons';
import customParseFormat from 'dayjs/plugin/customParseFormat';
dayjs.extend(customParseFormat);

const getFallbackDate = () => {
  // 2 Days Weekly - Sunday 1PM ET / Wednesday 6PM ET
  const today = new Date();
  const daysUntilSunday = (7 - today.getDay() + 0) % 7;
  const daysUntilWednesday = (7 - today.getDay() + 3) % 7;

  const wedsDate = dayjs(today).add(daysUntilWednesday, 'day').set('hour', 18).set('minute', 0);
  const sunDate = dayjs(today).add(daysUntilSunday, 'day').set('hour', 13).set('minute', 0);
  let nextDay = daysUntilSunday < daysUntilWednesday ? sunDate : wedsDate;
  const hasWebinarStarted = dayjs(today).isAfter(nextDay);
  if (hasWebinarStarted) {
    nextDay = daysUntilSunday < daysUntilWednesday ? wedsDate : sunDate;
  }

  return nextDay.format('MMM DD, YYYY h:mmA') + ' ET';
};

const getNextWebinar = webinars => {
  const nextWebinar = webinars.find(webinar => {
    const webinarDate = dayjs(webinar.date, 'DD/MM/YYYY hh:mm A');
    return webinarDate.isAfter(dayjs());
  });
  return nextWebinar ?? null;
};

const getWebinarDate = date => {
  const firstSpaceIndex = date.indexOf(' ');
  const datePart = date.substring(0, firstSpaceIndex);
  const timePart = date
    .substring(firstSpaceIndex + 1)
    .toString()
    .toUpperCase();
  const formattedDate = dayjs(datePart, 'DD/MM/YYYY').format('MMM DD, YYYY') + ' ' + timePart + ' ET';
  return formattedDate;
};

const defaultData = {
  button_text: 'Reserve Your Spot Now',
  cta_text:
    "Tom is a 30-year veteran trader who's trained over 300,000 investors and built one of the largest investing education companies in the world.",
  default_url:
    'https://bzresearch.myclickfunnels.com/latest-research-webinar-redirect-evergreen?utm_source=services-page',
  description:
    'Tom Gentile (who’s trained 300,000 investors) is joining Benzinga to reveal the market’s hidden ‘Power Patterns.’ The same ones he’s used in 2025 alone to lock in gains like 105% from Adobe…128% from Progressive…and 150% from Costco in less than two weeks. This is a FREE event exclusive for the Benzinga community.',
  title: 'Secure Your Spot to the Power Pattern Profits LIVE Event',
  webinar_dates: [],
};

export const WebinarBannerAd: React.FC<any> = props => {
  const { button_text, cta_text, description, title, webinar_dates } = props?.attrs?.data ?? defaultData;
  const { default_url } = defaultData;

  const nextWebinar = getNextWebinar([...webinar_dates]);
  const nextDate = nextWebinar ? getWebinarDate(nextWebinar.date) : getFallbackDate();
  const webinarUrl = nextWebinar && nextWebinar.url ? nextWebinar.url : props?.attrs?.data?.default_url ?? default_url;

  return (
    <Container className="webinar-banner-wrapper">
      <div className="banner-header">
        <div>
          <BzImage
            alt="Benzinga Research Logo"
            className="benzinga-logo"
            height="40px"
            src="/next-assets/images/benzinga-research-stacked-logo.png"
            width="120px"
          />
        </div>
        <div className="mobile-divider"></div>
        <div className="webinar-date-container">
          <div>Next webinar date:</div>
          <div className="webinar-date">
            <CalendarIcon height="16px" width="16px" />
            <div>{nextDate}</div>
          </div>
        </div>
      </div>
      <div className="banner-body">
        <div className="banner-body-cta">
          <h3>{title}</h3>
          <p>{description} </p>
          <a href={webinarUrl} rel="noreferrer" target="_blank">
            {button_text}
          </a>
        </div>
        <div className="banner-body-graphic">
          <BzImage
            alt="Newsletter Mavens"
            height="134px"
            src="/next-assets/images/services/services-people.png"
            width="390px"
          ></BzImage>
          <p>{cta_text}</p>
        </div>
      </div>
    </Container>
  );
};

export default WebinarBannerAd;

const Container = styled.div`
  border: 1px solid #ceddf2;
  border-radius: 4px;
  padding: 1rem;
  padding-left: 1.5rem;
  background-color: rgb(242, 248, 255);
  margin-bottom: 1rem;

  .banner-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    .benzinga-logo {
      width: 140px;
    }

    .mobile-divider {
      display: none;
    }

    .webinar-date-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border: 1px solid #ceddf2;
      border-radius: 4px;
      padding: 0.5rem 1rem;
      max-width: 390px;
      flex-grow: 1;

      div:first-child {
        color: #5b7292;
        font-size: 14px;
        line-height: 22px;
      }

      .webinar-date {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 700;
      }
    }
  }

  .banner-body {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    height: 100%;

    .banner-body-cta {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      min-height: 220px;
      gap: 1rem;

      h3 {
        font-size: 24px;
        font-weight: 700;
        line-height: 26px;
        color: #0b1f3d;
        text-transform: uppercase;
      }

      p {
        color: #5b7292;
        max-width: 540px;
        margin: 0px;
        font-size: 16px;
        line-height: 24px;
      }

      a {
        font-size: 14px;
        padding: 12px 32px;
        background-color: #3f83f8;
        color: white;
        border-radius: 4px;
        text-align: center;
        transition: background-color 0.3s ease-in-out;
        text-transform: uppercase;
        width: fit-content;
        font-weight: bold;
        &:hover {
          background-color: #2e6ad1;
        }
      }
    }

    .banner-body-graphic {
      position: relative;
      max-width: 390px;
      p {
        padding: 1rem;
        border-radius: 4px;
        background-color: #e1ebfa;
        text-align: center;
      }
    }
  }

  @media (max-width: 780px) {
    padding: 0px;
    .banner-header {
      flex-direction: column;
      align-items: flex-start;
      position: relative;
      padding: 1rem;

      .benzinga-logo {
        margin-top: 0.5rem;
        padding-bottom: 1rem;
      }

      .mobile-divider {
        display: block;
        width: 100%;
        border-top: 1px solid #ceddf2;
      }

      .webinar-date-container {
        max-width: 100%;
        flex-direction: column;
        border: none;
        align-items: flex-start;
        padding-left: 0px;
      }
    }

    .banner-body {
      flex-direction: column-reverse;
      align-items: center;
      text-align: center;
      gap: 0px;
      .banner-body-graphic {
        max-width: 100%;
        h3 {
          font-size: 18px;
        }
        p {
          border-radius: 0;
          margin-bottom: 0px;
          text-align: center;
          font-size: 14px;
          line-height: 18px;
          font-weight: 600;
        }
      }
      .banner-body-cta {
        padding: 1rem;
        width: 100%;
        align-items: center;
        text-align: left;
        h3 {
          font-size: 18px;
        }
        a {
          width: 100%;
        }
        p {
          font-size: 14px;
        }
      }
    }
  }
`;
