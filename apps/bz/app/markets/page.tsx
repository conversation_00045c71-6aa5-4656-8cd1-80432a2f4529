import React from 'react';
import { Layout } from '@benzinga/core-ui';
import { PageType } from '@benzinga/seo';

import { getMarketProps } from './data';
import { MarketPageProps } from '../../pages/api/market';
import PageLayout from '../_components/PageLayout';

import { QuoteCard } from './components/index';
import { SidebarList } from './components/SidebarList';
import { MarketPulseChart } from './components/MarketPulseChart';
import { TrendingStocksTape } from './components/TrendingStocksTape';
import { StockRankings } from './components/StockRankings';
import { GlobalMarkets } from './components/GlobalMarkets';
import LearningResources from './components/LearningResources';
import TopMoversSideWidget from './components/TopMoversSideWidget';
import { RaptiveAdWrapper } from './components/RaptiveAdWrapper';
import { ClientMoneyBlocks } from './components/ClientMoneyBlocks';
import { ClientNewsListElement } from './components/ClientNewsListElement';

export function generateMetadata() {
  const dateUpdated = new Date().toISOString();
  const description =
    'Get real-time stock market news, trading analysis, options insights, and financial information to make informed investment decisions. Track top movers, ETFs, crypto, and explore stock rankings.';
  const title = 'Stock Market News, Stock Quotes and Rankings, Stock Screener and Investment Analysis | Benzinga';

  return {
    alternates: {
      canonical: 'https://www.benzinga.com/market',
    },
    dateUpdated,
    description,
    openGraph: {
      description,
      title,
      modifiedTime: dateUpdated,
      url: 'https://www.benzinga.com/market',
    },
    pageType: PageType.Tool,
    title,
  };
}

export default async function Page() {
  const props = (await getMarketProps()) as unknown as MarketPageProps;

  const { inContentBlock, latestNews, marketPageTickers, movers, rankings, wiims } = props || {
    inContentBlock: null,
    latestNews: [],
    marketPageTickers: {},
    movers: [],
    rankings: {},
    wiims: [],
  };

  return (
    <PageLayout pageProps={props}>
      <Layout
        layoutBelow={
          <Layout
            layoutAbove={inContentBlock && <ClientMoneyBlocks blocks={inContentBlock} />}
            layoutMain={
              <SidebarList action="/recent" title={'Latest News'}>
                {latestNews?.map((article, index) => <ClientNewsListElement article={article} key={index} />)}
              </SidebarList>
            }
            layoutSidebar={
              <>
                <RaptiveAdWrapper placement="static-sidebar" />
                <LearningResources />
              </>
            }
            sidebarSettings={{ width: 400 }}
          ></Layout>
        }
        layoutMain={
          <div className="mt-8 md:mt-4">
            <MarketPulseChart markets={marketPageTickers?.markets} />
            <TrendingStocksTape trending={marketPageTickers?.trending} />
            <StockRankings rankings={rankings} />
            <GlobalMarkets
              commodities={marketPageTickers?.commodities}
              global={marketPageTickers?.internationalIndices}
            />
          </div>
        }
        layoutSidebar={
          <div className="mt-8 md:mt-4">
            <TopMoversSideWidget movers={movers ?? []} wiims={wiims ?? []} />
            <RaptiveAdWrapper placement="static-sidebar" />
            <SidebarList action="/crypto" edgeVariant="crypto" title={'Crypto Spotlight'}>
              {marketPageTickers?.crypto?.map((ticker, index) => (
                <QuoteCard key={'crypto-' + index} showLogo ticker={ticker} variant="list" />
              ))}
            </SidebarList>
            <SidebarList action="/etfs" edgeVariant="etf" title={'Top ETFs'}>
              {marketPageTickers?.etfs?.map((ticker, index) => (
                <QuoteCard key={'etf-' + index} showLogo ticker={ticker} variant="list" />
              ))}
            </SidebarList>
          </div>
        }
        sidebarSettings={{ width: 400 }}
      />
    </PageLayout>
  );
}
