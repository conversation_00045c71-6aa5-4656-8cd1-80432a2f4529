import React, { useRef, useEffect, useState } from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  StyleSheet,
  ScrollView,
  Animated,
  findNodeHandle,
  Modal,
  FlatList,
  SafeAreaView,
} from 'react-native';
import Icon, { IconTypes } from '../Icon/Icon';
import { useTheme } from '../../theme/themecontext';

function BZTabBar({ descriptors, fullWidth, navigation, state }) {
  const { colors } = useTheme();
  const styles = withColors(colors);
  const focusedOptions = descriptors[state.routes[state.index].key].options;
  const scrollViewRef = useRef(null);
  const tabRefs = useRef([]);
  const [modalVisible, setModalVisible] = useState(false);

  // For measuring and tracking tab positions
  const [indicatorMeasurements, setIndicatorMeasurements] = useState({
    width: 0,
    position: 0,
  });

  // Animated values for smooth indicator transitions
  const animatedIndicatorPosition = useRef(new Animated.Value(0)).current;
  const animatedIndicatorWidth = useRef(new Animated.Value(0)).current;

  // Initialize refs array for all tabs
  useEffect(() => {
    tabRefs.current = state.routes.map((_, i) => tabRefs.current[i] || React.createRef());
  }, [state.routes.length]);

  // Measure and update indicator when tab changes
  useEffect(() => {
    updateIndicatorPosition(state.index);
  }, [state.index]);

  // Update animated values when measurements change
  useEffect(() => {
    // Animate to the new position and width
    Animated.parallel([
      Animated.timing(animatedIndicatorPosition, {
        toValue: indicatorMeasurements.position,
        duration: 250,
        useNativeDriver: false,
      }),
      Animated.timing(animatedIndicatorWidth, {
        toValue: indicatorMeasurements.width,
        duration: 250,
        useNativeDriver: false,
      }),
    ]).start();
  }, [indicatorMeasurements]);

  // Function to measure tab and update indicator position
  const updateIndicatorPosition = index => {
    if (!scrollViewRef.current || !tabRefs.current[index]?.current) return;

    const scrollNode = findNodeHandle(scrollViewRef.current);
    if (!scrollNode) return;

    setTimeout(() => {
      tabRefs.current[index].current.measureLayout(
        scrollNode,
        (x, _y, width, _height) => {
          setIndicatorMeasurements({
            width,
            position: x,
          });
        },
        () => console.log('Measurement failed'),
      );
    }, 50);
  };

  // Auto scroll to center the focused tab
  useEffect(() => {
    if (scrollViewRef.current && tabRefs.current[state.index]?.current) {
      setTimeout(() => {
        // Get ScrollView width
        scrollViewRef.current?.measure((_x, _y, scrollViewWidth, _height, _pageX, _pageY) => {
          // Get the tab position and dimensions
          const node = findNodeHandle(scrollViewRef.current);
          if (!node) return;

          tabRefs.current[state.index].current?.measureLayout(
            node,
            (x, _y, width, _height) => {
              // Calculate position to center the tab in the ScrollView
              const centerPosition = x - scrollViewWidth / 2 + width / 2;
              const scrollToPosition = Math.max(0, centerPosition);

              // Update scroll position
              scrollViewRef.current.scrollTo({
                x: scrollToPosition, // Ensure we don't scroll before the start
                animated: true,
              });

              // Force indicator update after scrolling completes
              setTimeout(() => {
                updateIndicatorPosition(state.index);
              }, 350); // Wait for scroll animation to complete
            },
            () => console.log('Measurement failed'),
          );
        });
      }, 100);
    }
  }, [state.index]);

  if (focusedOptions.tabBarVisible === false) {
    return null;
  }

  return (
    <View style={{ flexDirection: 'row', height: 40, marginLeft: 10 }}>
      <View style={{ flex: 1 }}>
        <ScrollView
          ref={scrollViewRef}
          contentContainerStyle={{ flexGrow: fullWidth ? 1 : undefined }}
          style={styles.tabBarContainerScrollView}
          bounces={false}
          scrollEnabled={true}
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          onScroll={() => {
            // Update indicator position during scroll
            updateIndicatorPosition(state.index);
          }}
          scrollEventThrottle={16}
          onLayout={() => updateIndicatorPosition(state.index)}
        >
          <View style={[styles.tabBarContainer, { flex: fullWidth ? 1 : undefined }]}>
            {state.routes.map((route, index) => {
              const { options } = descriptors[route.key];
              const label =
                options.tabBarLabel !== undefined
                  ? options.tabBarLabel
                  : options.title !== undefined
                    ? options.title
                    : route.name;

              const isFocused = state.index === index;

              const onPress = () => {
                const event = navigation.emit({
                  type: 'tabPress',
                  target: route.key,
                  canPreventDefault: true,
                });

                if (!isFocused && !event.defaultPrevented) {
                  navigation.navigate(route.name);
                }
              };

              return (
                <TouchableOpacity
                  ref={tabRefs.current[index]}
                  key={route.key}
                  accessibilityRole="button"
                  accessibilityState={isFocused ? { selected: true } : {}}
                  accessibilityLabel={options.tabBarAccessibilityLabel}
                  testID={options.tabBarTestID}
                  onPress={onPress}
                  style={[styles.tabItem, { flex: fullWidth ? 1 : undefined }]}
                  onLayout={() => {
                    if (isFocused) {
                      updateIndicatorPosition(index);
                    }
                  }}
                >
                  <Text
                    style={[
                      styles.tabItemLabel,
                      {
                        color: colors.text,
                        fontWeight: isFocused ? 'bold' : 'normal',
                      },
                    ]}
                    numberOfLines={1}
                  >
                    {label}
                  </Text>
                </TouchableOpacity>
              );
            })}

            {/* Animated indicator */}
            <Animated.View
              style={[
                styles.tabIndicator,
                {
                  width: animatedIndicatorWidth,
                  left: animatedIndicatorPosition,
                  backgroundColor: colors.linkText,
                },
              ]}
            />
          </View>
        </ScrollView>
        <TouchableOpacity style={styles.moreButton} onPress={() => setModalVisible(true)}>
          <Icon type={IconTypes.Feather} name="more-vertical" size={25} color={colors.text} />
        </TouchableOpacity>
      </View>

      {/* Modal for tab navigation */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <SafeAreaView style={styles.modalSafeArea}>
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <FlatList
                data={state.routes}
                keyExtractor={item => item.key}
                renderItem={({ index, item }) => {
                  const { options } = descriptors[item.key];
                  const label =
                    options.tabBarLabel !== undefined
                      ? options.tabBarLabel
                      : options.title !== undefined
                        ? options.title
                        : item.name;

                  const isFocused = state.index === index;

                  const onPress = () => {
                    const event = navigation.emit({
                      type: 'tabPress',
                      target: item.key,
                      canPreventDefault: true,
                    });

                    if (!isFocused && !event.defaultPrevented) {
                      navigation.navigate(item.name);
                    }

                    setModalVisible(false); // Close modal on tab press
                  };

                  return (
                    <TouchableOpacity key={item.key} onPress={onPress} style={styles.modalTabItem}>
                      <Text
                        style={[
                          styles.modalTabText,
                          {
                            color: isFocused ? colors.linkText : colors.text,
                            fontWeight: isFocused ? 'bold' : 'normal',
                          },
                        ]}
                      >
                        {label}
                      </Text>
                      {isFocused && <View style={styles.modalTabIndicator} />}
                    </TouchableOpacity>
                  );
                }}
                ItemSeparatorComponent={() => <View style={styles.modalSeparator} />}
              />

              <TouchableOpacity onPress={() => setModalVisible(false)} style={styles.modalCloseButton}>
                <Text style={styles.modalCloseButtonText}>Close</Text>
              </TouchableOpacity>
            </View>
          </View>
        </SafeAreaView>
      </Modal>
    </View>
  );
}

const withColors = (colors: Record<string, string>) => {
  return StyleSheet.create({
    tabBarContainerScrollView: {
      maxHeight: 40,
      paddingRight: 10,
      marginRight: 34,
    },
    tabBarContainer: {
      flexDirection: 'row',
      height: 40,
      backgroundColor: colors.background,
      position: 'relative',
    },
    tabItem: {
      alignItems: 'center',
      justifyContent: 'center',
      marginHorizontal: 4,
    },
    tabItemLabel: {
      fontSize: 16,
      color: colors.text,
    },
    moreButton: {
      position: 'absolute',
      right: 0,
      backgroundColor: colors.background,
      width: 24,
      height: '100%',
      alignItems: 'center',
      justifyContent: 'center',
      elevation: 5,
      shadowColor: colors.text,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
    },
    moreButtonText: {
      color: 'white',
      fontWeight: 'bold',
    },
    tabIndicator: {
      position: 'absolute',
      height: 2,
      bottom: 0,
    },
    // Modal styles
    modalSafeArea: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    modalContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContent: {
      width: '90%',
      backgroundColor: colors.cardBackground,
      borderRadius: 6,
      padding: 12,
    },
    modalTabItem: {
      paddingVertical: 14,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      position: 'relative',
    },
    modalTabText: {
      paddingLeft: 10,
      fontSize: 16,
    },
    modalTabIndicator: {
      position: 'absolute',
      left: 0,
      top: 0,
      bottom: 0,
      width: 3,
      backgroundColor: colors.linkText,
    },
    modalSeparator: {
      height: 1,
      backgroundColor: colors.border,
    },
    modalCloseButton: {
      marginTop: 10,
      backgroundColor: colors.buttonBlueActive,
      borderRadius: 5,
      paddingVertical: 10,
      alignItems: 'center',
    },
    modalCloseButtonText: {
      color: colors.text,
      fontWeight: 'bold',
    },
  });
};

export default BZTabBar;
