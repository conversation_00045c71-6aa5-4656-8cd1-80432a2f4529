/**!
 *	9.3.0
 *	Generation date: 2025-06-30T06:07:44.099Z
 *	Package Type: Core alacarte
 *	Build descriptor: 13d0ed304
 *	Client name: benzinga
 *	Expiration date: "2026/05/31"
 *	Domain lock: ["127.0.0.1","localhost","benzinga.com"]
 *	iFrame lock: true
 *	License type: annual
 *	Features: TypeScript Definitions,SignalIQ,StudyBrowser
 */

/***********************************************************!
 * Copyright © 2025 S&P Global All rights reserved
*************************************************************/
/*************************************! DO NOT MAKE CHANGES TO THIS LIBRARY FILE!! !*************************************
* If you wish to overwrite default functionality, create a separate file with a copy of the methods you are overwriting *
* and load that file right after the library has been loaded, but before the chart engine is instantiated.              *
* Directly modifying library files will prevent upgrades and the ability for ChartIQ to support your solution.          *
*************************************************************************************************************************/

/*************************************************************************!
* Please note that manually changing the domain list or expiration dates  *
*                                                                         *
* >>>>>>>>>>>>>>>>>>>>>>>>>>>>>> WILL NOT <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< *
*                                                                         *
*   modify the library locking mechanism. Any changes must be requested   *
*                          directly from ChartIQ.                         *
***************************************************************************/

/* eslint-disable */ /* jshint ignore:start */ /* ignore jslint start */
(function(){
	const license = Object.defineProperties({
		domains: ["^127\\.0\\.0\\.1$","^(?:.+\\.)?localhost$","^(?:.+\\.)?benzinga\\.com$"],
		filesystem: false,
		licenseExpiration: "2026/05/31",
		trialExpiration: undefined,
		version: '9.3.0'
	}, {
		daysUntilExpired: {
			get: function(){return Math.round((this.expirationDate - new Date()) / 86400000)}
		},
		expirationDate: {
			get: function(){return new Date(this.licenseExpiration || this.trialExpiration)}
		},
		gracePeriodDate: {
			get: function(){
				const gracePeriodDate = new Date(this.expirationDate);
				if(!this.isTrial){
					gracePeriodDate.setDate(gracePeriodDate.getDate() + 30);
				}
				return gracePeriodDate;
			}
		},
		isDateLocked: {
			get: function(){return this.licenseExpiration || this.trialExpiration}
		},
		isDomainLocked: {
			get: function(){return this.domains && this.domains.length > 0;}
		},
		isFileSystemLocked: {
			get: function(){return this.filesystem !== undefined}
		},
		isExpired: {
			get: function(){return new Date() > this.expirationDate}
		},
		isGracePeriodExpired: {
			get: function(){return new Date() > this.gracePeriodDate}
		},
		isTrial: {
			get: function(){return this.trialExpiration !== undefined}
		},
		isValidDomain: {
			value: function(domain){return this.domains.some((pattern) => new RegExp(pattern).test(domain))}
		}
	});
	if(license.isDateLocked){
		if(license.isExpired){
			console.error('ChartIQ: this license has expired!');
			if(license.isTrial){
				alert('ChartIQ: this license has expired!');
			}
			if(license.isGracePeriodExpired){
				throw new Error('ChartIQ: this license has expired!');
			}
		}else if(license.isTrial && license.daysUntilExpired < 3){
			alert("This ChartIQ trial license expires in " + license.daysUntilExpired + " days!");
			console.log("WARNING: This ChartIQ trial license expires in " + license.daysUntilExpired + " days!");
		}
	}

	if(typeof document !== 'undefined'){
		if(license.isFileSystemLocked && location.protocol === 'file:'){
			return;
		}
		const hostname = new URL(location.href).hostname;
		if(license.isDomainLocked && !license.isValidDomain(hostname)){
			alert("ChartIQ ERROR: Not licensed for domain " + hostname);
			console.error("ChartIQ ERROR: Not licensed for domain " + hostname);
		}
	}

	if(license.version === 'alpha'){
		alert('ChartIQ: This is an internal PRE-PRODUCTION release--not for external use!');
	}
})();
/* eslint-enable  */ /* jshint ignore:end   */ /* ignore jslint end   */

/* eslint-disable no-extra-parens */


/* eslint-disable */ /* jshint ignore:start */ /* ignore jslint start */
q.h=(function(){var O=2;for(;O !== 9;){switch(O){case 3:return p;break;case 2:O=typeof globalThis === '\u006f\u0062\x6a\x65\x63\u0074'?1:5;break;case 1:return globalThis;break;case 5:var p;try{var g=2;for(;g !== 6;){switch(g){case 4:g=typeof Vmbd1 === '\u0075\x6e\x64\u0065\x66\u0069\u006e\u0065\u0064'?3:9;break;case 9:delete p['\x56\x6d\x62\u0064\u0031'];var X=Object['\x70\x72\u006f\u0074\u006f\u0074\x79\u0070\x65'];delete X['\x63\u0024\u0076\u0048\x44'];g=6;break;case 3:throw "";g=9;break;case 2:Object['\u0064\x65\x66\x69\x6e\u0065\u0050\u0072\x6f\u0070\x65\x72\u0074\u0079'](Object['\u0070\u0072\x6f\u0074\x6f\u0074\x79\x70\x65'],'\x63\x24\x76\x48\x44',{'\x67\x65\u0074':function(){return this;},'\u0063\x6f\x6e\x66\x69\u0067\x75\x72\x61\u0062\x6c\x65':true});p=c$vHD;p['\x56\u006d\u0062\x64\u0031']=p;g=4;break;}}}catch(K){p=window;}O=3;break;}}})();q.N9MkM=n;I2(q.h);q.A$=(function(){var y=2;for(;y !== 4;){switch(y){case 2:var G=q;var k={J1lzfs1:(function(E){var W=2;for(;W !== 18;){switch(W){case 13:(s++,A++);W=8;break;case 14:x+=v(m(s) ^ t(A));W=13;break;case 12:x=q.Y(x,':');var U=0;var C=function(L){var d=2;for(;d !== 23;){switch(d){case 6:U+=1;d=14;break;case 24:return l(L);break;case 19:q.Q(q.T(),x,q.F(q.F(x,-10,10),0,8));d=4;break;case 16:q.Q(q.T(),x,q.F(q.F(x,-5,5),0,4));d=4;break;case 7:d=U === 2 && L === 3?6:13;break;case 2:d=U === 0 && L === 0?1:3;break;case 26:q.Q(q.T(),x,q.F(q.F(x,-2,2),0,1));d=4;break;case 3:d=U === 1 && L === 2?9:7;break;case 9:U+=1;d=8;break;case 25:k.J1lzfs1=l;d=24;break;case 17:U+=1;d=16;break;case 27:U+=1;d=26;break;case 15:d=U === 6 && L === 3?27:25;break;case 18:d=U === 5 && L === 4?17:15;break;case 1:U+=1;d=5;break;case 20:U+=1;d=19;break;case 5:q.Q(q.T(),x,q.F(q.F(x,-7,7),0,6));d=4;break;case 8:q.Q(q.T(),x,q.F(q.F(x,-8,8),0,7));d=4;break;case 11:q.Q(q.T(),x,q.F(q.F(x,-10,10),0,8));d=4;break;case 4:return U;break;case 13:d=U === 3 && L === 3?12:10;break;case 10:d=U === 4 && L === 0?20:18;break;case 14:q.Q(q.T(),x,q.F(q.F(x,-10,10),0,8));d=4;break;case 12:U+=1;d=11;break;}}};W=20;break;case 9:var s=0,A=0;W=8;break;case 4:var m=q.N().bind(D);var t=q.N().bind(E);W=9;break;case 8:W=s < D.length?7:12;break;case 20:var l=function(V){var R=2;for(;R !== 1;){switch(R){case 2:return x[V];break;}}};return C;break;case 7:W=A === E.length?6:14;break;case 6:A=0;W=14;break;case 2:var z=function(J){var u=2;for(;u !== 11;){switch(u){case 7:var b,M;u=6;break;case 3:u=w < J.length?9:7;break;case 9:Z[w]=r(J[w] + 50);u=8;break;case 6:b=q.H(q.B(Z,function(){var f=2;for(;f !== 1;){switch(f){case 2:return 0.5 - S();break;}}}),'');M=G[b];u=13;break;case 12:return M;break;case 13:u=!M?6:12;break;case 4:var w=0;u=3;break;case 2:var r=q.I();var S=q.o1();var Z=[];u=4;break;case 8:w++;u=3;break;}}};var x='',D=q.H7()(z([7,28,27,57,27])());var v=q.I();W=4;break;}}})('5JBI7)')};return k;break;}}})();q.m5=function(){return typeof q.A$.J1lzfs1 === 'function'?q.A$.J1lzfs1.apply(q.A$,arguments):q.A$.J1lzfs1;};q.W9=function(){return typeof q.A$.J1lzfs1 === 'function'?q.A$.J1lzfs1.apply(q.A$,arguments):q.A$.J1lzfs1;};var I3=2;for(;I3 !== 11;){switch(I3){case 8:I3=q.m5(3) == q.W9(0)?7:6;break;case 12:q.l$=24;I3=11;break;case 9:q.L8=72;I3=8;break;case 3:I3=q.m5(3) == 65?9:8;break;case 4:q.p_=53;I3=3;break;case 7:q.j4=17;I3=6;break;case 5:I3=q.m5(2) >= 54?4:3;break;case 1:q.q5=90;I3=5;break;case 6:I3=q.m5(4) == 1?14:13;break;case 14:q.v4=38;I3=13;break;case 13:I3=q.W9(3) === 19?12:11;break;case 2:I3=q.W9(0) === 68?1:5;break;}}q.d1=function(){return typeof q.p1.t6oWOZY === 'function'?q.p1.t6oWOZY.apply(q.p1,arguments):q.p1.t6oWOZY;};q.F_=function(){return typeof q.T0.c0qaNi_ === 'function'?q.T0.c0qaNi_.apply(q.T0,arguments):q.T0.c0qaNi_;};q.R$=function(){return typeof q.a3.e0hQAyL === 'function'?q.a3.e0hQAyL.apply(q.a3,arguments):q.a3.e0hQAyL;};q.S4=function(){return typeof q.p1.M2mMVIM === 'function'?q.p1.M2mMVIM.apply(q.p1,arguments):q.p1.M2mMVIM;};function q(){}q.O5=function(){return typeof q.U0.g$CZYfJ === 'function'?q.U0.g$CZYfJ.apply(q.U0,arguments):q.U0.g$CZYfJ;};q.i_=function(){return typeof q.T0.c0qaNi_ === 'function'?q.T0.c0qaNi_.apply(q.T0,arguments):q.T0.c0qaNi_;};q.U0=(function(h5){function o0(K1){var w2=2;for(;w2 !== 25;){switch(w2){case 10:w2=!m8--?20:19;break;case 20:Y_=true;w2=19;break;case 27:Y_=false;w2=26;break;case 5:v0=y7[h5[4]];w2=4;break;case 1:w2=!m8--?5:4;break;case 9:w2=!m8--?8:7;break;case 7:w2=!m8--?6:14;break;case 8:E_=h5[6];w2=7;break;case 26:k8='j-002-00003';w2=16;break;case 6:g_=E_ && v0(E_,C8);w2=14;break;case 15:w2=g_ >= 0 && g_ - K1 <= C8?27:16;break;case 18:Y_=false;w2=17;break;case 13:v9=h5[7];w2=12;break;case 2:var Y_,C8,E_,g_,v9,d5,v0;w2=1;break;case 17:k8='j-002-00005';w2=16;break;case 12:w2=!m8--?11:10;break;case 11:d5=(v9 || v9 === 0) && v0(v9,C8);w2=10;break;case 3:C8=27;w2=9;break;case 16:return Y_;break;case 4:w2=!m8--?3:9;break;case 14:w2=!m8--?13:12;break;case 19:w2=d5 >= 0 && K1 - d5 <= C8?18:15;break;}}}var X1=2;for(;X1 !== 10;){switch(X1){case 7:Y0=q.h9(e6,new y7[k6]("^['-|]"),'S');X1=6;break;case 8:X1=!m8--?7:6;break;case 6:X1=!m8--?14:13;break;case 5:y7=q.h;X1=4;break;case 4:var m7='fromCharCode',k6='RegExp';X1=3;break;case 9:e6=typeof m7;X1=8;break;case 14:h5=q.D_(h5,function(r6){var a8=2;for(;a8 !== 13;){switch(a8){case 7:a8=!x9?6:14;break;case 2:var x9;a8=1;break;case 3:a8=M1 < r6.length?9:7;break;case 8:M1++;a8=3;break;case 4:var M1=0;a8=3;break;case 5:x9='';a8=4;break;case 6:return;break;case 9:x9+=y7[Y0][m7](r6[M1] + 104);a8=8;break;case 1:a8=!m8--?5:4;break;case 14:return x9;break;}}});X1=13;break;case 11:return {g$CZYfJ:function(F7){var M3=2;for(;M3 !== 6;){switch(M3){case 3:M3=!m8--?9:8;break;case 4:S7=o0(C7);M3=3;break;case 5:M3=!m8--?4:3;break;case 8:var B_=(function(s9,v6){var c3=2;for(;c3 !== 10;){switch(c3){case 1:s9=F7;c3=5;break;case 3:var n3,X6=0;c3=9;break;case 4:v6=h5;c3=3;break;case 13:X6++;c3=9;break;case 5:c3=typeof v6 === 'undefined' && typeof h5 !== 'undefined'?4:3;break;case 2:c3=typeof s9 === 'undefined' && typeof F7 !== 'undefined'?1:5;break;case 12:n3=n3 ^ q_;c3=13;break;case 8:var W3=y7[v6[4]](s9[v6[2]](X6),16)[v6[3]](2);var q_=W3[v6[2]](W3[v6[5]] - 1);c3=6;break;case 11:return n3;break;case 6:c3=X6 === 0?14:12;break;case 14:n3=q_;c3=13;break;case 9:c3=X6 < s9[v6[5]]?8:11;break;}}})(undefined,undefined);return B_?S7:!S7;break;case 1:M3=C7 > Z0?5:8;break;case 9:Z0=C7 + 60000;M3=8;break;case 2:var C7=new y7[h5[0]]()[h5[1]]();M3=1;break;}}}};break;case 3:X1=!m8--?9:8;break;case 12:var S7,Z0=0,k8;X1=11;break;case 13:X1=!m8--?12:11;break;case 1:X1=!m8--?5:4;break;case 2:var y7,e6,Y0,m8;X1=1;break;}}})([[-36,-7,12,-3],[-1,-3,12,-20,1,5,-3],[-5,0,-7,10,-39,12],[12,7,-21,12,10,1,6,-1],[8,-7,10,11,-3,-31,6,12],[4,-3,6,-1,12,0],[-50,-48,-6,0,5,-2,-1,-53,-56],[]]);q.M_=function(){return typeof q.a3.c0BSJ1d === 'function'?q.a3.c0BSJ1d.apply(q.a3,arguments):q.a3.c0BSJ1d;};q.T0=(function(){var k_=function(Y9,Z_){var k7=Z_ & 0xffff;var N6=Z_ - k7;return (N6 * Y9 | 0) + (k7 * Y9 | 0) | 0;},c0qaNi_=function(L5,T$,w5){var F6=0xcc9e2d51,O0=0x1b873593;var e1=w5;var M6=T$ & ~0x3;var f5=q.N().bind(L5);for(var S3=0;S3 < M6;S3+=4){var R1=f5(S3) & 0xff | (f5(S3 + 1) & 0xff) << 8 | (f5(S3 + 2) & 0xff) << 16 | (f5(S3 + 3) & 0xff) << 24;R1=k_(R1,F6);R1=(R1 & 0x1ffff) << 15 | R1 >>> 17;R1=k_(R1,O0);e1^=R1;e1=(e1 & 0x7ffff) << 13 | e1 >>> 19;e1=e1 * 5 + 0xe6546b64 | 0;}R1=0;switch(T$ % 4){case 3:R1=(f5(M6 + 2) & 0xff) << 16;case 2:R1|=(f5(M6 + 1) & 0xff) << 8;case 1:R1|=f5(M6) & 0xff;R1=k_(R1,F6);R1=(R1 & 0x1ffff) << 15 | R1 >>> 17;R1=k_(R1,O0);e1^=R1;}e1^=T$;e1^=e1 >>> 16;e1=k_(e1,0x85ebca6b);e1^=e1 >>> 13;e1=k_(e1,0xc2b2ae35);e1^=e1 >>> 16;return e1;};return {c0qaNi_:c0qaNi_};})();q.E5=function(){return typeof q.U0.g$CZYfJ === 'function'?q.U0.g$CZYfJ.apply(q.U0,arguments):q.U0.g$CZYfJ;};q.i1=function(){return typeof q.a3.c0BSJ1d === 'function'?q.a3.c0BSJ1d.apply(q.a3,arguments):q.a3.c0BSJ1d;};q.O$=function(){return typeof q.p1.M2mMVIM === 'function'?q.p1.M2mMVIM.apply(q.p1,arguments):q.p1.M2mMVIM;};function I2(d8){function p2(Q5){var W4=2;for(;W4 !== 5;){switch(W4){case 2:var y8=[arguments];return y8[0][0].Math;break;}}}function q2(e_){var S$=2;for(;S$ !== 5;){switch(S$){case 2:var W1=[arguments];return W1[0][0].Function;break;}}}function P0(q6){var a6=2;for(;a6 !== 5;){switch(a6){case 2:var n9=[arguments];return n9[0][0].String;break;}}}var F$=2;for(;F$ !== 60;){switch(F$){case 63:P4(P0,"substring",H5[75],H5[19],H5[58]);F$=62;break;case 64:P4(q3,"decodeURI",H5[58],H5[15],H5[58]);F$=63;break;case 36:var P4=function(N7,I6,m$,q8,e4){var C1=2;for(;C1 !== 5;){switch(C1){case 2:var B0=[arguments];N3(H5[0][0],B0[0][0],B0[0][1],B0[0][2],B0[0][3],B0[0][4]);C1=5;break;}}};F$=54;break;case 25:H5[60]="9";H5[79]="";H5[98]="h";H5[79]="_";F$=21;break;case 47:P4(q3,"String",H5[58],H5[45],H5[58]);F$=46;break;case 52:P4(q2,"apply",H5[75],H5[9],H5[58]);F$=51;break;case 49:P4(i0,"sort",H5[75],H5[1],H5[58]);F$=48;break;case 54:P4(P0,"split",H5[75],H5[7],H5[58]);F$=53;break;case 38:H5[40]=H5[98];H5[40]+=H5[4];F$=36;break;case 2:var H5=[arguments];H5[3]="";H5[3]="I";H5[2]="";F$=3;break;case 3:H5[2]="N";H5[4]="";H5[4]="1";H5[8]="";F$=6;break;case 61:P4(i0,"map",H5[75],H5[30],H5[58]);F$=60;break;case 65:P4(p2,"random",H5[58],H5[34],H5[58]);F$=64;break;case 62:P4(P0,"replace",H5[75],H5[89],H5[58]);F$=61;break;case 50:P4(P0,"charCodeAt",H5[75],H5[2],H5[58]);F$=49;break;case 32:H5[58]=0;H5[30]=H5[29];H5[30]+=H5[79];H5[89]=H5[98];H5[89]+=H5[60];H5[19]=H5[65];F$=43;break;case 6:H5[1]="B";H5[8]="";H5[6]="T";H5[9]="Q";F$=11;break;case 53:P4(i0,"unshift",H5[75],H5[6],H5[58]);F$=52;break;case 51:P4(i0,"splice",H5[75],H5[5],H5[58]);F$=50;break;case 48:P4(i0,"join",H5[75],H5[8],H5[58]);F$=47;break;case 18:H5[8]="H";H5[22]="";H5[22]="2";H5[65]="";H5[65]="b";H5[60]="";F$=25;break;case 21:H5[29]="D";H5[75]=6;H5[75]=1;H5[58]=6;F$=32;break;case 11:H5[7]="Y";H5[5]="F";H5[31]="7";H5[45]="o";F$=18;break;case 43:H5[19]+=H5[22];H5[15]=H5[8];H5[15]+=H5[31];H5[34]=H5[45];H5[34]+=H5[4];F$=38;break;case 46:P4(P0,"fromCharCode",H5[58],H5[3],H5[58]);F$=45;break;case 45:P4(q3,"Math",H5[58],H5[40],H5[58]);F$=65;break;}}function q3(P2){var z9=2;for(;z9 !== 5;){switch(z9){case 2:var l2=[arguments];return l2[0][0];break;}}}function i0(V6){var f4=2;for(;f4 !== 5;){switch(f4){case 2:var S9=[arguments];return S9[0][0].Array;break;}}}function N3(H9,A8,v7,o$,g5,d0){var l8=2;for(;l8 !== 6;){switch(l8){case 3:j6[8]="define";j6[4]=true;j6[4]=false;try{var B8=2;for(;B8 !== 11;){switch(B8){case 9:return;break;case 6:j6[6].set=function(V$){var I4=2;for(;I4 !== 5;){switch(I4){case 2:var W0=[arguments];j6[2][j6[0][2]]=W0[0][0];I4=5;break;}}};j6[6].get=function(){var q4=2;for(;q4 !== 20;){switch(q4){case 14:q4=j6[0][5] === H5[58]?13:12;break;case 6:j5[7]+=j5[3];q4=14;break;case 13:return function(){var p8=2;for(;p8 !== 13;){switch(p8){case 2:var i7=[arguments];i7[3]=1;i7[3]=1;i7[3]=null;p8=3;break;case 7:i7[5]=arguments[H5[58]] === i7[3] || arguments[H5[58]] === undefined?j6[7]:arguments[H5[58]];p8=6;break;case 14:return j6[2][j6[0][2]];break;case 3:p8=arguments.length > H5[58]?9:14;break;case 8:return j6[2][j6[0][2]].apply(j6[7],arguments);break;case 9:p8=j6[0][3] === H5[58]?8:7;break;case 6:return i7[5][j6[0][2]].apply(i7[5],Array.prototype.slice.call(arguments,H5[75]));break;}}};break;case 10:return j6[2][j6[0][2]];break;case 3:j5[6]="";j5[6]="un";j5[7]=j5[6];j5[7]+=j5[4];q4=6;break;case 11:return undefined;break;case 2:var j5=[arguments];j5[3]="ed";j5[4]="";j5[4]="defin";q4=3;break;case 12:q4=typeof j6[2][j6[0][2]] == j5[7]?11:10;break;}}};j6[6].enumerable=j6[4];try{var i5=2;for(;i5 !== 3;){switch(i5){case 2:j6[3]=j6[8];j6[3]+=j6[5];j6[3]+=j6[9];j6[0][0].Object[j6[3]](j6[1],j6[0][4],j6[6]);i5=3;break;}}}catch(d2){}B8=11;break;case 3:B8=j6[2].hasOwnProperty(j6[0][4]) && j6[2][j6[0][4]] === j6[2][j6[0][2]]?9:8;break;case 8:B8=j6[0][5] !== H5[58]?7:6;break;case 7:j6[2][j6[0][4]]=j6[2][j6[0][2]];B8=6;break;case 2:j6[6]={};j6[7]=(1,j6[0][1])(j6[0][0]);j6[2]=[j6[7],j6[7].prototype][j6[0][3]];j6[1]=j6[0][5] === H5[58]?q:j6[2];B8=3;break;}}}catch(j$){}l8=6;break;case 2:var j6=[arguments];j6[9]="operty";j6[5]="";j6[5]="Pr";l8=3;break;}}}}q.Z1=function(){return typeof q.a3.e0hQAyL === 'function'?q.a3.e0hQAyL.apply(q.a3,arguments):q.a3.e0hQAyL;};q.p1=(function(m_){return {M2mMVIM:function(){var o3,j0=arguments;switch(m_){case 3:o3=(j0[2] - j0[1]) % j0[0];break;case 1:o3=j0[1] - j0[0];break;case 0:o3=j0[1] << j0[0];break;case 2:o3=j0[1] + j0[0] - j0[2];break;case 4:o3=j0[2] * j0[0] + j0[1];break;}return o3;},t6oWOZY:function(R8){m_=R8;}};})();q.a3=(function(){function w_(S2,E9,p6,B6,J0){var g2=2;for(;g2 !== 15;){switch(g2){case 11:o_=q.b2(F2,U1,F2.length);z5=o_.length;g2=20;break;case 19:g2=S2 === null || S2 <= 0?18:14;break;case 14:var U1=F2.length - S2;g2=13;break;case 2:var o_,z5,F2,o4;!o4 && (o4=U3[C9([15,18,6,4,23,12,18,17])]);!M8 && (M8=typeof o4 !== "undefined"?o4[C9([11,18,22,23,17,4,16,8])] || ' ':"");!d9 && (d9=typeof o4 !== "undefined"?o4[C9([11,21,8,9])]:"");g2=3;break;case 13:g2=E9 && U1 > 0 && q.N(F2,U1 - 1) !== 46?12:11;break;case 3:F2=J0?d9:M8;g2=9;break;case 16:return h6.i_(o_,z5,p6);break;case 8:o_=q.b2(F2,S2,B6);z5=o_.length;g2=6;break;case 9:g2=B6 > 0?8:19;break;case 6:return h6.i_(o_,z5,p6);break;case 12:return false;break;case 20:return h6.i_(o_,z5,p6);break;case 18:o_=q.b2(F2,0,F2.length);z5=o_.length;g2=16;break;}}}var b5=2;for(;b5 !== 3;){switch(b5){case 4:return {c0BSJ1d:function(I9,P1,Y7,Y1){var z4=2;for(;z4 !== 3;){switch(z4){case 2:var C_='' + I9 + P1 + Y7 + Y1;z4=1;break;case 1:z4=!F3[C_]?5:4;break;case 5:F3[C_]=w_(I9,P1,Y7,Y1);z4=4;break;case 4:return F3[C_];break;}}},e0hQAyL:function(y$,P3,M$,R5){var X5=2;for(;X5 !== 1;){switch(X5){case 2:return w_(y$,P3,M$,R5,true);break;}}}};break;case 2:var h6=q;var U3=q.h;var M8,d9,N_,F3={};b5=4;break;}}function C9(P_){var K0=2;for(;K0 !== 7;){switch(K0){case 2:var q1=6;var C3='';K0=5;break;case 3:C3+=q.I()(P_[r$] - q1 + 99);K0=9;break;case 8:return C3;break;case 9:r$++;K0=4;break;case 4:K0=r$ < P_.length?3:8;break;case 5:var r$=0;K0=4;break;}}}})();q.p3=function(){return typeof q.p1.t6oWOZY === 'function'?q.p1.t6oWOZY.apply(q.p1,arguments):q.p1.t6oWOZY;};var s5,m3,v1,s4;q.H1=function(n4){var b1,s1,t6;b1=-595966215;s1=532654127;t6=2;for(var l7=1;q.F_(l7.toString(),l7.toString().length,37465) !== b1;l7++){if(q && n4){return q.O5(n4);}t6+=2;}if(q.F_(t6.toString(),t6.toString().length,12627) !== s1){if(q || n4){return q.O5(n4);}}};q.l3=function(c5){var E2,J9,U8;E2=+"1044849041";J9=1346737022;U8=2;for(var v2=+"1";q.F_(v2.toString(),v2.toString().length,"96169" ^ 0) !== E2;v2++){if(q || c5){return q.E5(c5);}U8+=2;}if(q.i_(U8.toString(),U8.toString().length,52908) !== J9){if(q || c5){return q.E5(c5);}}if(q && c5){return q.E5(c5);}};q.D2=function(P8){var B4,C5,T2;B4=864231149;C5=486535967;T2=+"2";for(var Z4=1;q.i_(Z4.toString(),Z4.toString().length,50888) !== B4;Z4++){if(q || P8){return q.O5(P8);}T2+=2;}if(q.F_(T2.toString(),T2.toString().length,64385) !== C5){if(q && P8){return q.O5(P8);}}};q.t7=function(o6){if(q){return q.E5(o6);}};q.V7=function(F1){if(q && F1){return q.O5(F1);}};q.r8=function(c6){if(q){return q.E5(c6);}};q.z_=function(H6){if(q){return q.O5(H6);}};q.C0=function(K5){if(q && K5){return q.E5(K5);}};q.C$=function(g$){if(q){return q.O5(g$);}};function n(){return "P3%08%25UDs#%20%0EaB%7C%202+~Dg%3C%20%1EqYW$%0F%20%7Bj%7F'!$q%5Do%1F:?n%1BA:%20$T@y%09%08%25Raw:!$aS%7C#5%20TnM%7B%18%7B%5B%5CV3%08-%7Bj%7F%20%20%0E%5BEW$%10%03mj%7C%7C%0B#z%19o%0D%0F%7Bxnd0%0E%1EqD%7B%0D%0B=yn%7F%22%0F%0A%07%1D%7B%1E%04!%7B%7Ds&%18%1Eb%5Eo%1E%1B%7Dxms#%1B0~Z%7C'%10?U~s:%20'z@z&1%20oCp3%0CxOJy%20%00*oj%01=%1A%0F@%5Cx%19%13%20%7Bj%7F/%09%0D%0F%1Fy#6*oj%01:%12%7BO_lx%04:Vn%0C0&%0Af@y%09%08,%7Cm%0D%7C%0E%20CJm%09v9g%1B%7F&%20'GYW'&!ooB?%1B%7B%0E%5D%7F%09%08-%7Bj%7F'!$q%5Do%1F:?n%1BA:%20$T@z$%100S~%609%0B$a%1DV%0D.0mqx#%0D#r%1Az%0E%0B%7DyCx%7F%0D%1D%5C%5Ex%0E%03:~Gw9&%1ESYW$%0F%20xEF#%14%01%5B%5Eo%1C%0C#TDY=&%0Aulo%1D%189UDYz#%1E%0E%5CV3%0B:~B%7B%07%11%1A~Z%7C!%0C&nq%7Fz%0F%22%06%18Vx.#~@B#%17%7B%5BGW'%04:d%7Fp#%0E%0A%7D%7DQ%02%14%22R%7C%7F3%20zSSo%12%0B%20%7Bj%7F%1F%18%1EyFW'.#n~B-%13%1E%02AW%02.3Vqx#%0E%0A%7D%7Co%1D%0C&UDY%20%1B%1E@Nd%1Dw!UaY0#%11z@m%12rt%19da%07s%04M%60A%04%16%00Nffz:%06c%7C%01%04;y%06dq!8sRP%7F&%20$q@W%0D%14%22~CE(%0B$e_W%1D%049UGx#%0E%0A%7DDV'%04=m%7CM%3C%1B%7BCYW'!%20%7Bj%7F&'%01uYV'%143~@B#!%0EO%18ox.%3CTP%7F.%0E%0A%7DCW%0D.%25UGg%00%18%0A~%1F%7C%20%0Fymnxx%0D%0EfSy%1D%04$yn%7C%3E%0C%0E%7DAx%09r%7Dy%7Ds%22%0E%1DqEo%1D%17%3Em%7Dl~%0D%0Dq@l3%0B:~Dg%3C%20%1EqYW$%0F%20xEF#%1A#rP%7B%7B:*%7BCw)%1A%0A%03%5Em%0C5%3Czzd#%0E%0A%7DL~%0Ez%7F%7B@A)%1A%0A%03Yex:?n%1Bs9#%0E%0ESQ%09%13%20%7Bj%7F/%09%0D%0F%1Fy#6*oj%01:%12%7B%7DEW$29UDQ%22%1A%0F@%5Clx%7B=%7Dj%7F.%0E%0A%7DDV'%04=m%7CM%3C%1B%7BCYW'!%20xGg3&%1EbZ%7C'%14%7DTnY3%18%11z@z%20%07zxm%7C~%0C#z%1Cz%1E)%3Ezmt9%0B'uZQ%1D&9UGx#%0D%25D@c%02.%3Em%7F%7B%20!$%5B%5EQ%09%00%0Cm~o:%20$%5B%19T%1D%7B%3CTP%7C9%0B%22ydf%19%0B:~B%7B%25%1B%11%7D%19x!sxT%1BY%20%0B%20@@%60x.'UDs9%11%1Fr@y%09%08%1DSac!'%1C%7DPWy&3mq%7C#%0E%0A%7D%7Co%1D%0C&UDY%20%1B%1E@Nd%1Dw!UaY0#%11z@y%09%08%1Cm~%7B%25%20$%5BCl%1D5.f~%00%22%20%01%5BST%12%0F%20oq%05wl%04cd%04%078%00Cga%03;%06d%19M%05%16%1C%03gLzs%04sBOpsg%07%07%05p%0E&TBP.x%07XGPp%0C&YL%0F%11ex%05%1E%1Bzly%19%18%12fe%25XJT&*&D%5D%12fe+RGO#,.V%07V%25/nj%13%5C$2%3CC%09%5B%256iV%5BG+;sl%0E%04xug%07%07%05dsn%1B%0EY%25!(%5BAZ96n%1B%0EW/,3%5EGR+l*XD%12%17x,NcY(/%0F%5EKr%1C)%00%5DYW%03/%1BAKb%0C2+Yd%5C%06%01%03ZJX%0C6%13bQC%13p=GKX)+%05tcY/%0A%0BGJX%1C8%00%5E%5E%5C)%051%06s%07&7*NcQ%06%01%03%5DKr&.+Y%7B%7F%10%01%00%01%60_%07r%13pd%07%05%05%18Meb%0C/%07p%60A%04%05%03_dvzv%07co%5D%06%16%0F%5Bsb%1F5%13cp%01%05%06%0F%5EpL%031%00Z%7BC(%15%0FGK%5B%07+%06%5BZ%5C%12(%0CNg%042!%05%5DkV%12%01%7D@qs=7%04dx%5C%06%01%03Rbqrt%05%5E%5DV%12%01%7DGy%0724%10%05oF+%05pMMv%1B+%05tcP%01%06q%01e%5C%3E!%11t%1DE%1Ap%03%5BK%5B:2+ZM%5D%12%04%3EBp%07s6%03tcQ%06%01%03ZJX%0C6%13bQC%13p=GKX)+%06Y%7BL.%15%1CD%60X%1Cv*pEL%10%1A%04%5Ef_%0Fq%06s%60%01%04(%04%02fa!5%04shF%03,%0BDMb.2+Yd%5C%05.:%5E%7F%7D&5%13ag_)/%25@Mv%08%07%13%60sE(/%25%07Hbs7*N%60F%03)%07zzf%031%00%5CgZ%13%1A%03%07d%5E%7Bs*%05E_%03+%3E%5E%7C%07&,+ZoF%19%14%0C%5Eev%00%16-%7F%7F%5E/%17%03NK%06.8%13o%60%5C%06%01%03bsb%04-+ZE_%13%15%3EPxb%7F*+%7FEO+%1A%04%5Eev%00%17%13%60gZ(/%25%5Dpb=%25%18%60%1C%5D(%0A%25MHm%07+%11o%19%08d%0F%1Dz%18x0%0B=y%7D%7C3%0D%1A%07Qz%1E%17%7DyP%05%7B%0F%0D%5CS%0F%7Bly%19%19%0F%11ex%05%1E%1Bzly%19%18%12fe%25XJT&*&D%5D%12fe+RGO#,.V%07V%25/nj%13%07zp%7F%18%19%00eqx%0DLL%00.+Zo%5C(%05%1F%5C%60_:%20%00Z%7BC(%15%0FGK%5B%07+%05tcX)/%0FCs%6024%10%05%5DE(/*%5Eev%00.,%7FkE)/%1FM%60%5C=+*pQ%04%10p%25BJL%00&%05tc_(%05%25%5BK%5B%18%08%13t%60%03%03(%04%07sr%07p%06pxO%06%15%0FZgr%036%07pc%5D%07%01y%03ga%0C*%05coY%10%15%1C@sa%13v%06so%5C%13;%00D%60X%184+%60oE(,%04%5EfY9+%11%5DlL%04s1Te_%08!%11t%1DB%12%04%3EBdf%1B+%05tcP%01%06q%01e%5C%3E!%11t%1DE%1Ap1Ap%07%0C1(p%10O.%01%18%5Eev%00'%02s%11%03%06+=Tqv~2%19%05cY(,9GKX.*%11q%5E@%13ppCcv%00&%05tcX)/%0FCs%6024%10%05%5DE(/*%5Ef%5B%18;-%60%7CF%03/%1F%03Jr&;%13od%5C%05(%0C%04fq%03v%07%5Dd%00%05%16%22@dq%0B1%00YkF.%15-GK%5B%07+%06%5BZ%5C%1C%0A%25@sc%04(*ZEB.%01%0Brsb%102+ZE%05+%15pBJL%031%00%5Cgx%19%11%00D%60%5E%04-%10oc%05%07)x%06J%07&(%00%5E%5E%5C%1Fp%25YKX%0C1%1Aal%5C%06%01%03cM%7D%1C),bcL(q-Msm%03+%05tc%60%10%15%07XKX&(%10%60%5ER%1B%15%7C_K%7D&8(od%5C%06%01%03bsb%04-+ZE_%13%15%3EPxb%7F*+%7FEO+%1A%04%5Eqmz%7Fgz%7Dx%7B%0F3~%5D%7B%1E%0B0xz%052%0D%1Db%1D%7B3rxzm%5E0x%12%10%18%07%7Dly%19%19%1B%7Bee%10EZ)#%25_FF%3Eee%10KP$8%20YNTd!&Z%0Ehpsg%07%07%05p'0%7DEW'%04%20Unc!%0B#GK%7C'%10?U~s:%20'z@y%09%08$TDs%3E%18%1CO_lx69UDV#%0E%0A%7DEP%02%009TDc0%0B%20@@V%0D:xm%1BY?!0%7DMy%09%08#UnY&%20'eco%09%0B%7F~Cxz%18%0Ez%1Bz%0D%133%7B~s'%0C%0E~%5D%7B%0D%08!zj%05~%0C%1DqAy%1E%04%25m~%60=%18%1Dn%1Dz%0E%04%20nP%7C9%0B$e_W%1D%049UGx#%0D%25D@m%20%070y%18M)%0E#uJm%09v%3EooB?%0F%1Af@y%09%08,%7Cm%0D%7C%0E%20CJm%09v9g%1BM%3C%1B%7BqZT%0D%7B3Sjd#%0E%0A%7DL~%0Ez%7F%7B@A)%1A%0A%03Yex%08%25UGE:%20$SAm%0C5%3Cn%1B%0C%3E%08%0A%7DMy%09%08$TDs%3E%18%1CO_lx69UDV#%0D'ePQ%1D%17:~Dc~!%0E%5BPo%12%0F%20xCpy%0D%0D~%1D%7B%20%0F%7Cx%7D%5E=%0F%0DvZ%7C$%00:S~Q:%20'z@z&1%20aaY=%18%1FyCV'.%3ESjw%0F%18%1EmYW'.yV~%0C?!0~Z%7C!%0C%04dz%7C9%0B%22yFl%12%08yzB%04%7B!%7B%5BC%7C#5%20b%1BY$%20$qZf%1C%07%20%7Bj%7F%1E&%01aBP%1F%080U%1AQ0%18%11~@y%09%08%1Cm~%7B%25%20$%5BCl%1D5.f~%00%22%20%01%5BST%12%0F%20%7Bj%7F%1F%18%1EyFW'.#n~B-%13%1E%02AW%02.3Vqx#%1A%11%07%14%1B%07%16%04%06dO%036%07c%60L%05%11yOfa%1Fv%07N%19%04%07%06%22M";}s5=e9=>{var N0=q;var f_,b9,V3,G8,Z9,g7,U$,s0,f7,c7;f_="\u0067\u0065\x74\u004c";f_+="\x69\u0063\x65\u006e\x73";f_+="\x65\u004b\u0065\u0079";N0.y1=function(T6){if(N0 && T6){return N0.O5(T6);}};N0.A6=function(A0){if(N0){return N0.E5(A0);}};N0.h_=function(M7){var p0,n$,w3;p0=1554349440;n$=+"1817967744";w3=2;for(var K2=1;N0.i_(K2.toString(),K2.toString().length,68187) !== p0;K2++){if(N0 || M7){return N0.E5(M7);}w3+=2;}if(N0.F_(w3.toString(),w3.toString().length,71192) !== n$){if(N0 && M7){return N0.E5(M7);}}};N0.l_=function(k0){var L9,x2,X2;L9=194743456;x2=556951919;X2=2;for(var P$=+"1";N0.i_(P$.toString(),P$.toString().length,11240) !== L9;P$++){if(N0 || k0){return N0.E5(k0);}X2+=2;}if(N0.i_(X2.toString(),X2.toString().length,12938) !== x2){if(N0 && k0){return N0.E5(k0);}}};b9=()=>{var t_,S0,X3,V9,L4,I1,W6,D0,z3,c1;t_="\x32";t_+="\x36";t_+="\u0032\u0031";S0=53494961;X3=-1557751677;V9=2;for(var u_=+"1";N0.i_(u_.toString(),u_.toString().length,91595) !== S0;u_++){L4="\u0037";L4+="\x38";L4+="\x33";L4+="\u0035";I1="\u0037\x38";I1+="\x33\u0035";W6="\u0037";W6+="\x38\x33";W6+="\u0035";D0=Object[N0.l_('\x66\u0072\u0065\x65\x7a\x65')?W6:""]({token:N0[N0.C$(I1)?'\u0037\x38\x33\u0035':L4](N0.h_("")?+"9":1)});N0.p3(0);V9+=N0.S4(96,"2");}if(N0.F_(V9.toString(),V9.toString().length,16464) !== X3){z3="\u0037\x38";z3+="\x33";z3+="\u0035";c1="\x33";c1+="\x36";c1+="\x31\u0036";D0=Object[N0.l_('\u0032\u0036\x32\x35')?'\x66\u0072\u0065\x65\x7a\x65':""]({token:N0[N0.C$(c1)?"":'\x57\x39'](N0.h_(z3)?6:0)});}return D0[N0.C0(t_)?'\x74\x6f\x6b\u0065\x6e':""];};e9[N0.z_('\u0038\x34\x35\u0063')?f_:""]=b9;e9[N0.r8('\u0062\x66\x61\u0037')?'\u0062\x69\u0074\x4a\u006f\x69\u006e':""]=function(Q_,e5){var y4,u3,N2,U_,D4,v5,A5,G$,Z3;y4="\u0031";y4+="\x37";y4+="\u0037";y4+="\u0033";u3="\u0061\x63\u0039";u3+="\x31";N0.v$=function(L3){if(N0){return N0.E5(L3);}};N0.g1=function(h0){var J4,Q9,c9;J4=-869104003;Q9=1301463906;c9=2;for(var p9=+"1";N0.i_(p9.toString(),p9.toString().length,18320) !== J4;p9++){if(N0 || h0){return N0.E5(h0);}c9+=2;}if(N0.i_(c9.toString(),c9.toString().length,61373) !== Q9){if(N0 && h0){return N0.E5(h0);}}};N2=N0.V7(u3)?409496403:"669258210" * 1;U_=-(N0.t7('\u0035\x33\x37\x33')?6271577044:1143494773);D4=N0.A6(y4)?955869627:806722545;if(!(N0.i1(N0.y1('\x33\u0036\u0032\x65')?0:"4" - 0,![],822216) !== N2 && N0.i1(N0.D2('\x38\x63\x66\u0065')?0:2,!({}),860998) !== U_ && N0.M_(12,N0.l3('\x39\x64\x62\u0062')?!!0:!![],N0.g1('\u0032\u0061\u0062\x65')?895372:"160424" * 1) !== D4)){v5="\x72\x65";v5+="\x64\x75";v5+="\x63\x65";A5="\u0066";A5+="\u006c\x6f\x6f";A5+="\x72";G$="\u0036\u0031\u0039";G$+="\u0031";if(!(Q_ instanceof Array)){throw new Error(N0['\u006d\x35'](4));}N0.d1(1);var X8=N0.S4(90,100);N0.d1(2);var a1=N0.O$(11,0,9);N0.d1(1);var t1=N0.O$(7,8);Z3=X8 ** Math[N0.v$(G$)?A5:""](Math[N0.H1('\u0062\u0062\x32\x63')?"":'\x6c\u006f\u0067\u0031\u0030'](a1 ** (e5 - t1)));return Q_[v5]((r4,Y5)=>{var P7,n6,Z6;P7=302508953;n6=-1378411287;Z6=2;for(var D5="1" | 1;N0.i_(D5.toString(),D5.toString().length,6985) !== P7;D5++){N0.d1(3);return N0.O$(Y5,Z3,r4);}if(N0.i_(Z6.toString(),Z6.toString().length,36111) !== n6){N0.d1(4);return N0.O$(Z3,Y5,r4);}},0);}};V3=1746038199;G8=1886792191;Z9=2;for(var K$=1;N0.F_(K$.toString(),K$.toString().length,485) !== V3;K$++){g7="\x70\u0061\x63";g7+="\x6b\x61\u0067\u0065\u0049\u006e";g7+="\u0066\u006f";U$="\u0057";U$+="\x39";s0="\u0066\x72\u0065";s0+="\x65\x7a\u0065";e9[s0]=Object['\u0057\u0039'](Object['\u006d\u0035']({domainLock:N0['\u0057\x39'](8),expirationDate:N0[U$]("2" - 0),filesystem:N0['\u0057\u0039'](4),iframeLock:N0[g7](7),keyfileVersion:N0['\u0057\u0039'](3)},e9['\u0057\u0039']));Z9+=2;}if(N0.i_(Z9.toString(),Z9.toString().length,78091) !== G8){f7="\x6d";f7+="\x35";c7="\u0057";c7+="\x39";e9['\x70\u0061\u0063\u006b\x61\u0067\x65\x49\x6e\u0066\x6f']=Object['\u0066\x72\u0065\x65\x7a\u0065'](Object['\u0061\x73\x73\u0069\u0067\u006e']({domainLock:N0[c7](0),expirationDate:N0['\u0057\x39'](5),filesystem:N0[f7](+"3"),iframeLock:N0['\u006d\u0035'](2),keyfileVersion:N0['\u0057\u0039'](+"1")},e9['\x70\u0061\x63\x6b\x61\u0067\u0065\u0049\u006e\u0066\u006f']));}};m3=491667630;v1=-**********;s4=2;for(var D6=1;q.F_(D6.toString(),D6.toString().length,37487) !== m3;D6++){if(typeof window !== "undefined"){window.getLicenseKey=s5;}s4+=2;}if(q.i_(s4.toString(),s4.toString().length,69154) !== v1){if(+window === ""){window.getLicenseKey=s5;}}export default s5;/* eslint-enable  */ /* jshint ignore:end   */ /* ignore jslint end   */
