import React from 'react';
import { GetServerSideProps, NextPage } from 'next';
import { getDeviceInfoFromRequestHeaders } from '@benzinga/device-utils';
import { getGlobalSession } from '../api/session';
import { ArticlePageTemplate, ArticlePageProps, getArticleServerSideProps } from '@benzinga/templates';
import { getArticleData } from '../api/articles';
import { ContentManager } from '@benzinga/content-manager';
import { ArticleCampaignSettings } from '../../src/config';
import IndiaBannerAd from '../../src/components/AdUnits/IndiaBannerAd';
import { ArticleTopPanel } from '../../src/components/TopPanels/ArticleTopPanel';
import { Mode } from '@benzinga/article';
import styled from 'styled-components';
import InfographicTopPanel from '../../src/components/TopPanels/InfographicsTopPanel';

const ArticlePage: NextPage<ArticlePageProps> = (props: ArticlePageProps) => {
  const hasInfographicsTag = props?.article?.tags?.some(tag => tag.name === 'infographics') || false;
  const primaryImageURL = props?.article?.primaryImage?.url?.replace('//www.benzinga', '//cdn.benzinga') ?? '';

  return (
    <>
      <IndiaBannerAd deviceType={props.deviceType} />
      {props.article && !hasInfographicsTag && <ArticleTopPanel {...props.article} />}
      {hasInfographicsTag && <InfographicTopPanel primaryImageURL={primaryImageURL} title={props?.article?.title} />}

      <ArticlePageTemplate
        {...props}
        articleScrollViewMoreLink={'news'}
        disablePartnerAdOnScroll={false}
        disablePaywall={true}
        disableWNSTNWidget={true}
        enableConnatixScript={false}
        enablePlaystreamIndiaScript={true}
        googleNewsUrlKey={'benzingaIndia'}
        hideSidebar={hasInfographicsTag}
        hideTaboola={hasInfographicsTag}
        hideTopPanel={true}
        loadInfiniteArticles={!hasInfographicsTag}
        loadMoreButtonVariant={'flat-ind-blue'}
        postedInVariant={'secondary'}
        showApiText={false}
        showCommentButton={false}
        showFontAwesomeIcons={true}
        showWhatsAppIcon={true}
        taboolaSettings={{
          placementMethod: 'below-article',
          unitKey: 'benzinga-benzingaind',
          unitMode: 'alternating-thumbnails-a' as Mode,
        }}
      />
    </>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ query: { id: nid }, req, res }) => {
  // console.log('Starting build for article page: ' + nid);

  const deviceInfo = getDeviceInfoFromRequestHeaders(req.headers, req.cookies);
  const deviceType = deviceInfo?.deviceType ?? null;

  try {
    const session = getGlobalSession();
    const contentManager = session.getManager(ContentManager);

    const result = await getArticleServerSideProps(
      session,
      nid as string,
      req.url as string,
      deviceInfo,
      getArticleData,
    );

    const menuResponse = await contentManager.getNavigation('money_india');
    const menuData = menuResponse?.ok ?? null;

    res.statusCode = result.statusCode;
    result.props.headerProps = {
      hideBanner: true,
      hideQuoteBar: true,
      hideSearchBar: true,
      hideTopBar: true,
      isMainDrawerVisible: true,
      menuData: menuData,
    };
    result.props.campaignSettings = ArticleCampaignSettings;
    result.props.deviceType = deviceType;

    if (result.props?.article?.metaProps?.canonical && result.props?.article?.type !== 'benzinga_wire_india') {
      result.props.article.metaProps.canonical = result.props.article.metaProps.canonical.replace(
        'https://in.',
        'https://www.',
      );
    }

    return {
      props: result.props,
    };
  } catch (error) {
    res.statusCode = 404;
    console.error('an article error:', error);
    return {
      props: {
        article: null,
        disablePageTracking: true,
        headerProps: {
          hideBanner: true,
          hideMenuBar: true,
          hideQuoteBar: true,
          hideSearchBar: true,
          hideTopBar: true,
          isMainDrawerVisible: true,
        },
        nid: nid,
        wordCount: null,
      },
    };
  }
};

// export const getServerSideProps: GetServerSideProps = async ({ query: { id: nid, ...queryParams }, req, res }) => {
//   console.log('Starting build for article page: ' + nid);

//   // ToDo: Update the content data source.  The current API has limited properties and is also limited to BZ (not PRO) content and will throw a 404 otherwise
//   const session = getGlobalSession();

//   try {
//     const articleAndCampaignJsonResp = await Promise.all([getArticleData(`${nid}`)]);
//     const articleRes = await articleAndCampaignJsonResp[0];
//     const article = articleRes.ok ?? null;

//     const menuResponse = await session.getManager(ContentManager).getNavigation('money_india');
//     const menuData = menuResponse?.ok ?? null;

//     if (articleRes.err || article.type !== 'benzinga_wire_india') {
//       res.statusCode = 404;
//       console.error('the article error:', articleRes.err);

//       return {
//         props: {
//           article: null,
//           disablePageTracking: true,
//           headerProps: {
//             hideBanner: true,
//             hideMenuBar: true,
//             hideQuoteBar: true,
//             hideSearchBar: true,
//             hideTopBar: true,
//             isMainDrawerVisible: true,
//           },
//           nid: nid,
//         },
//       };
//     }

//     const campaignStrategy = getArticleCampaignStrategy(queryParams, article);

//     const isCryptocurrencyChannel = nodeHasChannel(article as unknown as StoryObject, 'Cryptocurrency');
//     const headerProps = {
//       disableOptinMonster: campaignStrategy === 'none',
//       hideBanner: true,
//       hideQuoteBar: true,
//       hideSearchBar: true,
//       hideTopBar: true,
//       isMainDrawerVisible: true,
//       menuData: menuData,
//       navigationLogoVariant: isCryptocurrencyChannel ? 'crypto' : 'default',
//     };

//     const contentManager = session.getManager(ContentManager);

//     const node_id = nid as string;
//     const getLayoutReq = contentManager.getNodeLayout('31355142');
//     const layoutRes = await safeTimeout(getLayoutReq, 3000);
//     const layout: NodeLayout = layoutRes.ok ?? null;

//     try {
//       const translationResponse = await contentManager.getNodeTranslation(node_id);
//       if (translationResponse?.ok) {
//         const { data, status } = translationResponse.ok;

//         if (status === 'ok' && data) {
//           if (data.translations) {
//             article.translationMeta = data.translations;
//           }
//         }
//       }
//     } catch (e) {
//       console.error('ARTICLE TRANSLATION ERROR:', e);
//     }

//     const tickers = getPrimaryTickers(article?.tickers);

//     if (article?.metaProps && req?.headers?.referer) {
//       article.metaProps.referer = req.headers.referer;
//     }

//     // Removes Ads from sidebar if Campaign Strategy None
//     if (campaignStrategy === 'none' && !!layout?.sidebar?.blocks) {
//       layout.sidebar.blocks = removeAdsFromBlocks(layout.sidebar.blocks);
//     }

//     if (Array.isArray(layout?.sidebar?.blocks)) {
//       layout.sidebar.blocks = await loadServerSideBlockData(session, layout.sidebar.blocks);
//     }

//     const newsletterSubscriptionBox = layout.sidebar.blocks.find(
//       block => block.blockName === 'acf/newsletter-subscription-box',
//     );

//     if (newsletterSubscriptionBox && Array.isArray(layout.in_content.blocks)) {
//       layout.in_content.blocks.push(newsletterSubscriptionBox);
//     }

//     return {
//       props: {
//         article,
//         baseUrl: 'https://in.benzinga.com',
//         campaignStrategy,
//         headerProps,
//         layout,
//         metaProps: article?.metaProps ?? null,
//         nid: nid,
//         pageTrackerProps: {
//           pageLoad: {
//             contentId: article.nodeId,
//             tickers,
//             type: 'article_page',
//           },
//         },
//         tickers,
//       },
//     };
//   } catch (error) {
//     res.statusCode = 404;
//     console.error('an article error:', error);
//     return {
//       props: {
//         article: null,
//         disablePageTracking: true,
//         nid: nid,
//       },
//     };
//   }
// };

export default ArticlePage;

const SocialIcon = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 4px;
`;
